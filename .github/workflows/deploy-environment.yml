name: Deploy Environment

on:
  workflow_call:
    inputs:
      env_name:
        description: "Environment name (e.g., demo-1, prod-1)"
        required: true
        type: string
      aws_account:
        description: "AWS account ID to deploy to"
        required: true
        type: string
      aws_region:
        description: "AWS region for target account"
        required: true
        type: string
      cloudfront_distribution_id:
        description: "CloudFront distribution ID to invalidate"
        required: true
        type: string
      tag:
        description: "Release tag to deploy (optional)"
        required: false
        type: string

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    name: Deploy ${{ inputs.env_name }}
    runs-on:
      group: large_runners
    timeout-minutes: 60

    steps:
      - name: Notify deployment start
        run: |
          if [ -n "${{ inputs.tag }}" ]; then
            echo "::notice::🚀 Starting deployment to ${{ inputs.env_name }} for tag ${{ inputs.tag }} (commit ${{ github.sha }})"
          else
            echo "::notice::🚀 Starting deployment to ${{ inputs.env_name }} (commit ${{ github.sha }})"
          fi

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Debug OID<PERSON> Claims
        uses: github/actions-oidc-debugger@v1

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.6'

      - name: Debug OIDC Claims
        uses: github/actions-oidc-debugger@2e9ba5d3f4bebaad1f91a2cede055115738b7ae8
        with:
          audience: '${{ github.server_url }}/${{ github.repository_owner }}'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Prepare CDK (build lambda zip files)
        run: ./infra/scripts/prepare-cdk.sh

      - name: Configure AWS credentials for ${{ inputs.env_name }} account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ inputs.aws_account }}:role/GitHubActionsRole
          aws-region: ${{ inputs.aws_region }}

      - name: Deploy MigrationRunnerStack for Migration Lambda
        run: |
          cd infra
          npm install
          CDK_ENV=${{ inputs.env_name }} cdk deploy --require-approval never --exclusively MigrationRunnerStack
        env:
          AWS_USE_FIPS_ENDPOINT: "true"

      - name: Invoke Migration Lambda
        run: |
          echo "::notice::📦 Invoking migration lambda..."
          aws lambda invoke \
            --function-name MigrationRunnerLambda \
            --payload '{}' \
            /tmp/migration-output.json
          cat /tmp/migration-output.json
          if jq -e '.message | test("Migrations ran successfully!")' /tmp/migration-output.json > /dev/null; then
              echo "::notice::Migration lambda finished successfully."
          else
              echo "::error::Migration lambda returned an error."
              jq '.' /tmp/migration-output.json
              exit 1
          fi
        env:
          AWS_USE_FIPS_ENDPOINT: "true"

      - name: Run CDK Deploy
        run: |
          if [ -n "${{ inputs.tag }}" ]; then
            ./infra/scripts/deploy-services.sh ${{ inputs.env_name }} ${{ inputs.tag }}
          else
            ./infra/scripts/deploy-services.sh ${{ inputs.env_name }}
          fi
        env:
          AWS_USE_FIPS_ENDPOINT: "true"

      - name: Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ inputs.cloudfront_distribution_id }} \
            --paths "/*"
        env:
          AWS_USE_FIPS_ENDPOINT: "true"

      - name: Notify deployment success
        run: |
          if [ -n "${{ inputs.tag }}" ]; then
            echo "::notice::✅ Deployment to ${{ inputs.env_name }} completed successfully for tag ${{ inputs.tag }} (commit ${{ github.sha }})"
          else
            echo "::notice::✅ Deployment to ${{ inputs.env_name }} completed successfully (commit ${{ github.sha }})"
          fi


