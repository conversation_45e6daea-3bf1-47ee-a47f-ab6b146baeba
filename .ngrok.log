t=2025-08-25T11:03:49-0700 lvl=info msg="no configuration paths supplied"
t=2025-08-25T11:03:49-0700 lvl=info msg="using configuration at default config path" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml"
t=2025-08-25T11:03:49-0700 lvl=info msg="open config file" path="/Users/<USER>/Library/Application Support/ngrok/ngrok.yml" err=nil
t=2025-08-25T11:03:49-0700 lvl=info msg="starting web service" obj=web addr=127.0.0.1:4040 allow_hosts=[]
t=2025-08-25T11:03:49-0700 lvl=info msg="client session established" obj=tunnels.session
t=2025-08-25T11:03:49-0700 lvl=info msg="tunnel session started" obj=tunnels.session
t=2025-08-25T11:03:49-0700 lvl=info msg="started tunnel" obj=tunnels name=command_line addr=http://localhost:9084 url=https://8a23acb81f33.ngrok-free.app
t=2025-08-25T11:03:49-0700 lvl=info msg="update available" obj=updater
t=2025-08-25T11:03:50-0700 lvl=info msg="join connections" obj=join id=312c649b8dfd l=[::1]:9084 r=**************:49361
