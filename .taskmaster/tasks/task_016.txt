# Task ID: 16
# Title: Orgs Service Business Logic Implementation
# Status: pending
# Dependencies: 15
# Priority: medium
# Description: Implement the use case layer with business logic validation, line type management, and primary line handling in the orgs service
# Details:
Update `services/orgs/internal/usecase/orgs_usecase.go` with business logic for all phone line operations. Implement ValidateLineType() function supporting 'general', 'emergency', 'parking', 'dispatch', 'admin' types. Add CreateOrgPhoneLine() with organization existence validation, line type validation, and Twilio number uniqueness checks. Implement UpdateOrgPhoneLine() with proper authorization and validation. Add SetPrimaryOrgPhoneLine() with logic to ensure only one primary line per organization. Implement DeleteOrgPhoneLine() with primary line protection (cannot delete if it's the only line). Add ListOrgPhoneLines() with proper filtering and sorting. Include comprehensive error handling with business-specific error messages and proper HTTP status code mapping.

# Test Strategy:
Unit tests for all business logic methods, test line type validation with valid and invalid inputs, test primary line management scenarios, validate organization authorization checks, test edge cases like deleting last remaining line, integration tests with repository layer
