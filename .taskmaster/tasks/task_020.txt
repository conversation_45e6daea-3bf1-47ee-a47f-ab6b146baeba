# Task ID: 20
# Title: Webhook Handler and Call Routing Updates
# Status: pending
# Dependencies: 19
# Priority: medium
# Description: Update Twilio webhook handlers to identify receiving phone lines and modify call creation logic to preserve line context throughout the call lifecycle
# Details:
Update Twilio webhook handlers to extract the 'To' number from incoming webhooks and use GetOrgPhoneLineByTwilioNumber() to identify which phone line received the call. Modify call creation logic to include org_phone_line_id when creating new call records. Update call routing logic to consider line type for specialized handling (emergency vs general calls). Ensure webhook responses include phone line context for proper call flow continuation. Add logging to track which phone line handled each call for debugging and analytics. Update error handling to gracefully handle cases where phone line lookup fails. Maintain backward compatibility for existing single-line setups by defaulting to primary line when line context is missing.

# Test Strategy:
End-to-end testing with multiple phone lines and Twilio webhooks, test call routing preserves line context, validate emergency vs general call handling, test webhook error scenarios, verify logging captures phone line information, test backward compatibility with existing webhook flows, integration testing with staging Twilio environment
