import axios, { AxiosResponse } from "axios";
import { Empty } from "google-protobuf/google/protobuf/empty_pb";
import axiosInstance from "../axiosInstance";

import {
    AcknowledgeOrderRequest,
    AddAdditionalInfoRequest,
    AddAdditionalInfoResponse,
    AddAllowedAssetTypeRequest,
    AddBlacklistedAssetIdRequest,
    AddOrderStatusUpdateRequest,
    AddOrderUpdateRequest,
    CancelOrderRequest,
    CompleteOrderRequest,
    CreateOrderRequest,
    CreateOrderResponse,
    DeleteOrderRequest,
    GetOrderRequest,
    ListActiveAssignedOrdersForAssetRequest,
    ListActiveAssignedOrdersForAssetResponse,
    ListNewOrdersForAssetRequest,
    ListNewOrdersForAssetResponse,
    ListOrdersForAssetRequest,
    ListOrdersForAssetResponse,
    ListOrdersForCaseRequest,
    ListOrdersForCaseResponse,
    ListOrdersForSituationRequest,
    ListOrdersForSituationResponse,
    ListOrdersRequest,
    ListOrdersResponse,
    Order,
    RejectOrderRequest,
    RemoveAllowedAssetTypeRequest,
    RemoveBlacklistedAssetIdRequest,
    RemoveOrderUpdateRequest,
    SnoozeOrderRequest,
    UpdateOrderPermissionsRequest,
    UpdateOrderRequest
} from "proto/hero/orders/v2/orders_pb";

// Custom APIError class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const errorMsg = error.response?.data?.message || error.message;
            throw new APIError(status, `Request to ${url} failed: ${errorMsg}`);
        }
        throw error;
    }
};

// Create Order
export const createOrder = async (
    data: CreateOrderRequest
): Promise<CreateOrderResponse> => {
    return postRequest<CreateOrderResponse, CreateOrderRequest>(
        "/hero.orders.v2.OrderService/CreateOrder",
        data
    );
};

// Get Order
export const getOrder = async (
    data: GetOrderRequest
): Promise<Order> => {
    return postRequest<Order, GetOrderRequest>(
        "/hero.orders.v2.OrderService/GetOrder",
        data
    );
};

// Update Order
export const updateOrder = async (
    data: UpdateOrderRequest
): Promise<Order> => {
    return postRequest<Order, UpdateOrderRequest>(
        "/hero.orders.v2.OrderService/UpdateOrder",
        data
    );
};

// List Orders
export const listOrders = async (
    data: ListOrdersRequest
): Promise<ListOrdersResponse> => {
    return postRequest<ListOrdersResponse, ListOrdersRequest>(
        "/hero.orders.v2.OrderService/ListOrders",
        data
    );
};

// Delete Order
export const deleteOrder = async (
    data: DeleteOrderRequest
): Promise<Empty> => {
    return postRequest<Empty, DeleteOrderRequest>(
        "/hero.orders.v2.OrderService/DeleteOrder",
        data
    );
};

// Add Order Update
export const addOrderUpdate = async (
    data: AddOrderUpdateRequest
): Promise<Order> => {
    return postRequest<Order, AddOrderUpdateRequest>(
        "/hero.orders.v2.OrderService/AddOrderUpdate",
        data
    );
};

// Add Order Status Update
export const addOrderStatusUpdate = async (
    data: AddOrderStatusUpdateRequest
): Promise<Order> => {
    return postRequest<Order, AddOrderStatusUpdateRequest>(
        "/hero.orders.v2.OrderService/AddOrderStatusUpdate",
        data
    );
};

// Remove Order Update
export const removeOrderUpdate = async (
    data: RemoveOrderUpdateRequest
): Promise<Order> => {
    return postRequest<Order, RemoveOrderUpdateRequest>(
        "/hero.orders.v2.OrderService/RemoveOrderUpdate",
        data
    );
};

// Add Allowed Asset Type
export const addAllowedAssetType = async (
    data: AddAllowedAssetTypeRequest
): Promise<Order> => {
    return postRequest<Order, AddAllowedAssetTypeRequest>(
        "/hero.orders.v2.OrderService/AddAllowedAssetType",
        data
    );
};

// Remove Allowed Asset Type
export const removeAllowedAssetType = async (
    data: RemoveAllowedAssetTypeRequest
): Promise<Order> => {
    return postRequest<Order, RemoveAllowedAssetTypeRequest>(
        "/hero.orders.v2.OrderService/RemoveAllowedAssetType",
        data
    );
};

// Add Blacklisted Asset ID
export const addBlacklistedAssetId = async (
    data: AddBlacklistedAssetIdRequest
): Promise<Order> => {
    return postRequest<Order, AddBlacklistedAssetIdRequest>(
        "/hero.orders.v2.OrderService/AddBlacklistedAssetId",
        data
    );
};

// Remove Blacklisted Asset ID
export const removeBlacklistedAssetId = async (
    data: RemoveBlacklistedAssetIdRequest
): Promise<Order> => {
    return postRequest<Order, RemoveBlacklistedAssetIdRequest>(
        "/hero.orders.v2.OrderService/RemoveBlacklistedAssetId",
        data
    );
};

// Acknowledge Order
export const acknowledgeOrder = async (
    data: AcknowledgeOrderRequest
): Promise<Order> => {
    return postRequest<Order, AcknowledgeOrderRequest>(
        "/hero.orders.v2.OrderService/AcknowledgeOrder",
        data
    );
};

// Reject Order
export const rejectOrder = async (
    data: RejectOrderRequest
): Promise<Order> => {
    return postRequest<Order, RejectOrderRequest>(
        "/hero.orders.v2.OrderService/RejectOrder",
        data
    );
};

// Snooze Order
export const snoozeOrder = async (
    data: SnoozeOrderRequest
): Promise<Order> => {
    return postRequest<Order, SnoozeOrderRequest>(
        "/hero.orders.v2.OrderService/SnoozeOrder",
        data
    );
};

// Cancel Order
export const cancelOrder = async (
    data: CancelOrderRequest
): Promise<Order> => {
    return postRequest<Order, CancelOrderRequest>(
        "/hero.orders.v2.OrderService/CancelOrder",
        data
    );
};

// Complete Order
export const completeOrder = async (
    data: CompleteOrderRequest
): Promise<Order> => {
    return postRequest<Order, CompleteOrderRequest>(
        "/hero.orders.v2.OrderService/CompleteOrder",
        data
    );
};

// List Active Assigned Orders for Asset
export const listActiveAssignedOrdersForAsset = async (
    data: ListActiveAssignedOrdersForAssetRequest
): Promise<ListActiveAssignedOrdersForAssetResponse> => {
    return postRequest<ListActiveAssignedOrdersForAssetResponse, ListActiveAssignedOrdersForAssetRequest>(
        "/hero.orders.v2.OrderService/ListActiveAssignedOrdersForAsset",
        data
    );
};

// List New Orders for Asset
export const listNewOrdersForAsset = async (
    data: ListNewOrdersForAssetRequest
): Promise<ListNewOrdersForAssetResponse> => {
    return postRequest<ListNewOrdersForAssetResponse, ListNewOrdersForAssetRequest>(
        "/hero.orders.v2.OrderService/ListNewOrdersForAsset",
        data
    );
};

// Update Order Permissions
export const updateOrderPermissions = async (
    data: UpdateOrderPermissionsRequest
): Promise<Order> => {
    return postRequest<Order, UpdateOrderPermissionsRequest>(
        "/hero.orders.v2.OrderService/UpdateOrderPermissions",
        data
    );
};

// List Orders for Situation
export const listOrdersForSituation = async (
    data: ListOrdersForSituationRequest
): Promise<ListOrdersForSituationResponse> => {
    return postRequest<ListOrdersForSituationResponse, ListOrdersForSituationRequest>(
        "/hero.orders.v2.OrderService/ListOrdersForSituation",
        data
    );
};

// List Orders for Asset
export const listOrdersForAsset = async (
    data: ListOrdersForAssetRequest
): Promise<ListOrdersForAssetResponse> => {
    return postRequest<ListOrdersForAssetResponse, ListOrdersForAssetRequest>(
        "/hero.orders.v2.OrderService/ListOrdersForAsset",
        data
    );
};

// List Orders for Case
export const listOrdersForCase = async (
    data: ListOrdersForCaseRequest
): Promise<ListOrdersForCaseResponse> => {
    return postRequest<ListOrdersForCaseResponse, ListOrdersForCaseRequest>(
        "/hero.orders.v2.OrderService/ListOrdersForCase",
        data
    );
};

// Add Additional Info to Order
export const addAdditionalInfo = async (
    data: AddAdditionalInfoRequest
): Promise<AddAdditionalInfoResponse> => {
    return postRequest<AddAdditionalInfoResponse, AddAdditionalInfoRequest>(
        "/hero.orders.v2.OrderService/AddAdditionalInfo",
        data
    );
};
