"use client";
import { DEFAULT_LOCATION } from "@/app/constants";
import { Box, TextField, Typography } from "@mui/material";
import mapboxgl from "mapbox-gl";
import React, { useEffect, useRef, useState } from "react";
import * as Sentry from "@sentry/nextjs";
import { colors } from "@/design-system/tokens/colors";

mapboxgl.accessToken =
  "pk.eyJ1IjoiaGVyby1zYWZldHkiLCJhIjoiY202bDM3bGlzMDRybTJrcHJibm5sYTFzMSJ9.-ekjZGG1E_cWYCOnBrdEag";

interface Suggestion {
  mapbox_id: string;
  name: string;
  full_address?: string;
  address?: string;
  geometry?: { coordinates: [number, number] };
}

interface LocationAutocompleteProps {
  value: string;
  title?: string;
  onChange: (value: string) => void;
  onSelect?: (suggestion: Suggestion) => void;
}

const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({
  value,
  title,
  onChange,
  onSelect,
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const sessionTokenRef = useRef<string>("session-token-123"); // In production, generate a unique token

  const fetchSuggestions = async (query: string) => {
    if (query.trim() === "") {
      setSuggestions([]);
      return;
    }
    try {
      const suggestUrl = `https://api.mapbox.com/search/searchbox/v1/suggest?q=${encodeURIComponent(
        query
      )}&access_token=${mapboxgl.accessToken}&session_token=${sessionTokenRef.current
        }&proximity=${DEFAULT_LOCATION.longitude},${DEFAULT_LOCATION.latitude}`;
      const response = await fetch(suggestUrl);
      const data = await response.json();
      console.log("Suggestions:", data.suggestions);
      setSuggestions(data.suggestions || []);
    } catch (error) {
      console.error("Error fetching suggestions:", error);
      try {
        Sentry.withScope((scope) => {
          scope.setTag("error_key", "mapbox-search");
          scope.setTag("mapbox_op", "suggestions");
          scope.setTag("component", "ActionPane")
          Sentry.captureException(error);
        });
      } catch {
        console.error("Error capturing mapbox suggestions error:", error);
      }
    }
  };

  // Debounce the API call.
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      fetchSuggestions(value);
    }, 300);
    return () => clearTimeout(delayDebounce);
  }, [value]);

  return (
    <Box position="relative">
      <TextField
        placeholder={title || "Location"}
        variant="outlined"
        fullWidth
        autoComplete="off"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setTimeout(() => setIsFocused(false), 200)}
        size="small"
        InputProps={{
          style: {
            fontSize: "14px",
            display: "flex",
            alignItems: "center",
          },
        }}
        InputLabelProps={{ shrink: false }}
        sx={{
          "& input::placeholder": {
            color: "#969EA8",
            opacity: 1,
          },
          "& .MuiInputLabel-root": {
            display: "none",
          },
          "& .MuiOutlinedInput-root": {
            height: "32px",
            "& fieldset": {
              borderColor: colors.grey[300],
              borderRadius: "8px",
            },
            "&:hover fieldset": {
              borderColor: colors.grey[300],
            },
            "&.Mui-focused fieldset": {
              borderColor: colors.grey[300],
            },
            "& input": {
              height: "32px",
            },
          },
        }}
      />
      {isFocused && suggestions.length > 0 && (
        <Box
          position="absolute"
          top="100%"
          left={0}
          right={0}
          bgcolor="white"
          boxShadow={3}
          zIndex={10}
          maxHeight="200px"
          overflow="auto"
        >
          {suggestions.map((suggestion) => (
            <Box
              key={suggestion.mapbox_id}
              px={2}
              py={1}
              sx={{
                cursor: "pointer",
                "&:hover": { backgroundColor: "#f0f0f0" },
              }}
              onMouseDown={() => {
                onSelect && onSelect(suggestion);
                onChange(suggestion.name);
                setSuggestions([]);
              }}
            >
              <Typography variant="subtitle2" color="textPrimary" fontWeight={600}>
                {suggestion.name}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {suggestion.full_address || suggestion.address}
              </Typography>
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default LocationAutocomplete;
