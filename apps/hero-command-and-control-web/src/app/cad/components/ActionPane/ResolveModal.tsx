"use client";
import CloseIcon from "@mui/icons-material/Close";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  SelectChangeEvent,
  Typography
} from "@mui/material";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import { Order, OrderStatus } from "proto/hero/orders/v2/orders_pb";
import React, { useEffect, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Checkbox } from "../../../../design-system/components/Checkbox";
import { Dropdown, DropdownOption } from "../../../../design-system/components/Dropdown";

type ResolveModalProps = {
  open: boolean;
  onClose: () => void;
  onNext: (disposition: string, primaryResponderId?: string, createCase?: boolean) => void;
  isAllAssistMemberOrdersCompleted: boolean;
  hasExistingCase?: boolean;
  availableResponders?: Asset[];
  ordersForSituation?: Order[];
  incidentId?: string;
};

const ResolveModal: React.FC<ResolveModalProps> = ({
  open,
  onClose,
  onNext,
  isAllAssistMemberOrdersCompleted,
  hasExistingCase = false,
  availableResponders = [],
  ordersForSituation = [],
  incidentId,
}) => {
  const [disposition, setDisposition] = useState("");
  const [primaryResponderId, setPrimaryResponderId] = useState<string | null>(null);
  const [createCase, setCreateCase] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!open) {
      setDisposition("");
      setPrimaryResponderId(null);
      setCreateCase(false);
    }
  }, [open]);

  // Find the responder with the earliest uncancelled assist member order
  const getDefaultPrimaryResponder = () => {
    const uncancelledOrders = ordersForSituation.filter(
      (order) =>
        order.status !== OrderStatus.CANCELLED &&
        order.status !== OrderStatus.REJECTED
    );

    if (uncancelledOrders.length === 0) return null;

    // Sort by createTime, with fallback to order ID if createTime is not available
    const sortedOrders = uncancelledOrders.sort((a, b) => {
      const aTime = a.createTime ? new Date(a.createTime).getTime() : 0;
      const bTime = b.createTime ? new Date(b.createTime).getTime() : 0;

      if (aTime !== 0 && bTime !== 0) {
        return aTime - bTime;
      }

      // Fallback to string comparison of IDs if timestamps are not available
      return (a.id || '').localeCompare(b.id || '');
    });

    return sortedOrders[0]?.assetId || null;
  };

  const getRespondersWithUncancelledOrders = () => {
    const uncancelledOrderAssetIds = ordersForSituation
      .filter(order => order.status !== OrderStatus.CANCELLED && order.status !== OrderStatus.REJECTED)
      .map(order => order.assetId);

    return availableResponders.filter(responder => uncancelledOrderAssetIds.includes(responder.id));
  };

  useEffect(() => {
    if (open && availableResponders.length > 0 && ordersForSituation.length > 0) {
      const defaultResponderId = getDefaultPrimaryResponder();
      const filteredResponders = getRespondersWithUncancelledOrders();

      // If we have a default responder and it exists in the filtered responders, set it
      if (defaultResponderId && filteredResponders.some(r => r.id === defaultResponderId)) {
        setPrimaryResponderId(defaultResponderId);
      } else if (filteredResponders.length > 0) {
        // Fallback: if no default found but we have responders, use the first one
        setPrimaryResponderId(filteredResponders[0].id);
      }
    }
  }, [open, availableResponders, ordersForSituation]);

  const handleChange = (event: SelectChangeEvent) => {
    setDisposition(event.target.value);
  };

  const handleNext = () => {
    const fullDispositionLabel = getFullDispositionLabel(disposition);
    onNext(fullDispositionLabel, primaryResponderId || undefined, createCase);
  };

  const handleClose = () => {
    onClose();
  };

  const dispositionOptions: DropdownOption[] = [
    { label: "Arrest Made", value: "ARR" },
    { label: "Cancelled", value: "CAN" },
    { label: "Medical Assist", value: "MED" },
    { label: "No Report Required", value: "NRR" },
    { label: "Report Taken", value: "RPT" },
    { label: "Unfounded Event", value: "UNF" },
  ];

  // Map abbreviated values to full labels for backend compatibility
  const getFullDispositionLabel = (abbreviatedValue: string): string => {
    const mapping: Record<string, string> = {
      "ARR": "Arrest Made",
      "CAN": "Cancelled",
      "MED": "Medical Assist",
      "NRR": "No Report Required",
      "RPT": "Report Taken",
      "UNF": "Unfounded Event"
    };
    return mapping[abbreviatedValue] || abbreviatedValue;
  };

  const responderOptions: DropdownOption[] = getRespondersWithUncancelledOrders().map((responder) => ({
    value: responder.id,
    label: responder.name ? `${responder.id.replace(/[^0-9]/g, "").slice(0, 3)} ${responder.name}` : responder.id,
  }));

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth="xs"
      PaperProps={{
        style: {
          backgroundColor: "white",
          padding: "24px",
          borderRadius: "12px",
        },
      }}
      BackdropProps={{
        style: {
          backgroundColor: "rgba(0, 0, 0, 0.5)",
        },
      }}
    >
      <DialogTitle
        sx={{
          textAlign: "left",
          position: "relative",
          padding: "0 0 24px 0",
          fontSize: "16px",
          fontWeight: 500,
        }}
      >
        {`Resolve Incident`}
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          {incidentId
            ? `Incident ${incidentId.replace(/[^0-9]/g, "").slice(0, 3)}`
            : "Incident"}
        </Typography>
        <IconButton
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: -12,
            top: -12,
            color: "rgba(0, 0, 0, 0.54)",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {!isAllAssistMemberOrdersCompleted && (
          <Box
            sx={{
              display: "flex",
              alignItems: "flex-start",
              mb: 3,
              p: 2,
              backgroundColor: "#FFF3CD",
              borderRadius: "8px",
              border: "1px solid #FFE69C",
            }}
          >
            <WarningAmberIcon
              sx={{ color: "#856404", fontSize: 20, mr: 1, mt: 0.25 }}
            />
            <Box>
              <Typography
                variant="body2"
                sx={{ color: "#856404", fontWeight: 500 }}
              >
                All units not cleared
              </Typography>
              <Typography variant="body2" sx={{ color: "#856404", mt: 0.5 }}>
                Resolving incident will mark all assigned officers as cleared.
              </Typography>
            </Box>
          </Box>
        )}
        <Box sx={{ mb: 3 }}>
          <Dropdown
            title="Disposition"
            placeholder="Select Disposition"
            options={dispositionOptions}
            value={disposition}
            onChange={(val: string | null) => setDisposition(val || "")}
          />
        </Box>
        <Box sx={{ mb: 3 }}>
          <Dropdown
            title="Primary Responder"
            placeholder="Select Primary Responder"
            options={responderOptions}
            value={primaryResponderId}
            onChange={(val: string | null) => setPrimaryResponderId(val)}
            enableSearch
          />
        </Box>

        {!hasExistingCase && (
          <Box sx={{ mb: 3 }}>
            <Checkbox
              label="Create Case #"
              checked={createCase}
              onChange={(e) => setCreateCase(e.target.checked)}
              size="medium"
              color="blue"
            />
            {!createCase && (
              <Box sx={{ display: "flex", alignItems: "center", mt: 3 }}>
                <InfoOutlinedIcon
                  sx={{
                    color: "rgba(0, 0, 0, 0.54)",
                    fontSize: 20,
                    mr: 1,
                    mt: 0.5,
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  No case exists for this incident. A report will not be created
                  upon resolution
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 0, mt: 2, gap: 1 }}>
        <Button
          label="Cancel"
          onClick={handleClose}
          style="ghost"
          color="grey"
          size="medium"
        />
        <Button
          label="Resolve Incident"
          onClick={handleNext}
          disabled={!disposition}
          style="filled"
          color="blue"
          prominence={true}
          size="medium"
        />
      </DialogActions>
    </Dialog>
  );
};

export default ResolveModal;
