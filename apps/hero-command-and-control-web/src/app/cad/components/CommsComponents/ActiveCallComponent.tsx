import { useSituation } from "@/app/apis/services/workflow/situations/hooks";
import { formatPhoneNumberForDisplay, useCallerDisplayInfo } from "@/app/utils/caller-identification";
import { getIncidentLabel } from "@/app/utils/utils";
import { colors } from "@/design-system/tokens/colors";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";
import CallEndIcon from "@mui/icons-material/CallEnd";
import MicIcon from "@mui/icons-material/Mic";
import MicOffIcon from "@mui/icons-material/MicOff";
import PauseIcon from "@mui/icons-material/Pause";
import PhoneInTalkIcon from "@mui/icons-material/PhoneInTalk";
import { Box, Typography } from "@mui/material";
import { QueuedCall } from "proto/hero/communications/v1/conversation_pb";
import { useEffect, useState } from 'react';
import { protobufTimestampToMillis } from "./utils/timestampUtils";

interface ActiveCallProps {
  activeCall: QueuedCall | null;
  holdCall?: QueuedCall | null;
  isMuted?: boolean;
  isConnecting?: boolean;
  onEndCall?: () => void;
  onHoldToggle?: () => void;
  onToggleMute?: () => void;
}

const styles = {
  container: {
    display: "flex",
    width: "100%",
    flexDirection: "column",
    gap: spacing.xs,
  },
  header: {
    display: "flex",
    width: "100%",
    alignItems: "center",
    gap: spacing.xs,
  },
  outboundPhoneNumber: {
    ...typography.styles.h3,
    color: colors.grey[900],
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  situationId: {
    ...typography.styles.h2,
    color: colors.grey[900],
  },
  situationType: {
    ...typography.styles.h3,
    color: "#000000",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    lineHeight: "28px",
  },
  timerContainer: {
    ...typography.styles.body3,
    display: "flex",
    borderRadius: "22px",
    padding: `${spacing.xs} ${spacing.m}`,
    gap: spacing.xs,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: "auto",
    height: "32px",
  },
  timerText: {
    ...typography.styles.body2,
    fontWeight: 600,
    lineHeight: "1",
  },
  callerName: {
    ...typography.styles.body2,
    color: "#6B7280",
  },
  controls: {
    display: "flex",
    gap: spacing.s,
    width: "100%",
    marginTop: spacing.l,
  },
  controlButton: {
    flex: 1,
    height: "48px",
    borderRadius: "8px",
    padding: `${spacing.m} ${spacing.l}`,
    border: "none",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  }
} as const;

export const ActiveCall = ({
  activeCall: propsActiveCall,
  holdCall = null,
  isMuted = false,
  isConnecting = false,
  onEndCall,
  onHoldToggle,
  onToggleMute,
}: ActiveCallProps) => {
  const activeCall = propsActiveCall;
  const [timerDisplay, setTimerDisplay] = useState("--:--");

  const isOutbound = activeCall?.direction === "outbound";
  
  const { displayTitle, displayNumber} = useCallerDisplayInfo(activeCall?.caller, !isOutbound);
  const { data: situation } = useSituation(activeCall?.situationId || "", 5000);

  const situationData = situation;

  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined;

    const updateTimerDisplay = () => {
      if (isConnecting) {
        setTimerDisplay("Connecting...");
        return false;
      }

      if (!activeCall) {
        setTimerDisplay("--:--");
        return false;
      }

      const callStartTimeMillis = protobufTimestampToMillis(activeCall.callStartTime);
      const callEndTimeMillis = protobufTimestampToMillis(activeCall.callEndTime);

      if (callEndTimeMillis && callStartTimeMillis) {
        const durationSeconds = Math.max(0, Math.floor((callEndTimeMillis - callStartTimeMillis) / 1000));
        const minutes = String(Math.floor(durationSeconds / 60)).padStart(2, "0");
        const seconds = String(durationSeconds % 60).padStart(2, "0");
        setTimerDisplay(`${minutes}:${seconds}`);
        return false;
      } else if (callStartTimeMillis) {
        const elapsedSeconds = Math.max(0, Math.floor((Date.now() - callStartTimeMillis) / 1000));
        const minutes = String(Math.floor(elapsedSeconds / 60)).padStart(2, "0");
        const seconds = String(elapsedSeconds % 60).padStart(2, "0");
        setTimerDisplay(`${minutes}:${seconds}`);
        return true;
      } else {
        setTimerDisplay("--:--");
        return false;
      }
    };

    const shouldRunInterval = updateTimerDisplay();

    if (shouldRunInterval) {
      intervalId = setInterval(updateTimerDisplay, 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [activeCall, isConnecting]);

  const getTimerStyles = () => {
    if (isConnecting) {
      return {
        backgroundColor: "#002FFF2E",
        color: "#0048FF",
        iconColor: "#0048FF",
      };
    }
    if (holdCall && !activeCall) {
      return {
        backgroundColor: colors.amber[50],
        color: colors.amber[700],
        iconColor: colors.amber[700],
      };
    }
    return {
      backgroundColor: '#0CB9A240',
      color: '#027464',
      iconColor: '#027464',
    };
  };

  const getCallerDisplay = () => {
    if (situationData?.reporterName) {
      return situationData.reporterName;
    }
    if (displayTitle === "UNKNOWN CALLER") {
      return displayNumber;
    }
    return displayTitle;
  };

  const timerStyles = getTimerStyles();

  const renderTimer = () => (
    <Box sx={{ ...styles.timerContainer, backgroundColor: timerStyles.backgroundColor }}>
      <PhoneInTalkIcon sx={{ width: 20, height: 20, color: timerStyles.iconColor }} />
      <Box sx={{ ...styles.timerText, color: timerStyles.color }}>
        {timerDisplay}
      </Box>
    </Box>
  );

  const renderControls = () => (
    <Box sx={styles.controls}>
      <button
        style={{
          ...styles.controlButton,
          backgroundColor: isMuted ? colors.amber[50] : colors.blue[100],
        }}
        onClick={onToggleMute}
      >
        {isMuted ? (
          <MicOffIcon sx={{ width: 24, height: 24, color: colors.amber[700] }} />
        ) : (
          <MicIcon sx={{ color: colors.blue[600], width: 24, height: 24 }} />
        )}
      </button>

      {!isOutbound && (
        <button
          style={{
            ...styles.controlButton,
            backgroundColor: holdCall && !activeCall ? colors.blue[600] : colors.blue[100],
          }}
          onClick={onHoldToggle}
        >
          <PauseIcon sx={{
            color: holdCall && !activeCall ? colors.amber[50] : colors.blue[600],
            width: 24,
            height: 24
          }} />
        </button>
      )}

      <button
        style={{
          ...styles.controlButton,
          backgroundColor: colors.rose[100],
        }}
        onClick={onEndCall}
      >
        <CallEndIcon sx={{ color: colors.rose[600], width: 24, height: 24 }} />
      </button>
    </Box>
  );

  if (isOutbound) {
    return (
      <Box sx={styles.container}>
        <Box sx={styles.header}>
          <Typography
            component="div"
            sx={styles.outboundPhoneNumber}
          >
            {displayTitle !== "UNKNOWN CALLER" ? displayTitle : formatPhoneNumberForDisplay(activeCall?.caller || "")}
          </Typography>
          {renderTimer()}
        </Box>
        <Box sx={styles.callerName}>
          <PhoneInTalkIcon sx={{ width: 16, height: 16, marginRight: spacing.xs }} />
          Outbound
        </Box>
        {renderControls()}
      </Box>
    );
  }

  return (
    <Box sx={styles.container}>
      <Box sx={styles.header}>
        <Box sx={{ ...styles.situationId }}>
          {situationData?.id?.replace(/[^0-9]/g, "").slice(0, 3)}
        </Box>
        <Box sx={{ ...styles.situationType }}>
          {/* @ts-expect-error TODO: Fix type issue */}
          {getIncidentLabel(situationData?.type) || ""}
        </Box>
        <Box sx={{ flex: 1 }} />
        {renderTimer()}
      </Box>
      <Box sx={styles.callerName}>
        {getCallerDisplay()}
      </Box>
      {renderControls()}
    </Box>
  );
}; 