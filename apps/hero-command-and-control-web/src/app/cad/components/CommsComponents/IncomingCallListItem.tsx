import { usePersistCallerName } from '@/app/apis/services/communications/cellularcall/hooks';
import { useCallerDisplayInfo } from '@/app/utils/caller-identification';
import { Button } from '@/design-system/components/Button';
import { colors, spacing, typography } from '@/design-system/tokens';
import EmergencyIcon from '@mui/icons-material/Emergency';
import { Box, Typography } from '@mui/material';
import { QueuedCall } from 'proto/hero/communications/v1/conversation_pb';
import React, { useEffect, useState } from 'react';
import { protobufTimestampToMillis } from './utils/timestampUtils';

interface IncomingCallListItemProps {
  /** The queued call object containing call details */
  call: QueuedCall;
  /** Whether this is an emergency call */
  isEmergency?: boolean;
  /** Callback when the call is accepted */
  onAcceptCall: (call: QueuedCall) => void;
  /** Whether the system is currently processing an accept action */
  isAccepting?: boolean;
}

const styles = {
  container: {
    display: "flex",
    height: '68px',
    padding: `${spacing.m} ${spacing.m}`,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    flexShrink: 0,
    alignSelf: "stretch",
    cursor: "pointer",
    backgroundColor: "transparent",
    transition: "all 0.2s ease",
    borderBottom: `1px solid ${colors.grey[200]}`,
    '&:hover': {
      backgroundColor: colors.grey[50],
    },
  },
  header: {
    display: "flex",
    alignItems: "center",
    gap: spacing.s,
    alignSelf: "stretch",
    width: "100%",
  },
  callerInfoContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    gap: spacing.s,
  },
  emergencyContainer: {
    display: "flex",
    alignItems: "center",
    gap: spacing.xxs,
  },
  emergencyText: {
    ...typography.styles.body4,
    color: colors.rose[600],
  },
  callerInfo: {
    display: "flex",
    alignItems: "center",
    gap: spacing.xs,
    color: colors.grey[900],
    ...typography.styles.body1,
  },
  timer: {
    ...typography.styles.body3,
    color: colors.grey[500],
  },
  rightContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.s,
    marginLeft: 'auto'
  }
} as const;

/**
 * IncomingCallListItem Component
 * 
 * Renders a single incoming call item with emergency status, caller info, timer, and accept button.
 * Used in the Incoming tab of the CellularCallControlWidgetV2.
 */
export const IncomingCallListItem = React.memo<IncomingCallListItemProps>(({
  call,
  isEmergency = false,
  onAcceptCall,
  isAccepting = false,
}) => {
  const { displayTitle, displayNumber, isUnknown } = useCallerDisplayInfo(call.caller);
  const [timerDisplay, setTimerDisplay] = useState('--:--');

  // Automatically update caller name in backend when identification completes
  usePersistCallerName(call.callSid, call.caller, displayTitle, isUnknown);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined;

    const updateTimerDisplay = () => {
      const enqueueTimeMillis = protobufTimestampToMillis(call.enqueueTime);
      
      if (enqueueTimeMillis) {
        const elapsedSeconds = Math.max(0, Math.floor((Date.now() - enqueueTimeMillis) / 1000));
        const minutes = String(Math.floor(elapsedSeconds / 60)).padStart(2, '0');
        const seconds = String(elapsedSeconds % 60).padStart(2, '0');
        setTimerDisplay(`${minutes}:${seconds}`);
        return true; // Indicate that the timer should continue running
      } else {
        setTimerDisplay('--:--'); // Default to placeholder if timestamp is invalid
        return false; // Indicate that the timer should stop
      }
    };

    const shouldRunInterval = updateTimerDisplay();

    if (shouldRunInterval) {
      intervalId = setInterval(updateTimerDisplay, 1000);
    }

    // Cleanup function to clear interval on component unmount or dependency change
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [call]); // Rerun effect if the call object changes

  const getCallerDisplay = () => {
    if (displayTitle === 'UNKNOWN CALLER') {
      return displayNumber;
    }
    return displayTitle;
  };

  const handleAcceptClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAcceptCall(call);
  };

  return (
    <Box
      sx={styles.container}
    >
      <Box sx={styles.header}>
        <Box sx={styles.callerInfoContainer}>
          {isEmergency ? (
            <Box sx={styles.emergencyContainer}>
              <EmergencyIcon sx={{ color: colors.rose[600], width: '14px', height: '14px'}} />
              <Typography sx={styles.emergencyText}>
                Emergency
              </Typography>
            </Box>
          ) : (
            <Typography sx={{ ...typography.styles.body4, color: colors.grey[500] }}>
              Non-Emergency
            </Typography>
          )}
          <Box sx={styles.callerInfo}>
            {getCallerDisplay()}
          </Box>
        </Box>
        <Box sx={styles.rightContainer}>
          <Typography sx={styles.timer}>
            {timerDisplay}
          </Typography>
          <Button
            label="Accept"
            color="vine"
            prominence={true}
            onClick={handleAcceptClick}
            disabled={isAccepting}
            isLoading={isAccepting}
          />
        </Box>
      </Box>
    </Box>
  );
});

IncomingCallListItem.displayName = 'IncomingCallListItem';