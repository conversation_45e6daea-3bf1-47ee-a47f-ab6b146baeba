import { useSituation } from '@/app/apis/services/workflow/situations/hooks';
import { useCallerDisplayInfo } from '@/app/utils/caller-identification';
import { getIncidentLabel } from "@/app/utils/utils";
import { colors, spacing, typography } from '@/design-system/tokens';
import PauseCircleIcon from '@mui/icons-material/PauseCircle';
import { Box, Typography } from '@mui/material';
import { QueuedCall } from 'proto/hero/communications/v1/conversation_pb';
import React, { useEffect, useState } from 'react';
import { protobufTimestampToMillis } from './utils/timestampUtils';

interface OnHoldCallListItemProps {
  call: QueuedCall;
  isActive?: boolean;
  onSwitchCall: (call: QueuedCall) => void;
}

const styles = {
  container: {
    display: "flex",
    height: '68px',
    padding: `0 ${spacing.m}`,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    flexShrink: 0,
    alignSelf: "stretch",
    cursor: "pointer",
    backgroundColor: "transparent",
    transition: "all 0.2s ease",
    borderBottom: `1px solid ${colors.grey[200]}`,
    '&:hover': {
      backgroundColor: colors.grey[50],
    },
  },
  header: {
    display: "flex",
    alignItems: "center",
    gap: spacing.xs,
    alignSelf: "stretch",
    width: "100%",
  },
  situationId: {
    ...typography.styles.h4,
    color: colors.grey[900],
  },
  situationType: {
    ...typography.styles.body2,
    color: colors.grey[900],
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  timerContainer: (isHovered: boolean) => ({
    display: "flex",
    borderRadius: "29px",
    padding: `${spacing.xs} ${spacing.m}`,
    gap: spacing.xs,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: "auto",
    height: "32px",
    backgroundColor: isHovered ? colors.blue[600] : "#FBE9D0",
    transition: "all 0.2s ease",
  }),
  timerText: (isHovered: boolean) => ({
    ...typography.styles.body3,
    color: isHovered ? colors.grey[50] : "#ED8414",
    lineHeight: "1",
    width: "100%",
    textAlign: "center",
  }),
  callerInfo: {
    display: "flex",
    alignItems: "center",
    gap: spacing.xs,
    color: colors.grey[500],
    ...typography.styles.body4,
  },
} as const;

export const OnHoldCallListItem: React.FC<OnHoldCallListItemProps> = ({
  call,
  isActive,
  onSwitchCall,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [timerDisplay, setTimerDisplay] = useState('--:--');
  const isInbound = call.direction !== "outbound";
  const { displayTitle, displayNumber} = useCallerDisplayInfo(call.caller, isInbound);
  const { data: situation } = useSituation(call.situationId || '', 5000);


  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined;

    const updateTimerDisplay = () => {
      const lastHoldStartMillis = protobufTimestampToMillis(call.lastHoldStart);

      if (lastHoldStartMillis) {
        const elapsedSeconds = Math.max(0, Math.floor((Date.now() - lastHoldStartMillis) / 1000));
        const minutes = String(Math.floor(elapsedSeconds / 60)).padStart(2, '0');
        const seconds = String(elapsedSeconds % 60).padStart(2, '0');
        setTimerDisplay(`${minutes}:${seconds}`);
        return true; // Indicate that the timer should continue running
      } else {
        setTimerDisplay('--:--'); // Default to placeholder if timestamp is invalid
        return false; // Indicate that the timer should stop
      }
    };

    const shouldRunInterval = updateTimerDisplay();

    if (shouldRunInterval) {
      intervalId = setInterval(updateTimerDisplay, 1000);
    }

    // Cleanup function to clear interval on component unmount or dependency change
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [call]); // Rerun effect if the call object changes

  const getCallerDisplay = () => {
    if (situation?.reporterName) {
      return situation.reporterName;
    }
    if (displayTitle === 'UNKNOWN CALLER') {
      return displayNumber;
    }
    return displayTitle;
  };

  return (
    <Box
      sx={styles.container}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => onSwitchCall(call)}
    >
      <Box sx={styles.header}>
        <Typography sx={styles.situationId}>
          {situation?.id?.replace(/[^0-9]/g, '').slice(0, 3) || ''}
        </Typography>
        <Typography sx={styles.situationType}>
          {/* @ts-expect-error TODO: Fix type issue */}
          {(situation && getIncidentLabel(situation.type)) || ""}
        </Typography>
        <Box sx={styles.timerContainer(isHovered)}>
          {isHovered ? (
            <Typography sx={styles.timerText(isHovered)}>
              Switch Call
            </Typography>
          ) : (
            <>
              <PauseCircleIcon
                sx={{
                  width: 20,
                  height: 20,
                  color: "#E37820"
                }}
              />
              <Typography variant="body2" sx={styles.timerText(isHovered)}>
                {timerDisplay}
              </Typography>
            </>
          )}
        </Box>
      </Box>
      <Box sx={styles.callerInfo}>
        {getCallerDisplay()}
      </Box>
    </Box>
  );
};
