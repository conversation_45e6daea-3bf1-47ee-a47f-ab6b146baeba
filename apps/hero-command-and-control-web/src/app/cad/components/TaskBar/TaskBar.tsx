"use client";
import { usePersistForwardedCallerName } from "@/app/apis/services/communications/cellularcall/hooks";
import { useCreateSituation } from "@/app/apis/services/workflow/situations/hooks";
import { useCallContext } from "@/app/contexts/Call/CallContext";
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { useCallerDisplayInfo } from "@/app/utils/caller-identification";
import { ChevronDownIcon } from "@/design-system/components/icons";
import { colors } from "@/design-system/tokens/colors";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import { Box, Button, Collapse, List, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import type { QueuedCall } from "proto/hero/communications/v1/conversation_pb";
import {
  CreateSituationRequest,
  Situation,
  SituationStatus,
  UpdateEntry,
  UpdateSource,
} from "proto/hero/situations/v2/situations_pb";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { CSSTransition, TransitionGroup } from "react-transition-group";
import CellularCallControlWidgetV2 from "../CommsComponents/CellularCallControlWidgetV2/CellularCallControlWidgetV2";
import SituationItem from "./SituationItem";
import styles from "./TaskBar.module.css";

type TransitionItemProps = Omit<
  React.ComponentProps<typeof CSSTransition>,
  "addEndListener"
> & {
  children: React.ReactNode;
};

const SlideToLeftBeforeRemovedItem: React.FC<TransitionItemProps> = ({
  children,
  ...props
}) => {
  const nodeRef = useRef<HTMLDivElement>(null);
  return (
    <CSSTransition
      {...props}
      nodeRef={nodeRef}
      timeout={800}
      classNames={{
        enter: "",
        enterActive: "",
        exit: styles.swipeExit,
        exitActive: styles.swipeExitActive,
      }}
      unmountOnExit
    >
      <div ref={nodeRef}>{children}</div>
    </CSSTransition>
  );
};

type CallState = "none" | "active" | "hold";

type TaskBarProps = {
  situations: Situation[];
  onSituationSelect: (situation: Situation | null) => void;
  selectedSituation: Situation | null;
  assets: Asset[];
};

export const TaskBar: React.FC<TaskBarProps> = ({
  situations,
  onSituationSelect,
  selectedSituation,
  assets,
}) => {
  const { asset: dispatcherAsset } = useUserAsset();
  const assetId = dispatcherAsset?.id;
  const queryClient = useQueryClient();
  const [selectedSituationId, setSelectedSituationId] = useState<string | null>(
    null
  );
  const [resumingCallId] = useState<string | null>(null);
  const [collapsedSections, setCollapsedSections] = useState<{
    new: boolean;
    inProgress: boolean;
    completed: boolean;
  }>({
    new: false,
    inProgress: false,
    completed: false,
  });

  // State to track which headers are sticky to bottom
  const [stickyHeaders, setStickyHeaders] = useState<{
    new: boolean;
    inProgress: boolean;
    completed: boolean;
  }>({
    new: false,
    inProgress: false,
    completed: false,
  });

  // Refs for section headers
  const newHeaderRef = useRef<HTMLDivElement>(null);
  const inProgressHeaderRef = useRef<HTMLDivElement>(null);
  const completedHeaderRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const callWidgetRef = useRef<HTMLDivElement>(null);

  // Ref to track current sticky state to prevent infinite loops
  const stickyHeadersRef = useRef(stickyHeaders);

  // State to track call widget height for sticky header positioning
  const [callWidgetHeight, setCallWidgetHeight] = useState<number>(0);

  // State to track header heights for dynamic sticky positioning
  const [headerHeights, setHeaderHeights] = useState<{
    new: number;
    inProgress: number;
    completed: number;
  }>({
    new: 0,
    inProgress: 0,
    completed: 0,
  });

  // Use states and functions directly from CallContext (updated names)
  const createSituationMutation = useCreateSituation();

  useEffect(() => {
    if (selectedSituation) {
      setSelectedSituationId(selectedSituation.id);
    }
  }, [selectedSituation]);

  // ------------------------------------------------------
  // TWILIO CALL MANAGER INTEGRATION
  // ------------------------------------------------------
  const {
    currentActiveCall,
    currentlyHeldCall,
    isMuted,
    heldCalls,
    queueStatus,
    callStatus,

    // Actions
    holdCurrentCall,
    endCurrentCall,
    muteCurrentCall,
    resumeSpecificCall,
    checkAndAddHeldCallForSituation,
    makeOutboundCall,
  } = useCallContext();

  const currentCall = currentActiveCall || currentlyHeldCall;
  // Helper function to find situation ID from phone number
  const findSituationIdByPhone = useCallback(
    (phoneNumber: string) => {
      // Clean the number for comparison
      const cleanNumber = phoneNumber.replace(/\D/g, "");

      // Find matching situation by comparing cleaned numbers
      const matchingSituation = situations.find((s) => {
        if (!s.contactNo) return false;
        return s.contactNo.replace(/\D/g, "") === cleanNumber;
      });

      return matchingSituation?.id;
    },
    [situations]
  );
  const doesSituationHaveNewIncomingCall = (situationId: string) => {
    // If this is the current active call, it's not new
    if (situationId === currentActiveCall?.situationId) {
      return false;
    }

    // If this is a held call, it's not new
    if (
      heldCalls?.heldCalls?.some((call) => call.situationId === situationId)
    ) {
      return false;
    }

    // If this is a waiting call, it's new
    return (
      queueStatus?.waitingCalls?.some(
        (call) => call.situationId === situationId
      ) ?? false
    );
  };

  // Define call states for the UI
  const callStates = useMemo(() => {
    const states: Record<string, CallState> = {};

    // Add active call to states
    if (currentActiveCall) {
      const situationId =
        currentActiveCall.situationId ||
        findSituationIdByPhone(currentActiveCall.caller);
      if (situationId) states[situationId] = "active";
    }

    // Add held calls to states
    heldCalls?.heldCalls?.forEach((call: QueuedCall) => {
      const situationId =
        call.situationId || findSituationIdByPhone(call.caller);
      if (situationId) states[situationId] = "hold";
    });

    return states;
  }, [currentActiveCall, heldCalls, findSituationIdByPhone]);

  // Refresh queries if call activity is present
  useEffect(() => {
    const interval = setInterval(() => {
      if (
        currentActiveCall ||
        (heldCalls?.heldCalls?.length ?? 0) > 0 ||
        (queueStatus?.queueSize ?? 0) > 0
      ) {
        queryClient.invalidateQueries({ queryKey: ["queueStatus"] });
        if (assetId) {
          queryClient.invalidateQueries({ queryKey: ["heldCalls", assetId] });
        }
      }
    }, 2000);
    return () => clearInterval(interval);
  }, [queryClient, currentActiveCall, heldCalls, queueStatus, assetId]);

  // Find a call (active or held) by situation ID
  const findCallBySituationId = useCallback(
    (
      situationId: string
    ): { call: QueuedCall; status: "active" | "hold" } | null => {
      const heldCallData = heldCalls?.heldCalls?.find(
        (call: QueuedCall) => call.situationId === situationId
      );
      if (heldCallData) {
        return { call: heldCallData, status: "hold" };
      }
      if (currentActiveCall && currentActiveCall.situationId === situationId) {
        return { call: currentActiveCall, status: "active" };
      }
      return null;
    },
    [currentActiveCall, heldCalls]
  );

  // Handle resume the held calls
  const handleSwitchCall = () => {
    if (currentlyHeldCall) {
      resumeSpecificCall(currentlyHeldCall);
    }
  };

  // Group situations by status for better organization
  const groupedSituations = useMemo(() => {
    const groups = {
      new: [] as Situation[],
      inProgress: [] as Situation[],
      completed: [] as Situation[],
    };

    situations.forEach((situation) => {
      // Handle both string and enum status values
      const statusStr = String(situation.status);

      if (statusStr === 'SITUATION_STATUS_CREATED' || situation.status === SituationStatus.CREATED) {
        groups.new.push(situation);
      } else if (
        statusStr === 'SITUATION_STATUS_TRIAGING' || situation.status === SituationStatus.TRIAGING ||
        statusStr === 'SITUATION_STATUS_DISPATCHING' || situation.status === SituationStatus.DISPATCHING ||
        statusStr === 'SITUATION_STATUS_ADDRESSING' || situation.status === SituationStatus.ADDRESSING
      ) {
        groups.inProgress.push(situation);
      } else if (
        statusStr === 'SITUATION_STATUS_ESCALATED' || situation.status === SituationStatus.ESCALATED
      ) {
        groups.completed.push(situation);
      } else {
        // For any unspecified status, treat as new
        groups.new.push(situation);
      }
    });

    return groups;
  }, [situations]);

  // Toggle call hold/resume from the footer component
  const handleHoldToggle = useCallback(() => {
    if (callStatus === "active") {
      holdCurrentCall();
    } else if (callStatus === "held") {
      if (currentCall) {
        resumeSpecificCall(currentCall);
      }
    }
  }, [callStatus, holdCurrentCall, resumeSpecificCall, currentCall]);

  const createSituation = () => {
    createSituationMutation.mutate(
      {
        situation: {
          title: "Dispatcher Created Incident",
          updates: [
            {
              message: "Incident created by dispatcher",
              timestamp: new Date(Date.now()).toISOString(),
              eventType: "info change",
              triggerSource: "TRIGGER_SOURCE_MANUAL_ENTRY",
              updateSource: UpdateSource.HUMAN_OPERATOR,
              displayName: "Dispatcher",
              updaterId: "dispatch",
            } as unknown as UpdateEntry,
          ],
        } as Situation,
      } as CreateSituationRequest,
      {
        onSuccess: (response) => {
          const situation = response.situation;
          if (situation) {
            setSelectedSituationId(situation.id);
            onSituationSelect(situation);
          }
        },
      }
    );
  };

  // Helper component to persist caller name for forwarded calls using situation.additionalInfoJson
  const ForwardedCallerPersistor: React.FC<{ situation: Situation }> = React.memo(function ForwardedCallerPersistor({ situation }) {
    // Memoize the parsing to avoid re-parsing on every render
    const { callSid, phone } = useMemo(() => {
      let callSid: string | undefined = undefined;
      let phone: string | undefined = undefined;

      try {
        const info = situation.additionalInfoJson ? JSON.parse(situation.additionalInfoJson as unknown as string) : undefined;
        callSid = info?.callSid || undefined;
        phone = info?.callerNo || info?.caller || situation.contactNo || undefined;
      } catch {
        // ignore parse errors
        phone = situation.contactNo || undefined;
      }

      return { callSid, phone };
    }, [situation.additionalInfoJson, situation.contactNo]);

    const { displayTitle, isUnknown } = useCallerDisplayInfo(phone);
    usePersistForwardedCallerName(callSid, phone, displayTitle, isUnknown);
    return null;
  });

  // Timer to update elapsed time for incident creation
  const [currentTime, setCurrentTime] = useState<number>(Date.now());
  useEffect(() => {
    const intervalId = setInterval(() => setCurrentTime(Date.now()), 1000);
    return () => clearInterval(intervalId);
  }, []);

  // Handle incident selection
  const handleItemClick = useCallback(
    (situation: Situation) => {
      if (selectedSituationId === situation.id) {
        // Deselect if clicking the already selected situation
        setSelectedSituationId(null);
        onSituationSelect(null);
      } else {
        // Select the new situation
        setSelectedSituationId(situation.id);
        onSituationSelect(situation);
        checkAndAddHeldCallForSituation(situation.id);
      }
    },
    [onSituationSelect, selectedSituationId, checkAndAddHeldCallForSituation]
  );

  // Handle section collapse/expand
  const toggleSection = useCallback((section: 'new' | 'inProgress' | 'completed') => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  }, []);

  // Detect when headers should be sticky
  useEffect(() => {
    const handleScroll = () => {
      if (!scrollContainerRef.current) return;

      const viewportHeight = window.innerHeight;

      // Get current header heights immediately to ensure accuracy
      const currentNewHeight = newHeaderRef.current?.offsetHeight || 0;
      const currentInProgressHeight = inProgressHeaderRef.current?.offsetHeight || 0;
      const currentCompletedHeight = completedHeaderRef.current?.offsetHeight || 0;

      const newCalculatedStickyState = {
        new: false,
        inProgress: false,
        completed: false,
      };

      // Check completed header
      if (completedHeaderRef.current) {
        const rect = completedHeaderRef.current.getBoundingClientRect();
        const stickTriggerY = viewportHeight - callWidgetHeight;
        newCalculatedStickyState.completed = rect.bottom >= stickTriggerY;
      }

      // Check inProgress header
      if (inProgressHeaderRef.current) {
        const rect = inProgressHeaderRef.current.getBoundingClientRect();
        const completedOffset = newCalculatedStickyState.completed ? currentCompletedHeight : 0;
        const stickTriggerY =
          viewportHeight - callWidgetHeight - completedOffset;
        newCalculatedStickyState.inProgress = rect.bottom >= stickTriggerY;
      }

      // Check new header
      if (newHeaderRef.current) {
        const rect = newHeaderRef.current.getBoundingClientRect();
        const completedOffset = newCalculatedStickyState.completed ? currentCompletedHeight : 0;
        const inProgressOffset = newCalculatedStickyState.inProgress ? currentInProgressHeight : 0;
        const stickTriggerY =
          viewportHeight -
          callWidgetHeight -
          completedOffset -
          inProgressOffset;
        newCalculatedStickyState.new = rect.bottom >= stickTriggerY;
      }

      // Update header heights if they've changed
      if (
        currentNewHeight !== headerHeights.new ||
        currentInProgressHeight !== headerHeights.inProgress ||
        currentCompletedHeight !== headerHeights.completed
      ) {
        setHeaderHeights({
          new: currentNewHeight,
          inProgress: currentInProgressHeight,
          completed: currentCompletedHeight,
        });
      }

      // Compare with current state and update only if changed to prevent infinite loops
      const currentState = stickyHeadersRef.current;
      if (
        newCalculatedStickyState.new !== currentState.new ||
        newCalculatedStickyState.inProgress !== currentState.inProgress ||
        newCalculatedStickyState.completed !== currentState.completed
      ) {
        stickyHeadersRef.current = newCalculatedStickyState;
        setStickyHeaders(newCalculatedStickyState);
      }
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      // Initial check
      handleScroll();
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [groupedSituations, callWidgetHeight, collapsedSections, headerHeights.new, headerHeights.inProgress, headerHeights.completed]);

  // Keep ref in sync with state changes
  useEffect(() => {
    stickyHeadersRef.current = stickyHeaders;
  }, [stickyHeaders]);

  // Measure call widget height for sticky header positioning
  useEffect(() => {
    const measureCallWidget = () => {
      if (callWidgetRef.current) {
        const height = callWidgetRef.current.offsetHeight;
        setCallWidgetHeight(height);
      }
    };

    // Initial measurement
    measureCallWidget();

    // Set up ResizeObserver to watch for size changes
    const resizeObserver = new ResizeObserver(measureCallWidget);
    if (callWidgetRef.current) {
      resizeObserver.observe(callWidgetRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [currentActiveCall, currentlyHeldCall, callStatus]); // Re-measure when call state changes

  // Measure header heights for dynamic sticky positioning
  useEffect(() => {
    const measureHeaderHeights = () => {
      const newHeight = newHeaderRef.current?.offsetHeight || 0;
      const inProgressHeight = inProgressHeaderRef.current?.offsetHeight || 0;
      const completedHeight = completedHeaderRef.current?.offsetHeight || 0;

      setHeaderHeights({
        new: newHeight,
        inProgress: inProgressHeight,
        completed: completedHeight,
      });
    };

    // Initial measurement
    measureHeaderHeights();

    // Set up ResizeObserver to watch for size changes
    const resizeObserver = new ResizeObserver(() => {
      // Use requestAnimationFrame to ensure DOM has updated
      requestAnimationFrame(measureHeaderHeights);
    });

    if (newHeaderRef.current) {
      resizeObserver.observe(newHeaderRef.current);
    }
    if (inProgressHeaderRef.current) {
      resizeObserver.observe(inProgressHeaderRef.current);
    }
    if (completedHeaderRef.current) {
      resizeObserver.observe(completedHeaderRef.current);
    }

    // Also measure on scroll to catch any missed updates
    const handleScroll = () => {
      requestAnimationFrame(measureHeaderHeights);
    };

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }

    // Measure on window resize
    const handleResize = () => {
      requestAnimationFrame(measureHeaderHeights);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      resizeObserver.disconnect();
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [groupedSituations, collapsedSections]); // Re-measure when sections change

  // Scroll to top functionality
  const scrollToHeader = useCallback(
    (section: 'new' | 'inProgress' | 'completed') => {
      if (!scrollContainerRef.current) return;

      const headerRef =
        section === 'new'
          ? newHeaderRef
          : section === 'inProgress'
            ? inProgressHeaderRef
            : completedHeaderRef;

      if (headerRef.current) {
        // Expand section if collapsed
        if (collapsedSections[section]) {
          setCollapsedSections((prev) => ({
            ...prev,
            [section]: false,
          }));
        }

        // Calculate the scroll position to show the header with some top padding
        const container = scrollContainerRef.current;
        const headerTop = headerRef.current.offsetTop;
        const containerTop = container.offsetTop;
        const offset = 20; // 20px padding from top

        // Scroll to position with smooth animation
        container.scrollTo({
          top: headerTop - containerTop - offset,
          behavior: 'smooth',
        });

        // Remove sticky state
        setStickyHeaders((prev) => ({
          ...prev,
          [section]: false,
        }));
      }
    },
    [collapsedSections]
  );

  return (
    <Box
      sx={{
        width: "100%",
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        backgroundColor: "#fff",
        borderRight: `1px solid ${colors.grey[300]}`,
        borderLeft: `1px solid ${colors.grey[300]}`,
        boxShadow: "3px 0 12px 0 rgba(0, 0, 0, 0.06)",
        overflow: "hidden",
        position: "relative",
      }}
    >
      <Box
        sx={{
          backgroundColor: "#f8fafd",
          p: 1,
          pt: 2,
          pb: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          borderBottom: `1px solid ${colors.grey[300]}`,
        }}
      >
        {/* Left side: Incidents title and count */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography
            color={"#7C7D7F"}
            fontWeight={600}
            fontSize={15}
            lineHeight={"44px"}
          >
            Incidents{" "}
            <Box
              component="span"
              sx={{
                backgroundColor: "#E1E4E9",
                borderRadius: "50%",
                width: 20,
                height: 20,
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "10px",
                fontWeight: 400,
                marginLeft: "4px",
                verticalAlign: "middle",
              }}
            >
              {situations.length}
            </Box>
          </Typography>
        </Box>

        {/* Right side: Create Incident button */}
        <Button
          startIcon={
            <AddCircleIcon sx={{ color: "#374151", height: 16, width: 16 }} />
          }
          sx={{
            color: "#374151",
            backgroundColor: "#E5E7EB",
            padding: "4px 12px",
            borderRadius: "6px",
            fontSize: "10px",
            fontWeight: 600,
            textTransform: "none",
            fontFamily: "Roboto",
          }}
          onClick={createSituation}
        >
          Create Incident
        </Button>
      </Box>
      <Box
        sx={{ flex: 1, overflowY: 'auto', backgroundColor: '#fff' }}
        ref={scrollContainerRef}
        className={styles.scrollContainerWithStickyPadding}
      >
        {/* Caller name persistence for all situations */}
        {situations.map((situation) => (
          <ForwardedCallerPersistor key={situation.id} situation={situation} />
        ))}
        
        {/* Pre-Dispatch Section */}
        <Box
          sx={{
            position: 'sticky',
            top: 0,
            zIndex: 3,
            display: 'flex',
            padding: '4px 16px',
            alignItems: 'center',
            gap: '10px',
            alignSelf: 'stretch',
            borderBottom: `1px solid ${colors.grey[300]}`,
            background: '#F9FAFB',
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: '#F3F4F6',
            },
            visibility: stickyHeaders.new ? 'hidden' : 'visible',
          }}
          onClick={() => toggleSection('new')}
          ref={newHeaderRef}
        >
          <Typography
            color="#4A5565"
            fontWeight={500}
            fontSize={14}
            sx={{
              flex: '1 0 0',
              fontFamily: 'Roboto',
              fontStyle: 'normal',
              lineHeight: '140%',
              textTransform: 'uppercase',
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Pending
          </Typography>
          <Box
            component="span"
            sx={{
              display: 'flex',
              width: '20px',
              height: '20px',
              padding: '4px 3px',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              borderRadius: '20px',
              background: '#E2EFFD',
              color: '#0354C9',
              textAlign: 'center',
              fontFamily: 'Open Sans',
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 600,
              lineHeight: 'normal',
            }}
          >
            {groupedSituations.new.length}
          </Box>
          <ChevronDownIcon
            style={{
              transform: collapsedSections.new
                ? 'rotate(180deg)'
                : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
            }}
          />
        </Box>
        <Collapse
          in={!collapsedSections.new}
          timeout={300}
          easing={{
            enter: 'cubic-bezier(0.4, 0, 0.2, 1)',
            exit: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          unmountOnExit={false}
        >
          <List disablePadding>
            <TransitionGroup component={null}>
              {groupedSituations.new.map((situation) => (
                <SlideToLeftBeforeRemovedItem key={situation.id}>
                  <SituationItem
                    situation={situation}
                    hasNewIncomingCall={doesSituationHaveNewIncomingCall(
                      situation.id
                    )}
                    isActive={situation.id === selectedSituationId}
                    selectedSituationId={selectedSituationId}
                    callStates={callStates}
                    resumingCallId={resumingCallId}
                    findCallBySituationId={findCallBySituationId}
                    handleItemClick={handleItemClick}
                    currentTime={currentTime}
                    assets={assets}
                  />
                </SlideToLeftBeforeRemovedItem>
              ))}
            </TransitionGroup>
          </List>
        </Collapse>

        {/* Dispatched Section */}
        <Box
          sx={{
            position: 'sticky',
            top: headerHeights.new, // Dynamic height instead of hardcoded 41px
            zIndex: 2,
            display: 'flex',
            padding: '4px 16px',
            alignItems: 'center',
            gap: '10px',
            alignSelf: 'stretch',
            borderBottom: `1px solid ${colors.grey[300]}`,
            background: '#F9FAFB',
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: '#F3F4F6',
            },
            visibility: stickyHeaders.inProgress ? 'hidden' : 'visible',
          }}
          onClick={() => toggleSection('inProgress')}
          ref={inProgressHeaderRef}
        >
          <Typography
            color="#4A5565"
            fontWeight={500}
            fontSize={14}
            sx={{
              flex: '1 0 0',
              fontFamily: 'Roboto',
              fontStyle: 'normal',
              lineHeight: '140%',
              textTransform: 'uppercase',
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Dispatched
          </Typography>
          <Box
            component="span"
            sx={{
              display: 'flex',
              width: '20px',
              height: '20px',
              padding: '4px 3px',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              borderRadius: '20px',
              background: '#E2EFFD',
              color: '#0354C9',
              textAlign: 'center',
              fontFamily: 'Open Sans',
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 600,
              lineHeight: 'normal',
            }}
          >
            {groupedSituations.inProgress.length}
          </Box>
          <ChevronDownIcon
            style={{
              transform: collapsedSections.inProgress
                ? 'rotate(180deg)'
                : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
            }}
          />
        </Box>
        <Collapse
          in={!collapsedSections.inProgress}
          timeout={300}
          easing={{
            enter: 'cubic-bezier(0.4, 0, 0.2, 1)',
            exit: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          unmountOnExit={false}
        >
          <List disablePadding>
            <TransitionGroup component={null}>
              {groupedSituations.inProgress.map((situation) => (
                <SlideToLeftBeforeRemovedItem key={situation.id}>
                  <SituationItem
                    situation={situation}
                    hasNewIncomingCall={doesSituationHaveNewIncomingCall(
                      situation.id
                    )}
                    isActive={situation.id === selectedSituationId}
                    selectedSituationId={selectedSituationId}
                    callStates={callStates}
                    resumingCallId={resumingCallId}
                    findCallBySituationId={findCallBySituationId}
                    handleItemClick={handleItemClick}
                    currentTime={currentTime}
                    assets={assets}
                  />
                </SlideToLeftBeforeRemovedItem>
              ))}
            </TransitionGroup>
          </List>
        </Collapse>

        {/* Escalated Section */}
        {groupedSituations.completed.length > 0 && (
          <>
            <Box
              sx={{
                position: 'sticky',
                top: headerHeights.new + headerHeights.inProgress, // Dynamic height instead of hardcoded 82px
                zIndex: 1,
                display: 'flex',
                padding: '4px 16px',
                alignItems: 'center',
                gap: '10px',
                alignSelf: 'stretch',
                borderTop: '1px solid #D1D5DC',
                borderBottom: '1px solid #D1D5DC',
                background: '#F9FAFB',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: '#F3F4F6',
                },
                visibility: stickyHeaders.completed ? 'hidden' : 'visible',
              }}
              onClick={() => toggleSection('completed')}
              ref={completedHeaderRef}
            >
              <Typography
                color="#4A5565"
                fontWeight={500}
                fontSize={14}
                sx={{
                  flex: '1 0 0',
                  fontFamily: 'Roboto',
                  fontStyle: 'normal',
                  lineHeight: '140%',
                  textTransform: 'uppercase',
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                Escalated
              </Typography>
              <Box
                component="span"
                sx={{
                  display: 'flex',
                  width: '20px',
                  height: '20px',
                  padding: '4px 3px',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                  borderRadius: '20px',
                  background: '#E2EFFD',
                  color: '#0354C9',
                  textAlign: 'center',
                  fontFamily: 'Open Sans',
                  fontSize: '12px',
                  fontStyle: 'normal',
                  fontWeight: 600,
                  lineHeight: 'normal',
                }}
              >
                {groupedSituations.completed.length}
              </Box>
              <ChevronDownIcon
                style={{
                  transform: collapsedSections.completed
                    ? 'rotate(180deg)'
                    : 'rotate(0deg)',
                  transition: 'transform 0.2s ease',
                }}
              />
            </Box>
            <Collapse
              in={!collapsedSections.completed}
              timeout={300}
              easing={{
                enter: 'cubic-bezier(0.4, 0, 0.2, 1)',
                exit: 'cubic-bezier(0.4, 0, 0.2, 1)',
              }}
              unmountOnExit={false}
            >
              <List disablePadding>
                <TransitionGroup component={null}>
                  {groupedSituations.completed.map((situation) => (
                    <SlideToLeftBeforeRemovedItem key={situation.id}>
                      <SituationItem
                        situation={situation}
                        hasNewIncomingCall={doesSituationHaveNewIncomingCall(
                          situation.id
                        )}
                        isActive={situation.id === selectedSituationId}
                        selectedSituationId={selectedSituationId}
                        callStates={callStates}
                        resumingCallId={resumingCallId}
                        findCallBySituationId={findCallBySituationId}
                        handleItemClick={handleItemClick}
                        currentTime={currentTime}
                        assets={assets}
                      />
                    </SlideToLeftBeforeRemovedItem>
                  ))}
                </TransitionGroup>
              </List>
            </Collapse>
          </>
        )}
      </Box>

      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          mb: 0.3,
          backgroundColor: "#fff",
        }}
        ref={callWidgetRef}
      >
        <CellularCallControlWidgetV2
          activeCall={currentActiveCall}
          holdCall={currentlyHeldCall}
          isMuted={isMuted}
          isConnecting={callStatus === "connecting"}
          onEndCall={endCurrentCall}
          onHoldToggle={handleHoldToggle}
          onDial={makeOutboundCall}
          onToggleMute={muteCurrentCall}
          onSwitchCall={handleSwitchCall}
        />
      </Box>

      {/* Sticky Bottom Headers */}
      {stickyHeaders.new && (
        <Box
          onClick={() => scrollToHeader('new')}
          sx={{
            position: 'absolute',
            bottom:
              callWidgetHeight +
              (stickyHeaders.inProgress ? headerHeights.inProgress : 0) +
              (stickyHeaders.completed ? headerHeights.completed : 0), // Top of stack
            left: 0,
            right: 0,
            zIndex: 10, // Reduced z-index to not appear above sidebar
            background: '#F9FAFB',
            borderTop: `1px solid ${colors.grey[300]}`,
            borderBottom: `1px solid ${colors.grey[300]}`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
            width: '100%',
            maxWidth: '100%',
            display: 'flex',
            padding: '4px 16px',
            alignItems: 'center',
            gap: '10px',
            alignSelf: 'stretch',
            '&:hover': {
              backgroundColor: '#F3F4F6',
              boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.15)',
            },
          }}
        >
          <Typography
            color="#4A5565"
            fontWeight={500}
            fontSize={14}
            sx={{
              flex: '1 0 0',
              fontFamily: 'Roboto',
              fontStyle: 'normal',
              lineHeight: '140%',
              textTransform: 'uppercase',
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            PENDING
          </Typography>
          <Box
            component="span"
            sx={{
              display: 'flex',
              width: '20px',
              height: '20px',
              padding: '4px 3px',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              borderRadius: '20px',
              background: '#E2EFFD',
              color: '#0354C9',
              textAlign: 'center',
              fontFamily: 'Open Sans',
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 600,
              lineHeight: 'normal',
            }}
          >
            {groupedSituations.new.length}
          </Box>
          <ChevronDownIcon
            style={{ transform: 'rotate(180deg)' }}
          />
        </Box>
      )}

      {stickyHeaders.inProgress && (
        <Box
          onClick={() => scrollToHeader('inProgress')}
          sx={{
            position: 'absolute',
            bottom: callWidgetHeight + (stickyHeaders.completed ? headerHeights.completed : 0), // Above completed header
            left: 0,
            right: 0,
            zIndex: 10, // Reduced z-index to not appear above sidebar
            background: '#F9FAFB',
            borderTop: `1px solid ${colors.grey[300]}`,
            borderBottom: `1px solid ${colors.grey[300]}`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
            width: '100%',
            maxWidth: '100%',
            display: 'flex',
            padding: '4px 16px',
            alignItems: 'center',
            gap: '10px',
            alignSelf: 'stretch',
            '&:hover': {
              backgroundColor: '#F3F4F6',
              boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.15)',
            },
          }}
        >
          <Typography
            color="#4A5565"
            fontWeight={500}
            fontSize={14}
            sx={{
              flex: '1 0 0',
              fontFamily: 'Roboto',
              fontStyle: 'normal',
              lineHeight: '140%',
              textTransform: 'uppercase',
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Dispatched
          </Typography>
          <Box
            component="span"
            sx={{
              display: 'flex',
              width: '20px',
              height: '20px',
              padding: '4px 3px',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              borderRadius: '20px',
              background: '#E2EFFD',
              color: '#0354C9',
              textAlign: 'center',
              fontFamily: 'Open Sans',
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 600,
              lineHeight: 'normal',
            }}
          >
            {groupedSituations.inProgress.length}
          </Box>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M8.12501 15L12.005 11.12L15.885 15C16.275 15.39 16.905 15.39 17.295 15C17.685 14.61 17.685 13.98 17.295 13.59L12.705 8.99999C12.315 8.60999 11.685 8.60999 11.295 8.99999L6.70501 13.59C6.31501 13.98 6.31501 14.61 6.70501 15C7.09501 15.38 7.73501 15.39 8.12501 15Z"
              fill="#4A5565"
              style={{ transform: 'rotate(180deg)' }}
            />
          </svg>
        </Box>
      )}

      {stickyHeaders.completed && groupedSituations.completed.length > 0 && (
        <Box
          onClick={() => scrollToHeader('completed')}
          sx={{
            position: 'absolute',
            bottom: callWidgetHeight, // Closest to call widget
            left: 0,
            right: 0,
            zIndex: 10, // Reduced z-index to not appear above sidebar
            background: '#F9FAFB',
            borderTop: `1px solid ${colors.grey[300]}`,
            borderBottom: `1px solid ${colors.grey[300]}`,
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
            width: '100%',
            maxWidth: '100%',
            display: 'flex',
            padding: '4px 16px',
            alignItems: 'center',
            gap: '10px',
            alignSelf: 'stretch',
            '&:hover': {
              backgroundColor: '#F3F4F6',
              boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.15)',
            },
          }}
        >
          <Typography
            color="#4A5565"
            fontWeight={500}
            fontSize={14}
            sx={{
              flex: '1 0 0',
              fontFamily: 'Roboto',
              fontStyle: 'normal',
              lineHeight: '140%',
              textTransform: 'uppercase',
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Escalated
          </Typography>
          <Box
            component="span"
            sx={{
              display: 'flex',
              width: '20px',
              height: '20px',
              padding: '4px 3px',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              borderRadius: '20px',
              background: '#E2EFFD',
              color: '#0354C9',
              textAlign: 'center',
              fontFamily: 'Open Sans',
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 600,
              lineHeight: 'normal',
            }}
          >
            {groupedSituations.completed.length}
          </Box>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M8.12501 15L12.005 11.12L15.885 15C16.275 15.39 16.905 15.39 17.295 15C17.685 14.61 17.685 13.98 17.295 13.59L12.705 8.99999C12.315 8.60999 11.685 8.60999 11.295 8.99999L6.70501 13.59C6.31501 13.98 6.31501 14.61 6.70501 15C7.09501 15.38 7.73501 15.39 8.12501 15Z"
              fill="#4A5565"
              style={{ transform: 'rotate(180deg)' }}
            />
          </svg>
        </Box>
      )}
    </Box>
  );
};
