.mainContainer {
  display: flex;
  height: 100vh;
  width: 100vw;
  color: white;
  background-color: #fff;
  overflow: hidden;
  overscroll-behavior: none;
}

.contentContainer {
  display: flex;
  height: 100vh;
  width: calc(100vw - 40px);
  background-color: #fff;
  margin-left: 40px;
}

.taskBarContainer {
  display: flex;
  height: 100vh;
  width: 18.5%;
  background-color: #fff;
  flex-direction: column;
}

.mapAndActionContainer {
  display: flex;
  height: 100vh;
  width: 81.5%;
  background-color: #fff;
}

.mapContainer {
  display: flex;
  height: 100vh;
  width: 60%;
  background-color: #fff;
}

/* Action pane panel styling */
.actionPanePanel {
  overflow: hidden;
  position: relative;
}