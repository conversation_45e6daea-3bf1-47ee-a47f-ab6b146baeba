"use client";

import { stringToCaseStatus } from "@/app/apis/services/workflow/cases/enumConverters";
import { useUpdateCase, useUpdateCaseStatus } from "@/app/apis/services/workflow/cases/hooks";
import { useBatchGetLatestEntities, useListLatestEntitySchemas } from "@/app/apis/services/workflow/entity/hooks";
import { useBatchGetReports } from "@/app/apis/services/workflow/reports/v2/hooks";
import CaseResolveModal from "@/app/cases/caseComponents/components/CaseResolveModal";
import ExportModal, { ExportOptions } from "@/app/cases/caseComponents/components/CaseSelector";
import ManageModal from "@/app/components/ManageModal";
import { useBreadcrumbHeader } from "@/app/hooks/useBreadcrumbHeader";
import { useRecentlyViewedTracker } from "@/app/hooks/useRecentlyViewedTracker";
import { getCaseStatusLabel, getCaseTypeLabel } from "@/app/utils/utils";
import { Header } from "@/design-system/components/Header";
import { colors } from "@/design-system/tokens";
import ExportDocument from "@/export/pdf/CaseExport";
import { registerRoboto } from "@/export/pdf/utils/fonts";
import AdfScannerIcon from '@mui/icons-material/AdfScanner';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import TaskIcon from '@mui/icons-material/Task';
import { Box, List, ListItem, ListItemText, Popover } from "@mui/material";
import { pdf } from "@react-pdf/renderer";
import * as Sentry from "@sentry/nextjs";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { getCurrentUser } from 'aws-amplify/auth';
import { Case, CaseStatus } from "proto/hero/cases/v1/cases_pb";
import { EntityType } from "proto/hero/entity/v1/entity_pb";
import { useRef, useState } from "react";

// Register fonts
registerRoboto();

// Create a client for PDF generation
const pdfQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: 0,
      gcTime: 0,
    },
  },
});

interface CaseHeaderProps {
  caseData: Case;
  onBack?: () => void;
  onCaseUpdate?: () => void;
  primaryInvestigator?: any;
  isReadOnly?: boolean;
}

// Priority options for the dropdown
const PRIORITY_OPTIONS = [
  { value: 1, label: "P1", color: "rose" as const },
  { value: 2, label: "P2", color: "amber" as const },
  { value: 3, label: "P3", color: "amber" as const },
  { value: 4, label: "P4", color: "amber" as const },
  { value: 5, label: "P5", color: "vine" as const },
];

// Helper function to format user ID
const formatUserId = (userId: string): string => {
  if (!userId) return "USER UNKNOWN";
  const digits = String(userId).replace(/[^0-9]/g, "").slice(0, 8);
  return digits ? `USER ${digits}` : "USER UNKNOWN";
};

// Utility function for priority colors
const getPriorityColors = (color: string) => {
  const baseColors = {
    rose: { base: colors.rose[100], hover: colors.rose[200], text: colors.rose[600] },
    amber: { base: colors.amber[100], hover: colors.amber[200], text: colors.amber[600] },
    vine: { base: colors.vine[100], hover: colors.vine[200], text: colors.vine[600] },
    grey: { base: colors.grey[100], hover: colors.grey[200], text: colors.grey[600] },
  };

  return baseColors[color as keyof typeof baseColors] || baseColors.grey;
};

// Custom Priority Label Component
const PriorityLabel = ({
  label,
  color,
  isHovered,
  isOpen,
  onClick,
  onMouseEnter,
  onMouseLeave,
  tagRef
}: {
  label: string;
  color: string;
  isHovered: boolean;
  isOpen: boolean;
  onClick: () => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  tagRef: React.RefObject<HTMLDivElement | null>;
}) => {
  const colorPalette = getPriorityColors(color);

  const getBackgroundColor = () => {
    return isHovered ? colorPalette.hover : colorPalette.base;
  };

  const getTextColor = () => {
    return colorPalette.text;
  };

  return (
    <div
      ref={tagRef}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '4px 12px',
        backgroundColor: getBackgroundColor(),
        color: getTextColor(),
        borderRadius: '8px',
        fontSize: '16px',
        fontWeight: 400,
        letterSpacing: '0.15px',
        lineHeight: '22px',
        cursor: 'pointer',
        transition: 'background-color 0.2s ease',
        width: 'fit-content',
        minWidth: '40px', // Ensure minimum width for consistent spacing
        flexShrink: 0, // Prevent the priority label from shrinking
        overflow: 'hidden', // Prevent text overflow
      }}
    >
      <span>{label}</span>
      <div style={{
        width: (isHovered || isOpen) ? '12px' : '0px',
        height: '12px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        transition: 'width 0.2s ease',
      }}>
        <KeyboardArrowDownIcon style={{
          fontSize: '12px',
          transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.2s ease',
          color: getTextColor(),
        }} />
      </div>
    </div>
  );
};

export default function CaseHeader({ caseData, onBack, onCaseUpdate, primaryInvestigator, isReadOnly }: CaseHeaderProps) {
  const caseIdNumber = caseData.id
    ? String(caseData.id).replace(/[^0-9]/g, "").slice(0, 8)
    : "";

  const formattedCaseId = caseIdNumber.length >= 8
    ? caseIdNumber.slice(0, 8)
    : caseIdNumber;

  const caseType = caseData.type ? getCaseTypeLabel(caseData.type.toString()) : "Unspecified";

  // Helper function to check if case status is in a "closed" state (resolved, closed, or archived)
  const isCaseClosed = (status: any): boolean => {
    // Normalize the status to enum value if it's a string
    const normalizedStatus = typeof status === 'string'
      ? stringToCaseStatus(status)
      : status;

    // Check only against enum values for consistency
    return normalizedStatus === CaseStatus.CLOSED ||
      normalizedStatus === CaseStatus.ARCHIVED ||
      normalizedStatus === CaseStatus.RESOLVED;
  };

  const { breadcrumbs } = useBreadcrumbHeader({
    id: `case-${caseData.id}`,
    label: `Case ${formattedCaseId}`,
    path: `/cases?caseId=${caseData.id}`,
  });

  useRecentlyViewedTracker({
    id: `case-${caseData.id}`,
    title: `CS ${formattedCaseId}`,
    subtitle: `${caseType} Case`,
    path: `/cases?caseId=${caseData.id}`,
  });

  // Priority dropdown state
  const [isPriorityDropdownOpen, setIsPriorityDropdownOpen] = useState(false);
  const [isPriorityHovered, setIsPriorityHovered] = useState(false);
  const priorityTagRef = useRef<HTMLDivElement>(null);

  // Case update mutation
  const { mutate: updateCase, isPending: isUpdatingPriority } = useUpdateCase();
  const { mutate: updateCaseStatus, isPending: isUpdatingCaseStatus } = useUpdateCaseStatus();

  // Modal states
  const [isExportOpen, setIsExportOpen] = useState(false);
  const [isManageModalOpen, setIsManageModalOpen] = useState(false);
  const [isResolveModalOpen, setIsResolveModalOpen] = useState(false);

  // Force assignment state for reopening closed cases
  const [isForcedAssignmentMode, setIsForcedAssignmentMode] = useState(false);

  // Format date from timestamp or ISO string
  const formatDate = (timestamp: string): string => {
    if (!timestamp) return "N/A";
    try {
      const date = new Date(timestamp);
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    } catch (_e) {
      return "N/A";
    }
  };

  // Get priority as a string (P0, P1, etc.)
  const priorityLabel = caseData.priority ? `P${caseData.priority}` : "P --";
  const priorityColor = caseData.priority
    ? caseData.priority === 1
      ? "rose"
      : caseData.priority >= 2 && caseData.priority <= 4
        ? "amber"
        : caseData.priority === 5
          ? "vine"
          : "grey"
    : "grey";

  // Handle priority change
  const handlePriorityChange = (newPriority: number) => {
    const updatedCase = {
      ...caseData,
      priority: newPriority,
      etag: caseData.etag,
    };

    updateCase(
      {
        case: updatedCase,
      } as any,
      {
        onSuccess: () => {
          console.log('Priority updated successfully to P', newPriority);
        },
        onError: (error) => {
          console.error('Failed to update priority:', error);
        },
      }
    );

    setIsPriorityDropdownOpen(false);
  };

  const tags = [
    ...(caseData.status ? [{
      label: getCaseStatusLabel(caseData.status.toString()),
      color: "blue" as const
    }] : []),
    // Custom priority tag
    {
      custom: (
        <PriorityLabel
          label={priorityLabel}
          color={priorityColor}
          isHovered={isPriorityHovered}
          isOpen={isPriorityDropdownOpen}
          onClick={isReadOnly ? () => { } : () => setIsPriorityDropdownOpen(!isPriorityDropdownOpen)}
          onMouseEnter={isReadOnly ? () => { } : () => setIsPriorityHovered(true)}
          onMouseLeave={isReadOnly ? () => { } : () => setIsPriorityHovered(false)}
          tagRef={priorityTagRef}
        />
      )
    },
    // Read-only indicator
    ...(isReadOnly ? [{
      label: "Read Only",
      color: "grey" as const,
    }] : [])
  ];

  // Get entity IDs from case data
  const entityIds = caseData.entityRefs?.map(ref => ref.id) || [];

  // Fetch reports using the hook
  const { data: reportsResponse, isLoading: _isLoadingReports } = useBatchGetReports(caseData.reportIds || [], {
    enabled: Array.isArray(caseData.reportIds) && caseData.reportIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Fetch full entity data using the hook
  const { data: entitiesResponse, isLoading: _isLoadingEntities } = useBatchGetLatestEntities(entityIds, {
    queryKey: ["entity", "batch", entityIds],
    enabled: entityIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Fetch latest schemas for each entity type
  const { data: personSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.PERSON,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: propertySchemas } = useListLatestEntitySchemas({
    entityType: EntityType.PROPERTY,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: vehicleSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.VEHICLE,
    pageSize: 1,
    pageToken: "",
  } as any);

  const handleExport = async (options: ExportOptions) => {
    console.log('Starting PDF export with options:', options);
    setIsExportOpen(false);

    try {
      // Get current user
      const currentUser = await getCurrentUser();
      const printedBy = formatUserId(currentUser.username || "");

      // Get reports from the hook's response
      const reports = reportsResponse?.reports || [];
      console.log('Using reports:', {
        count: reports.length,
        reportIds: reports.map(r => r.id),
        reportDetails: reports.map(r => ({
          id: r.id,
          title: r.title,
          status: r.status,
          createdAt: r.createdAt
        }))
      });

      // Get full entity data
      const entities = entitiesResponse?.entities || [];
      console.log('Using entities:', {
        count: entities.length,
        entityIds: entities.map(e => e.id),
        entityTypes: entities.map(e => e.entityType)
      });

      if (reports.length === 0) {
        console.warn('No reports found for case:', caseData.id);
        // You might want to show a warning to the user here
      }

      // Get the latest update for reviewer info
      const latestUpdate = caseData.updates && caseData.updates.length > 0
        ? caseData.updates[caseData.updates.length - 1]
        : null;

      // Generate and download PDF immediately
      console.log('Creating PDF document for case:', caseData.id);
      const doc = (
        <QueryClientProvider client={pdfQueryClient}>
          <ExportDocument
            caseData={caseData}
            reports={reports}
            entities={entities}
            options={{
              printedBy: printedBy,
              reviewer: latestUpdate?.displayName || printedBy,
              approvedDateTime: latestUpdate?.eventTime,
              includeSections: options.includeSections,
              excludeIdentifiers: options.excludeIdentifiers
            }}
            schemas={{
              person: personSchemas?.schemas?.[0]?.schemaDefinition,
              property: propertySchemas?.schemas?.[0]?.schemaDefinition,
              vehicle: vehicleSchemas?.schemas?.[0]?.schemaDefinition
            }}
          />
        </QueryClientProvider>
      );

      console.log('Document created, generating PDF blob...');

      // Create PDF blob
      const blob = await pdf(doc).toBlob();
      console.log('PDF blob created:', {
        type: blob.type,
        size: blob.size,
        hasValidHeader: await validatePdfHeader(blob),
        firstBytes: await getFirstBytes(blob, 10)
      });

      // Validate PDF blob
      if (!blob || blob.size === 0) {
        throw new Error('Generated PDF blob is empty');
      }

      if (blob.type !== 'application/pdf') {
        throw new Error(`Invalid blob type: ${blob.type}`);
      }

      const isValidHeader = await validatePdfHeader(blob);
      if (!isValidHeader) {
        throw new Error('Invalid PDF header');
      }

      // Create download link and trigger download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `case-${caseData.id}-export.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('PDF download completed successfully');

    } catch (error) {
      Sentry.withScope((scope) => {
        scope.setTag("error_key", "pdf-export");
        scope.setTag("export_type", "case-pdf");
        scope.setTag("failure_stage", "pdf_generation");
        scope.setContext("export_info", {
          caseId: caseData.id,
          caseTitle: caseData.title,
          caseStatus: caseData.status,
          reportsCount: reportsResponse?.reports?.length || 0,
          entitiesCount: entitiesResponse?.entities?.length || 0,
          exportOptions: options,
          hasReports: !!reportsResponse?.reports?.length,
          hasEntities: !!entitiesResponse?.entities?.length,
        });
        Sentry.captureException(error);
      });
      console.error('Error generating PDF:', error);
      // TODO: Show error to user
    }
  };

  // Helper function to validate PDF header
  const validatePdfHeader = async (blob: Blob): Promise<boolean> => {
    try {
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const header = String.fromCharCode.apply(null, Array.from(uint8Array.slice(0, 5)));
      return header === '%PDF-';
    } catch (error) {
      console.error('Error validating PDF header:', error);
      return false;
    }
  };

  // Helper function to get first N bytes of a blob
  const getFirstBytes = async (blob: Blob, n: number): Promise<string> => {
    try {
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      return Array.from(uint8Array.slice(0, n))
        .map(b => b.toString(16).padStart(2, '0'))
        .join(' ');
    } catch (error) {
      console.error('Error getting first bytes:', error);
      return '';
    }
  };

  // Handle case resolution
  const handleCaseResolve = (status: CaseStatus, explanation: string) => {
    updateCaseStatus(
      {
        caseId: caseData.id || '',
        status: status,
        note: explanation || 'Case status updated',
      } as any,
      {
        onSuccess: () => {
          console.log('Case resolved successfully with status:', status);
          setIsResolveModalOpen(false);
        },
        onError: (error) => {
          console.error('Failed to resolve case:', error);
        },
      }
    );
  };



  return (
    <>
      <Header
        breadcrumbs={breadcrumbs}
        title={formattedCaseId}
        tags={tags}
        onClose={onBack}
        metadata={[
          {
            label: "Date Opened",
            value: formatDate(caseData.createTime || "")
          },
          {
            label: "Last Updated",
            value: formatDate(caseData.updateTime || "")
          },
          {
            label: "Assigned Investigator",
            value: primaryInvestigator?.name || primaryInvestigator?.displayName || "N/A"
          }
        ]}
        actions={[
          {
            label: "Export",
            leftIcon: <AdfScannerIcon />,
            size: "medium",
            color: "grey",
            prominence: false,
            // Export should always be available
            onClick: () => {
              console.log('Export button clicked');
              setIsExportOpen(true);
            }
          },
          {
            label: "Audit",
            leftIcon: <TaskIcon />,
            size: "medium",
            color: "grey",
            prominence: false,
            // Audit should always be available
            onClick: () => console.log("Audit")
          },
          // Only show Manage button for open cases
          ...(!isCaseClosed(caseData.status) ? [{
            label: "Manage",
            leftIcon: <ManageAccountsIcon />,
            size: "medium" as const,
            color: "grey" as const,
            prominence: false,
            onClick: () => {
              console.log('Manage button clicked');
              setIsManageModalOpen(true);
            }
          }] : []),
          {
            label: isCaseClosed(caseData.status) ? "Reopen" : "Close",
            leftIcon: <TaskIcon />,
            size: "medium" as const,
            color: "grey" as const,
            prominence: false,
            // Allow reopening even when read-only, but disable closing
            onClick: (isReadOnly && !isCaseClosed(caseData.status)) ? undefined : () => {
              if (isCaseClosed(caseData.status)) {
                console.log('Reopen button clicked');
                // For closed cases, open manage modal in forced assignment mode
                // Case will be reopened when assignment is completed
                setIsForcedAssignmentMode(true);
                setIsManageModalOpen(true);
              } else {
                console.log('Close button clicked');
                setIsResolveModalOpen(true);
              }
            }
          }
        ]}
      />



      {/* Priority Dropdown */}
      <Popover
        open={isPriorityDropdownOpen}
        anchorEl={priorityTagRef.current}
        onClose={() => setIsPriorityDropdownOpen(false)}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        keepMounted={false}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              borderRadius: "8px",
              boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
              border: "1px solid #E1E4E9",
              minWidth: 120,
              zIndex: 1300,
            },
          },
        }}
      >
        <List sx={{ p: 0 }}>
          {PRIORITY_OPTIONS.map((option) => (
            <ListItem
              key={option.value}
              onClick={() => handlePriorityChange(option.value)}
              sx={{
                py: 1,
                px: 2,
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: '#F5F5F5',
                },
                '&:first-of-type': {
                  borderTopLeftRadius: '8px',
                  borderTopRightRadius: '8px',
                },
                '&:last-of-type': {
                  borderBottomLeftRadius: '8px',
                  borderBottomRightRadius: '8px',
                },
              }}
            >
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        padding: '4px 8px',
                        backgroundColor: getPriorityColors(option.color).base,
                        color: getPriorityColors(option.color).text,
                        borderRadius: '6px',
                        fontSize: '11px',
                        fontWeight: 700,
                      }}
                    >
                      {option.label}
                    </div>
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Popover>

      <ExportModal
        isOpen={isExportOpen}
        onClose={() => {
          console.log('Export modal closed');
          setIsExportOpen(false);
        }}
        onExport={(options) => {
          console.log('Export modal triggered export with options:', options);
          handleExport(options);
        }}
      />
      {isManageModalOpen && (
        <ManageModal
          onClose={() => {
            setIsManageModalOpen(false);
            setIsForcedAssignmentMode(false);
          }}
          objectId={caseData.id || ''}
          objectType="case"
          showAssignTab={isForcedAssignmentMode || !isCaseClosed(caseData.status)}
          showDetailsTab={false}
          objectName="Case"
          isForcedAssignment={isForcedAssignmentMode}
          onAssignmentComplete={() => {
            console.log('ManageModal onAssignmentComplete triggered');


            setIsManageModalOpen(false);
            setIsForcedAssignmentMode(false);
            console.log('Calling onCaseUpdate to trigger refetch');
            onCaseUpdate?.(); // Trigger parent refetch
          }}
        />
      )}
      <CaseResolveModal
        open={isResolveModalOpen}
        onClose={() => setIsResolveModalOpen(false)}
        onResolve={handleCaseResolve}
        caseData={caseData}
        isLoading={isUpdatingCaseStatus}
      />

    </>
  );
} 