/* Existing Styles */
.filterButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 9vw;
  border-radius: 8px;
  background-color: #fff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #000000b2;
  transition: width 0.3s ease, padding 0.3s ease, background-color 0.3s ease;
  height: 36px;
}

.searchFilterBar {
  position: absolute;
  top: 44px;
  left: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 16px;
}

.searchBoxContainer {
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  flex: 1 1 auto;
  min-width: 0;
}

.searchBoxInputContainer {
  position: relative;
  border-radius: 8px;
}

.searchBoxIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
}

.searchBoxInput {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #ccc;
  color: #333;
  outline: none;
  height: 36px;
}

.searchResultsDropdown {
  list-style: none;
  padding: 0;
  margin-top: 4px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.searchResultsItem {
  border-bottom: 1px solid #eee;
  cursor: pointer;
  color: #333;
}

.searchResultsItemName {
  font-weight: bold;
}

.searchResultsItemAddress {
  font-size: 0.9em;
  color: #888;
}

.filterButtonsContainer {
  display: flex;
  flex-direction: row;
  gap: 6px;
}

.filterButton:hover {
  background-color: #f5f5f5;
}

.filterButtonSelected {
  background-color: #eaf2ff;
  color: #0060ff;
}

.filterButtonSelected:hover {
  background-color: #d0e4ff;
}

.filterButtonLabel {
  margin-left: 8px;
  white-space: nowrap;
  font-size: 12px;
  transition: width 0.3s ease, opacity 0.3s ease, margin 0.3s ease;
}

.filterButton.collapsed {
  width: 64px;
  padding: 0px;
}

.filterButton.collapsed .filterButtonLabel {
  width: 0;
  margin: 0;
  opacity: 0;
  overflow: hidden;
}

.unitStatusContainer {
  position: relative;
  display: inline-block;
}

.unitStatusDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  margin-top: 4px;
  padding: 14px;
  z-index: 999;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.unitStatusFilterButtons {
  display: flex;
  flex-direction: row;
  gap: 14px;
}

.responderListModal {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.responderGroupTitle {
  font-weight: bold;
  color: #9ca3af;
  font-size: 12px;
  margin-bottom: 10px;
}

.responderName {
  font-weight: 400;
  color: #35353e;
  font-size: 14px;
}

.responderRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2px;
  margin-bottom: 14px;
}

.responderActions {
  display: flex;
  flex-direction: row;
  gap: 4px;
}

.addButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f0f0f0;
  cursor: pointer;
  color: #000000b2;
  transition: width 0.3s ease, padding 0.3s ease, background-color 0.3s ease;
}

.addButton:hover {
  background-color: #dcdcdc;
}

.addButton:active {
  background-color: #c0c0c0;
}

.chipButton {
  background-color: #f0f0f0;
  color: #000;
  border: none;
  border-radius: 16px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 0.85rem;
  outline: none;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.chipButton:hover {
  background-color: #7e7e7e;
}

.chipButtonSelected {
  background-color: #000;
  color: #f0f0f0;
}
