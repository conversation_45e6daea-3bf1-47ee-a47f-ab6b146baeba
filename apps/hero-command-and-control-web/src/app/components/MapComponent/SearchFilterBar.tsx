"use client";

import SearchIcon from "@mui/icons-material/Search";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import { Feature } from "proto/hero/featureflags/v1/featureflags_pb";
import React from "react";
import { FaCamera, FaUser } from "react-icons/fa";
import { useUserAsset } from "../../contexts/User/UserAssetContext";
import styles from "./SearchFilterBar.module.css";

interface SearchFilterBarProps {
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSuggestionClick: (suggestion: any) => void;
  searchResults: any[];
  isSearchFocused: boolean;
  onSearchFocus: () => void;
  onSearchBlur: () => void;

  // Filter props
  showCameras: boolean;
  setShowCameras: React.Dispatch<React.SetStateAction<boolean>>;
  unitStatusFilter: string;
  setUnitStatusFilter: React.Dispatch<React.SetStateAction<string>>;
  responders: Asset[];

  // Computed style props
  currentSearchWidth: number;
  buttonPaddingX: number;
  buttonPaddingY: number;
  fontSize: number;

  // View Props
  onSwitchToPlan: () => void;
  onSwitchTo3D: () => void;
  onCenterMap: () => void;

  // Handle agent assignment
  onAssignAgent: (assetId: string) => void;

  // Handle position
  isCameraIncident: boolean;
}

export default function SearchFilterBar({
  // Search
  searchQuery,
  onSearchChange,
  onSuggestionClick,
  searchResults,
  isSearchFocused,
  onSearchFocus,
  onSearchBlur,

  // Filters
  showCameras,
  setShowCameras,
  unitStatusFilter,
  setUnitStatusFilter,
  responders,

  // Dynamic style values
  currentSearchWidth,
  buttonPaddingX,
  buttonPaddingY,
  fontSize,

  // View
  onSwitchToPlan,
  onSwitchTo3D,
  onCenterMap,

  // Handle agent assignment
  onAssignAgent,

  // Handle position
  isCameraIncident,
}: SearchFilterBarProps) {
  const { featureFlags } = useUserAsset();
  const isCameraFeatureEnabled = featureFlags.has(Feature.EXPERIMENTAL_CAMERA);

  // Measure available bar width and the buttons width to cap the search width
  const barRef = React.useRef<HTMLDivElement | null>(null);
  const buttonsRef = React.useRef<HTMLDivElement | null>(null);
  const [barWidth, setBarWidth] = React.useState<number>(0);
  const [buttonsWidth, setButtonsWidth] = React.useState<number>(0);

  React.useEffect(() => {
    const barEl = barRef.current;
    const btnEl = buttonsRef.current;
    if (!barEl) return;

    const barObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setBarWidth(entry.contentRect.width);
      }
    });
    barObserver.observe(barEl);

    let btnObserver: ResizeObserver | null = null;
    if (btnEl) {
      btnObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setButtonsWidth(entry.contentRect.width);
        }
      });
      btnObserver.observe(btnEl);
    }

    return () => {
      barObserver.disconnect();
      btnObserver?.disconnect();
    };
  }, []);

  // Grow to fill available space; cap by desired currentSearchWidth
  const effectiveSearchWidth = Math.min(
    currentSearchWidth,
    Math.max(0, barWidth - buttonsWidth)
  );

  const [unitStatusAnchorEl, setUnitStatusAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const isUnitStatusOpen = Boolean(unitStatusAnchorEl);

  const dynamicButtonStyle: React.CSSProperties = {
    padding: `${buttonPaddingY}px ${buttonPaddingX}px`,
    fontSize: `${fontSize}px`,
  };

  // Toggle filter selection: if already selected, deselect (set to "None")
  const handleUnitStatusSelect = () => {
    if (unitStatusFilter === "All Units") {
      setUnitStatusFilter("None");
    } else {
      setUnitStatusFilter("All Units");
    }
  };

  const filteredResponders = React.useMemo(() => {
    // First filter out responders with unknown or offline statuses
    const validResponders = responders.filter(
      (r) =>
        r.status &&
        // @ts-expect-error TODO: Fix type issue
        r.status !== "ASSET_STATUS_UNSPECIFIED" &&
        // @ts-expect-error TODO: Fix type issue
        r.status !== "ASSET_STATUS_OFFLINE" &&
        // @ts-expect-error TODO: Fix type issue
        r.status !== "ASSET_STATUS_DEACTIVATED"
    );

    if (unitStatusFilter === "None") {
      return [];
    } else if (unitStatusFilter === "All Units") {
      return validResponders;
    } else if (unitStatusFilter === "Available") {
      return validResponders.filter(
        // @ts-expect-error TODO: Fix type issue
        (r) => r.status === "ASSET_STATUS_AVAILABLE"
      );
    }
    return validResponders;
  }, [unitStatusFilter, responders]);

  return (
    <>
      <div
        ref={barRef}
        className={styles.searchFilterBar}
        style={{
          top: isCameraIncident ? "44px" : "10px",
          transition: "top 0.3s ease",
        }}
      >
        {/* Left Section: Search Box */}
        <div className={styles.searchBoxContainer}>
          <div className={styles.searchBoxInputContainer}>
            <SearchIcon
              className={styles.searchBoxIcon}
              sx={{
                left: `${buttonPaddingX}px`,
                fontSize: `20px`,
              }}
            />
            <input
              type="text"
              value={searchQuery}
              onChange={onSearchChange}
              onFocus={onSearchFocus}
              onBlur={onSearchBlur}
              placeholder="Search for an address"
              className={styles.searchBoxInput}
              style={{
                paddingTop: `${buttonPaddingY}px`,
                paddingRight: `${buttonPaddingX}px`,
                paddingBottom: `${buttonPaddingY}px`,
                paddingLeft: `${buttonPaddingX + 30}px`,
                fontSize: `16px`,
              }}
            />
          </div>

          {searchResults.length > 0 && isSearchFocused && (
            <ul className={styles.searchResultsDropdown}>
              {searchResults.map((suggestion) => (
                <li
                  key={suggestion.mapbox_id}
                  className={styles.searchResultsItem}
                  style={{
                    padding: `${buttonPaddingY}px ${buttonPaddingX}px`,
                    fontSize: `16px`,
                  }}
                  onMouseDown={() => onSuggestionClick(suggestion)}
                  onMouseEnter={(e) =>
                    (e.currentTarget.style.backgroundColor = "#f0f0f0")
                  }
                  onMouseLeave={(e) =>
                    (e.currentTarget.style.backgroundColor = "#fff")
                  }
                >
                  <div className={styles.searchResultsItemName}>
                    {suggestion.name}
                  </div>
                  <div className={styles.searchResultsItemAddress}>
                    {suggestion.full_address || suggestion.address}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* Right Section: Filter Buttons */}
        <div ref={buttonsRef} className={styles.filterButtonsContainer}>
          <button
            onClick={onCenterMap}
            className={`
              ${styles.filterButton} 
              ${styles.collapsed}
              ${styles.centerButton}
            `}
            style={dynamicButtonStyle}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 18 18"
                id="Recenter--Streamline-Sharp-Material"
                height={18}
                width={18}
                color="#4D4D4D"
              >
                <desc>
                  {"Recenter Streamline Icon: https://streamlinehq.com"}
                </desc>
                <path
                  fill="#000000"
                  d="M8.4375 17.25v-3.5062499999999996l-1.03125 1.03125 -0.8062499999999999 -0.7875000000000001 2.4000000000000004 -2.4000000000000004 2.4000000000000004 2.4000000000000004 -0.7875000000000001 0.7875000000000001 -1.0499999999999998 -1.03125V17.25h-1.125ZM4.012499999999999 11.399999999999999l-0.7875000000000001 -0.8062499999999999 1.03125 -1.03125H0.75v-1.125h3.5062499999999996L3.2249999999999996 7.387499999999999l0.7875000000000001 -0.7875000000000001 2.4000000000000004 2.4000000000000004 -2.4000000000000004 2.4000000000000004Zm9.993749999999999 0 -2.4000000000000004 -2.4000000000000004 2.4000000000000004 -2.4000000000000004 0.7875000000000001 0.7875000000000001 -1.03125 1.0499999999999998H17.25v1.125h-3.4875000000000003l1.03125 1.03125 -0.7875000000000001 0.8062499999999999ZM9 9.9375c-0.26249999999999996 0 -0.4843875 -0.0906375 -0.6656249999999999 -0.271875 -0.1812375 -0.1812375 -0.271875 -0.40312499999999996 -0.271875 -0.6656249999999999s0.0906375 -0.4906125 0.271875 -0.684375c0.1812375 -0.1937625 0.40312499999999996 -0.290625 0.6656249999999999 -0.290625 0.2749875 0 0.5062500000000001 0.09375 0.6937500000000001 0.28125s0.28125 0.41876250000000004 0.28125 0.6937500000000001c0 0.26249999999999996 -0.09686249999999999 0.4843875 -0.290625 0.6656249999999999 -0.1937625 0.1812375 -0.421875 0.271875 -0.684375 0.271875Zm0 -3.5437499999999997 -2.4000000000000004 -2.4000000000000004 0.8062499999999999 -0.7875000000000001 1.03125 1.03125V0.75h1.125v3.4875000000000003l1.0499999999999998 -1.03125 0.7875000000000001 0.7875000000000001 -2.4000000000000004 2.4000000000000004Z"
                  strokeWidth={0.375}
                />
              </svg>
            </div>
          </button>

          {isCameraFeatureEnabled && (
            <button
              onClick={() => setShowCameras((prev) => !prev)}
              className={`
                ${styles.filterButton} 
                ${showCameras ? styles.filterButtonSelected : ""}
                ${styles.collapsed}
              `}
              style={dynamicButtonStyle}
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                <FaCamera size={14} />
              </div>
            </button>
          )}

          {/* Unit Status Button */}
          <button
            onClick={handleUnitStatusSelect}
            className={`
                ${styles.filterButton} 
                ${
                  unitStatusFilter !== "None" ? styles.filterButtonSelected : ""
                }
                ${styles.collapsed}
              `}
            style={dynamicButtonStyle}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              <FaUser size={14} />
            </div>
          </button>
        </div>
      </div>
    </>
  );
}
