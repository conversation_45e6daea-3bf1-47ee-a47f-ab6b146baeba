import { PanelType } from "../types";

import { PANEL_CLOSE_TIMEOUT_MS } from "../constants";

import { create } from "@bufbuild/protobuf";
import { AddEntityRefToCaseRequestSchema } from "proto/hero/cases/v1/cases_pb";
import {
  GetLatestEntityRequest,
  Reference,
} from "proto/hero/entity/v1/entity_pb";
import { getLatestEntity as getLatestEntityEndpoint } from "../../../../apis/services/workflow/entity/endpoints";
import { getProperty } from "../../../../apis/services/workflow/property/endpoints";
import { propertyToEntityFormat } from "../utils/propertyUtils";
import { updateEntityListSection } from "../utils/sectionUtils";
import { entitiesToPropertyData } from "../utils/utils";

interface EntityHandlersProps {
  // State
  people: any[];
  vehicles: any[];
  properties: any[];
  organizations: any[];
  setPeople: (people: any[]) => void;
  setVehicles: (vehicles: any[]) => void;
  setProperties: (properties: any[]) => void;
  setOrganizations: (organizations: any[]) => void;
  setMediaFiles: (files: any[]) => void;
  setSidePanelOpen: (open: boolean) => void;
  setActivePanelType: (type: PanelType | null) => void;
  setEditingEntityId: (id: string | null) => void;
  setEditingEntityFormData: (data: any) => void;
  setSelectedRowId: (id: string | null) => void;
  setActiveOffenseRelation: (relation: any) => void;
  setNotification: (notification: any) => void;
  activeOffenseRelation: any;
  activeVehicleOffenseContext: any;
  activePropertyOffenseContext: any;
  activeOrganizationOffenseContext: any;
  activeVictimOrganizationOffenseContext: any;
  setActiveVehicleOffenseContext: (context: any) => void;
  setActivePropertyOffenseContext: (context: any) => void;
  setActiveOrganizationOffenseContext: (context: any) => void;
  setActiveVictimOrganizationOffenseContext: (context: any) => void;

  // IDs and refs
  reportId: string | null;
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  reportSections: any;
  associatedCases: any[];

  // API mutations
  updateReportSectionMutation: any;
  addEntityRefToCaseMutation: any;

  // Other handlers

  //Alec@ note to come back here when needing to handle Victim Organizations -- add relationType as prop
  handleAddPersonToOffense: (
    personId: string,
    offenseId: string,
    relationType: any,
    entity?: any
  ) => void;
  handleAddVehicleToOffense: (
    vehicleId: string,
    offenseId: string,
    entity?: any
  ) => void;
  handleAddPropertyToOffense: (
    propertyId: string,
    offenseId: string,
    entity?: any
  ) => void;
  handleAddOrganizationToOffense: (
    organizationId: string,
    offenseId: string,
    relationType?: "victim" | "general",
    entity?: any
  ) => void;
}

export const createEntityHandlers = ({
  people,
  vehicles,
  properties,
  organizations,
  setPeople,
  setVehicles,
  setProperties,
  setOrganizations,
  setMediaFiles,
  setSidePanelOpen,
  setActivePanelType,
  setEditingEntityId,
  setEditingEntityFormData,
  setSelectedRowId,
  setActiveOffenseRelation,
  setNotification,
  activeOffenseRelation,
  activeVehicleOffenseContext,
  activePropertyOffenseContext,
  activeOrganizationOffenseContext,
  activeVictimOrganizationOffenseContext,
  setActiveVehicleOffenseContext,
  setActivePropertyOffenseContext,
  setActiveOrganizationOffenseContext,
  setActiveVictimOrganizationOffenseContext,
  reportId,
  peopleListSectionId,
  vehicleListSectionId,
  propertyListSectionId,
  organizationListSectionId,
  reportSections,
  associatedCases,
  updateReportSectionMutation,
  addEntityRefToCaseMutation,
  handleAddPersonToOffense,
  handleAddVehicleToOffense,
  handleAddPropertyToOffense,
  handleAddOrganizationToOffense,
}: EntityHandlersProps) => {
  const handlePropertyRecordSelect = (property: any) => {
    try {
      // Convert property-service property to entity-like shape for UI compatibility
      const entity = propertyToEntityFormat(property);

      // De-duplicate and update state
      let updatedEntities: any[];
      if (!properties.some((p) => p.id === entity.id)) {
        updatedEntities = [...properties, entity];
        setProperties(updatedEntities);
      } else {
        updatedEntities = properties;
      }

      // Update the report section
      if (propertyListSectionId && reportId) {
        updatePropertyListSection(
          "Properties",
          entity,
          propertyListSectionId,
          reportId,
          reportSections,
          updateReportSectionMutation,
          { people, vehicles, properties, organizations },
          updatedEntities
        );
      }

      // If selected from an offense context, create relation
      if (activePropertyOffenseContext) {
        handleAddPropertyToOffense(
          entity.id,
          activePropertyOffenseContext.offenseId,
          entity
        );
        setActivePropertyOffenseContext(null);
      }

      setNotification({
        open: true,
        message: `property added successfully`,
        severity: "success",
      });
    } catch (error) {
      console.error("Error handling property selection:", error);
      setNotification({
        open: true,
        message: `Error adding property: ${error}`,
        severity: "error",
      });
    }
  };
  const handleEntityDelete = (
    entityId: string,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => {
    let updatedEntities: any[] = [];
    let sectionId: string | null = null;
    let stateSetter: ((value: any[]) => void) | null = null;
    let title: string = "";

    switch (entityType) {
      case "person":
        updatedEntities = people.filter((entity) => entity.id !== entityId);
        sectionId = peopleListSectionId;
        stateSetter = setPeople;
        title = "People";
        break;
      case "property":
        updatedEntities = properties.filter((entity) => entity.id !== entityId);
        sectionId = propertyListSectionId;
        stateSetter = setProperties;
        title = "Properties";
        break;
      case "vehicle":
        updatedEntities = vehicles.filter((entity) => entity.id !== entityId);
        sectionId = vehicleListSectionId;
        stateSetter = setVehicles;
        title = "Vehicles";
        break;
      case "organization":
        updatedEntities = organizations.filter(
          (entity) => entity.id !== entityId
        );
        sectionId = organizationListSectionId;
        stateSetter = setOrganizations;
        title = "Organizations";
        break;
    }

    // Optimistic UI update: Remove from local state
    if (stateSetter) {
      stateSetter(updatedEntities);
    }

    // Optimistic UI update: Remove reference from report section
    if (sectionId && reportId) {
      updateEntityListSection(
        title,
        null,
        sectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedEntities
      );
    }
  };

  const handleEntityEdit = async (
    entityId: string,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => {
    let panelType: PanelType | null = null;

    switch (entityType) {
      case "person":
        panelType = PanelType.PERSON;
        break;
      case "property":
        panelType = PanelType.PROPERTY;
        // Fetch existing property data for editing
        try {
          const propertyData = await getProperty(entityId);

          if (propertyData?.details) {
            setEditingEntityFormData(propertyData.details);
          }
        } catch (error) {
          // Continue with edit even if fetch fails
        }
        break;
      case "vehicle":
        panelType = PanelType.VEHICLE;
        break;
      case "organization":
        panelType = PanelType.ORGANIZATION;
        break;
    }

    if (panelType) {
      setActivePanelType(panelType);
      setEditingEntityId(entityId);
      setSidePanelOpen(true);
    }
  };

  const updatePropertyListSection = (
    title: string,
    entity: any,
    sectionId: string,
    reportId: string,
    reportSections: any,
    updateReportSectionMutation: any,
    allEntities: any,
    updatedProperties: any[]
  ) => {
    if (!sectionId || !reportId) return;

    const section = reportSections?.sections?.find(
      (s: any) => s.id === sectionId
    );

    if (!section) {
      console.error("Property section not found:", sectionId);
      return;
    }

    // Convert entity to property reference format
    const propertyData = entitiesToPropertyData([entity])[0];
    const propertyRef = {
      id: entity.id,
      displayName: propertyData.category || propertyData.description || "Unknown",
      relationType: "involved",
      version: entity.version || 1,
    };

    // Get existing property refs or initialize empty array
    const existingPropertyRefs = section.propertyList?.propertyRefs || [];

    // Check if property already exists in the section
    const existingIndex = existingPropertyRefs.findIndex(
      (ref: any) => ref.id === entity.id
    );

    let updatedPropertyRefs;
    if (existingIndex >= 0) {
      // Update existing property ref
      updatedPropertyRefs = [...existingPropertyRefs];
      updatedPropertyRefs[existingIndex] = propertyRef;
    } else {
      // Add new property ref
      updatedPropertyRefs = [...existingPropertyRefs, propertyRef];
    }

    // Create the updated section
    const updatedSection = {
      ...section,
      propertyList: {
        id: section.propertyList?.id || "",
        title: title,
        propertyRefs: updatedPropertyRefs,
      },
    };

    // Update the report section
    updateReportSectionMutation.mutate({
      reportId: reportId,
      section: updatedSection,
    });
  };

  const handleEntitySelect = (
    entityRef: Reference,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => {
    const getLatestEntity = async () => {
      try {
        const entity = await getLatestEntityEndpoint({
          id: entityRef.id,
        } as GetLatestEntityRequest);

        if (!entity) {
          setNotification({
            open: true,
            message: "Entity not found",
            severity: "error",
          });
          return;
        }

        let updatedEntities: any[];
        let sectionId: string | null = null;
        let title: string = "";

        switch (entityType) {
          case "person":
            // Check for duplicates before adding
            if (!people.some(p => p.id === entity.id)) {
              updatedEntities = [...people, entity];
              setPeople(updatedEntities);
            } else {
              updatedEntities = people;
            }
            sectionId = peopleListSectionId;
            title = "People";

            // If this person was selected from an offense context, create the relation
            if (activeOffenseRelation) {
              handleAddPersonToOffense(
                entity.id,
                activeOffenseRelation.offenseId,
                activeOffenseRelation.relationType,
                entity
              );
              // Clear the active offense relation
              setActiveOffenseRelation(null);
            }
            break;
          case "vehicle":
            // Check for duplicates before adding
            if (!vehicles.some(v => v.id === entity.id)) {
              updatedEntities = [...vehicles, entity];
              setVehicles(updatedEntities);
            } else {
              updatedEntities = vehicles;
            }
            sectionId = vehicleListSectionId;
            title = "Vehicles";

            // If this vehicle was selected from an offense context, create the relation
            if (activeVehicleOffenseContext) {
              handleAddVehicleToOffense(
                entity.id,
                activeVehicleOffenseContext.offenseId,
                entity
              );
              // Clear the active vehicle offense context
              setActiveVehicleOffenseContext(null);
            }
            break;
          case "property":
            // Check for duplicates before adding
            if (!properties.some(p => p.id === entity.id)) {
              updatedEntities = [...properties, entity];
              setProperties(updatedEntities);
            } else {
              updatedEntities = properties;
            }
            sectionId = propertyListSectionId;
            title = "Properties";

            // If this property was selected from an offense context, create the relation
            if (activePropertyOffenseContext) {
              handleAddPropertyToOffense(
                entity.id,
                activePropertyOffenseContext.offenseId,
                entity
              );
              // Clear the active property offense context
              setActivePropertyOffenseContext(null);
            }
            break;
          case "organization":
            // Check for duplicates before adding
            if (!organizations.some(o => o.id === entity.id)) {
              updatedEntities = [...organizations, entity];
              setOrganizations(updatedEntities);
            } else {
              updatedEntities = organizations;
            }
            sectionId = organizationListSectionId;
            title = "Organizations";

            // If this organization was selected from an offense context, create the relation
            if (activeVictimOrganizationOffenseContext) {
              handleAddOrganizationToOffense(
                entity.id,
                activeVictimOrganizationOffenseContext.offenseId,
                "victim",
                entity
              );
              // Clear the active victim organization offense context
              setActiveVictimOrganizationOffenseContext(null);
            } else if (activeOrganizationOffenseContext) {
              handleAddOrganizationToOffense(
                entity.id,
                activeOrganizationOffenseContext.offenseId,
                "general",
                entity
              );
              // Clear the active organization offense context
              setActiveOrganizationOffenseContext(null);
            }
            break;
        }

        // Update the report section based on entity type
        if (sectionId && reportId) {
          if (entityType === "property") {
            // Use the new property list section update function
            updatePropertyListSection(
              title,
              entity,
              sectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedEntities
            );
          } else {
            // Use the existing entity list section update function for other entity types
            updateEntityListSection(
              title,
              entity,
              sectionId,
              reportId,
              reportSections,
              updateReportSectionMutation,
              { people, vehicles, properties, organizations },
              updatedEntities
            );
          }

          // Add entity reference to all associated cases
          if (associatedCases?.length > 0) {
            associatedCases.forEach((caseItem) => {
              // Check if entity is already in this case to prevent duplicates
              const isAlreadyInCase = caseItem.entityRefs?.some(
                (ref: any) => ref.id === entityRef.id
              );

              if (!isAlreadyInCase) {
                const request = create(AddEntityRefToCaseRequestSchema, {
                  caseId: caseItem.id,
                  entityRef: {
                    id: entityRef.id,
                    type: `ENTITY_TYPE_${entityType.toUpperCase()}`,
                    displayName: entityRef.displayName,
                  },
                });
                addEntityRefToCaseMutation.mutate(request);
              }
            });
          }
        }

        setNotification({
          open: true,
          message: `${entityType} added successfully`,
          severity: "success",
        });
      } catch (error) {
        console.error("Error fetching entity:", error);
        setNotification({
          open: true,
          message: `Error fetching ${entityType}: ${error}`,
          severity: "error",
        });
      }
    };

    getLatestEntity();
  };

  const handleRemovePersonFromReport = (
    personId: string,
    entityType: "person"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedPeople = people.filter((person) => person.id !== personId);
    setPeople(updatedPeople);

    // Update the report section to remove the entity reference
    if (peopleListSectionId) {
      updateEntityListSection(
        "People",
        null,
        peopleListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedPeople
      );
    }
  };

  const handleRemoveVehicleFromReport = (
    vehicleId: string,
    entityType: "vehicle"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedVehicles = vehicles.filter(
      (vehicle) => vehicle.id !== vehicleId
    );
    setVehicles(updatedVehicles);

    // Update the report section to remove the entity reference
    if (vehicleListSectionId) {
      updateEntityListSection(
        "Vehicles",
        null,
        vehicleListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedVehicles
      );
    }
  };

  const handleRemovePropertyFromReport = (
    propertyId: string,
    entityType: "property"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedProperties = properties.filter(
      (property) => property.id !== propertyId
    );
    setProperties(updatedProperties);

    // Update the report section to remove the entity reference
    if (propertyListSectionId) {
      updateEntityListSection(
        "Properties",
        null,
        propertyListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedProperties
      );
    }
  };

  const handleRemoveOrganizationFromReport = (
    organizationId: string,
    entityType: "organization"
  ) => {
    if (!reportId) return;

    // Remove from local state
    const updatedOrganizations = organizations.filter(
      (organization) => organization.id !== organizationId
    );
    setOrganizations(updatedOrganizations);

    // Update the report section to remove the entity reference
    if (organizationListSectionId) {
      updateEntityListSection(
        "Organizations",
        null,
        organizationListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedOrganizations
      );
    }
  };

  // Batch removal functions for offense/incident deletion
  const handleBatchRemovePeopleFromReport = (personIds: string[]) => {
    if (!reportId || personIds.length === 0) return;

    // Remove multiple people from local state in one operation
    const updatedPeople = people.filter(
      (person) => !personIds.includes(person.id)
    );
    setPeople(updatedPeople);

    // Update the report section with single API call
    if (peopleListSectionId) {
      updateEntityListSection(
        "People",
        null,
        peopleListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedPeople
      );
    }
  };

  const handleBatchRemoveVehiclesFromReport = (vehicleIds: string[]) => {
    if (!reportId || vehicleIds.length === 0) return;

    // Remove multiple vehicles from local state in one operation
    const updatedVehicles = vehicles.filter(
      (vehicle) => !vehicleIds.includes(vehicle.id)
    );
    setVehicles(updatedVehicles);

    // Update the report section with single API call
    if (vehicleListSectionId) {
      updateEntityListSection(
        "Vehicles",
        null,
        vehicleListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedVehicles
      );
    }
  };

  const handleBatchRemovePropertiesFromReport = (propertyIds: string[]) => {
    if (!reportId || propertyIds.length === 0) return;

    // Remove multiple properties from local state in one operation
    const updatedProperties = properties.filter(
      (property) => !propertyIds.includes(property.id)
    );
    setProperties(updatedProperties);

    // Update the report section with single API call
    if (propertyListSectionId) {
      updateEntityListSection(
        "Properties",
        null,
        propertyListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedProperties
      );
    }
  };

  const handleBatchRemoveOrganizationsFromReport = (
    organizationIds: string[]
  ) => {
    if (!reportId || organizationIds.length === 0) return;

    // Remove multiple organizations from local state in one operation
    const updatedOrganizations = organizations.filter(
      (organization) => !organizationIds.includes(organization.id)
    );
    setOrganizations(updatedOrganizations);

    // Update the report section with single API call
    if (organizationListSectionId) {
      updateEntityListSection(
        "Organizations",
        null,
        organizationListSectionId,
        reportId,
        reportSections,
        updateReportSectionMutation,
        { people, vehicles, properties, organizations },
        updatedOrganizations
      );
    }
  };

  const handleCloseSidePanel = (
    force: boolean = false,
    isSaveLoading: boolean,
    isUpdateLoading: boolean,
    isSaveAndAddAnotherLoading: boolean
  ) => {
    if (
      !force &&
      (isSaveLoading || isUpdateLoading || isSaveAndAddAnotherLoading)
    ) {
      return;
    }

    setSidePanelOpen(false);
    setTimeout(() => {
      setActivePanelType(null);
      setEditingEntityId(null);
      setEditingEntityFormData(null);
      setSelectedRowId(null);
      // Clear active offense relation when panel closes
      setActiveOffenseRelation(null);
      // Don't clear vehicle and property offense or org contexts here - they need to persist until entity creation
    }, PANEL_CLOSE_TIMEOUT_MS);
  };

  const handleOpenSidePanel = (panelType: PanelType, readOnly: boolean) => {
    if (readOnly) return; // Don't open panel in read-only mode

    setActivePanelType(panelType);
    setEditingEntityId(null);
    setEditingEntityFormData(null);
    setSidePanelOpen(true);
  };

  return {
    handleEntityDelete,
    handleEntityEdit,
    handleEntitySelect,
    handlePropertyRecordSelect,
    handleRemovePersonFromReport,
    handleRemoveVehicleFromReport,
    handleRemovePropertyFromReport,
    handleRemoveOrganizationFromReport,
    // Batch removal functions for offense/incident deletion
    handleBatchRemovePeopleFromReport,
    handleBatchRemoveVehiclesFromReport,
    handleBatchRemovePropertiesFromReport,
    handleBatchRemoveOrganizationsFromReport,
    handleCloseSidePanel,
    handleOpenSidePanel,
  };
};
