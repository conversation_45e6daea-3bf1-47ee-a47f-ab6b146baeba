import { ListCasesByReportIdRequest } from "proto/hero/cases/v1/cases_pb";
import { EntityType } from "proto/hero/entity/v1/entity_pb";
import {
  useAddEntityRefToCase,
  useAddPropertyRefToCase,
  useCase,
  useListCasesByReportId,
  useRemoveEntityRefFromCase,
  useRemovePropertyRefFromCase,
} from "../../../../apis/services/workflow/cases/hooks";
import {
  useBatchGetLatestEntities,
  useCreateEntity,
  useGetEntitySchemaByVersion,
  useGetLatestEntity,
  useListLatestEntitySchemas,
  useUpdateEntity,
} from "../../../../apis/services/workflow/entity/hooks";
import { useBatchGetProperties, useCreateProperty, useProperty } from "../../../../apis/services/workflow/property/hooks";
import {
  useAddComment,
  useCreateRelation,
  useCreateReportSection,
  useDeleteRelation,
  useGetComments,
  useListReportSections,
  useListReviewRoundsForReport,
  useReport,
  useResolveComment,
  useUpdateRelation,
  useUpdateReportSection,
} from "../../../../apis/services/workflow/reports/v2/hooks";
import { PanelType } from "../types";

interface UseReportingPageAPIProps {
  reportId: string | null;
  editingEntityId: string | null;
  editingEntity: any;
  activePanelType: PanelType | null; // PanelType to know if we're editing property vs entity
  entityIdsToFetch: string[];
  propertyIdsToFetch: string[];
  incidentDetailsSectionId: string | null;
  narrativeSectionId: string | null;
  peopleListSectionId: string | null;
  vehicleListSectionId: string | null;
  propertyListSectionId: string | null;
  organizationListSectionId: string | null;
  mediaSectionId: string | null;
  associatedCases: any[];
  reviewRoundId: string;
  isReviseOrder: boolean;
}

export const useReportingPageAPI = ({
  reportId,
  editingEntityId,
  editingEntity,
  activePanelType,
  entityIdsToFetch,
  propertyIdsToFetch,
  incidentDetailsSectionId,
  narrativeSectionId,
  peopleListSectionId,
  vehicleListSectionId,
  propertyListSectionId,
  organizationListSectionId,
  mediaSectionId,
  associatedCases,
  reviewRoundId,
  isReviseOrder,
}: UseReportingPageAPIProps) => {
  // Report API hooks
  const reportQuery = useReport(reportId || "", 0, {
    enabled: !!reportId,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  const reportSectionsQuery = useListReportSections(reportId || "", {
    enabled: !!reportId,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  const reviewRoundsQuery = useListReviewRoundsForReport(
    reportId || "",
    1,
    "",
    {
      enabled: !!reportId && isReviseOrder,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  // Comment API hooks
  const incidentCommentQuery = useGetComments(
    reportId || "",
    incidentDetailsSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!incidentDetailsSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const narrativeCommentQuery = useGetComments(
    reportId || "",
    narrativeSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!narrativeSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const peopleCommentQuery = useGetComments(
    reportId || "",
    peopleListSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!peopleListSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const vehicleCommentQuery = useGetComments(
    reportId || "",
    vehicleListSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!vehicleListSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const propertyCommentQuery = useGetComments(
    reportId || "",
    propertyListSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!propertyListSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const organizationCommentQuery = useGetComments(
    reportId || "",
    organizationListSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!organizationListSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const mediaCommentQuery = useGetComments(
    reportId || "",
    mediaSectionId || undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId && !!mediaSectionId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const globalCommentQuery = useGetComments(
    reportId || "",
    undefined,
    undefined,
    undefined,
    {
      enabled: !!reportId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  // Entity API hooks
  const personSchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.PERSON,
    pageSize: 1,
    pageToken: "",
  } as any);

  const propertySchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.PROPERTY,
    pageSize: 1,
    pageToken: "",
  } as any);

  const vehicleSchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.VEHICLE,
    pageSize: 1,
    pageToken: "",
  } as any);
  const organizationSchemasQuery = useListLatestEntitySchemas({
    entityType: EntityType.ORGANIZATION,
    pageSize: 1,
    pageToken: "",
  } as any);

  const editingEntityQuery = useGetLatestEntity(
    editingEntityId || "",
    undefined,
    {
      enabled: !!editingEntityId && activePanelType !== "PROPERTY", // Don't fetch entity for properties
      queryKey: ["entity", editingEntityId],
    }
  );

  // Add property query for when editing properties
  const editingPropertyQuery = useProperty(
    (!!editingEntityId && activePanelType === "PROPERTY") ? editingEntityId : ""
  );

  const specificSchemaQuery = useGetEntitySchemaByVersion(
    editingEntity?.schemaId || "",
    editingEntity?.schemaVersion || 0,
    {
      enabled: !!editingEntity?.schemaId && !!editingEntity?.schemaVersion,
      queryKey: [
        "entitySchema",
        editingEntity?.schemaId,
        editingEntity?.schemaVersion,
      ],
    }
  );

  const batchEntitiesQuery = useBatchGetLatestEntities(entityIdsToFetch, {
    queryKey: ["entity", "batch", reportId, entityIdsToFetch],
    enabled: !!reportId && entityIdsToFetch.length > 0,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });

  const batchPropertiesQuery = useBatchGetProperties(propertyIdsToFetch);

  // Case API hooks
  const casesQuery = useListCasesByReportId(
    {
      reportId: reportId || "",
      pageSize: 50,
      pageToken: "",
    } as ListCasesByReportIdRequest,
    {
      enabled: !!reportId,
      staleTime: 0,
      refetchOnMount: "always",
      refetchOnWindowFocus: true,
    }
  );

  const firstCaseId = associatedCases?.[0]?.id;
  const fullCaseQuery = useCase(firstCaseId || "", undefined, {
    enabled: !!firstCaseId,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });



  // Mutations
  const updateReportSectionMutation = useUpdateReportSection();
  const createReportSectionMutation = useCreateReportSection();
  const addCommentMutation = useAddComment();
  const resolveCommentMutation = useResolveComment();
  const createEntityMutation = useCreateEntity();
  const createPropertyMutation = useCreateProperty();
  const updateEntityMutation = useUpdateEntity();
  const addEntityRefToCaseMutation = useAddEntityRefToCase();
  const removeEntityRefFromCaseMutation = useRemoveEntityRefFromCase();
  const addPropertyRefToCaseMutation = useAddPropertyRefToCase();
  const removePropertyRefFromCaseMutation = useRemovePropertyRefFromCase();
  const createRelationMutation = useCreateRelation();
  const deleteRelationMutation = useDeleteRelation();
  const updateRelationMutation = useUpdateRelation();

  return {
    // Queries
    reportQuery,
    reportSectionsQuery,
    reviewRoundsQuery,
    incidentCommentQuery,
    narrativeCommentQuery,
    peopleCommentQuery,
    vehicleCommentQuery,
    propertyCommentQuery,
    organizationCommentQuery,
    mediaCommentQuery,
    globalCommentQuery,
    personSchemasQuery,
    propertySchemasQuery,
    vehicleSchemasQuery,
    organizationSchemasQuery,
    editingEntityQuery,
    editingPropertyQuery,
    specificSchemaQuery,
    batchEntitiesQuery,
    batchPropertiesQuery,
    casesQuery,
    fullCaseQuery,

    // Mutations
    updateReportSectionMutation,
    createReportSectionMutation,
    addCommentMutation,
    resolveCommentMutation,
    createEntityMutation,
    createPropertyMutation,
    updateEntityMutation,
    addEntityRefToCaseMutation,
    removeEntityRefFromCaseMutation,
    addPropertyRefToCaseMutation,
    removePropertyRefFromCaseMutation,
    createRelationMutation,
    deleteRelationMutation,
    updateRelationMutation,
  };
};
