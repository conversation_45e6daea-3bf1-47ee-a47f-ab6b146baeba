import { Comment as ProtoComment } from "proto/hero/reports/v2/reports_pb";
import { PropertyStatus } from "../../../../apis/services/workflow/property/types";
import { getPropertyStatusDisplay, getReadablePropertyType } from "../../../../utils/propertyHelpers";
import {
  OrganizationData,
  PersonData,
  PropertyData,
  VehicleData,
} from "../../entities/EntityCard";
import { LocalComment } from "../types";

// Helper function to format comments
export const formatComments = (
  apiComments: ProtoComment[] | undefined
): LocalComment[] => {
  if (!apiComments) return [];
  return apiComments.map((comment) => ({
    id: comment.id,
    author: comment.authorAssetId,
    displayName: comment.displayName,
    date: new Date(comment.createdAt).toLocaleString(),
    text: comment.text,
    resolved: comment.resolved || false,
    sectionId: comment.sectionId,
    reportId: comment.reportId,
  }));
};

// Navigation items for sidebar
export const navItems = [
  { id: "incident-details", label: "Incident Details" },
  { id: "offenses", label: "Events" },
  { id: "arrests", label: "Arrests" },
  { id: "people", label: "People" },
  { id: "vehicles", label: "Vehicles" },
  { id: "property", label: "Property" },
  { id: "organizations", label: "Organization" },
  { id: "narrative", label: "Narrative" },
  { id: "media", label: "Media" },
];

// getReadablePropertyType and getPropertyStatusDisplay are now imported from shared helpers

// Get entity display name based on entity type
export const getEntityDisplayName = (entity: any): string => {
  if (!entity || !entity.data) return "Unknown Entity";

  switch (entity.entityType) {
    case "ENTITY_TYPE_PERSON": {
      const firstName = entity.data.classificationSection?.firstName || "";
      const lastName = entity.data.classificationSection?.lastName || "";
      return `${firstName} ${lastName}`.trim() || "Unnamed Person";
    }

    case "ENTITY_TYPE_VEHICLE": {
      const year = entity.data.vehicleInformationSection?.year || "";
      const make = entity.data.vehicleInformationSection?.make || "";
      const model = entity.data.vehicleInformationSection?.model || "";
      return `${year} ${make} ${model}`.trim() || "Unknown Vehicle";
    }

    case "ENTITY_TYPE_PROPERTY": {
      const desc = entity.data.propertyInformationSection?.description || entity.data.description || "";
      return desc || "Unknown Property";
    }

    case "ENTITY_TYPE_ORGANIZATION": {
      const name = entity.data.informationSection?.name || "";
      const type = entity.data.informationSection?.type || "";
      return `${name} - ${type}`.trim() || "Unknown Organization";
    }
    default:
      return "Unknown Entity";
  }
};

// Convert Property objects to PropertyData format (for PropertySection)
export const propertiesToPropertyData = (properties: any[]): PropertyData[] =>
  properties.map((property) => {
    const schema = property.propertySchema || {};

    return {
      id: property.id,
      propertyType: getReadablePropertyType(schema.propertyType),
      serialNumber: schema.serialNumber || "",
      category: schema.category || "",
      collectedValue: schema.value || "",
      makeModelBrand: schema.makeModelBrand || "",
      description: schema.description || "",
      status: property.propertyStatus ? getPropertyStatusDisplay(property.propertyStatus as PropertyStatus) : "",
      owner: schema.owner || "",
    };
  });

// Convert entities for card display
export const entitiesToPersonData = (entities: any[]): PersonData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      name: `${d.classificationSection?.firstName || ""} ${d.classificationSection?.lastName || ""
        }`.trim(),
      sex: d.classificationSection?.sex || "",
      height: d.descriptorsSection?.height || "",
      hair: d.descriptorsSection?.hairColor || "",
      weight: d.descriptorsSection?.weight || "",
      eye: d.descriptorsSection?.eyeColor || "",
      dateOfBirth: d.classificationSection?.dateOfBirth || "",
    };
  });

export const entitiesToPropertyData = (entities: any[]): PropertyData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    const schema = entity.propertySchema || {}; // Direct property API format

    // Handle both entity format (d.propertyInformationSection) and direct property API format (entity.propertySchema)
    const propertyType = schema.nibrsPropertyType || d.propertyInformationSection?.nibrsPropertyType || d.nibrsPropertyType || "";

    // Handle status mapping - check multiple possible locations for status
    let status: any = null;
    if (entity.propertyStatus !== undefined && entity.propertyStatus !== null) {
      // Direct property API format - status is on the entity itself
      status = entity.propertyStatus;
    } else if (d.status !== undefined && d.status !== null) {
      // Entity format - status is in the data object
      status = d.status;
    } else if (d.propertyInformationSection?.status !== undefined && d.propertyInformationSection?.status !== null) {
      // Entity format - status is in the propertyInformationSection
      status = d.propertyInformationSection.status;
    }

    return {
      id: entity.id,
      propertyType: getReadablePropertyType(propertyType),
      serialNumber: schema.serialNumber || d.propertyInformationSection?.serialNumber || d.serialNumber || "",
      category: schema.category || d.propertyInformationSection?.category || d.category || "",
      collectedValue: schema.value || d.propertyInformationSection?.value || d.value || "",
      makeModelBrand: schema.makeModelBrand || d.propertyInformationSection?.makeModelBrand || d.makeModelBrand || "",
      description: schema.description || d.propertyInformationSection?.description || d.description || "",
      status: status !== null ? getPropertyStatusDisplay(status as PropertyStatus) : "",
      owner: schema.owner || d.propertyInformationSection?.owner || d.owner || "",
    };
  });

export const entitiesToVehicleData = (entities: any[]): VehicleData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      vIN: d.vehicleInformationSection?.vIN || "",
      year: d.vehicleInformationSection?.year || "",
      make: d.vehicleInformationSection?.make || "",
      model: d.vehicleInformationSection?.model || "",
      color: d.vehicleInformationSection?.color || "",
      ownerIfApplicable: d.vehicleInformationSection?.ownerIfApplicable || "",
    };
  });

export const entitiesToOrganizationData = (
  entities: any[]
): OrganizationData[] =>
  entities.map((entity) => {
    const d = entity.data || {};
    return {
      id: entity.id,
      name: d.informationSection?.name || "",
      type: d.informationSection?.type || "",
      status: d.informationSection?.status || undefined,
      phone: d.informationSection?.phone || undefined,
      website: d.informationSection?.website || undefined,
      email: d.informationSection?.email || undefined,
      address: d.informationSection?.address || [],
      primaryContactName: d.informationSection?.primaryContactName || undefined,
      primaryContactTitle: d.informationSection?.primaryContactTitle || undefined,
      primaryContactPhone: d.informationSection?.primaryContactPhone || undefined,
      campusOrganization: d.informationSection?.campusOrganization || undefined,
    };
  });
