"use client";

import { useOrders } from "@/app/contexts/OrderContext";
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { Box } from "@mui/material";
import { useRouter, useSearchParams } from "next/navigation";
import { Order, OrderType } from "proto/hero/orders/v2/orders_pb";
import { useEffect, useState } from "react";
import IncidentDetailPage from "../../../incidents/incidentComponents/IncidentDetailPage";
import { CasesSection } from "./components/CasesSection";
import { Header } from "./components/Header";
import { IncidentsSection } from "./components/IncidentsSection";
import { RightSidebar } from "./components/RightSidebar";
import { TaskItem, TasksSection } from "./components/TasksSection";

export default function HomePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { asset: dispatcherAsset } = useUserAsset();
  const { ordersData, isLoading, isError, error, acknowledgeOrder } =
    useOrders();
  const [tasksData, setTasksData] = useState<TaskItem[]>([]);

  // Check if an incident is selected from the URL
  const incidentId = searchParams?.get("incidentId");

  // Update tasks when ordersData changes
  useEffect(() => {
    if (ordersData?.orders) {
      // Filter and transform orders
      const reportOrders = ordersData.orders.filter(
        (order: Order) =>
          // @ts-expect-error TODO: Fix type issue
          (order.type === "ORDER_TYPE_WRITE_REPORT" ||
            // @ts-expect-error TODO: Fix type issue
            order.type === "ORDER_TYPE_REVIEW_REPORT" ||
            // @ts-expect-error TODO: Fix type issue
            order.type === "ORDER_TYPE_REVISE_REPORT" ||
            // @ts-expect-error TODO: Fix type issue
            order.type === "ORDER_TYPE_MANAGE_CASE") &&
          // @ts-expect-error TODO: Fix type issue
          order.status !== "ORDER_STATUS_COMPLETED" &&
          // @ts-expect-error TODO: Fix type issue
          order.status !== "ORDER_STATUS_CANCELLED"
      );

      // Transform to task items
      const tasks = reportOrders.map(
        (order: Order): TaskItem => {
          // @ts-expect-error TODO: Fix type issue
          const isManageCase = order.type === "ORDER_TYPE_MANAGE_CASE";

          return {
            id: order.id || "",
            name: getOrderTypeString(order.type),
            // @ts-expect-error TODO: Fix type issue
            status: getOrderStatusString(order.status),
            displayId: isManageCase ? (order.caseId || "") : compactSituationId(order.reportId),
            targetId: isManageCase ? (order.caseId || "") : (order.reportId || ""),
            caseType: isManageCase ? "Case" : "Incident",
            reportDate: formatDate(order.createTime),
            reportingOfficer: dispatcherAsset?.name || "Unknown",
            // @ts-expect-error TODO: Fix type issue
            originalStatus: getOriginalStatusString(order.status),
          };
        }
      );

      setTasksData(tasks);
      console.log("Updated tasks data:", tasks.length);
    }
  }, [ordersData, dispatcherAsset?.name]);

  const compactSituationId = (situationId: string | undefined): string => {
    return situationId?.replace(/[^0-9]/g, "").slice(0, 7) || "";
  };

  const getOrderTypeString = (type: OrderType | undefined): string => {
    switch (type) {
      // @ts-expect-error TODO: Fix type issue
      case "ORDER_TYPE_WRITE_REPORT":
        return "Write Report";
      // @ts-expect-error TODO: Fix type issue
      case "ORDER_TYPE_REVIEW_REPORT":
        return "Review Report";
      // @ts-expect-error TODO: Fix type issue
      case "ORDER_TYPE_REVISE_REPORT":
        return "Revise Report";
      // @ts-expect-error TODO: Fix type issue
      case "ORDER_TYPE_MANAGE_CASE":
        return "Investigate Case";
      default:
        return (
          OrderType[type || 0]?.replace("ORDER_TYPE_", "").replace("_", " ") ||
          "Unknown Task"
        );
    }
  };

  const getOrderStatusString = (status: string): string => {
    switch (status) {
      case "ORDER_STATUS_CREATED":
        return "Not Started";
      case "ORDER_STATUS_ACKNOWLEDGED":
        return "In Progress";
      case "ORDER_STATUS_IN_PROGRESS":
        return "In Progress";
      default: {
        const words = status?.replace("ORDER_STATUS_", "").split("_");
        return words
          ?.map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(" ");
      }
    }
  };

  // Helper function to get the original status
  const getOriginalStatusString = (status: string): string => {
    return status?.replace("ORDER_STATUS_", "");
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return "N/A";
    try {
      if (timestamp.seconds) {
        const date = new Date(Number(timestamp.seconds) * 1000);
        return date.toLocaleDateString();
      }
      return new Date(timestamp).toLocaleDateString();
    } catch (e) {
      return "N/A";
    }
  };

  const handleTaskClick = (
    orderId: string,
    targetId: string,
    originalStatusString: string
  ) => {
    // Find the task to determine its type
    const task = tasksData.find(t => t.id === orderId);

    // Navigate based on task type
    if (task?.caseType === "Case") {
      // For MANAGE_CASE orders, navigate to the case detail page with query param
      router.push(`/cases?caseId=${targetId}`);
    } else {
      // For report orders, navigate to the report page
      router.push(`/reports?reportId=${targetId}`);
    }

    if (originalStatusString === "CREATED") {
      // Optimistic update after 1 second
      setTimeout(() => {
        setTasksData((current) =>
          current.map((task) =>
            task.id === orderId
              ? {
                ...task,
                status: "In Progress",
                originalStatus: "ACKNOWLEDGED",
              }
              : task
          )
        );
      }, 1000);
      acknowledgeOrder(orderId);
    }
  };

  // For dismissing the incident detail view
  const handleCloseIncidentDetail = () => {
    router.push("/reports");
  };

  // If an incident is selected, render the incident detail page
  if (incidentId) {
    return <IncidentDetailPage onClose={handleCloseIncidentDetail} />;
  }

  // Check if user is a responder or supervisor
  const isResponderOrSupervisor =
    // @ts-expect-error TODO: Fix type issue
    dispatcherAsset?.type === "ASSET_TYPE_RESPONDER" ||
    // @ts-expect-error TODO: Fix type issue
    dispatcherAsset?.type === "ASSET_TYPE_SUPERVISOR";

  return (
    <Box
      sx={{
        height: "100vh",
        overflow: "auto",
        display: "flex",
        flexDirection: "column",
        pl: "48px",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flex: 1,
        }}
      >
        {/* Main Content Area */}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            pr: "24px",
          }}
        >
          {/* Header */}
          <Box
            sx={{
              p: 3,
              pb: 0,
            }}
          >
            <Header />
          </Box>

          {/* Main Content with padding */}
          <Box
            sx={{
              p: 3,
              pt: 3,
              display: "flex",
              flexDirection: "column",
            }}
          >
            {/* Show TasksSection only for responders or supervisors */}
            {isResponderOrSupervisor && (
              <TasksSection
                tasks={tasksData}
                isLoading={isLoading}
                isError={isError}
                error={error}
                onTaskClick={handleTaskClick}
              />
            )}

            <IncidentsSection />

            {/* Show CasesSection only for responders or supervisors */}
            {isResponderOrSupervisor && (
              <CasesSection />
            )}
          </Box>
        </Box>

        {/* Sticky Right Sidebar */}
        <Box
          sx={{
            position: "sticky",
            top: 0,
            height: "100vh",
            overflow: "auto",
            pr: 3,
            pt: 3,
          }}
        >
          <RightSidebar />
        </Box>
      </Box>
    </Box>
  );
}
