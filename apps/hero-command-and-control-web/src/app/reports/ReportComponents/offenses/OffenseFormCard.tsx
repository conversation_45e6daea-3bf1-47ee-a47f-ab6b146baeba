import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Dropdown } from "@/design-system/components/Dropdown";
import { Radio } from "@/design-system/components/Radio";
import {
  DropdownOption,
  InputType,
  TextInput,
} from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Box, Collapse, Divider, IconButton } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useBatchGetLatestEntities } from "../../../apis/services/workflow/entity/hooks";
import { OffenseData } from "./OffenseSearchModal";

// Import the extracted components
import OrganizationList, { OrganizationData } from "./OrganizationList";
import PersonList, { PersonData } from "./PersonList";
import PropertyList, { PropertyData } from "./PropertyList";
import VehicleList, { VehicleData } from "./VehicleList";

// Import constants
import {
  ADDITIONAL_JUSTIFIABLE_HOMICIDE_CIRCUMSTANCES_OPTIONS,
  AGGRAVATED_ASSAULT_HOMICIDE_CIRCUMSTANCES_OPTIONS,
  BIAS_MOTIVATION_OPTIONS,
  CRIMINAL_ACTIVITY_AND_GANG_OPTIONS,
  METHOD_OF_ENTRY_OPTIONS,
  OFFENSE_WAS_OPTIONS,
  WEAPON_TYPE_OPTIONS,
} from "./constants";

interface OffenseFormData {
  offenseWas: string;
  cargoTheftRelated: boolean;
  offenseSpecialCircumstance: string;
  justifiableHomicideCircumstance: string;
  biasMotivation: string;
  numberOfPremisesEntered: string;
  methodOfEntry: string;
  offenderSuspectedOfUsing: {
    alcohol: boolean;
    computerEquipment: boolean;
    drugsNarcotics: boolean;
  };
  attributes: {
    domesticAbuse: boolean;
    gangRelated: boolean;
    gamblingRelated: boolean;
  };
  weapons: Array<{
    id: string;
    weaponType: string;
    isAutomatic: boolean;
  }>;
  criminalActivityAndGang: DropdownOption[];
}

interface OffenseFormCardProps {
  offense: OffenseData;
  offenseId: string;
  onSave: (offenseId: string, data: OffenseFormData) => void;
  onDelete: (offenseId: string) => void;
  onSaveStatusChange?: (status: {
    isSaving: boolean;
    hasUnsavedChanges: boolean;
    source: string;
  }) => void;
  readOnly?: boolean;
  initialData?: Partial<OffenseFormData>;
  // Entity management props
  people?: PersonData[];
  vehicles?: VehicleData[];
  properties?: PropertyData[];
  organizations?: OrganizationData[];
  relations?: any[];
  reportId?: string;
  associatedCases?: any[];
  onAddPerson?: (
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onAddVehicle?: () => void;
  onAddProperty?: () => void;
  onAddOrganization?: () => void;
  onAddVictimOrganization?: () => void;
  onRemovePersonFromOffense?: (personId: string, offenseId: string) => void;
  onRemoveVehicleFromOffense?: (vehicleId: string, offenseId: string) => void;
  onRemovePropertyFromOffense?: (propertyId: string, offenseId: string) => void;
  onRemoveOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemoveVictimOrganizationFromOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;
  onRemovePersonFromReport?: (personId: string, entityType: "person") => void;
  onRemoveVehicleFromReport?: (
    vehicleId: string,
    entityType: "vehicle"
  ) => void;
  onRemovePropertyFromReport?: (
    propertyId: string,
    entityType: "property"
  ) => void;

  onRemoveOrganizationFromReport?: (
    organizationId: string,
    entityType: "organization"
  ) => void;

  onOpenSidePanel?: (
    panelType: "PERSON" | "VEHICLE" | "PROPERTY" | "ORGANIZATION"
  ) => void;

  onQuickAddPersonToOffense?: (
    personId: string,
    offenseId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => void;
  onQuickAddVehicleToOffense?: (vehicleId: string, offenseId: string) => void;
  onQuickAddPropertyToOffense?: (propertyId: string, offenseId: string) => void;

  onQuickAddOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;

  onQuickAddVictimOrganizationToOffense?: (
    organizationId: string,
    offenseId: string
  ) => void;

  onEntityEdit?: (
    entityId: string,
    entityType: "person" | "vehicle" | "property" | "organization"
  ) => void;
  onAddVictimDetails?: (personId: string) => void;
  onAddVictimDetailsOrganization?: (organizationId: string) => void;
}

// Helper function to convert text to title case
const toTitleCase = (str: string): string => {
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const OffenseFormCard: React.FC<OffenseFormCardProps> = ({
  offense,
  offenseId,
  onSave,
  onDelete,
  onSaveStatusChange,
  readOnly = false,
  initialData,
  people,
  vehicles,
  properties,
  organizations,
  relations,
  reportId,
  associatedCases,
  onAddPerson,
  onAddVehicle,
  onAddProperty,
  onAddOrganization,
  onAddVictimOrganization,
  onRemovePersonFromOffense,
  onRemoveVehicleFromOffense,
  onRemovePropertyFromOffense,
  onRemoveOrganizationFromOffense,
  onRemoveVictimOrganizationFromOffense,
  onRemovePersonFromReport,
  onRemoveVehicleFromReport,
  onRemovePropertyFromReport,
  onRemoveOrganizationFromReport,
  onOpenSidePanel,
  onQuickAddPersonToOffense,
  onQuickAddVehicleToOffense,
  onQuickAddPropertyToOffense,
  onQuickAddOrganizationToOffense,
  onQuickAddVictimOrganizationToOffense,
  onEntityEdit,
  onAddVictimDetails,
  onAddVictimDetailsOrganization,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // State for case entities (for quick add)
  const [caseEntityIds, setCaseEntityIds] = useState<string[]>([]);
  const [caseEntities, setCaseEntities] = useState<{
    people: PersonData[];
    vehicles: VehicleData[];
    properties: PropertyData[];
    organizations: OrganizationData[];
  }>({
    people: [],
    vehicles: [],
    properties: [],
    organizations: [],
  });

  const [formData, setFormData] = useState<OffenseFormData>({
    offenseWas: "completed",
    cargoTheftRelated: false,
    offenseSpecialCircumstance: "",
    justifiableHomicideCircumstance: "",
    biasMotivation: "",
    numberOfPremisesEntered: "",
    methodOfEntry: "",
    offenderSuspectedOfUsing: {
      alcohol: false,
      computerEquipment: false,
      drugsNarcotics: false,
    },
    attributes: {
      domesticAbuse: false,
      gangRelated: false,
      gamblingRelated: false,
    },
    weapons: [],
    criminalActivityAndGang: [],
    ...initialData,
  });

  const currentFormDataRef = useRef(formData);

  // Update refs when state changes
  useEffect(() => {
    currentFormDataRef.current = formData;
  }, [formData]);

  // Extract entity IDs from associated cases
  useEffect(() => {
    if (associatedCases && associatedCases.length > 0) {
      const firstCase = associatedCases[0];
      if (firstCase?.entityRefs) {
        const entityIds = firstCase.entityRefs.map((ref: any) => ref.id);
        setCaseEntityIds(entityIds);
      } else {
        setCaseEntityIds([]);
      }
    } else {
      setCaseEntityIds([]);
    }
  }, [associatedCases]);

  // Fetch case entities using batch get
  const { data: batchCaseEntitiesData } = useBatchGetLatestEntities(
    caseEntityIds,
    {
      enabled: caseEntityIds.length > 0,
      staleTime: 30 * 1000, // 30 seconds instead of 5 minutes for more responsive updates
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      queryKey: ["caseEntities", offenseId, caseEntityIds],
      placeholderData: (previousData) => previousData,
    }
  );

  // Process batch fetched case entities
  useEffect(() => {
    if (batchCaseEntitiesData?.entities) {
      const fetchedEntities = batchCaseEntitiesData.entities;
      const peopleEntities: any[] = [];
      const vehicleEntities: any[] = [];
      const propertyEntities: any[] = [];
      const organizationEntities: any[] = [];

      fetchedEntities.forEach((entity) => {
        // @ts-expect-error TODO: Fix type issue
        switch (entity.entityType as string) {
          case "ENTITY_TYPE_PERSON":
            peopleEntities.push(entity);
            break;
          case "ENTITY_TYPE_VEHICLE":
            vehicleEntities.push(entity);
            break;
          case "ENTITY_TYPE_PROPERTY":
            propertyEntities.push(entity);
            break;
          case "ENTITY_TYPE_ORGANIZATION":
            organizationEntities.push(entity);
            break;
        }
      });

      // Convert entities to display format
      const convertedPeople = peopleEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const classificationSection = data?.classificationSection;
          const descriptorsSection = data?.descriptorsSection;

          const firstName = classificationSection?.firstName || "";
          const lastName = classificationSection?.lastName || "";
          const middleName = classificationSection?.middleName || "";
          const name =
            `${firstName} ${middleName} ${lastName}`
              .trim()
              .replace(/\s+/g, " ") || "---";

          return {
            id: entity.id,
            name,
            sex: classificationSection?.sex || "---",
            height: descriptorsSection?.height || "---",
            hair: descriptorsSection?.hairColor || "---",
            weight: descriptorsSection?.weight || "---",
            eye: descriptorsSection?.eyeColor || "---",
            dateOfBirth: classificationSection?.dateOfBirth || "---",
          };
        } catch (error) {
          console.error("Error parsing person entity data:", error);
          return {
            id: entity.id,
            name: "---",
            sex: "---",
            height: "---",
            hair: "---",
            weight: "---",
            eye: "---",
            dateOfBirth: "---",
          };
        }
      });

      const convertedVehicles = vehicleEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const vehicleSection = data?.vehicleInformationSection;

          return {
            id: entity.id,
            vIN: vehicleSection?.vIN || "---",
            year: vehicleSection?.year || "---",
            make: vehicleSection?.make || "---",
            model: vehicleSection?.model || "---",
            color: vehicleSection?.color || "---",
            ownerIfApplicable: vehicleSection?.ownerIfApplicable || "---",
          };
        } catch (error) {
          console.error("Error parsing vehicle entity data:", error);
          return {
            id: entity.id,
            vIN: "---",
            year: "---",
            make: "---",
            model: "---",
            color: "---",
            ownerIfApplicable: "---",
          };
        }
      });

      const convertedProperties = propertyEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const propertySection = data?.propertyInformationSection;

          return {
            id: entity.id,
            propertyType: propertySection?.nibrsPropertyType || "---",
            serialNumber: propertySection?.serialNumber || "---",
            category: propertySection?.category || "---",
            collectedValue: propertySection?.collectedValue || "---",
            makeModelBrand: propertySection?.makeModelBrand || "---",
            description: propertySection?.description || "---",
          };
        } catch (error) {
          console.error("Error parsing property entity data:", error);
          return {
            id: entity.id,
            propertyType: "---",
            serialNumber: "---",
            category: "---",
            collectedValue: "---",
            makeModelBrand: "---",
            description: "---",
          };
        }
      });

      const convertedOrganizations = organizationEntities.map((entity) => {
        try {
          const data =
            typeof entity.data === "string"
              ? JSON.parse(entity.data)
              : entity.data;
          const organizationSection = data?.informationSection;

          return {
            id: entity.id,
            name: organizationSection?.name || "---",
            type: organizationSection?.type || "---",
            status: organizationSection?.status || undefined,
            phone: organizationSection?.phone || undefined,
            website: organizationSection?.website || undefined,
            email: organizationSection?.email || undefined,
            address: organizationSection?.address || [],
            primaryContactName:
              organizationSection?.primaryContactName || undefined,
            primaryContactTitle:
              organizationSection?.primaryContactTitle || undefined,
            primaryContactPhone:
              organizationSection?.primaryContactPhone || undefined,
            campusOrganization:
              organizationSection?.campusOrganization || undefined,
          };
        } catch (error) {
          console.error("Error parsing organization entity data:", error);
          return {
            id: entity.id,
            name: "---",
            type: "---",
            status: undefined,
            phone: undefined,
            website: undefined,
            email: undefined,
            address: [],
            primaryContactName: undefined,
            primaryContactTitle: undefined,
            primaryContactPhone: undefined,
            campusOrganization: undefined,
          };
        }
      });

      setCaseEntities({
        people: convertedPeople,
        vehicles: convertedVehicles,
        properties: convertedProperties,
        organizations: convertedOrganizations,
      });
    } else {
      setCaseEntities({
        people: [],
        vehicles: [],
        properties: [],
        organizations: [],
      });
    }
  }, [batchCaseEntitiesData]);

  // Use a ref to track previous status like IncidentDetailsCard
  const lastStatusRef = useRef<{
    isSaving: boolean;
    hasUnsavedChanges: boolean;
  } | null>(null);

  // Notify parent on save/dirty changes like IncidentDetailsCard
  useEffect(() => {
    if (!onSaveStatusChange) return;

    const currentStatus = { isSaving, hasUnsavedChanges };
    const lastStatus = lastStatusRef.current;
    const changed =
      !lastStatus ||
      lastStatus.isSaving !== currentStatus.isSaving ||
      lastStatus.hasUnsavedChanges !== currentStatus.hasUnsavedChanges;

    if (changed) {
      lastStatusRef.current = { ...currentStatus };
      onSaveStatusChange({ ...currentStatus, source: `offense_${offenseId}` });
    }
  }, [isSaving, hasUnsavedChanges, onSaveStatusChange, offenseId]);

  // Save offense immediately when created
  useEffect(() => {
    if (!initialData) {
      // This is a new offense, save immediately
      saveOffenseData();
    }
  }, []); // Only run on mount

  // Handler for criminal activity and gang multi-select
  const handleCriminalActivityAndGangChange = (newItems: DropdownOption[]) => {
    let processedItems = newItems;
    const noneUnknownOption = newItems.find(
      (item) => item.value === "noneUnknown"
    );
    const previousItems = formData.criminalActivityAndGang;
    const hadNoneUnknown = previousItems.find(
      (item) => item.value === "noneUnknown"
    );

    // If "None/Unknown" was just added
    if (noneUnknownOption && !hadNoneUnknown) {
      // Keep only "None/Unknown"
      processedItems = [noneUnknownOption];
    }
    // If "None/Unknown" was already selected and user added something else
    else if (hadNoneUnknown && newItems.length > previousItems.length) {
      // Remove "None/Unknown" and keep the new selection
      processedItems = newItems.filter((item) => item.value !== "noneUnknown");
    }

    // If "None/Unknown" is not selected, enforce maximum of 3 selections
    if (!processedItems.find((item) => item.value === "noneUnknown")) {
      processedItems = processedItems.slice(0, 3);
    }

    setFormData((prev) => {
      const updated = { ...prev, criminalActivityAndGang: processedItems };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    // Save immediately on change
    setTimeout(() => saveOffenseData(), 0);
  };

  const saveOffenseData = () => {
    if (isSaving || readOnly) return;

    setIsSaving(true);
    setHasUnsavedChanges(false);

    const currentData = currentFormDataRef.current;
    onSave(offenseId, currentData);

    // Simulate save completion
    setTimeout(() => {
      setIsSaving(false);
    }, 300);
  };

  const handleBlur = () => {
    if (hasUnsavedChanges && !readOnly) {
      saveOffenseData();
    }
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData((prev) => {
      const keys = field.split(".");
      if (keys.length === 1) {
        const updated = { ...prev, [field]: value };
        currentFormDataRef.current = updated;
        return updated;
      } else if (keys.length === 2) {
        const parentKey = keys[0] as keyof OffenseFormData;
        const parent = prev[parentKey];
        if (
          typeof parent === "object" &&
          parent !== null &&
          !Array.isArray(parent)
        ) {
          const updated = {
            ...prev,
            [parentKey]: {
              ...parent,
              [keys[1]]: value,
            },
          };
          currentFormDataRef.current = updated;
          return updated;
        }
      }
      return prev;
    });
    setHasUnsavedChanges(true);
  };

  const handleDropdownChange = (field: string) => (value: string | null) => {
    if (value !== null) {
      setFormData((prev) => {
        const updated = { ...prev, [field]: value };
        currentFormDataRef.current = updated;
        return updated;
      });
      setHasUnsavedChanges(true);
      // Save immediately on dropdown selection
      setTimeout(() => saveOffenseData(), 0);
    }
  };

  // Handle checkbox changes with proper signature
  const handleCheckboxChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const checked = e.target.checked;
      handleFormChange(field, checked);
      // Save immediately on checkbox change
      setTimeout(() => saveOffenseData(), 0);
    };

  // Handle radio changes for boolean values
  const handleRadioChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      handleFormChange(field, value === "true");
      // Save immediately on radio change
      setTimeout(() => saveOffenseData(), 0);
    };

  // Handle weapon dropdown changes
  const handleWeaponDropdownChange =
    (weaponId: string, field: string) => (value: string | null) => {
      setFormData((prev) => {
        const updated = {
          ...prev,
          weapons: prev.weapons.map((weapon) =>
            weapon.id === weaponId
              ? { ...weapon, [field]: value || "" }
              : weapon
          ),
        };
        currentFormDataRef.current = updated;
        return updated;
      });
      setHasUnsavedChanges(true);
      // Save immediately on weapon dropdown change
      setTimeout(() => saveOffenseData(), 0);
    };

  const handleWeaponChange = (weaponId: string, field: string, value: any) => {
    setFormData((prev) => {
      const updated = {
        ...prev,
        weapons: prev.weapons.map((weapon) =>
          weapon.id === weaponId ? { ...weapon, [field]: value } : weapon
        ),
      };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    // Save immediately on weapon change
    setTimeout(() => saveOffenseData(), 0);
  };

  const addWeapon = () => {
    const newWeaponId = `weapon_${Date.now()}`;
    setFormData((prev) => {
      const updated = {
        ...prev,
        weapons: [
          ...prev.weapons,
          { id: newWeaponId, weaponType: "", isAutomatic: false },
        ],
      };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    setTimeout(() => saveOffenseData(), 0);
  };

  const removeWeapon = (weaponId: string) => {
    setFormData((prev) => {
      const updated = {
        ...prev,
        weapons: prev.weapons.filter((weapon) => weapon.id !== weaponId),
      };
      currentFormDataRef.current = updated;
      return updated;
    });
    setHasUnsavedChanges(true);
    setTimeout(() => saveOffenseData(), 0);
  };

  // Helper function to get people related to this offense by relation type
  const getRelatedPeople = (
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ): PersonData[] => {
    if (!relations || !people) return [];

    const relationTypeMapping = {
      victim: "RELATION_TYPE_OFFENSE_VICTIM",
      offender: "RELATION_TYPE_OFFENSE_OFFENDER",
      witness: "RELATION_TYPE_OFFENSE_WITNESS",
      suspect: "RELATION_TYPE_OFFENSE_SUSPECT",
      involved_party: "RELATION_TYPE_OFFENSE_INVOLVED_PARTY",
    };

    const targetRelationType = relationTypeMapping[relationType];

    const relatedPersonIds = relations
      .filter((rel: any) => {
        if (rel.relationType !== targetRelationType) return false;

        const isThisOffense =
          (rel.objectA?.objectType === "offense" &&
            rel.objectA?.reportScopedId === offenseId) ||
          (rel.objectB?.objectType === "offense" &&
            rel.objectB?.reportScopedId === offenseId);

        return isThisOffense;
      })
      .map((rel: any) => {
        if (rel.objectA?.objectType === "entity") return rel.objectA.globalId;
        if (rel.objectB?.objectType === "entity") return rel.objectB.globalId;
        return null;
      })
      .filter(Boolean);

    return people.filter((person) => relatedPersonIds.includes(person.id));
  };

  // Helper function to get vehicles related to this offense
  const getRelatedVehicles = (): VehicleData[] => {
    if (!vehicles || !relations) return [];

    const relationTypeKey = "RELATION_TYPE_OFFENSE_VEHICLE";

    // Find relations where this offense is involved and relation type matches
    const relevantRelations = relations.filter((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      return isOffenseInvolved && relation.relationType === relationTypeKey;
    });

    // Extract vehicle IDs from relations
    const vehicleIds = relevantRelations
      .map((relation: any) => {
        // If object A is the offense, then object B is the vehicle
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the offense, then object A is the vehicle
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return vehicles that match these IDs
    return vehicles.filter((vehicle) => vehicleIds.includes(vehicle.id));
  };

  // Helper function to get properties related to this offense
  const getRelatedProperties = (): PropertyData[] => {
    if (!properties || !relations) return [];

    const relationTypeKey = "RELATION_TYPE_OFFENSE_PROPERTY";

    // Find relations where this offense is involved and relation type matches
    const relevantRelations = relations.filter((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      return isOffenseInvolved && relation.relationType === relationTypeKey;
    });

    // Extract property IDs from relations
    const propertyIds = relevantRelations
      .map((relation: any) => {
        // If object A is the offense, then object B is the property
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the offense, then object A is the property
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return properties that match these IDs
    return properties.filter((property) => propertyIds.includes(property.id));
  };

  // Helper function to get victim organizations related to this offense
  const getVictimOrganizations = (): OrganizationData[] => {
    if (!organizations || !relations) return [];

    // Find victim relations for organizations (using RELATION_TYPE_OFFENSE_VICTIM with organization entities)
    const relevantRelations = relations.filter((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      // Check if this is a victim relation involving an organization
      const isVictimRelation =
        relation.relationType === "RELATION_TYPE_OFFENSE_VICTIM";

      // Check if the relation involves an organization entity
      const hasOrganizationEntity =
        (relation.objectA?.objectType === "entity" &&
          organizations.some((org) => org.id === relation.objectA?.globalId)) ||
        (relation.objectB?.objectType === "entity" &&
          organizations.some((org) => org.id === relation.objectB?.globalId));

      return isOffenseInvolved && isVictimRelation && hasOrganizationEntity;
    });

    // Extract organization IDs from relations
    const organizationIds = relevantRelations
      .map((relation: any) => {
        // If object A is the offense, then object B is the organization
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the offense, then object A is the organization
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return organizations that match these IDs
    return organizations.filter((organization) =>
      organizationIds.includes(organization.id)
    );
  };

  // Helper function to get non-victim organizations related to this offense
  const getRelatedOrganizations = (): OrganizationData[] => {
    if (!organizations || !relations) return [];

    const relationTypeKey = "RELATION_TYPE_OFFENSE_ORGANIZATION";

    // Find relations where this offense is involved and relation type matches
    const relevantRelations = relations.filter((relation: any) => {
      const isOffenseInvolved =
        (relation.objectA?.objectType === "offense" &&
          relation.objectA?.reportScopedId === offenseId) ||
        (relation.objectB?.objectType === "offense" &&
          relation.objectB?.reportScopedId === offenseId);

      return isOffenseInvolved && relation.relationType === relationTypeKey;
    });

    // Extract organization IDs from relations
    const organizationIds = relevantRelations
      .map((relation: any) => {
        // If object A is the offense, then object B is the organization
        if (relation.objectA?.objectType === "offense") {
          return relation.objectB?.globalId;
        }
        // If object B is the offense, then object A is the organization
        return relation.objectA?.globalId;
      })
      .filter(Boolean);

    // Return organizations that match these IDs
    return organizations.filter((organization) =>
      organizationIds.includes(organization.id)
    );
  };

  // Get related entities for this offense
  const victims = getRelatedPeople("victim");
  const offenders = getRelatedPeople("offender");
  const witnesses = getRelatedPeople("witness");
  const suspects = getRelatedPeople("suspect");
  const involvedParties = getRelatedPeople("involved_party");
  const otherPeople = [...witnesses, ...suspects, ...involvedParties];
  const relatedVehicles = getRelatedVehicles();
  const relatedProperties = getRelatedProperties();
  const relatedOrganizations = getRelatedOrganizations();

  // Handler for adding entities to this offense
  const handleAddPerson = (
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => {
    if (onAddPerson) {
      onAddPerson(relationType);
    }
  };

  const handleAddVehicle = () => {
    if (onAddVehicle) {
      onAddVehicle();
    }
  };

  const handleAddProperty = () => {
    if (onAddProperty) {
      onAddProperty();
    }
  };

  const handleAddOrganization = () => {
    if (onAddOrganization) {
      onAddOrganization();
    }
  };

  const handleAddVictimOrganization = () => {
    if (onAddVictimOrganization) {
      onAddVictimOrganization();
    }
  };

  // Quick add handlers - these will directly add entities from the case to the offense
  const handleQuickAddPerson = (
    personId: string,
    relationType:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ) => {
    if (onQuickAddPersonToOffense) {
      onQuickAddPersonToOffense(personId, offenseId, relationType);
    }
  };

  const handleQuickAddVehicle = (vehicleId: string) => {
    if (onQuickAddVehicleToOffense) {
      onQuickAddVehicleToOffense(vehicleId, offenseId);
    }
  };

  const handleQuickAddProperty = (propertyId: string) => {
    if (onQuickAddPropertyToOffense) {
      onQuickAddPropertyToOffense(propertyId, offenseId);
    }
  };

  const handleQuickAddOrganization = (organizationId: string) => {
    if (onQuickAddOrganizationToOffense) {
      onQuickAddOrganizationToOffense(organizationId, offenseId);
    }
  };

  // Special handler for adding victim organizations
  const handleQuickAddVictimOrganization = (organizationId: string) => {
    if (onQuickAddVictimOrganizationToOffense) {
      // Call the victim-specific handler that will create relations with RELATION_TYPE_OFFENSE_VICTIM
      onQuickAddVictimOrganizationToOffense(organizationId, offenseId);
    }
  };

  // Helper function to get available entities for quick add (entities in case but not in this offense section)
  const getAvailableEntitiesForQuickAdd = (
    entityType: "person" | "vehicle" | "property" | "organization",
    relationType?:
      | "victim"
      | "offender"
      | "witness"
      | "suspect"
      | "involved_party"
  ): any[] => {
    let caseEntitiesForType: any[] = [];
    let currentRelatedEntities: any[] = [];

    if (entityType === "person" && relationType) {
      caseEntitiesForType = caseEntities.people;
      currentRelatedEntities = getRelatedPeople(relationType);
    } else if (entityType === "vehicle") {
      caseEntitiesForType = caseEntities.vehicles;
      currentRelatedEntities = getRelatedVehicles();
    } else if (entityType === "property") {
      caseEntitiesForType = caseEntities.properties;
      currentRelatedEntities = getRelatedProperties();
    } else if (entityType === "organization") {
      caseEntitiesForType = caseEntities.organizations;
      currentRelatedEntities = getRelatedOrganizations();
    }

    // Return case entities that are not already related to this offense
    return caseEntitiesForType.filter(
      (entity) =>
        !currentRelatedEntities.some((related) => related.id === entity.id)
    );
  };

  // Helper function to get available people for "Other People" section
  const getAvailablePeopleForOtherSection = (): PersonData[] => {
    const allOtherPeople = [
      ...getAvailableEntitiesForQuickAdd("person", "witness"),
      ...getAvailableEntitiesForQuickAdd("person", "suspect"),
      ...getAvailableEntitiesForQuickAdd("person", "involved_party"),
    ];

    // Remove duplicates and exclude people already in the "Other People" section
    const uniquePeople = allOtherPeople.filter(
      (entity, index, array) =>
        array.findIndex((e) => e.id === entity.id) === index
    );

    // Exclude people already in the otherPeople list
    return uniquePeople.filter(
      (entity) => !otherPeople.some((existing) => existing.id === entity.id)
    );
  };

  // Helper function to get available organizations for victim quick add
  // This should exclude both victim organizations AND general organizations
  const getAvailableVictimOrganizationsForQuickAdd = (): OrganizationData[] => {
    if (!organizations) return [];

    const victimOrganizations = getVictimOrganizations();
    const generalOrganizations = getRelatedOrganizations();
    const allRelatedOrganizations = [
      ...victimOrganizations,
      ...generalOrganizations,
    ];

    // Return case organizations that are not already related to this offense as either victims or general
    return caseEntities.organizations.filter(
      (organization) =>
        !allRelatedOrganizations.some(
          (related) => related.id === organization.id
        )
    );
  };

  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        flexDirection: "column",
        alignItems: "flex-start",
        borderRadius: "12px",
        border: `1px solid ${colors.grey[200]}`,
        background: "#FFF",
        overflow: "hidden",
        boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
        mb: 3,
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: `1px solid ${colors.grey[200]}`,
          backgroundColor: colors.grey[50],
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography style="h3" color={colors.grey[900]}>
            {offense.citation} {toTitleCase(offense.literal)}
          </Typography>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {!readOnly && (
            <IconButton
              onClick={() => onDelete(offenseId)}
              size="small"
              sx={{
                color: colors.grey[600],
                "&:hover": {
                  bgcolor: colors.grey[100],
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          )}
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            size="small"
            sx={{
              color: colors.grey[600],
              "&:hover": {
                bgcolor: colors.grey[100],
              },
            }}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Form Content */}
      <Collapse in={isExpanded} sx={{ width: "100%" }}>
        <Box sx={{ padding: "24px" }}>
          {/* Crime & Motivation Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Crime & Motivation
              </Typography>
            </Box>

            {/* NIBRS Offense Code (read-only) */}
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <TextInput
                  title="NIBRS Offense Code"
                  value={`${offense.citation} - ${toTitleCase(
                    offense.literal
                  )}`}
                  readOnly={true}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Offense was"
                  placeholder="Select status"
                  options={OFFENSE_WAS_OPTIONS}
                  value={formData.offenseWas}
                  onChange={handleDropdownChange("offenseWas")}
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Cargo Theft Related */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Cargo Theft Related
                </Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 3 }}>
                <Radio
                  name={`cargoTheft_${offenseId}`}
                  value="true"
                  checked={formData.cargoTheftRelated === true}
                  onChange={handleRadioChange("cargoTheftRelated")}
                  label="Yes"
                  readOnly={readOnly}
                />
                <Radio
                  name={`cargoTheft_${offenseId}`}
                  value="false"
                  checked={formData.cargoTheftRelated === false}
                  onChange={handleRadioChange("cargoTheftRelated")}
                  label="No"
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Special Circumstances */}
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Aggravated Assault/Homicide Circumstances"
                  placeholder="Select circumstance"
                  options={AGGRAVATED_ASSAULT_HOMICIDE_CIRCUMSTANCES_OPTIONS}
                  value={formData.offenseSpecialCircumstance}
                  onChange={handleDropdownChange("offenseSpecialCircumstance")}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Additional Justifiable Homicide Circumstances"
                  placeholder="Select circumstance"
                  options={
                    ADDITIONAL_JUSTIFIABLE_HOMICIDE_CIRCUMSTANCES_OPTIONS
                  }
                  value={formData.justifiableHomicideCircumstance}
                  onChange={handleDropdownChange(
                    "justifiableHomicideCircumstance"
                  )}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
            </Box>

            {/* Bias Motivation */}
            <Box sx={{ display: "flex", gap: 2, mb: 3 }}>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Bias Motivation"
                  placeholder="Select bias motivation"
                  options={BIAS_MOTIVATION_OPTIONS}
                  value={formData.biasMotivation}
                  onChange={handleDropdownChange("biasMotivation")}
                  readOnly={readOnly}
                  enableSearch
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <Dropdown
                  title="Method Of Entry"
                  placeholder="Select Method"
                  options={METHOD_OF_ENTRY_OPTIONS}
                  value={formData.methodOfEntry}
                  onChange={handleDropdownChange("methodOfEntry")}
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Number of Premises Entered */}
            <Box sx={{ mb: 3 }}>
              <TextInput
                title="Number of Premises Entered"
                placeholder="Enter number"
                value={formData.numberOfPremisesEntered}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFormChange("numberOfPremisesEntered", e.target.value)
                }
                onBlur={handleBlur}
                readOnly={readOnly}
              />
            </Box>

            {/* Offender Suspected of Using */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Offender Suspected of Using
                </Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
                <Checkbox
                  label="Alcohol"
                  checked={formData.offenderSuspectedOfUsing.alcohol}
                  onChange={handleCheckboxChange(
                    "offenderSuspectedOfUsing.alcohol"
                  )}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Computer Equipment"
                  checked={formData.offenderSuspectedOfUsing.computerEquipment}
                  onChange={handleCheckboxChange(
                    "offenderSuspectedOfUsing.computerEquipment"
                  )}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Drugs/Narcotics"
                  checked={formData.offenderSuspectedOfUsing.drugsNarcotics}
                  onChange={handleCheckboxChange(
                    "offenderSuspectedOfUsing.drugsNarcotics"
                  )}
                  readOnly={readOnly}
                />
              </Box>
            </Box>

            {/* Attributes */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography style="body4" color={colors.grey[500]}>
                  Attributes
                </Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
                <Checkbox
                  label="Domestic Abuse"
                  checked={formData.attributes.domesticAbuse}
                  onChange={handleCheckboxChange("attributes.domesticAbuse")}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Gang Related"
                  checked={formData.attributes.gangRelated}
                  onChange={handleCheckboxChange("attributes.gangRelated")}
                  readOnly={readOnly}
                />
                <Checkbox
                  label="Gambling Related"
                  checked={formData.attributes.gamblingRelated}
                  onChange={handleCheckboxChange("attributes.gamblingRelated")}
                  readOnly={readOnly}
                />
              </Box>
            </Box>
          </Box>

          {/* Weapons / Force Involved Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 2 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Weapons / Force Involved
              </Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Add up to 3
              </Typography>
            </Box>

            {/* Show weapons if any exist */}
            {formData.weapons.length > 0 && (
              <>

                {formData.weapons.map((weapon) => (
                  <Box key={weapon.id} sx={{ mb: 3 }}>
                    {/* Weapon Type and Remove Button Row */}
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "flex-end",
                        gap: 2,
                        mb: 2,
                      }}
                    >
                      <Box sx={{ flex: 1 }}>
                        <Dropdown
                          title="Weapon Type"
                          placeholder="Select weapon type"
                          options={WEAPON_TYPE_OPTIONS}
                          value={weapon.weaponType}
                          onChange={handleWeaponDropdownChange(
                            weapon.id,
                            "weaponType"
                          )}
                          readOnly={readOnly}
                          enableSearch
                        />
                      </Box>
                      {!readOnly && (
                        <Box sx={{ flexShrink: 0 }}>
                          <Button
                            label=""
                            prominence={false}
                            color="grey"
                            size="large"
                            onClick={() => removeWeapon(weapon.id)}
                            rightIcon={<DeleteIcon />}
                          />
                        </Box>
                      )}
                    </Box>
                    
                    {/* Checkbox Row */}
                    <Box sx={{ width: "100%" }}>
                      <Checkbox
                        label="Firearm is automatic"
                        checked={weapon.isAutomatic}
                        onChange={(e) => {
                          handleWeaponChange(
                            weapon.id,
                            "isAutomatic",
                            e.target.checked
                          );
                        }}
                        readOnly={readOnly}
                      />
                    </Box>
                  </Box>
                ))}
              </>
            )}

            {/* Add Weapon Button */}
            {formData.weapons.length < 3 && !readOnly && (
              <Button
                label="Add Weapon"
                style="ghost"
                color="blue"
                size="small"
                leftIcon={<AddIcon />}
                onClick={addWeapon}
              />
            )}
          </Box>

          {/* Criminal Activity & Gang Information Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 3 }}>
              <Typography style="caps1" color={colors.grey[700]}>
                Type of Criminal Activity
              </Typography>
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography style="body4" color={colors.grey[500]}>
                Select up to 3
              </Typography>
            </Box>

            <TextInput
              type={InputType.Dropdown}
              isMultiSelect={true}
              options={CRIMINAL_ACTIVITY_AND_GANG_OPTIONS}
              selectedItems={formData.criminalActivityAndGang}
              onChangeSelectedItems={handleCriminalActivityAndGangChange}
              readOnly={readOnly}
              hideSelectedFromOptions={true}
            />
          </Box>

          <Divider sx={{ mx: -3 }} />

          {/* People Involved Section */}
          {people && relations && (
            <Box>
              {/* Victims Section */}
              <PersonList
                people={victims}
                allPeople={people}
                title="Victims"
                relationType="victim"
                offenseId={offenseId}
                readOnly={readOnly}
                relations={relations}
                reportId={reportId}
                availablePeopleForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "person",
                  "victim"
                )}
                organizations={getVictimOrganizations()}
                availableOrganizationsForQuickAdd={getAvailableVictimOrganizationsForQuickAdd()}
                onAddPerson={handleAddPerson}
                onAddOrganization={handleAddVictimOrganization}
                onRemovePersonFromOffense={onRemovePersonFromOffense}
                onRemovePersonFromReport={onRemovePersonFromReport}
                onRemoveOrganizationFromOffense={
                  onRemoveOrganizationFromOffense
                }
                onRemoveVictimOrganizationFromOffense={
                  onRemoveVictimOrganizationFromOffense
                }
                onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
                onQuickAddPerson={handleQuickAddPerson}
                onQuickAddOrganization={handleQuickAddVictimOrganization}
                onEntityEdit={onEntityEdit}
                onEntityEditOrganization={onEntityEdit}
                onAddVictimDetails={onAddVictimDetails}
                onAddVictimDetailsOrganization={onAddVictimDetailsOrganization}
              />

              <Divider sx={{ mx: -3 }} />

              {/* Offenders Section */}
              <PersonList
                people={offenders}
                allPeople={people}
                title="Offenders"
                relationType="offender"
                offenseId={offenseId}
                readOnly={readOnly}
                relations={relations}
                availablePeopleForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "person",
                  "offender"
                )}
                onAddPerson={handleAddPerson}
                onRemovePersonFromOffense={onRemovePersonFromOffense}
                onRemovePersonFromReport={onRemovePersonFromReport}
                onQuickAddPerson={handleQuickAddPerson}
                onEntityEdit={onEntityEdit}
              />

              <Divider sx={{ mx: -3 }} />

              {/* Other People Section */}
              <PersonList
                people={otherPeople}
                allPeople={people}
                title="Other People"
                relationType="other"
                offenseId={offenseId}
                readOnly={readOnly}
                relations={relations}
                availablePeopleForQuickAdd={getAvailablePeopleForOtherSection()}
                onAddPerson={handleAddPerson}
                onRemovePersonFromOffense={onRemovePersonFromOffense}
                onRemovePersonFromReport={onRemovePersonFromReport}
                onQuickAddPerson={handleQuickAddPerson}
                onEntityEdit={onEntityEdit}
              />
            </Box>
          )}

          {/* Vehicles Section */}
          {vehicles && relations && (
            <>
              <Divider sx={{ mx: -3 }} />
              <VehicleList
                vehicles={relatedVehicles}
                title="Vehicles"
                offenseId={offenseId}
                readOnly={readOnly}
                availableVehiclesForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "vehicle"
                )}
                onAddVehicle={handleAddVehicle}
                onRemoveVehicleFromOffense={onRemoveVehicleFromOffense}
                onRemoveVehicleFromReport={onRemoveVehicleFromReport}
                onQuickAddVehicle={handleQuickAddVehicle}
                onEntityEdit={onEntityEdit}
              />
            </>
          )}

          {/* Properties Section */}
          {properties && relations && (
            <>
              <Divider sx={{ mx: -3 }} />
              <PropertyList
                properties={relatedProperties}
                title="Properties"
                offenseId={offenseId}
                readOnly={readOnly}
                availablePropertiesForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "property"
                )}
                onAddProperty={handleAddProperty}
                onRemovePropertyFromOffense={onRemovePropertyFromOffense}
                onRemovePropertyFromReport={onRemovePropertyFromReport}
                onQuickAddProperty={handleQuickAddProperty}
                onEntityEdit={onEntityEdit}
              />
            </>
          )}

          {/* Organizations Section */}
          {organizations && relations && (
            <>
              <Divider sx={{ mx: -3 }} />
              <OrganizationList
                organizations={relatedOrganizations}
                title="Organizations"
                offenseId={offenseId}
                readOnly={readOnly}
                availableOrganizationsForQuickAdd={getAvailableEntitiesForQuickAdd(
                  "organization"
                )}
                onAddOrganization={handleAddOrganization}
                onRemoveOrganizationFromOffense={
                  onRemoveOrganizationFromOffense
                }
                onRemoveOrganizationFromReport={onRemoveOrganizationFromReport}
                onQuickAddOrganization={handleQuickAddOrganization}
                onEntityEdit={onEntityEdit}
              />
            </>
          )}
        </Box>
      </Collapse>
    </Box>
  );
};

export default OffenseFormCard;
