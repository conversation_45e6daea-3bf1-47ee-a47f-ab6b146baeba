import { Button } from "@/design-system/components/Button";
import { Checkbox } from "@/design-system/components/Checkbox";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import { Box, Collapse, Dialog, DialogActions, DialogContent, IconButton, Menu, MenuItem } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
import React, { useEffect, useState } from "react";
import { getPropertyStatusDisplay, getReadablePropertyCategory } from "../../../utils/propertyHelpers";

export interface PropertyData {
  id: string;
  serialNumber: string;
  category: string;
  propertyType: string;
  collectedValue: string;
  makeModelBrand: string;
  description: string;
  status?: string;
  owner?: string;
}

interface PropertyListProps {
  properties: PropertyData[];
  title: string;
  offenseId: string;
  readOnly?: boolean;
  availablePropertiesForQuickAdd: PropertyData[];
  onAddProperty: () => void;
  onRemovePropertyFromOffense?: (propertyId: string, offenseId: string) => void;
  onRemovePropertyFromReport?: (propertyId: string, entityType: "property") => void;
  onQuickAddProperty: (propertyId: string) => void;
  onEntityEdit?: (entityId: string, entityType: "property") => void;
}

const PropertyList: React.FC<PropertyListProps> = ({
  properties,
  title,
  offenseId,
  readOnly = false,
  availablePropertiesForQuickAdd,
  onAddProperty,
  onRemovePropertyFromOffense,
  onRemovePropertyFromReport,
  onQuickAddProperty,
  onEntityEdit,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPropertyForMenu, setSelectedPropertyForMenu] = useState<PropertyData | null>(null);
  const [removeModalOpen, setRemoveModalOpen] = useState(false);
  const [removeFromReport, setRemoveFromReport] = useState(false);
  const [quickAddExpanded, setQuickAddExpanded] = useState(true);
  const [loadingIds, setLoadingIds] = useState<Set<string>>(new Set());

  // Using shared getPropertyStatusDisplay function from propertyHelpers

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, property: PropertyData) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
    setSelectedPropertyForMenu(property);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedPropertyForMenu(null);
  };

  const handleOpenRecord = () => {
    if (selectedPropertyForMenu) {
      window.open(`/property?propertyId=${selectedPropertyForMenu.id}`, '_blank');
    }
    setMenuAnchorEl(null);
    setSelectedPropertyForMenu(null);
  };

  const handleRemoveClick = () => {
    setRemoveModalOpen(true);
    setMenuAnchorEl(null);
  };

  const handleRemoveCancel = () => {
    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedPropertyForMenu(null);
  };

  const handleRemoveConfirm = () => {
    if (selectedPropertyForMenu) {
      if (onRemovePropertyFromOffense) {
        onRemovePropertyFromOffense(selectedPropertyForMenu.id, offenseId);
      }

      if (removeFromReport && onRemovePropertyFromReport) {
        onRemovePropertyFromReport(selectedPropertyForMenu.id, "property");
      }
    }

    setRemoveModalOpen(false);
    setRemoveFromReport(false);
    setSelectedPropertyForMenu(null);
  };

  const handleQuickAddClick = (propertyId: string) => {
    setLoadingIds(prev => new Set(prev).add(propertyId));
    onQuickAddProperty(propertyId);
  };

  useEffect(() => {
    setLoadingIds(prev => {
      const newSet = new Set([...prev].filter(id => availablePropertiesForQuickAdd.some(p => p.id === id)));
      return newSet;
    });
  }, [availablePropertiesForQuickAdd]);

  return (
    <>
      <Box sx={{ my: 4 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
          <Typography style="caps1" color={colors.grey[900]}>
            {title}
          </Typography>
        </Box>

        {properties.length > 0 && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            {properties.map((property) => (
              <Box
                key={property.id}
                sx={{
                  padding: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  borderRadius: '8px',
                  backgroundColor: colors.grey[50],
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  cursor: readOnly ? 'default' : 'pointer',
                  '&:hover': {
                    backgroundColor: colors.grey[100]
                  },
                  '&:active': {
                    backgroundColor: colors.grey[200]
                  }
                }}
                onClick={() => {
                  if (readOnly) return;
                  onEntityEdit && onEntityEdit(property.id, "property");
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ mb: 1 }}>
                    <Typography style="body3" color={colors.grey[900]}>
                      {property.propertyType || getReadablePropertyCategory(property.category) || "Unknown Property"}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                    <Typography style="tag2" color={colors.grey[500]}>
                      Description: {property.description || "---"}{property.status && `, Status: ${property.status}`}{property.owner && `, Owner: ${property.owner}`}
                    </Typography>
                  </Box>
                </Box>
                {!readOnly && (
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, property)}
                    sx={{
                      color: colors.grey[600],
                      "&:hover": {
                        bgcolor: colors.grey[200],
                        color: colors.grey[800]
                      },
                    }}
                  >
                    <MoreVertIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            ))}
          </Box>
        )}

        {/* Add button below the cards */}
        {!readOnly && (
          <Box sx={{ mt: properties.length > 0 ? 3 : 0 }}>
            <Button
              label={`Add ${title}`}
              leftIcon={<AddIcon />}
              style="ghost"
              color="blue"
              size="small"
              onClick={onAddProperty}
            />
          </Box>
        )}

        {/* Quick Add Section */}
        {!readOnly && availablePropertiesForQuickAdd.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                cursor: "pointer",
                py: 1
              }}
              onClick={() => setQuickAddExpanded(!quickAddExpanded)}
            >
              <Typography style="caps2" color={colors.grey[500]}>
                QUICK ADD
              </Typography>
              <IconButton size="small">
                {quickAddExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>

            <Collapse in={quickAddExpanded}>
              <Box sx={{
                display: "grid",
                gridTemplateColumns: "repeat(3, 1fr)",
                gap: 1.5,
                pt: 1
              }}>
                {availablePropertiesForQuickAdd.map((property) => (
                  <Box
                    key={property.id}
                    onClick={() => handleQuickAddClick(property.id)}
                    sx={{
                      px: 1.5,
                      py: 0,
                      border: `1px solid ${colors.grey[200]}`,
                      borderRadius: '8px',
                      backgroundColor: "white",
                      display: "flex",
                      alignItems: "center",
                      cursor: "pointer",
                      height: "68px",
                      boxShadow: "0px 0px 16px 0px rgba(0, 0, 0, 0.04)",
                      '&:hover': {
                        backgroundColor: colors.grey[50]
                      }
                    }}
                  >
                    <Box sx={{ mr: 2, display: "flex", alignItems: "center" }}>
                      {loadingIds.has(property.id) ? (
                        <CircularProgress size={20} sx={{ color: colors.grey[900] }} />
                      ) : (
                        <AddIcon sx={{ color: colors.grey[900], fontSize: 20 }} />
                      )}
                    </Box>
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box sx={{ mb: 0.5 }}>
                        <Typography style="body3" color={colors.grey[900]}>
                          {property.propertyType || getReadablePropertyCategory(property.category) || "Unknown Property"}
                        </Typography>
                      </Box>
                      <Box sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                      }}>
                        <Typography style="tag2" color={colors.grey[500]}>
                          Description: {property.description || "---"}{property.status && `, Status: ${getPropertyStatusDisplay(property.status)}`}{property.owner && `, Owner: ${property.owner}`}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Collapse>
          </Box>
        )}
      </Box>

      {/* Menu for property actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          mt: 0.25,
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "200px",
          },
          "& .MuiMenuItem-root": {
            py: 1.5,
            display: "flex",
            alignItems: "center",
            gap: 1.5,
          },
        }}
      >
        <MenuItem onClick={handleOpenRecord}>
          <OpenInNewIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Open record in new tab
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleRemoveClick}>
          <DeleteIcon fontSize="small" sx={{ color: colors.grey[500] }} />
          <Typography style="body2" color={colors.grey[500]}>
            Remove from offense
          </Typography>
        </MenuItem>
      </Menu>

      {/* Remove confirmation modal */}
      <Dialog
        open={removeModalOpen}
        onClose={handleRemoveCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '12px',
            p: 1
          }
        }}
      >
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography style="h2" color={colors.grey[900]}>
              Delete {selectedPropertyForMenu?.propertyType || getReadablePropertyCategory(selectedPropertyForMenu?.category || "") || "Unknown Property"}
            </Typography>
          </Box>
          <Box sx={{ mb: 3 }}>
            <Typography style="body3" color={colors.grey[700]}>
              {selectedPropertyForMenu?.propertyType || getReadablePropertyCategory(selectedPropertyForMenu?.category || "") || "Unknown Property"} will be deleted from the offense
            </Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Checkbox
              checked={removeFromReport}
              onChange={(e) => setRemoveFromReport(e.target.checked)}
              label="Also delete from the report"
              size="small"
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            label="Cancel"
            color="grey"
            prominence={false}
            onClick={handleRemoveCancel}
          />
          <Button
            label="Delete"
            color="blue"
            prominence={true}
            onClick={handleRemoveConfirm}
          />
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PropertyList; 