import { Button } from "@/design-system/components/Button";
import { InputMask, TextInput } from "@/design-system/components/TextInput";
import { Typography } from "@/design-system/components/Typography";
import { FormRenderer } from "@/design-system/form/FormRenderer";
import { colors } from "@/design-system/tokens";
import { create } from "@bufbuild/protobuf";
import AddIcon from "@mui/icons-material/Add";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CloseIcon from "@mui/icons-material/Close";
import { Box, CircularProgress, Dialog, DialogContent } from "@mui/material";
import {
  EntityType,
  Reference,
  ReferenceSchema,
  SearchOrderBy,
} from "proto/hero/entity/v1/entity_pb";
import React, { useEffect, useState } from "react";
import {
  useGetEntitySchemaByVersion,
  useListLatestEntitySchemas,
  useSearchEntities,
} from "../../../apis/services/workflow/entity/hooks";
import { PanelType } from "../core/types";

interface EntitySearchFormProps {
  panelType: PanelType;
  onEntitySelect?: (
    entityRef: Reference,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => void;
  onStartNewRecord: (searchData?: any) => void;
  handleCloseSidePanel: (isOpen: boolean) => void;
  currentEntities?: any[]; // Array of entities already in the report
  // Offense context props for contextual titles
  offenseContext?: {
    relationType?:
    | "victim"
    | "offender"
    | "witness"
    | "suspect"
    | "involved_party";
    offenseId?: string;
    isIncident?: boolean;
  };
}

export const EntitySearchForm: React.FC<EntitySearchFormProps> = ({
  panelType,
  onEntitySelect,
  onStartNewRecord,
  handleCloseSidePanel,
  currentEntities = [],
  offenseContext,
}) => {
  // Search state
  const [searchFormData, setSearchFormData] = useState<any>({});
  const [hasSearched, setHasSearched] = useState(false);
  const [searchResults, setSearchResults] = useState<any>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [searchTrigger, setSearchTrigger] = useState<any>(null);
  const [searchCounter, setSearchCounter] = useState(0);

  // Modal state for entity preview
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState<any>(null);

  // Map panel type to entity type
  const getEntityTypeFromPanel = (panelType: PanelType): EntityType | null => {
    switch (panelType) {
      case PanelType.PERSON:
        return EntityType.PERSON;
      case PanelType.VEHICLE:
        return EntityType.VEHICLE;
      case PanelType.PROPERTY:
        return EntityType.PROPERTY;
      case PanelType.ORGANIZATION:
        return EntityType.ORGANIZATION;
      default:
        return null;
    }
  };

  // Fetch schemas for modal
  const { data: personSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.PERSON,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: propertySchemas } = useListLatestEntitySchemas({
    entityType: EntityType.PROPERTY,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: vehicleSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.VEHICLE,
    pageSize: 1,
    pageToken: "",
  } as any);

  const { data: organizationSchemas } = useListLatestEntitySchemas({
    entityType: EntityType.ORGANIZATION,
    pageSize: 1,
    pageToken: "",
  } as any);

  // Fetch specific schema for selected entity
  const { data: entitySchema } = useGetEntitySchemaByVersion(
    selectedEntity?.schemaId || "",
    selectedEntity?.schemaVersion || 0,
    {
      enabled: !!selectedEntity?.schemaId && !!selectedEntity?.schemaVersion,
      queryKey: [
        "entitySchema",
        selectedEntity?.schemaId,
        selectedEntity?.schemaVersion,
      ],
    }
  );

  // Create search parameters from form data
  const createSearchParams = () => {
    const entityType = getEntityTypeFromPanel(panelType);
    if (!entityType) return null;

    // Build field queries based on form data
    const fieldQueries: any[] = [];

    Object.entries(searchFormData).forEach(([key, value]) => {
      if (value && typeof value === "string" && value.trim() !== "") {
        fieldQueries.push({
          field: "data",
          query: value.trim(),
        });
      }
    });

    if (fieldQueries.length === 0) return null;

    return {
      query: "",
      pageSize: 20,
      orderBy: SearchOrderBy.CREATED_AT,
      ascending: false,
      entityTypes: [entityType],
      fieldQueries,
    };
  };

  const handleSearch = () => {
    const searchParams = createSearchParams();
    if (searchParams) {
      setHasSearched(true);
      setIsSearching(true);
      setSearchError(null);
      setSearchCounter((prev) => prev + 1);
      setSearchTrigger({ ...searchParams, _searchId: Date.now() });
    } else {
      // No search criteria provided
      setSearchError("Please enter at least one search criteria.");
    }
  };

  // Add handler for enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Use search hook - only triggered when searchTrigger changes
  const {
    data: searchData,
    isLoading: isLoadingSearch,
    isError: isSearchError,
  } = useSearchEntities(
    searchTrigger || {
      query: "",
      pageSize: 20,
      orderBy: SearchOrderBy.CREATED_AT,
      ascending: false,
      entityTypes: [],
    },
    {
      enabled: !!searchTrigger,
    } as any
  );

  // Handle search results from hook
  useEffect(() => {
    if (searchData) {
      setSearchResults(searchData);
      setIsSearching(false);
      setSearchError(null);
    }
  }, [searchData]);

  // Handle search errors from hook
  useEffect(() => {
    if (isSearchError) {
      setSearchError("Error searching entities. Please try again.");
      setIsSearching(false);
    }
  }, [isSearchError]);

  // Handle search loading state
  useEffect(() => {
    setIsSearching(isLoadingSearch);
  }, [isLoadingSearch]);

  // Check if an entity is already in the report
  const isEntityAlreadyInReport = (entity: any): boolean => {
    return currentEntities.some(
      (existingEntity) => existingEntity.id === entity.id
    );
  };

  const handleEntityClick = (entity: any) => {
    setSelectedEntity(entity);
    setPreviewModalOpen(true);
  };

  const handleModalCancel = () => {
    setPreviewModalOpen(false);
    setSelectedEntity(null);
  };

  const handleModalAdd = () => {
    if (selectedEntity && onEntitySelect) {
      let entityType: "person" | "property" | "vehicle" | "organization";
      let type: string;
      switch (panelType) {
        case PanelType.PERSON:
          entityType = "person";
          type = "ENTITY_TYPE_PERSON";
          break;
        case PanelType.VEHICLE:
          entityType = "vehicle";
          type = "ENTITY_TYPE_VEHICLE";
          break;
        case PanelType.PROPERTY:
          entityType = "property";
          type = "ENTITY_TYPE_PROPERTY";
          break;
        case PanelType.ORGANIZATION:
          entityType = "organization";
          type = "ENTITY_TYPE_ORGANIZATION";
          break;
        default:
          return;
      }

      // Create a proper Reference object
      const reference = create(ReferenceSchema, {
        id: selectedEntity.id,
        type,
        version: selectedEntity.version || 0,
        displayName:
          selectedEntity.data?.name ||
          selectedEntity.data?.make ||
          selectedEntity.data?.category ||
          "",
        relationType: "",
      });

      onEntitySelect(reference, entityType);
      setPreviewModalOpen(false);
      setSelectedEntity(null);
      handleCloseSidePanel(true); // Force close the panel after selection
    }
  };

  // Get the appropriate schema for the modal
  const getModalSchema = () => {
    if (!selectedEntity) return null;

    // If we have the specific schema for this entity, use it
    if (entitySchema?.schemaDefinition) {
      return JSON.parse(JSON.stringify(entitySchema.schemaDefinition));
    }

    // Otherwise fall back to the latest schema for this entity type
    switch (panelType) {
      case PanelType.PERSON:
        return personSchemas?.schemas?.[0]?.schemaDefinition
          ? JSON.parse(
            JSON.stringify(personSchemas.schemas[0].schemaDefinition)
          )
          : null;
      case PanelType.VEHICLE:
        return vehicleSchemas?.schemas?.[0]?.schemaDefinition
          ? JSON.parse(
            JSON.stringify(vehicleSchemas.schemas[0].schemaDefinition)
          )
          : null;
      case PanelType.PROPERTY:
        return propertySchemas?.schemas?.[0]?.schemaDefinition
          ? JSON.parse(
            JSON.stringify(propertySchemas.schemas[0].schemaDefinition)
          )
          : null;
      case PanelType.ORGANIZATION:
        return organizationSchemas?.schemas?.[0]?.schemaDefinition
          ? JSON.parse(
            JSON.stringify(organizationSchemas.schemas[0].schemaDefinition)
          )
          : null;
      default:
        return null;
    }
  };

  // Process entity data for modal
  const getModalInitialValues = () => {
    if (!selectedEntity?.data) return {};

    try {
      return typeof selectedEntity.data === "string"
        ? JSON.parse(selectedEntity.data)
        : selectedEntity.data;
    } catch (error) {
      console.error("Error parsing entity data for modal:", error);
      return {};
    }
  };

  // Get entity display data for cards using same patterns as result tables
  const getEntityDisplayData = (entity: any) => {
    if (!entity.data) return {};

    try {
      const data =
        typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;

      switch (panelType) {
        case PanelType.PERSON: {
          // Use same pattern as PeopleResultsTable.tsx
          const classificationSection = data?.classificationSection;
          const identifiersSection = data?.identifiersSection;
          const contactSection = data?.contactInformationSection;

          if (classificationSection) {
            const firstName = classificationSection.firstName || "";
            const lastName = classificationSection.lastName || "";
            const middleName = classificationSection.middleName || "";
            const name =
              `${firstName} ${middleName} ${lastName}`
                .trim()
                .replace(/\s+/g, " ") || "---";

            return {
              name,
              dob: classificationSection.dateOfBirth || "---",
              studentId:
                identifiersSection?.studentID ||
                identifiersSection?.studentId ||
                "---",
              phone: contactSection?.phoneNumber || "---",
              affiliation: classificationSection.affiliation || "---",
              updateTime: entity.updateTime || "---",
            };
          }
          return {
            name: "---",
            dob: "---",
            studentId: "---",
            phone: "---",
            affiliation: "---",
            updateTime: entity.updateTime || "---",
          };
        }

        case PanelType.VEHICLE: {
          // Use same pattern as VehiclesResultsTable.tsx
          const vehicleSection = data?.vehicleInformationSection;
          if (vehicleSection) {
            return {
              name: `${vehicleSection.make || "---"} ${vehicleSection.model || "---"
                }`.trim(),
              licensePlate:
                vehicleSection.licensePlate ||
                vehicleSection.plateNumber ||
                "---",
              make: vehicleSection.make || "---",
              model: vehicleSection.model || "---",
              year: vehicleSection.year || "---",
              color: vehicleSection.color || "---",
              vin: vehicleSection.vIN || "---",
              updateTime: entity.updateTime || "---",
            };
          }
          return {
            name: "---",
            licensePlate: "---",
            make: "---",
            model: "---",
            year: "---",
            color: "---",
            vin: "---",
            updateTime: entity.updateTime || "---",
          };
        }

        case PanelType.ORGANIZATION: {
          // Use same pattern as OrganizationsResultsTable.tsx
          const organizationSection = data?.informationSection;
          if (organizationSection) {
            return {
              name: `${organizationSection.name || "---"} ${organizationSection.type || "---"
                }`.trim(),
              type: organizationSection.type || "---",
              status: organizationSection.status || "---",
              phone: organizationSection.phone || "---",
              email: organizationSection.email || "---",
              city: organizationSection.address?.[0]?.city || "---",
              state: organizationSection.address?.[0]?.state || "---",
              zipCode: organizationSection.address?.[0]?.zipCode || "---",
              updateTime: entity.updateTime || "---",
            };
          }
          return {
            name: "---",
            type: "---",
            status: "---",
            phone: "---",
            email: "---",
            city: "---",
            state: "---",
            zipCode: "---",
            updateTime: entity.updateTime || "---",
          };
        }

        case PanelType.PROPERTY: {
          // Use same pattern as PropertyResultsTable.tsx
          const propertySection = data?.propertyInformationSection;
          if (propertySection) {
            return {
              name:
                propertySection.propertyType ||
                propertySection.category ||
                "Property",
              propertyNumber:
                propertySection.propertyNumber ||
                propertySection.serialNumber ||
                "---",
              category: propertySection.category || "---",
              description: propertySection.description || "---",
              value: propertySection.collectedValue || "---",
              createTime: entity.createTime || "---",
              storageLocation: propertySection.storageLocation || "---",
              status: entity.status || "---",
            };
          }
          return {
            name: "Property",
            propertyNumber: "---",
            category: "---",
            description: "---",
            value: "---",
            createTime: entity.createTime || "---",
            storageLocation: "---",
            status: entity.status || "---",
          };
        }
        default:
          return {};
      }
    } catch (error) {
      console.error("Error parsing entity data:", error);
      return {};
    }
  };

  // Get panel title for current panel type
  const getPanelTitle = () => {
    if (offenseContext?.relationType && panelType === PanelType.PERSON) {
      // For incidents, always show "Add Person" regardless of relation type
      if (offenseContext.isIncident) {
        return "Add Person";
      }

      switch (offenseContext.relationType) {
        case "victim":
          return "Add Victim";
        case "offender":
          return "Add Offender";
        case "witness":
          return "Add Witness";
        case "suspect":
          return "Add Suspect";
        case "involved_party":
          return "Add Involved Party";
        default:
          return "Add Person";
      }
    }

    switch (panelType) {
      case PanelType.PERSON:
        return "Add Person";
      case PanelType.PROPERTY:
        return "Add Property";
      case PanelType.VEHICLE:
        return "Add Vehicle";
      case PanelType.ORGANIZATION:
        return "Add Organization";
      default:
        return "Add Entity";
    }
  };

  // Get entity type label for modal title
  const getEntityTypeLabel = () => {
    switch (panelType) {
      case PanelType.PERSON:
        return "Person";
      case PanelType.VEHICLE:
        return "Vehicle";
      case PanelType.PROPERTY:
        return "Property";
      case PanelType.ORGANIZATION:
        return "Organization";
      default:
        return "Entity";
    }
  };

  return (
    <>
      <Box sx={{ display: "flex", flexDirection: "column", pb: 12 }}>
        <Box sx={{ mb: 3 }}>
          <Typography style="h1" color={colors.grey[900]}>
            {getPanelTitle()}
          </Typography>
          <Box sx={{ mt: 1 }}>
            <Typography style="body3" color={colors.grey[600]}>
              {offenseContext
                ? "Search existing records to add to offense or start a new one"
                : "Search existing records or start a new one"}
            </Typography>
          </Box>
        </Box>

        {/* Search Fields */}
        <Box sx={{ mb: 4 }}>
          {/* Render specific search fields based on panel type */}
          {panelType === PanelType.PERSON && (
            <>
              {/* Name fields in a row */}
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr 1fr",
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextInput
                  title="First Name"
                  placeholder="First Name"
                  value={searchFormData.firstName || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      firstName: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Middle Name"
                  placeholder="Middle Name"
                  value={searchFormData.middleName || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      middleName: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Last Name"
                  placeholder="Last Name"
                  value={searchFormData.lastName || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      lastName: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>

              {/* Nicknames/Alias */}
              <Box sx={{ mb: 2 }}>
                <TextInput
                  title="Nicknames/Alias"
                  placeholder=""
                  value={searchFormData.nicknames || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      nicknames: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>

              {/* Student ID */}
              <Box sx={{ mb: 2 }}>
                <TextInput
                  title="Student ID"
                  placeholder="Student ID"
                  value={searchFormData.studentId || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      studentId: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>

              {/* DOB and State ID in a row */}
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextInput
                  title="DOB"
                  placeholder="MM/DD/YYYY"
                  mask={InputMask.Date}
                  value={searchFormData.dob || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      dob: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="State ID or DL"
                  placeholder="Enter Number"
                  value={searchFormData.stateId || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      stateId: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>

              {/* Phone and Email in a row */}
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 3,
                }}
              >
                <TextInput
                  title="Phone Number"
                  placeholder="************"
                  mask={InputMask.Phone}
                  value={searchFormData.phone || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      phone: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Email"
                  placeholder=""
                  value={searchFormData.email || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      email: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
            </>
          )}
          {panelType === PanelType.VEHICLE && (
            <>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextInput
                  title="VIN"
                  placeholder="Enter VIN"
                  value={searchFormData.vin || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      vin: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Year"
                  placeholder="Enter year"
                  value={searchFormData.year || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      year: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextInput
                  title="Make"
                  placeholder="Enter make"
                  value={searchFormData.make || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      make: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Model"
                  placeholder="Enter model"
                  value={searchFormData.model || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      model: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 3,
                }}
              >
                <TextInput
                  title="Color"
                  placeholder="Enter color"
                  value={searchFormData.color || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      color: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Owner"
                  placeholder="Enter owner"
                  value={searchFormData.owner || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      owner: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
            </>
          )}
          {panelType === PanelType.PROPERTY && (
            <>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextInput
                  title="Serial Number"
                  placeholder="Enter serial number"
                  value={searchFormData.serialNumber || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      serialNumber: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Category"
                  placeholder="Enter category"
                  value={searchFormData.category || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      category: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 3,
                }}
              >
                <TextInput
                  title="Value"
                  placeholder="Enter value"
                  value={searchFormData.value || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      value: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Make/Model/Brand"
                  placeholder="Enter make/model/brand"
                  value={searchFormData.makeModelBrand || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      makeModelBrand: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
            </>
          )}
          {panelType === PanelType.ORGANIZATION && (
            <>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 2,
                }}
              >
                <TextInput
                  title="Name"
                  placeholder="Enter organization name"
                  value={searchFormData.name || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      name: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="Type"
                  placeholder="Enter type"
                  value={searchFormData.type || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      type: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 2,
                  mb: 3,
                }}
              >
                <TextInput
                  title="Street Address"
                  placeholder="Enter street address"
                  value={searchFormData.streetAddress || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      streetAddress: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
                <TextInput
                  title="ZIP"
                  placeholder="Enter zip codde"
                  value={searchFormData.zip || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchFormData({
                      ...searchFormData,
                      zip: e.target.value,
                    })
                  }
                  onKeyDown={handleKeyDown}
                />
              </Box>
            </>
          )}

          {/* Search Button - always visible */}
          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              label="Search"
              color="blue"
              prominence={true}
              onClick={handleSearch}
            />
          </Box>
        </Box>

        {/* Search Results */}
        {hasSearched && (
          <Box sx={{ mb: 4 }}>
            <Box sx={{ mb: 2 }}>
              <Typography style="h2" color={colors.grey[900]}>
                Search Results
              </Typography>
            </Box>

            {isSearching && (
              <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                <CircularProgress />
              </Box>
            )}

            {searchError && (
              <Box
                sx={{ p: 2, bgcolor: colors.rose[50], borderRadius: 1, mb: 2 }}
              >
                <Typography style="body3" color={colors.rose[700]}>
                  {searchError}
                </Typography>
              </Box>
            )}

            {!isSearching && searchResults?.entities && (
              <>
                <Box sx={{ mb: 2 }}>
                  <Typography style="body3" color={colors.grey[600]}>
                    Found {searchResults.entities.length} results
                  </Typography>
                </Box>

                {searchResults.entities.length > 0 ? (
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 2 }}
                  >
                    {searchResults.entities.map((entity: any) => {
                      const displayData = getEntityDisplayData(entity);

                      return (
                        <Box
                          key={entity.id}
                          sx={{
                            padding: 2,
                            border: `1px solid ${colors.grey[200]}`,
                            borderRadius: "8px",
                            cursor: "pointer",
                            backgroundColor: isEntityAlreadyInReport(entity)
                              ? colors.blue[50]
                              : colors.grey[50],
                            "&:hover": {
                              backgroundColor: isEntityAlreadyInReport(entity)
                                ? colors.blue[100]
                                : colors.grey[100],
                            },
                            "&:active": {
                              backgroundColor: isEntityAlreadyInReport(entity)
                                ? colors.blue[200]
                                : colors.grey[200],
                            },
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            position: "relative",
                          }}
                          onClick={() => handleEntityClick(entity)}
                        >
                          <Box sx={{ flex: 1 }}>
                            <Box
                              sx={{
                                mb: 1,
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Typography
                                style="body3"
                                color={colors.grey[900]}
                              >
                                {displayData.name}
                              </Typography>
                              {isEntityAlreadyInReport(entity) && (
                                <Box
                                  sx={{
                                    backgroundColor: colors.blue[100],
                                    color: colors.blue[700],
                                    px: 1,
                                    py: 0.5,
                                    borderRadius: 1,
                                    fontSize: "10px",
                                    fontWeight: 500,
                                  }}
                                >
                                  Already in report
                                </Box>
                              )}
                            </Box>

                            {panelType === PanelType.PERSON && (
                              <Box
                                sx={{
                                  display: "flex",
                                  gap: 1,
                                  flexWrap: "wrap",
                                }}
                              >
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  DOB:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.dob}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Student ID:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.studentId}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Phone:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.phone}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Affiliation:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.affiliation}
                                </Typography>
                              </Box>
                            )}

                            {panelType === PanelType.VEHICLE && (
                              <Box
                                sx={{
                                  display: "flex",
                                  gap: 1,
                                  flexWrap: "wrap",
                                }}
                              >
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  License:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.licensePlate}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Year:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.year}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Color:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.color}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  VIN:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.vin}
                                </Typography>
                              </Box>
                            )}

                            {panelType === PanelType.PROPERTY && (
                              <Box
                                sx={{
                                  display: "flex",
                                  gap: 1,
                                  flexWrap: "wrap",
                                }}
                              >
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  #
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.propertyNumber}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Category:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.category}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Value:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.value}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Storage:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.storageLocation}
                                </Typography>
                              </Box>
                            )}

                            {panelType === PanelType.ORGANIZATION && (
                              <Box
                                sx={{
                                  display: "flex",
                                  gap: 1,
                                  flexWrap: "wrap",
                                }}
                              >
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Name
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.name}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Type:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.type}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  Address:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.city || "---"}
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  ,
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[500]}
                                >
                                  State:
                                </Typography>
                                <Typography
                                  style="tag2"
                                  color={colors.grey[900]}
                                >
                                  {displayData.state}
                                </Typography>
                              </Box>
                            )}
                          </Box>

                          <ChevronRightIcon
                            sx={{ color: colors.grey[500], ml: 2 }}
                          />
                        </Box>
                      );
                    })}
                  </Box>
                ) : (
                  <Box
                    sx={{
                      p: 3,
                      textAlign: "center",
                      bgcolor: colors.grey[50],
                      borderRadius: 1,
                    }}
                  >
                    <Typography style="body3" color={colors.grey[600]}>
                      No existing records found matching your search terms
                    </Typography>
                  </Box>
                )}
              </>
            )}

            {!isSearching && searchResults && !searchResults.entities && (
              <Box
                sx={{
                  p: 3,
                  textAlign: "center",
                  bgcolor: colors.grey[50],
                  borderRadius: 1,
                }}
              >
                <Typography style="body3" color={colors.grey[600]}>
                  No results found
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Start New Record Button */}
        <Box sx={{ display: "flex", justifyContent: "flex-start" }}>
          <Button
            label={`Start New ${(() => {
              if (
                panelType === PanelType.PERSON &&
                offenseContext?.relationType
              ) {
                // For incidents, always show "Person" regardless of relation type
                if (offenseContext.isIncident) {
                  return "Person";
                }

                switch (offenseContext.relationType) {
                  case "victim":
                    return "Victim";
                  case "offender":
                    return "Offender";
                  case "witness":
                    return "Witness";
                  case "suspect":
                    return "Suspect";
                  case "involved_party":
                    return "Involved Party";
                  default:
                    return "Person";
                }
              }
              return (
                panelType.charAt(0).toUpperCase() +
                panelType.slice(1).toLowerCase()
              );
            })()} Record`}
            color="blue"
            prominence={true}
            style="ghost"
            onClick={() => onStartNewRecord(searchFormData)}
            leftIcon={<AddIcon />}
            size="small"
          />
        </Box>
      </Box>

      {/* Entity Preview Modal */}
      <Dialog
        open={previewModalOpen}
        onClose={handleModalCancel}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            width: "80vw",
            maxWidth: "800px",
            height: "80vh",
            maxHeight: "700px",
            m: 2,
            borderRadius: "12px",
            overflow: "hidden",
          },
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 16,
            right: 16,
            zIndex: 1000,
          }}
        >
          <Box
            component="button"
            onClick={handleModalCancel}
            sx={{
              background: "rgba(255, 255, 255, 0.9)",
              border: "none",
              cursor: "pointer",
              padding: "8px",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 1)",
              },
            }}
          >
            <CloseIcon sx={{ color: colors.grey[600] }} />
          </Box>
        </Box>

        <DialogContent sx={{ p: 3, flex: 1, overflow: "auto" }}>
          {getModalSchema() ? (
            <FormRenderer
              config={getModalSchema()}
              initialValues={getModalInitialValues()}
              readOnly={true}
              containerHeight="100%"
            />
          ) : (
            <Box
              sx={{
                p: 3,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "200px",
              }}
            >
              <Typography style="body3" color={colors.grey[600]}>
                Unable to load entity details
              </Typography>
            </Box>
          )}
        </DialogContent>

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            p: 3,
            borderTop: `1px solid ${colors.grey[200]}`,
            background: "#FFFFFF",
            boxShadow: "0px 0px 20px 0px rgba(0, 0, 0, 0.12)",
          }}
        >
          <Button
            label="Back to Results"
            color="grey"
            prominence={false}
            onClick={handleModalCancel}
          />
          <Button
            label="Select"
            color="blue"
            prominence={true}
            onClick={handleModalAdd}
          />
        </Box>
      </Dialog>
    </>
  );
};
