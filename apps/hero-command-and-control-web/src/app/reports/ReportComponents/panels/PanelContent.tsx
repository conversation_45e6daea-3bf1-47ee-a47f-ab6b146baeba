import { Button } from "@/design-system/components/Button";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormRendererRef,
} from "@/design-system/form/FormRenderer";
import { colors } from "@/design-system/tokens";
import { Box, CircularProgress } from "@mui/material";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";

import { Typography } from "@/design-system/components/Typography";
import type { Reference } from "proto/hero/entity/v1/entity_pb";
import { FaArrowLeft } from "react-icons/fa";
import { PanelType } from "../core/types";
import { AdditionalVictimQuestions } from "./AdditionalVictimQuestions";
import { EntitySearchForm } from "./EntitySearchForm";
import { MediaPanel } from "./MediaPanel";
import { PropertyIngestWizard } from "./PropertyIngestWizard";
import { PropertySearchForm } from "./PropertySearchForm";

type PanelContentProps = {
  panelType: PanelType;
  onSubmit: (values: any, additionalVictimData?: any) => void;
  onUpdate: (values: any) => void;
  onCancel: () => void;
  onSaveAndAddAnother: (values: any) => void;
  onPropertyUpdated?: (updatedProperty: any) => void; // Custom callback for property updates
  onEntitySelect?: (
    entityRef: Reference,
    entityType: "person" | "property" | "vehicle" | "organization"
  ) => void;
  onPropertyRecordSelect?: (property: any) => void;
  personSchema: any;
  isLoadingSchema: boolean;
  propertySchema: any;
  isLoadingPropertySchema: boolean;
  vehicleSchema: any;
  isLoadingVehicleSchema: boolean;
  organizationSchema: any;
  isLoadingOrganizationSchema: any;
  initialValues?: any;
  isEditing?: boolean;
  editingEntityId?: string | null;
  isSaveAndAddAnotherLoading?: boolean;
  isSaveLoading?: boolean;
  isUpdateLoading?: boolean;
  readOnly?: boolean;
  currentEntities?: any[]; // Array of entities already in the report for the current panel type
  // Offense context props for contextual titles
  offenseContext?: {
    relationType?:
    | "victim"
    | "offender"
    | "witness"
    | "suspect"
    | "involved_party";
    offenseId?: string;
    isIncident?: boolean;
  };
  // Victim details mode
  victimDetailsMode?: {
    personId: string;
    personInfo?: any;
    organizationInfo?: any;
    existingRelation?: any;
    isOrganization?: boolean;
  };
  onCreateVictimReportRelation?: (
    personId: string,
    reportId: string,
    entity?: any,
    victimData?: any
  ) => void;
  onUpdateVictimReportRelation?: (relationId: string, victimData: any) => void;
  reportId?: string;
};

// Define the ref type
export type PanelContentRef = {
  scrollToTop: () => void;
};

// Mode for the panel

// alec@ note to come back and make victim questions more generic
enum PanelMode {
  SEARCH = "SEARCH",
  CREATE = "CREATE",
  VICTIM_QUESTIONS = "VICTIM_QUESTIONS",
}

export const PanelContent = forwardRef<PanelContentRef, PanelContentProps>(
  (
    {
      panelType,
      onSubmit,
      onUpdate,
      onCancel,
      onSaveAndAddAnother,
      onPropertyUpdated,
      onEntitySelect,
      onPropertyRecordSelect,
      personSchema,
      isLoadingSchema = false,
      propertySchema,
      isLoadingPropertySchema = false,
      vehicleSchema,
      isLoadingVehicleSchema = false,
      organizationSchema,
      isLoadingOrganizationSchema = false,
      initialValues,
      isEditing = false,
      editingEntityId = null,
      isSaveAndAddAnotherLoading = false,
      isSaveLoading = false,
      isUpdateLoading = false,
      readOnly = false,
      currentEntities,
      offenseContext,
      victimDetailsMode,
      onCreateVictimReportRelation,
      onUpdateVictimReportRelation,
      reportId,
    },
    ref
  ) => {
    const formRef = useRef<FormRendererRef>(null);
    const contentRef = useRef<HTMLDivElement>(null);

    // Panel mode state - start with victim questions if in victim details mode
    const [mode, setMode] = useState<PanelMode>(() => {
      if (victimDetailsMode) return PanelMode.VICTIM_QUESTIONS;
      if (panelType === PanelType.MEDIA || isEditing) return PanelMode.CREATE;
      return PanelMode.SEARCH;
    });
    const [searchData, setSearchData] = useState<any>(null);
    const [validationError, setValidationError] = useState<string | null>(null);

    // Expose the scrollToTop method to parent components
    useImperativeHandle(ref, () => ({
      scrollToTop: () => {
        if (contentRef.current) {
          contentRef.current.scrollTo({ top: 0, behavior: "smooth" });
        }
      },
    }));

    // Update mode when editing changes or victim details mode changes
    useEffect(() => {
      if (victimDetailsMode) {
        setMode(PanelMode.VICTIM_QUESTIONS);
      } else {
        setMode(
          panelType === PanelType.MEDIA || isEditing
            ? PanelMode.CREATE
            : PanelMode.SEARCH
        );
      }
    }, [panelType, isEditing, victimDetailsMode]);

    const handleSubmit = async () => {
      // Handle media panel submissions (no form involved)
      if (panelType === PanelType.MEDIA) {
        // Media submissions are handled directly by MediaPanel
        return;
      }

      // Handle form-based submissions (person, vehicle, property)
      if (formRef.current) {
        // Validate first
        const isValid = formRef.current.validate();
        if (!isValid) {
          // Validation failed, show error message
          setValidationError(
            "Please fill in all required fields before saving."
          );
          return;
        }

        // Clear any previous validation error
        setValidationError(null);

        // Get values
        const values = formRef.current.getValues();

        // Proceed normally for all entities
        if (isEditing && onUpdate) {
          onUpdate(values);
        } else if (onSubmit) {
          onSubmit(values);
        }
      }
    };

    const handleSaveAndAddAnother = () => {
      if (formRef.current && onSaveAndAddAnother) {
        // Validate first
        const isValid = formRef.current.validate();
        if (!isValid) {
          // Validation failed, show error message
          setValidationError(
            "Please fill in all required fields before saving."
          );
          return;
        }

        // Clear any previous validation error
        setValidationError(null);

        // Get values
        const values = formRef.current.getValues();

        // Proceed normally for all entities
        onSaveAndAddAnother(values);
      }
    };

    const handleCancel = () => {
      if (onCancel) {
        onCancel();
      }
    };

    const handleStartNewRecord = (searchFormData?: any) => {
      setSearchData(searchFormData);
      setMode(PanelMode.CREATE);
    };

    const handleBackToSearch = () => {
      setSearchData(null);
      setMode(PanelMode.SEARCH);
    };

    // Handle victim questions submissions
    const handleVictimQuestionsSaveToReport = (additionalData: any) => {
      if (victimDetailsMode && reportId) {
        if (
          victimDetailsMode.existingRelation &&
          onUpdateVictimReportRelation
        ) {
          // Update existing victim report relation
          onUpdateVictimReportRelation(
            victimDetailsMode.existingRelation.id,
            additionalData
          );
        } else if (onCreateVictimReportRelation) {
          // Create new victim report relation
          // For organizations, pass organizationInfo instead of personInfo
          const entityInfo = victimDetailsMode.isOrganization
            ? victimDetailsMode.organizationInfo
            : victimDetailsMode.personInfo;

          onCreateVictimReportRelation(
            victimDetailsMode.personId,
            reportId,
            entityInfo,
            additionalData
          );
        }
      }
    };

    // Function to map search form data to entity structure
    const mapSearchDataToEntityStructure = (
      searchFormData: any,
      panelType: PanelType
    ) => {
      if (!searchFormData) return undefined;

      switch (panelType) {
        case PanelType.PERSON:
          return {
            classificationSection: {
              firstName: searchFormData.firstName || "",
              middleName: searchFormData.middleName || "",
              lastName: searchFormData.lastName || "",
              nicknames: searchFormData.nicknames || "",
              dateOfBirth: searchFormData.dob || "",
              weight: "",
              affiliation: "",
              age: "",
              sex: "",
              gangAffiliation: "",
              gender: "",
            },
            descriptorsSection: {
              build: "",
              weight: "",
              completion: "",
              height: "",
              hairColor: "",
              eyeColor: "",
              marksScars: "",
              speechImpedimentsDisabilities: "",
            },
            contactInformationSection: {
              phoneNumber: searchFormData.phone || "",
              email: searchFormData.email || "",
              currentAddress: "",
              room: "",
              homeAddress: "",
              employer: "",
              position: "",
            },
            addressesSection: {
              homeAddress: "",
              roomUnitApt: "",
              address: "",
              addressRoomUnitApt: "",
            },
            identifiersSection: {
              driversLicense: searchFormData.stateId || "",
              state: "",
              governmentIssuedID: "",
              sSN: "",
              iDNumber: "",
              studentID: searchFormData.studentId || "",
              bookingJailID: "",
            },
          };

        case PanelType.VEHICLE:
          return {
            vehicleInformationSection: {
              vehicleType: "",
              description: "",
              year: searchFormData.year || "",
              role: "",
              make: searchFormData.make || "",
              model: searchFormData.model || "",
              color: searchFormData.color || "",
              licensePlate: "",
              ownerIfApplicable: searchFormData.owner || "",
              location: "",
              style: "",
              vIN: searchFormData.vin || "",
            },
          };

        case PanelType.PROPERTY:
          return {
            propertyInformationSection: {
              propertyType: "",
              description: "",
              quantity: "",
              category: searchFormData.category || "",
              makeModelBrand: searchFormData.makeModelBrand || "",
              ownerIfApplicable: "",
              condition: "",
              serialNumber: searchFormData.serialNumber || "",
              locationFound: "",
              dateCollected: "",
              collectedValue: searchFormData.value || "",
              seizedFromFoundBy: "",
              timeCollected: "",
              storageLocation: "",
            },
          };

        case PanelType.ORGANIZATION:
          return {
            informationSection: {
              name: searchFormData.name || "",
              type: searchFormData.type || "",
              status: searchFormData.status || "",
              phone: searchFormData.phone || "",
              website: searchFormData.website || "",
              email: searchFormData.email || "",
              address: [{
                streetAddress1: searchFormData.streetAddress || "",
                streetAddress2: searchFormData.streetAddress2 || "",
                city: searchFormData.city || "",
                state: searchFormData.state || "",
                zipCode: searchFormData.zip || "",
              }],
              primaryContactName: searchFormData.primaryContactName || "",
              primaryContactTitle: searchFormData.primaryContactTitle || "",
              primaryContactPhone: searchFormData.primaryContactPhone || "",
              campusOrganization: searchFormData.campusOrganization || "",
            },
          };

        default:
          return undefined;
      }
    };

    // Memoize initial values to prevent form reset on every render
    const memoizedInitialValues = useMemo(() => {
      if (isEditing) {
        return initialValues;
      }

      const mappedData = mapSearchDataToEntityStructure(searchData, panelType);
      return {
        ...mappedData,
        ...initialValues,
      };
    }, [isEditing, initialValues, searchData, panelType]);

    // Get panel title for current mode
    const getPanelTitle = () => {
      if (victimDetailsMode) {
        return "Victim Details";
      }

      if (isEditing) {
        switch (panelType) {
          case PanelType.PERSON:
            return "Edit Person";
          case PanelType.PROPERTY:
            return "Edit Property";
          case PanelType.VEHICLE:
            return "Edit Vehicle";
          case PanelType.ORGANIZATION:
            return "Edit Organization";
          case PanelType.MEDIA:
            return "Edit Media";
          default:
            return "Edit";
        }
      }

      if (offenseContext?.relationType && panelType === PanelType.PERSON) {
        // For incidents, always show "Add Person" regardless of relation type
        if (offenseContext.isIncident) {
          return "Add Person";
        }

        switch (offenseContext.relationType) {
          case "victim":
            return "Add Victim";
          case "offender":
            return "Add Offender";
          case "witness":
            return "Add Witness";
          case "suspect":
            return "Add Suspect";
          case "involved_party":
            return "Add Involved Party";
          default:
            return "Add Person";
        }
      }

      if (offenseContext?.offenseId && panelType === PanelType.VEHICLE) {
        return "Add Vehicle to Offense";
      }

      if (offenseContext?.offenseId && panelType === PanelType.PROPERTY) {
        return "Add Property to Offense";
      }

      if (offenseContext?.offenseId && panelType === PanelType.ORGANIZATION) {
        return "Add Organization to Offense";
      }

      switch (panelType) {
        case PanelType.PERSON:
          return "Add Person";
        case PanelType.PROPERTY:
          return "Add Property";
        case PanelType.VEHICLE:
          return "Add Vehicle";
        case PanelType.ORGANIZATION:
          return "Add Organization";
        case PanelType.MEDIA:
          return "Add Media";
        default:
          return "Add";
      }
    };

    // Render search interface
    const renderSearchContent = () => {
      if (panelType === PanelType.PROPERTY) {
        return (
          <PropertySearchForm
            onPropertySelect={(p) => onPropertyRecordSelect?.(p)}
            onStartNewRecord={handleStartNewRecord}
            handleCloseSidePanel={handleCancel}
            currentEntities={currentEntities}
          />
        );
      }
      return (
        <EntitySearchForm
          panelType={panelType}
          onEntitySelect={onEntitySelect}
          onStartNewRecord={handleStartNewRecord}
          handleCloseSidePanel={handleCancel}
          currentEntities={currentEntities}
          offenseContext={offenseContext}
        />
      );
    };

    // Render different content based on panel type and mode
    const renderContent = () => {
      // Show victim questions for victim details mode
      if (mode === PanelMode.VICTIM_QUESTIONS && victimDetailsMode) {
        return (
          <AdditionalVictimQuestions
            personInfo={victimDetailsMode.personInfo}
            organizationInfo={victimDetailsMode.organizationInfo}
            isOrganization={victimDetailsMode.isOrganization}
            existingData={victimDetailsMode.existingRelation?.metadata}
            isEditMode={!!victimDetailsMode.existingRelation}
            onBack={handleCancel}
            onSaveToReport={handleVictimQuestionsSaveToReport}
            readOnly={readOnly}
            isSaveAndAddAnotherLoading={isSaveAndAddAnotherLoading}
            isSaveToReportLoading={isSaveLoading}
          />
        );
      }

      // Show search interface for entity types (not editing and not media)
      if (
        mode === PanelMode.SEARCH &&
        panelType !== PanelType.MEDIA &&
        !isEditing
      ) {
        return renderSearchContent();
      }

      // Show form/creation interface
      switch (panelType) {
        case PanelType.PERSON: {
          if (isLoadingSchema) {
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <CircularProgress />
              </Box>
            );
          }

          // Use personSchema if available, otherwise show an error
          const formConfig = personSchema?.schemaDefinition
            ? JSON.parse(JSON.stringify(personSchema.schemaDefinition))
            : undefined;

          return (
            <>
              {/* Back to Search button for create mode */}
              {mode === PanelMode.CREATE && !isEditing && (
                <Box sx={{ mb: 3 }}>
                  <Button
                    label="Back to Search"
                    style="ghost"
                    color="grey"
                    size="medium"
                    onClick={handleBackToSearch}
                    leftIcon={<FaArrowLeft />}
                  />
                </Box>
              )}

              {formConfig ? (
                <FormRenderer
                  ref={formRef}
                  config={formConfig}
                  customTitle={getPanelTitle()}
                  initialValues={memoizedInitialValues}
                  readOnly={readOnly}
                />
              ) : (
                <Box sx={{ p: 2 }}>
                  <Typography style="body1" color={colors.grey[900]}>
                    Unable to load person schema. Please try again later.
                  </Typography>
                </Box>
              )}
              <Box sx={{ height: "100px" }} />
            </>
          );
        }

        case PanelType.VEHICLE: {
          const vehicleFormConfig = vehicleSchema?.schemaDefinition
            ? JSON.parse(JSON.stringify(vehicleSchema.schemaDefinition))
            : undefined;
          if (isLoadingVehicleSchema) {
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <CircularProgress />
              </Box>
            );
          }

          return (
            <>
              {/* Back to Search button for create mode */}
              {mode === PanelMode.CREATE && !isEditing && (
                <Box sx={{ mb: 3 }}>
                  <Button
                    label="Back to Search"
                    style="ghost"
                    color="grey"
                    size="medium"
                    onClick={handleBackToSearch}
                    leftIcon={<FaArrowLeft />}
                  />
                </Box>
              )}

              {vehicleFormConfig ? (
                <FormRenderer
                  ref={formRef}
                  config={vehicleFormConfig}
                  customTitle={getPanelTitle()}
                  initialValues={memoizedInitialValues}
                  readOnly={readOnly}
                />
              ) : (
                <Box sx={{ p: 2 }}>
                  <Typography style="body1" color={colors.grey[900]}>
                    Unable to load vehicle schema. Please try again later.
                  </Typography>
                </Box>
              )}
              <Box sx={{ height: "100px" }} />
            </>
          );
        }

        case PanelType.PROPERTY: {
          return (
            <>
              <PropertyIngestWizard
                onSubmit={onSubmit}
                onCancel={handleCancel}
                onSaveAndAddAnother={handleSaveAndAddAnother}
                onPropertyUpdated={onPropertyUpdated}
                initialValues={memoizedInitialValues}
                readOnly={readOnly}
                customTitle={getPanelTitle()}
                propertyId={editingEntityId || undefined}
                reportId={reportId}
              />
            </>
          );
        }

        case PanelType.ORGANIZATION: {
          if (isLoadingOrganizationSchema) {
            return (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <CircularProgress />
              </Box>
            );
          }

          // Use organizationSchema if available, otherwise show an error
          const organizationFormConfig = organizationSchema?.schemaDefinition
            ? JSON.parse(JSON.stringify(organizationSchema.schemaDefinition))
            : undefined;

          return (
            <>
              {/* Back to Search button for create mode */}
              {mode === PanelMode.CREATE && !isEditing && (
                <Box sx={{ mb: 3 }}>
                  <Button
                    label="Back to Search"
                    style="ghost"
                    color="grey"
                    size="medium"
                    onClick={handleBackToSearch}
                    leftIcon={<FaArrowLeft />}
                  />
                </Box>
              )}

              {organizationFormConfig ? (
                <FormRenderer
                  ref={formRef}
                  config={organizationFormConfig}
                  customTitle={getPanelTitle()}
                  initialValues={memoizedInitialValues}
                  readOnly={readOnly}
                />
              ) : (
                <Box sx={{ p: 2 }}>
                  <Typography style="body1" color={colors.grey[900]}>
                    Unable to load organization schema. Please try again later.
                  </Typography>
                </Box>
              )}
              <Box sx={{ height: "100px" }} />
            </>
          );
        }

        case PanelType.MEDIA:
          return (
            <MediaPanel
              onSubmit={onSubmit}
              onCancel={handleCancel}
              readOnly={readOnly}
              isSaveLoading={isSaveLoading}
            />
          );

        default:
          return (
            <Typography style="body1" color={colors.grey[900]}>
              Select a panel type
            </Typography>
          );
      }
    };

    return (
      <Box
        sx={{ p: 1, pb: 8, position: "relative", height: "100%" }}
        ref={contentRef}
      >
        {renderContent()}

        {/* Sticky bottom bar - Hide in read-only mode or search mode or victim questions mode */}
        {/* MediaPanel and PropertyForm handle their own bottom bars, so exclude them here */}
        {!readOnly &&
          mode === PanelMode.CREATE &&
          panelType !== PanelType.MEDIA &&
          panelType !== PanelType.PROPERTY && (
            <Box
              sx={{
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
                padding: "16px 24px",
                backgroundColor: "white",
                borderTop: "1px solid #E0E0E0",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 2,
                zIndex: 10,
              }}
            >
              {/* Validation error message */}
              {validationError && (
                <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
                  <Typography style="body2" color={colors.rose[600]}>
                    {validationError}
                  </Typography>
                </Box>
              )}

              {/* Action buttons */}
              <Box sx={{ display: "flex", gap: 2, marginLeft: "auto" }}>
                <Button
                  label="Cancel"
                  color="grey"
                  prominence={false}
                  onClick={handleCancel}
                  disabled={
                    isSaveLoading ||
                    isUpdateLoading ||
                    isSaveAndAddAnotherLoading
                  }
                />
                {/* Show Save + Add Another for all non-editing scenarios */}
                {!isEditing && (
                  <Button
                    label="Save + Add Another"
                    color="blue"
                    prominence={false}
                    onClick={handleSaveAndAddAnother}
                    isLoading={isSaveAndAddAnotherLoading}
                    disabled={
                      isSaveLoading ||
                      isUpdateLoading ||
                      isSaveAndAddAnotherLoading
                    }
                  />
                )}
                <Button
                  label={isEditing ? "Update" : "Save"}
                  color="blue"
                  prominence={true}
                  onClick={handleSubmit}
                  isLoading={isEditing ? isUpdateLoading : isSaveLoading}
                  disabled={
                    isSaveLoading ||
                    isUpdateLoading ||
                    isSaveAndAddAnotherLoading
                  }
                />
              </Box>
            </Box>
          )}
      </Box>
    );
  }
);

// Add display name to fix the linting error
PanelContent.displayName = "PanelContent";
