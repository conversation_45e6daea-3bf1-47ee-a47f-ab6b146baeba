"use client";

import { Label } from "@/design-system/components/Label";
import { Typography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens/colors";
import DirectionsCarIcon from "@mui/icons-material/DirectionsCar";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import HomeIcon from "@mui/icons-material/Home";
import PeopleIcon from "@mui/icons-material/People";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import { Box, Chip, Collapse, Divider, Stack } from "@mui/material";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { useGetPresignedDownloadUrl } from "../../../apis/services/filerepository/hooks";

interface ReportSummaryTabProps {
  people?: any[];
  vehicles?: any[];
  properties?: any[];
  organizations?: any[];
  mediaFiles?: any[];
  relations?: any[];
  reportId?: string;
}

// Count badge component for section headers
const CountBadge = ({ count }: { count: number }) => {
  if (count <= 0) return null;

  return (
    <Chip
      size="small"
      label={count}
      sx={{
        height: 20,
        fontSize: "10px",
        bgcolor: colors.blue[100],
        color: colors.blue[600],
        fontWeight: 500,
      }}
    />
  );
};

// Helper function to convert property type to readable format
const getReadablePropertyType = (propertyType: string): string => {
  switch (propertyType) {
    case "PROPERTY_TYPE_FOUND":
      return "Found Property";
    case "PROPERTY_TYPE_SEIZED":
      return "Seized Property";
    case "PROPERTY_TYPE_STOLEN":
      return "Stolen Property";
    case "PROPERTY_TYPE_RECOVERED":
      return "Recovered Property";
    case "PROPERTY_TYPE_BURNED":
      return "Burned Property";
    case "PROPERTY_TYPE_FORGED":
      return "Forged Property";
    case "PROPERTY_TYPE_DAMAGED":
      return "Damaged Property";
    case "PROPERTY_TYPE_UNKNOWN":
      return "Unknown Property";
    case "PROPERTY_TYPE_NONE":
      return "Property";
    default:
      return "Property";
  }
};

// Helper function to get color based on designation prefix
const getLabelColor = (
  designation: string
): "vine" | "rose" | "purple" | "amber" | "grey" | "blue" => {
  if (designation === "RP") {
    return "blue"; // Reporting Party - blue
  }
  if (designation.startsWith("PR")) {
    return "amber"; // Property - amber
  }
  if (designation.startsWith("VEH")) {
    return "blue"; // Vehicle - blue
  }
  if (designation.startsWith("ORG")) {
    return "purple"; // Organization - purple
  }
  if (designation.startsWith("MED")) {
    return "grey"; // Media - grey
  }

  // Handle multi-character prefixes for people
  if (designation.startsWith("VIC")) {
    return "vine"; // Victim - green
  }
  if (designation.startsWith("OFF")) {
    return "rose"; // Offender - red
  }
  if (designation.startsWith("WIT")) {
    return "purple"; // Witness - purple
  }

  // Handle legacy single-character prefixes for backward compatibility
  const prefix = designation.charAt(0);
  switch (prefix) {
    case "V":
      return "vine"; // Victim - green (legacy)
    case "O":
      return "rose"; // Offender - red (legacy)
    case "W":
      return "purple"; // Witness - purple (legacy)
    case "S":
      return "amber"; // Suspect - amber
    case "I":
      return "grey"; // Involved Party - grey
    default:
      return "grey";
  }
};

export default function ReportSummaryTab({
  people = [],
  vehicles = [],
  properties = [],
  organizations = [],
  mediaFiles = [],
  relations = [],
  reportId,
}: ReportSummaryTabProps) {
  const router = useRouter();
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});
  const [openingFileId, setOpeningFileId] = useState<string | null>(null);

  // Mutation hook for generating presigned download URLs
  const getDownloadUrlMutation = useGetPresignedDownloadUrl({
    onSuccess: () => {
      // Download URL generated successfully
    },
    onError: (error) => {
      // Error generating download URL
    },
  });

  const handleSectionClick = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const getVictimOffenderNumbers = (personId: string): string[] => {
    if (!relations || relations.length === 0) return [];

    // Check if this person still exists in the report
    const personExists = people.some((p) => p.id === personId);
    if (!personExists) {
      // Person has been removed from report, don't show any chips
      return [];
    }

    const personRelations = relations.filter((r: any) => {
      if (!r.metadata?.victimOffenderNumber) return false;
      const involvesPerson =
        (r.objectA?.objectType === "entity" &&
          r.objectA?.globalId === personId) ||
        (r.objectB?.objectType === "entity" &&
          r.objectB?.globalId === personId);
      return (
        involvesPerson &&
        (r.relationType === "RELATION_TYPE_OFFENSE_VICTIM" ||
          r.relationType === "RELATION_TYPE_OFFENSE_OFFENDER" ||
          r.relationType === "RELATION_TYPE_OFFENSE_WITNESS" ||
          r.relationType === "RELATION_TYPE_OFFENSE_SUSPECT" ||
          r.relationType === "RELATION_TYPE_OFFENSE_INVOLVED_PARTY")
      );
    });

    // Get all person role numbers and remove duplicates
    const numbers = personRelations
      .map((rel) => rel.metadata?.victimOffenderNumber)
      .filter(Boolean);

    // Remove duplicates and ensure no multiple tags of same type
    const uniqueNumbers = [];
    const seenTypes = new Set();

    for (const number of numbers) {
      const type = number.charAt(0);
      if (!seenTypes.has(type)) {
        uniqueNumbers.push(number);
        seenTypes.add(type);
      }
    }

    // Check for reporting party status from victim report relations
    if (reportId) {
      const victimReportRelation = relations.find((rel: any) => {
        const isVictimReportRelation =
          rel.relationType === "RELATION_TYPE_VICTIM_REPORT";
        const isPersonInvolved =
          (rel.objectA?.objectType === "entity" &&
            rel.objectA?.globalId === personId) ||
          (rel.objectB?.objectType === "entity" &&
            rel.objectB?.globalId === personId);
        const isReportInvolved =
          (rel.objectA?.objectType === "report" &&
            rel.objectA?.reportScopedId === reportId) ||
          (rel.objectB?.objectType === "report" &&
            rel.objectB?.reportScopedId === reportId);

        return isVictimReportRelation && isPersonInvolved && isReportInvolved;
      });

      // Add RP tag if person is reporting party
      if (victimReportRelation?.metadata?.isReportingParty) {
        uniqueNumbers.push("RP");
      }
    }

    return uniqueNumbers;
  };

  // Generic function to generate sequential numbers for any item list
  const getSequentialNumber = (items: any[], itemId: string, prefix: string): string[] => {
    const index = items.findIndex(item => item.id === itemId);
    return index === -1 ? [] : [`${prefix}${index + 1}`];
  };

  const getPropertyNumbers = (propertyId: string): string[] =>
    getSequentialNumber(properties, propertyId, 'PR');

  const getVehicleNumbers = (vehicleId: string): string[] =>
    getSequentialNumber(vehicles, vehicleId, 'VEH');

  const getOrganizationNumbers = (organizationId: string): string[] =>
    getSequentialNumber(organizations, organizationId, 'ORG');

  const getMediaNumbers = (mediaFileId: string, groupIndex: number, fileIndex: number): string[] => {
    // Generate sequential media numbers across all groups
    const groupedMedia = getGroupedMediaFiles;

    let totalPreviousFiles = 0;
    for (let i = 0; i < groupIndex; i++) {
      totalPreviousFiles += groupedMedia[i].files.length;
    }

    const mediaNumber = totalPreviousFiles + fileIndex + 1;
    return [`MED${mediaNumber}`];
  };

  const getGroupedMediaFiles = useMemo(() => {
    const groups: Array<{
      label: string;
      files: any[];
      type: 'report' | 'property' | 'vehicle' | 'person' | 'organization';
    }> = [];

    // Add report media files
    if (mediaFiles && mediaFiles.length > 0) {
      groups.push({
        label: 'Report',
        files: mediaFiles,
        type: 'report'
      });
    }

    // Add property media files
    if (properties && properties.length > 0) {
      properties.forEach((property, index) => {
        // Check if property has media files (from converted property data)
        const propertyFiles = property.data?.uploadImage || [];
        if (propertyFiles.length > 0) {
          // Convert property file format to match media file format for consistency
          const convertedFiles = propertyFiles.map((file: any) => ({
            id: file.id,
            displayName: file.name || `Property File ${index + 1}`,
            fileName: file.name,
            caption: `Attached to ${property.data?.description || 'Property'}`,
            metadata: {
              fileType: file.type,
              originalFilename: file.name,
            }
          }));

          groups.push({
            label: `PR${index + 1}`,
            files: convertedFiles,
            type: 'property'
          });
        }
      });
    }

    // TODO: Add vehicle, person, and organization media files when available

    return groups;
  }, [properties, mediaFiles]);

  // Handle entity click to navigate to entity page
  const handleEntityClick = (entityId: string) => {
    router.push(`/entity?entityId=${entityId}`);
  };

  // Handle media file click to open in new tab with presigned URL
  const handleMediaClick = async (mediaFile: any) => {
    // Check if the media file has a fileId (like in MediaSection)
    if (!mediaFile.fileId && !mediaFile.id) {
      console.warn("No fileId found for media file:", mediaFile);
      return;
    }

    const fileId = mediaFile.fileId || mediaFile.id;
    const fileRefId = mediaFile.id || fileId;

    try {
      setOpeningFileId(fileRefId);

      // Get presigned download URL with 5-minute expiration
      const response = await getDownloadUrlMutation.mutateAsync({
        id: fileId,
        expiresIn: 300 // 5 minutes
      } as any);

      // Open the file in a new tab
      window.open(response.presignedUrl, '_blank');
    } catch (error) {
      alert('Failed to open media file. Please try again.');
    } finally {
      setOpeningFileId(null);
    }
  };

  return (
    <Stack spacing={0}>
      {/* People Section */}
      <Box>
        <Box
          onClick={() => handleSectionClick("people")}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            py: 1.5,
            cursor: "pointer",
            "&:hover": {
              backgroundColor: "#F9FAFB",
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <PeopleIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
            <Typography style="caps3" color={colors.grey[500]}>
              People
            </Typography>
            <CountBadge count={people.length} />
          </Box>
          <ExpandMoreIcon
            sx={{
              transform: expandedSections["people"] ? "rotate(180deg)" : "none",
              transition: "transform 0.2s",
              color: "#6B7280",
              height: 20,
              width: 20,
            }}
          />
        </Box>
        <Collapse in={expandedSections["people"]}>
          <Box sx={{ p: 1, pt: 0 }}>
            {people.length > 0 ? (
              <Stack spacing={1}>
                {people.map((person, index) => {
                  const data = person.data || {};
                  const firstName = data.classificationSection?.firstName || "";
                  const lastName = data.classificationSection?.lastName || "";

                  return (
                    <Box
                      key={index}
                      sx={{
                        px: 1,
                        py: 0.75,
                        cursor: "pointer",
                        "&:hover": { bgcolor: colors.grey[100] },
                        "&:active": { bgcolor: colors.grey[200] },
                      }}
                      onClick={() => handleEntityClick(person.id)}
                    >
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <Typography style="body3" color={colors.grey[900]}>
                          {firstName} {lastName}
                        </Typography>
                        {(() => {
                          const numbers = getVictimOffenderNumbers(person.id);
                          if (numbers.length === 0) return null;
                          return (
                            <Stack direction="row" spacing={0.5}>
                              {numbers.map((number, index) => {
                                const color = getLabelColor(number);
                                return (
                                  <Label
                                    key={index}
                                    label={number}
                                    size="small"
                                    color={color}
                                    prominence={false}
                                    pilled
                                  />
                                );
                              })}
                            </Stack>
                          );
                        })()}
                      </Box>
                    </Box>
                  );
                })}
              </Stack>
            ) : null}
          </Box>
        </Collapse>
        <Divider />
      </Box>

      {/* Vehicles Section */}
      <Box>
        <Box
          onClick={() => handleSectionClick("vehicles")}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            py: 1.5,
            cursor: "pointer",
            "&:hover": {
              backgroundColor: "#F9FAFB",
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <DirectionsCarIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
            <Typography style="caps3" color={colors.grey[500]}>
              Vehicles
            </Typography>
            <CountBadge count={vehicles.length} />
          </Box>
          <ExpandMoreIcon
            sx={{
              transform: expandedSections["vehicles"]
                ? "rotate(180deg)"
                : "none",
              transition: "transform 0.2s",
              color: "#6B7280",
              height: 20,
              width: 20,
            }}
          />
        </Box>
        <Collapse in={expandedSections["vehicles"]}>
          <Box sx={{ p: 1, pt: 0 }}>
            {vehicles.length > 0 ? (
              <Stack spacing={1}>
                {vehicles.map((vehicle, index) => {
                  const data = vehicle.data || {};
                  const make = data.vehicleInformationSection?.make || "";
                  const model = data.vehicleInformationSection?.model || "";
                  const licensePlate =
                    data.vehicleInformationSection?.licensePlate || "";

                  return (
                    <Box
                      key={index}
                      sx={{
                        px: 1,
                        py: 0.75,
                        cursor: "pointer",
                        "&:hover": { bgcolor: colors.grey[100] },
                        "&:active": { bgcolor: colors.grey[200] },
                      }}
                      onClick={() => handleEntityClick(vehicle.id)}
                    >
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <Typography style="body3" color={colors.grey[900]}>
                          {make} {model}
                        </Typography>
                        {(() => {
                          const numbers = getVehicleNumbers(vehicle.id);
                          if (numbers.length === 0) return null;
                          return (
                            <Stack direction="row" spacing={0.5}>
                              {numbers.map((number, index) => {
                                const color = getLabelColor(number);
                                return (
                                  <Label
                                    key={index}
                                    label={number}
                                    size="small"
                                    color={color}
                                    prominence={false}
                                    pilled
                                  />
                                );
                              })}
                            </Stack>
                          );
                        })()}
                      </Box>
                      <Typography style="tag2" color={colors.grey[500]}>
                        {licensePlate}
                      </Typography>
                    </Box>
                  );
                })}
              </Stack>
            ) : null}
          </Box>
        </Collapse>
        <Divider />
      </Box>

      {/* Property Section */}
      <Box>
        <Box
          onClick={() => handleSectionClick("property")}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            py: 1.5,
            cursor: "pointer",
            "&:hover": {
              backgroundColor: "#F9FAFB",
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <HomeIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
            <Typography style="caps3" color={colors.grey[500]}>
              Property
            </Typography>
            <CountBadge count={properties.length} />
          </Box>
          <ExpandMoreIcon
            sx={{
              transform: expandedSections["property"]
                ? "rotate(180deg)"
                : "none",
              transition: "transform 0.2s",
              color: "#6B7280",
              height: 20,
              width: 20,
            }}
          />
        </Box>
        <Collapse in={expandedSections["property"]}>
          <Box sx={{ p: 1, pt: 0 }}>
            {properties.length > 0 ? (
              <Stack spacing={1}>
                {properties.map((property, index) => {
                  const data = property.data || {};
                  const category =
                    data.propertyInformationSection?.propertyType || "";
                  const description =
                    data.propertyInformationSection?.description || "";

                  return (
                    <Box
                      key={index}
                      sx={{
                        px: 1,
                        py: 0.75,
                        cursor: "pointer",
                        "&:hover": { bgcolor: colors.grey[100] },
                        "&:active": { bgcolor: colors.grey[200] },
                      }}
                      onClick={() => router.push(`/property?propertyId=${property.id}`)}
                    >
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <Typography style="body3" color={colors.grey[900]}>
                          {description}
                        </Typography>
                        {(() => {
                          const numbers = getPropertyNumbers(property.id);
                          if (numbers.length === 0) return null;
                          return (
                            <Stack direction="row" spacing={0.5}>
                              {numbers.map((number, index) => {
                                const color = getLabelColor(number);
                                return (
                                  <Label
                                    key={index}
                                    label={number}
                                    size="small"
                                    color={color}
                                    prominence={false}
                                    pilled
                                  />
                                );
                              })}
                            </Stack>
                          );
                        })()}
                      </Box>
                      <Typography style="tag2" color={colors.grey[500]}>
                        {getReadablePropertyType(category)}
                      </Typography>
                    </Box>
                  );
                })}
              </Stack>
            ) : null}
          </Box>
        </Collapse>
        <Divider />
      </Box>

      {/* Organizations Section */}
      <Box>
        <Box
          onClick={() => handleSectionClick("organizations")}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            py: 1.5,
            cursor: "pointer",
            "&:hover": {
              backgroundColor: "#F9FAFB",
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <DirectionsCarIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
            <Typography style="caps3" color={colors.grey[500]}>
              Organizations
            </Typography>
            <CountBadge count={organizations.length} />
          </Box>
          <ExpandMoreIcon
            sx={{
              transform: expandedSections["organizations"]
                ? "rotate(180deg)"
                : "none",
              transition: "transform 0.2s",
              color: "#6B7280",
              height: 20,
              width: 20,
            }}
          />
        </Box>
        <Collapse in={expandedSections["organizations"]}>
          <Box sx={{ p: 1, pt: 0 }}>
            {organizations.length > 0 ? (
              <Stack spacing={1}>
                {organizations.map((organization, index) => {
                  const data = organization.data || {};
                  const name = data.informationSection?.name || "";
                  const type = data.informationSection?.type || "";

                  return (
                    <Box
                      key={index}
                      sx={{
                        px: 1,
                        py: 0.75,
                        cursor: "pointer",
                        "&:hover": { bgcolor: colors.grey[100] },
                        "&:active": { bgcolor: colors.grey[200] },
                      }}
                      onClick={() => handleEntityClick(organization.id)}
                    >
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <Typography style="body3" color={colors.grey[900]}>
                          {name}
                        </Typography>
                        {(() => {
                          const numbers = getOrganizationNumbers(organization.id);
                          if (numbers.length === 0) return null;
                          return (
                            <Stack direction="row" spacing={0.5}>
                              {numbers.map((number, index) => {
                                const color = getLabelColor(number);
                                return (
                                  <Label
                                    key={index}
                                    label={number}
                                    size="small"
                                    color={color}
                                    prominence={false}
                                    pilled
                                  />
                                );
                              })}
                            </Stack>
                          );
                        })()}
                      </Box>
                      <Typography style="tag2" color={colors.grey[500]}>
                        {type}
                      </Typography>
                    </Box>
                  );
                })}
              </Stack>
            ) : null}
          </Box>
        </Collapse>
        <Divider />
      </Box>

      {/* Media Section */}
      <Box>
        <Box
          onClick={() => handleSectionClick("media")}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            py: 1.5,
            cursor: "pointer",
            "&:hover": {
              backgroundColor: "#F9FAFB",
            },
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <PhotoLibraryIcon sx={{ fontSize: 16, color: colors.grey[500] }} />
            <Typography style="caps3" color={colors.grey[500]}>
              Media
            </Typography>
            <CountBadge count={getGroupedMediaFiles.reduce((total, group) => total + group.files.length, 0)} />
          </Box>
          <ExpandMoreIcon
            sx={{
              transform: expandedSections["media"] ? "rotate(180deg)" : "none",
              transition: "transform 0.2s",
              color: "#6B7280",
              height: 20,
              width: 20,
            }}
          />
        </Box>
        <Collapse in={expandedSections["media"]}>
          <Box sx={{ p: 1, pt: 0 }}>
            {(() => {
              const groupedMedia = getGroupedMediaFiles;
              if (groupedMedia.length === 0) return null;

              return (
                <Stack spacing={1}>
                  {groupedMedia.map((group, groupIndex) => {
                    const isLastGroup = groupIndex === groupedMedia.length - 1;

                    return (
                      <Box key={groupIndex}>
                        {group.files.map((mediaFile, fileIndex) => {
                          const fileName = mediaFile.displayName ||
                            mediaFile.fileName ||
                            mediaFile.name ||
                            (typeof mediaFile.metadata?.originalFilename === 'string' ? mediaFile.metadata.originalFilename : '') ||
                            `Media ${fileIndex + 1}`;
                          const description = mediaFile.caption || 'No description';

                          return (
                            <Box
                              key={`${groupIndex}-${fileIndex}`}
                              sx={{
                                px: 1,
                                py: 0.75,
                                cursor: openingFileId === (mediaFile.id || mediaFile.fileId) ? "wait" : "pointer",
                                "&:hover": { bgcolor: colors.grey[100] },
                                "&:active": { bgcolor: colors.grey[200] },
                                opacity: openingFileId === (mediaFile.id || mediaFile.fileId) ? 0.6 : 1,
                              }}
                              onClick={() => {
                                if (openingFileId !== (mediaFile.id || mediaFile.fileId)) {
                                  handleMediaClick(mediaFile);
                                }
                              }}
                            >
                              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                {openingFileId === (mediaFile.id || mediaFile.fileId) && (
                                  <Box
                                    sx={{
                                      width: 12,
                                      height: 12,
                                      border: `2px solid ${colors.blue[200]}`,
                                      borderTop: `2px solid ${colors.blue[600]}`,
                                      borderRadius: '50%',
                                      animation: 'spin 1s linear infinite',
                                      '@keyframes spin': {
                                        '0%': { transform: 'rotate(0deg)' },
                                        '100%': { transform: 'rotate(360deg)' },
                                      },
                                    }}
                                  />
                                )}
                                <Typography style="body3" color={colors.grey[900]}>
                                  {fileName}
                                </Typography>
                                {(() => {
                                  const numbers = getMediaNumbers(mediaFile.id, groupIndex, fileIndex);
                                  if (numbers.length === 0) return null;
                                  return (
                                    <Stack direction="row" spacing={0.5}>
                                      {numbers.map((number, index) => {
                                        const color = getLabelColor(number);
                                        return (
                                          <Label
                                            key={index}
                                            label={number}
                                            size="small"
                                            color={color}
                                            prominence={false}
                                            pilled
                                          />
                                        );
                                      })}
                                    </Stack>
                                  );
                                })()}
                              </Box>
                              <Typography style="tag2" color={colors.grey[500]}>
                                {group.label} • {description}
                              </Typography>
                            </Box>
                          );
                        })}

                        {/* Add separator between groups but not after the last group */}
                        {!isLastGroup && (
                          <Box sx={{
                            height: '1px',
                            backgroundColor: colors.grey[200],
                            mx: 1,
                            my: 1
                          }} />
                        )}
                      </Box>
                    );
                  })}
                </Stack>
              );
            })()}
          </Box>
        </Collapse>
        <Divider />
      </Box>
    </Stack>
  );
}
