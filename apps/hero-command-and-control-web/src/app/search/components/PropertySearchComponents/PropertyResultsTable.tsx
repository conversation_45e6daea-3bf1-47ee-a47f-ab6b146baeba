import CheckIcon from "@mui/icons-material/Check";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import {
  Box,
  Checkbox,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import * as Sentry from "@sentry/nextjs";
import { useRouter } from "next/navigation";
import { NIBRSPropertyType } from "proto/hero/property/v1/property_pb";
import { useEffect, useState } from "react";
import { Button } from "../../../../design-system/components/Button";
import { Typography } from "../../../../design-system/components/Typography";
import { colors } from "../../../../design-system/tokens";
import { searchProperties } from "../../../apis/services/workflow/property/endpoints";
import { hookPropertyStatusToString, stringToPropertyType } from "../../../apis/services/workflow/property/enumConverters";
import { useBatchGetProperties } from "../../../apis/services/workflow/property/hooks";
import { propertyTypeToDisplayString } from "../../../apis/services/workflow/property/propertyTypeManager";
import { Property, SearchOrderBy as PropertySearchOrderBy, PropertyStatus } from "../../../apis/services/workflow/property/types";
import { ExportModal } from "../../../components/ExportModal";
import { getPropertyStatusDisplay, getReadablePropertyCategory } from "../../../utils/propertyHelpers";
import {
  SearchState,
  saveSearchStateForNavigation,
} from "../../utils/searchStateManager";
import { TableSkeleton } from "../TableSkeleton";

const PROPERTY_SORT_OPTIONS = [
  { value: PropertySearchOrderBy.SEARCH_ORDER_BY_CREATED_AT, label: "Created Date" },
  { value: PropertySearchOrderBy.SEARCH_ORDER_BY_UPDATED_AT, label: "Updated Date" },
  { value: PropertySearchOrderBy.SEARCH_ORDER_BY_STATUS, label: "Status" },
];

interface PropertyResultsTableProps {
  data: any;
  isLoading: boolean;
  isError: boolean;
  error: any;
  page: number;
  rowsPerPage: number;
  totalPages: number;
  totalResults: number;
  nextPageToken?: string;
  handleChangePage: (pageIndex: number) => void;
  onChangeRowsPerPage?: (newRowsPerPage: number) => void;
  selectedSort: PropertySearchOrderBy;
  onSortChange: (sortValue: PropertySearchOrderBy) => void;
  getCurrentSearchState: () => SearchState;
}

export const PropertyResultsTable = ({
  data,
  isLoading,
  isError,
  error,
  page,
  rowsPerPage,
  totalPages,
  totalResults,
  nextPageToken,
  handleChangePage,
  onChangeRowsPerPage,
  selectedSort,
  onSortChange,
  getCurrentSearchState,
}: PropertyResultsTableProps) => {
  const router = useRouter();
  const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(null);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isExportLoading, setIsExportLoading] = useState(false);
  const [selectedProperties, setSelectedProperties] = useState<Set<string>>(new Set());
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);

  // State to track all fetched results
  const [allFetchedResults, setAllFetchedResults] = useState<Property[]>([]);
  const [isFetchingAll, setIsFetchingAll] = useState(false);

  // Get the current search state for export
  const currentSearchState = getCurrentSearchState();

  // Function to fetch all pages of results
  const fetchAllResults = async () => {
    setIsFetchingAll(true);
    let allProperties: Property[] = [];
    let currentPageToken: string | undefined = undefined; // Start from the beginning
    const maxPages = 200; // Safety limit to prevent infinite loops
    let pageCount = 0;

    try {
      while (pageCount < maxPages) {
        const response = await searchProperties({
          query: currentSearchState.searchParams.query,
          pageSize: 100, // Cap to 100 as requested
          orderBy: currentSearchState.searchParams.orderBy as any,
          pageToken: currentPageToken,
        });

        // Break if no properties returned
        if (!response.properties || response.properties.length === 0) {
          break;
        }

        // Add properties to our collection
        allProperties = [...allProperties, ...response.properties];

        // Break if no more pages
        if (!response.nextPageToken) {
          break;
        }

        // Update page token for next iteration
        currentPageToken = response.nextPageToken;
        pageCount++;
      }

      setAllFetchedResults(allProperties);
    } catch (error) {
      // Error fetching all results
    } finally {
      setIsFetchingAll(false);
    }
  };

  // Effect to fetch all results when export modal opens
  useEffect(() => {
    if (isExportModalOpen && !selectedProperties.size) {
      fetchAllResults();
    }
  }, [isExportModalOpen, selectedProperties.size]);

  // Fetch complete property data for export
  const { data: exportData, isLoading: isExportDataLoading } = useBatchGetProperties(
    selectedProperties.size > 0
      ? Array.from(selectedProperties)
      : allFetchedResults.map((property: Property) => property.id)
  );

  const handleDownloadCsv = () => {
    try {
      if (!exportData || exportData.length === 0) {
        return { data: [], headers: [] };
      }

      // Filter properties based on selection if any are selected
      const propertiesToExport = selectedProperties.size > 0
        ? exportData.filter(property => selectedProperties.has(property.id))
        : exportData;

      // Filter out properties with incomplete data
      const completeProperties = propertiesToExport.filter(property => {
        return property.id && property.propertyStatus;
      });

      // Define CSV headers for property data
      const csvHeaders = [
        { label: "ID", key: "id" },
        { label: "Property Type", key: "propertyType" },
        { label: "Property Status", key: "propertyStatus" },
        { label: "Current Location", key: "currentLocation" },
        { label: "Current Custodian", key: "currentCustodian" },
        { label: "Notes", key: "notes" },
        { label: "Is Evidence", key: "isEvidence" },
        { label: "Retention Period", key: "retentionPeriod" },
        { label: "Disposal Type", key: "disposalType" },
        { label: "Created", key: "created" },
        { label: "Last Updated", key: "lastUpdated" }
      ];

      const csvData = completeProperties.map((property: Property) => {
        return {
          id: property.id,
          propertyType: getPropertyType(property),
          propertyStatus: getPropertyStatusDisplay(property.propertyStatus),
          currentLocation: property.currentLocation || "---",
          currentCustodian: property.currentCustodian || "---",
          notes: property.notes || "---",
          isEvidence: property.isEvidence ? "Yes" : "No",
          retentionPeriod: property.retentionPeriod || "---",
          disposalType: property.disposalType,
          created: formatDate(property.createTime),
          lastUpdated: formatDate(property.updateTime)
        };
      });

      return { data: csvData, headers: csvHeaders };
    } catch (error) {
      Sentry.withScope((scope) => {
        scope.setTag("error_key", "csv-export");
        scope.setTag("export_type", "property");
        scope.setTag("failure_stage", "data_processing");
        scope.setContext("export_info", {
          selectedCount: selectedProperties.size,
          totalFetched: allFetchedResults.length,
          hasExportData: !!exportData,
          propertiesCount: exportData?.length || 0,
        });
        Sentry.captureException(error);
      });
      return { data: [], headers: [] };
    }
  };

  const isSortMenuOpen = Boolean(sortMenuAnchor);

  const handleSortMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setSortMenuAnchor(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortMenuAnchor(null);
  };

  const handleSortSelect = (sortValue: PropertySearchOrderBy) => {
    onSortChange(sortValue);
    handleSortMenuClose();
  };

  // Handle property click to navigate to details
  const handlePropertyClick = (property: Property) => {
    // Save current search state before navigating
    const currentSearchState = getCurrentSearchState();
    saveSearchStateForNavigation(currentSearchState);

    // Navigate to property details using property id
    router.push(`/property?propertyId=${property.id}`);
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "---";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Extract property address from property data
  const getPropertyAddress = (property: Property) => {
    return property.currentLocation || "---";
  };

  // Extract property type from property data (check both details and legacy propertySchema)
  const getPropertyType = (property: Property) => {
    // PRIORITY 1: Check for property type in top-level field (from proto) - this is the NIBRS loss type
    if (property.nibrsPropertyType) {
      return getPropertyTypeDisplay(property.nibrsPropertyType);
    }

    // PRIORITY 2: Fallback to property type in details field (legacy)
    if (property.details?.propertyType && typeof property.details.propertyType === 'string') {
      return getPropertyTypeDisplay(property.details.propertyType);
    }

    // PRIORITY 3: Check for property type in legacy propertySchema field
    if (property.propertySchema?.propertyType) {
      return getPropertyTypeDisplay(property.propertySchema.propertyType);
    }

    return "---";
  };

  // Extract property number from property data
  const getPropertyNumber = (property: Property) => {
    return property.propertyNumber || "---";
  };

  // Extract and format property category from property data (now from details field)
  const getPropertyCategory = (property: Property) => {
    if (property.details) {
      const detailsMap = property.details;
      const category = detailsMap.category;
      if (category && typeof category === 'string') {
        return getReadablePropertyCategory(category);
      }
    }
    return "---";
  };

  // Extract property description from property data (now from details field)
  const getPropertyDescription = (property: Property) => {
    if (property.details) {
      const detailsMap = property.details;
      const description = detailsMap.description;
      if (description && typeof description === 'string') {
        return description;
      }
    }
    return property.notes || "---";
  };

  // Extract property value from property data (now from details field)
  const getPropertyValue = (property: Property) => {
    if (property.details) {
      const detailsMap = property.details;
      const value = detailsMap.value;
      if (value && (typeof value === 'string' || typeof value === 'number')) {
        return value.toString();
      }
    }
    return "---";
  };

  // Extract storage location from property data
  const getStorageLocation = (property: Property) => {
    return property.currentLocation || "---";
  };

  // Format date for creation date
  const formatCreatedDate = (dateString: string) => {
    if (!dateString) return "---";
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get property type display
  const getPropertyTypeDisplay = (type: NIBRSPropertyType | string): string => {
    // Handle both string and numeric enum values
    let propertyType: NIBRSPropertyType;

    if (typeof type === 'string') {
      // Handle lowercase property type strings from the API
      const normalizedType = type.toUpperCase();
      const fullEnumString = `PROPERTY_TYPE_${normalizedType}`;

      // Try the full enum string first
      if (fullEnumString in NIBRSPropertyType) {
        propertyType = NIBRSPropertyType[fullEnumString as keyof typeof NIBRSPropertyType];
      } else {
        // Fall back to the original stringToPropertyType function
        propertyType = stringToPropertyType(type);
      }
    } else {
      propertyType = type;
    }

    // Use centralized display function
    return propertyTypeToDisplayString(propertyType);
  };

  // Using shared getPropertyStatusDisplay function from propertyHelpers

  // Using shared getPropertyTypeDisplay function from propertyHelpers

  // Get property status display for hook data (which comes as string but typed as enum)
  const getPropertyStatusDisplayFromHook = (status: PropertyStatus): string => {
    const statusString = hookPropertyStatusToString(status);
    const result = getPropertyStatusDisplay(statusString);
    return result;
  };

  // Custom pagination component
  const CustomPagination = () => {
    const getPageNumbers = () => {
      if (totalPages <= 1) {
        return [0];
      }

      const pageNumbers = [];

      if (totalPages <= 7) {
        for (let i = 0; i < totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        if (page <= 3) {
          // Near beginning: 1 2 3 4 5 ... 10
          for (let i = 0; i < 5; i++) {
            pageNumbers.push(i);
          }
          pageNumbers.push(-1);
          pageNumbers.push(totalPages - 1);
        } else if (page >= totalPages - 4) {
          // Near end: 1 ... 6 7 8 9 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          for (let i = totalPages - 5; i < totalPages; i++) {
            pageNumbers.push(i);
          }
        } else {
          // Middle: 1 ... 3 4 5 ... 10
          pageNumbers.push(0);
          pageNumbers.push(-1);
          pageNumbers.push(page - 1);
          pageNumbers.push(page);
          pageNumbers.push(page + 1);
          pageNumbers.push(-2);
          pageNumbers.push(totalPages - 1);
        }
      }

      return pageNumbers;
    };

    const pageNumbers = getPageNumbers();

    if (totalResults === 0) {
      return null;
    }

    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
        <Button
          label="Previous"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page > 0 && !isLoading) {
              handleChangePage(page - 1);
            }
          }}
          disabled={page === 0 || isLoading}
          leftIcon={<KeyboardArrowLeftIcon />}
        />

        {pageNumbers.map((pageNum, index) => {
          if (pageNum === -1 || pageNum === -2) {
            return (
              <Box
                key={`ellipsis-${index}`}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mx: 1,
                }}
              >
                <Typography style="body2" color={colors.grey[500]}>
                  ...
                </Typography>
              </Box>
            );
          }

          return (
            <Box key={pageNum} sx={{ mx: 0.5 }}>
              <Button
                label={(pageNum + 1).toString()}
                style={page === pageNum ? "filled" : "ghost"}
                color="grey"
                prominence={false}
                onClick={() => !isLoading && handleChangePage(pageNum)}
                disabled={isLoading}
              />
            </Box>
          );
        })}

        <Button
          label="Next"
          style="ghost"
          color="grey"
          onClick={() => {
            if (page < totalPages - 1 && !isLoading) {
              handleChangePage(page + 1);
            }
          }}
          disabled={page >= totalPages - 1 || !nextPageToken || isLoading}
          rightIcon={<KeyboardArrowRightIcon />}
        />
      </Box>
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = new Set<string>(data?.properties?.map((property: Property) => property.id) || []);
      setSelectedProperties(newSelected);
    } else {
      setSelectedProperties(new Set<string>());
    }
  };

  const handleSelectProperty = (propertyId: string) => {
    const newSelected = new Set(selectedProperties);
    if (newSelected.has(propertyId)) {
      newSelected.delete(propertyId);
    } else {
      newSelected.add(propertyId);
    }
    setSelectedProperties(newSelected);
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "white",
        borderRadius: 2,
        boxShadow: "none",
        overflow: "hidden",
        border: `1px solid ${colors.grey[200]}`,
      }}
    >
      <Box
        sx={{
          px: "32px",
          py: "24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography style="caps1" color={colors.grey[900]}>
          RESULTS
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box sx={{ mr: 3, display: "flex", alignItems: "center" }}>
            <Button
              label="Table Sort"
              size="small"
              style={isSortMenuOpen ? "filled" : "ghost"}
              color="grey"
              prominence={isSortMenuOpen ? true : false}
              leftIcon={<SwapVertIcon />}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={handleSortMenuOpen}
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Button
              label="Export to CSV"
              size="small"
              style={selectedProperties.size > 0 ? "filled" : "ghost"}
              color="grey"
              prominence={selectedProperties.size > 0}
              rightIcon={<KeyboardArrowDownIcon />}
              onClick={(e) => {
                const menuAnchor = e.currentTarget;
                setExportMenuAnchor(menuAnchor);
              }}
            />
            <Menu
              anchorEl={exportMenuAnchor}
              open={Boolean(exportMenuAnchor)}
              onClose={() => setExportMenuAnchor(null)}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: 2,
                  border: `1px solid ${colors.grey[200]}`,
                  boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
                },
              }}
            >
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M20.1673 4.74825L9.70815 15.2166L5.82148 11.3299L7.11398 10.0374L9.70815 12.6316L18.8748 3.46492L20.1673 4.74825ZM18.1415 9.36825C18.2607 9.89075 18.334 10.4408 18.334 10.9999C18.334 15.0516 15.0523 18.3333 11.0007 18.3333C6.94898 18.3333 3.66732 15.0516 3.66732 10.9999C3.66732 6.94825 6.94898 3.66659 11.0007 3.66659C12.449 3.66659 13.7873 4.08825 14.924 4.81242L16.244 3.49242C14.759 2.44742 12.9532 1.83325 11.0007 1.83325C5.94065 1.83325 1.83398 5.93992 1.83398 10.9999C1.83398 16.0599 5.94065 20.1666 11.0007 20.1666C16.0607 20.1666 20.1673 16.0599 20.1673 10.9999C20.1673 9.90909 19.9657 8.86408 19.6173 7.89242L18.1415 9.36825Z" fill="#364153" />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    Selected Results ({selectedProperties.size})
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setExportMenuAnchor(null);
                  setIsExportModalOpen(true);
                  setSelectedProperties(new Set());
                }}
                sx={{
                  px: 3,
                  py: 2,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  "&:hover": {
                    backgroundColor: colors.grey[50],
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                    <path d="M6.42183 5.50008H15.5885L10.996 11.2751L6.42183 5.50008ZM3.901 5.14258C5.75267 7.51675 9.17183 11.9167 9.17183 11.9167V17.4167C9.17183 17.9209 9.58433 18.3334 10.0885 18.3334H11.9218C12.426 18.3334 12.8385 17.9209 12.8385 17.4167V11.9167C12.8385 11.9167 16.2485 7.51675 18.1002 5.14258C18.5677 4.53758 18.1368 3.66675 17.376 3.66675H4.62517C3.86433 3.66675 3.4335 4.53758 3.901 5.14258Z" fill="#4A5565" />
                  </svg>
                  <Typography style="body2" color={colors.grey[900]}>
                    {currentSearchState.searchParams.query ? "All Search Results" : "All Results"}
                  </Typography>
                </Box>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      </Box>

      <TableContainer
        component={Box}
        sx={{
          flexGrow: 1,
          width: "100%",
          overflowY: "auto",
          overflowX: "auto",
          px: "24px",
          height: "calc(100vh - 300px)",
        }}
      >
        {isLoading ? (
          <TableSkeleton tableType="Property" />
        ) : isError ? (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              p: 4,
            }}
          >
            <Box
              sx={{
                bgcolor: colors.rose[50],
                color: colors.rose[700],
                p: 4,
                borderRadius: 1,
              }}
            >
              Error: {error?.message || "An error occurred while fetching properties"}
            </Box>
          </Box>
        ) : (
          <Table
            stickyHeader
            aria-label="properties table"
            sx={{
              tableLayout: "fixed",
              width: "100%",
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" />
                <TableCell>Type</TableCell>
                <TableCell>Number</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Storage Location</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data?.properties && data.properties.length > 0 ? (
                data.properties.map((property: Property) => (
                  <TableRow
                    key={property.id}
                    hover
                    onClick={() => handlePropertyClick(property)}
                    sx={{ cursor: "pointer", height: "54px" }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedProperties.has(property.id)}
                        onChange={(event) => {
                          event.stopPropagation();
                          handleSelectProperty(property.id);
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "150px"
                        }}
                      >
                        {getPropertyType(property)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "100px"
                        }}
                      >
                        {getPropertyNumber(property)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "150px"
                        }}
                      >
                        {getPropertyCategory(property)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "200px"
                        }}
                      >
                        {getPropertyDescription(property)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "100px"
                        }}
                      >
                        {getPropertyValue(property)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "150px"
                        }}
                      >
                        {getStorageLocation(property)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "100px"
                        }}
                      >
                        {formatCreatedDate(property.createTime)}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "100px"
                        }}
                      >
                        {getPropertyStatusDisplay(property.propertyStatus)}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow sx={{ height: 300 }}>
                  <TableCell colSpan={9} align="center" sx={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none' }}>
                    <Typography style="body1" color={colors.grey[600]}>
                      {data
                        ? "No properties found. Try adjusting your filters."
                        : "Use the search and filter options to find properties."}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </TableContainer>

      {(totalPages > 1 || totalResults > 0) && <CustomPagination />}

      <Menu
        anchorEl={sortMenuAnchor}
        open={isSortMenuOpen}
        onClose={handleSortMenuClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 200,
            borderRadius: 2,
            border: `1px solid ${colors.grey[200]}`,
            boxShadow: "0px 4px 16px rgba(0, 0, 0, 0.1)",
          },
        }}
      >
        {PROPERTY_SORT_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => handleSortSelect(option.value)}
            sx={{
              px: 3,
              py: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              "&:hover": {
                backgroundColor: colors.grey[50],
              },
            }}
          >
            <Typography
              style={selectedSort === option.value ? "body1" : "body2"}
              color={
                selectedSort === option.value
                  ? colors.grey[900]
                  : colors.grey[500]
              }
            >
              {option.label}
            </Typography>
            {selectedSort === option.value && (
              <CheckIcon
                sx={{
                  color: colors.blue[600],
                  fontSize: 20,
                  ml: 2,
                }}
              />
            )}
          </MenuItem>
        ))}
      </Menu>

      <ExportModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        columns={0} // Properties don't have a schema, so no fixed columns
        rows={selectedProperties.size > 0 ? selectedProperties.size : (allFetchedResults.length || 0)}
        fileSize={`${Math.round((selectedProperties.size > 0 ? selectedProperties.size : (allFetchedResults.length || 0)) * 0.5)} KB`}
        csvData={handleDownloadCsv().data}
        csvHeaders={handleDownloadCsv().headers}
        isLoading={isExportDataLoading || isFetchingAll}
        filename="property_export.csv"
      />
    </Box>
  );
};
