import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import SearchIcon from "@mui/icons-material/Search";
import { Box, Divider, IconButton, InputBase, ListItemText, Menu, MenuItem } from "@mui/material";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button } from "../../../design-system/components/Button";
import { Checkbox } from "../../../design-system/components/Checkbox";
import { colors } from "../../../design-system/tokens";

interface SearchField {
  id: string;
  label: string;
}

interface SearchBarProps {
  searchValue: string;
  setSearchValue: (value: string) => void;
  handleSearch: () => void;
  availableSearchFields: SearchField[];
  selectedSearchFields: string[];
  onSearchFieldsChange: (fields: string[]) => void;
}

const SearchBarComponent = ({
  searchValue,
  setSearchValue,
  handleSearch,
  availableSearchFields,
  selectedSearchFields,
  onSearchFieldsChange,
}: SearchBarProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const selectAllCheckboxRef = useRef<HTMLInputElement>(null);

  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleFieldToggle = useCallback((fieldId: string) => {
    const currentIndex = selectedSearchFields.indexOf(fieldId);
    const newSelectedFields = [...selectedSearchFields];

    if (currentIndex === -1) {
      newSelectedFields.push(fieldId);
    } else {
      newSelectedFields.splice(currentIndex, 1);
    }

    onSearchFieldsChange(newSelectedFields);
  }, [selectedSearchFields, onSearchFieldsChange]);

  const handleSelectAllToggle = useCallback(() => {
    if (selectedSearchFields.length === availableSearchFields.length) {
      // If all are selected, deselect all
      onSearchFieldsChange([]);
    } else {
      // Otherwise, select all
      const allFieldIds = availableSearchFields.map(field => field.id);
      onSearchFieldsChange(allFieldIds);
    }
  }, [selectedSearchFields.length, availableSearchFields, onSearchFieldsChange]);

  const getSelectAllState = useCallback(() => {
    if (selectedSearchFields.length === 0) {
      return false; // No items selected
    } else if (selectedSearchFields.length === availableSearchFields.length) {
      return true; // All items selected
    } else {
      return 'indeterminate'; // Some items selected
    }
  }, [selectedSearchFields.length, availableSearchFields.length]);

  // Update indeterminate state of the select all checkbox
  useEffect(() => {
    if (selectAllCheckboxRef.current) {
      selectAllCheckboxRef.current.indeterminate = getSelectAllState() === 'indeterminate';
    }
  }, [getSelectAllState]);

  // Get button label based on selection - memoized to prevent recalculation
  const buttonLabel = useMemo(() => {
    if (selectedSearchFields.length === 0 || selectedSearchFields.length === availableSearchFields.length) {
      return "Search Across: All Fields";
    } else {
      const selectedFieldLabels = selectedSearchFields
        .map(fieldId => availableSearchFields.find(f => f.id === fieldId)?.label)
        .filter(Boolean);

      if (selectedFieldLabels.length <= 3) {
        return `Search Across: ${selectedFieldLabels.join(", ")}`;
      } else {
        const firstThree = selectedFieldLabels.slice(0, 3);
        const remaining = selectedFieldLabels.length - 3;
        return `Search Across: ${firstThree.join(", ")} +${remaining}`;
      }
    }
  }, [selectedSearchFields, availableSearchFields]);

  return (
    <Box
      sx={{
        position: "relative",
        display: "flex",
        alignItems: "center",
        bgcolor: "white",
        border: `1px solid ${colors.grey[200]}`,
        borderRadius: "12px",
      }}
    >
      <InputBase
        sx={{
          flex: 1,
          fontSize: "16px",
          pl: 2,
          fontFamily: "Roboto, sans-serif",
        }}
        placeholder="Type to search..."
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            handleSearch();
          }
        }}
      />

      {/* Field Selector Button */}
      <Box sx={{ px: 1 }}>
        <Button
          label={buttonLabel}
          size="small"
          style={open ? "outline" : "filled"}
          color="blue"
          prominence={open ? true : false}
          rightIcon={<KeyboardArrowDownIcon />}
          onClick={handleClick}
        />
      </Box>

      {/* Field Selection Menu */}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'search-fields-button',
        }}
        PaperProps={{
          sx: {
            maxHeight: 300,
            minWidth: 200,
          }
        }}
        // Anchor to the bottom left
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {/* Select All checkbox */}
        <MenuItem
          onClick={(e) => {
            // Only toggle if the click didn't come from within the checkbox
            if (!(e.target as HTMLElement).closest('label')) {
              handleSelectAllToggle();
            }
          }}
          sx={{
            padding: '8px 16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontWeight: 500,
          }}
        >
          <Checkbox
            checked={getSelectAllState() === true}
            size="small"
            onChange={(e) => {
              e.stopPropagation();
              handleSelectAllToggle();
            }}
            ref={selectAllCheckboxRef}
          />
          <ListItemText
            primary="Select All"
            sx={{ margin: 0 }}
          />
        </MenuItem>
        <Divider sx={{ my: 1 }} />

        {availableSearchFields.map((field) => (
          <MenuItem
            key={field.id}
            onClick={(e) => {
              // Only toggle if the click didn't come from within the checkbox
              if (!(e.target as HTMLElement).closest('label')) {
                handleFieldToggle(field.id);
              }
            }}
            sx={{
              padding: '8px 16px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <Checkbox
              checked={selectedSearchFields.includes(field.id)}
              size="small"
              onChange={(e) => {
                e.stopPropagation();
                handleFieldToggle(field.id);
              }}
            />
            <ListItemText
              primary={field.label}
              sx={{ margin: 0 }}
            />
          </MenuItem>
        ))}
      </Menu>

      <IconButton
        sx={{
          py: "12px",
          px: "16px",
          backgroundColor: colors.blue[600],
          color: "white",
          borderRadius: "0px",
          borderTopRightRadius: "11px",
          borderBottomRightRadius: "11px",
          "&:hover": {
            backgroundColor: colors.blue[700],
          },
        }}
        onClick={handleSearch}
        aria-label="search"
      >
        <SearchIcon />
      </IconButton>
    </Box>
  );
};

// Export the component directly - removed unnecessary React.memo
export const SearchBar = SearchBarComponent; 