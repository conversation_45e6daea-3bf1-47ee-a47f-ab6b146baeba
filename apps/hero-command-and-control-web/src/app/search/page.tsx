"use client";

import { Box } from "@mui/material";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import {
  CaseStatus,
  CaseType
} from "proto/hero/cases/v1/cases_pb";
import {
  SearchOrderBy as EntitySearchOrderBy,
  RecordStatus
} from "proto/hero/entity/v1/entity_pb";
import {
  ReportStatus,
  SearchOrderBy as ReportsSearchOrderBy,
} from "proto/hero/reports/v2/reports_pb";
import {
  SearchOrderBy,
  SituationStatus,
  SituationType,
  TriggerSource,
} from "proto/hero/situations/v2/situations_pb";
import { useCallback, useEffect, useRef, useState } from "react";
import { Typography } from "../../design-system/components/Typography";
import { colors } from "../../design-system/tokens";
import { useSearchCases } from "../apis/services/workflow/cases/hooks";
import { useSearchEntities } from "../apis/services/workflow/entity/hooks";
import { useSearchProperties } from "../apis/services/workflow/property/hooks";

import { SearchOrderBy as PropertySearchOrderBy, SearchPropertiesRequest } from "../apis/services/workflow/property/types";
import { useSearchReports } from "../apis/services/workflow/reports/v2/hooks";
import { useSearchSituations } from "../apis/services/workflow/situations/hooks";
import { useBreadcrumbs } from "../contexts/Breadcrumb/BreadcrumbContext";
import { useBreadcrumbHeader } from "../hooks/useBreadcrumbHeader";
import { useRecentSearchTracker } from "../hooks/useRecentSearchTracker";
import { CasesResultsTable } from "./components/CasesSearchComponents/CasesResultsTable";
import { CasesSearchFilters } from "./components/CasesSearchComponents/CasesSearchFilters";
import { IncidentResultsTable } from "./components/IncidentSearchComponents/IncidentResultsTable";
import { IncidentSearchFilters } from "./components/IncidentSearchComponents/IncidentSearchFilters";
import { OrganizationResultsTable } from "./components/OrganizationSearchComponents/OrganizationResultsTable";
import { OrganizationSearchFilters } from "./components/OrganizationSearchComponents/OrganizationSearchFilters";
import { PeopleResultsTable } from "./components/PeopleSearchComponents/PeopleResultsTable";
import { PeopleSearchFilters } from "./components/PeopleSearchComponents/PeopleSearchFilters";
import { PropertyResultsTable } from "./components/PropertySearchComponents/PropertyResultsTable";
import { PropertySearchFilters } from "./components/PropertySearchComponents/PropertySearchFilters";
import { ReportsResultsTable } from "./components/ReportSearchComponents/ReportsResultsTable";
import { ReportsSearchFilters } from "./components/ReportSearchComponents/ReportsSearchFilters";
import { SearchBar } from "./components/SearchBar";
import { TabSelector } from "./components/TabSelector";
import { VehiclesResultsTable } from "./components/VehiclesSearchComponents/VehiclesResultsTable";
import { VehiclesSearchFilters } from "./components/VehiclesSearchComponents/VehiclesSearchFilters";
import { useMultiTabSearch } from "./hooks/useMultiTabSearch";
import {
  CasesFilters,
  EntityFilters,
  IncidentFilters,
  ReportsFilters,
  buildEntitySearchParams,
  buildIncidentSearchParams,
  buildReportsSearchParams,
} from "./utils/searchHandlers";
import {
  SearchState,
  clearSearchState,
  createURLSearchParams,
  parseURLSearchParams,
  restoreSearchState,
  shouldRestoreSearchState,
} from "./utils/searchStateManager";
import {
  getEntitySortOrderBy,
  getIncidentSortOrderBy,
  getReportsSortOrderBy,
} from "./utils/sortUtils";

const SituationSearch = () => {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  const { trackSearch } = useRecentSearchTracker();
  const { updateBreadcrumb } = useBreadcrumbs();

  useBreadcrumbHeader({
    id: "search",
    label: "Search",
    path: "/search",
  });

  // Initialize the multi-tab search hook
  const {
    activeTab,
    searchValue,
    page,
    rowsPerPage,
    selectedSort,
    currentTabConfig,
    casesSearchParams,
    incidentSearchParams,
    reportsSearchParams,
    peopleSearchParams,
    vehiclesSearchParams,
    propertySearchParams,
    organizationSearchParams,
    setSearchValue,
    setCasesSearchParams,
    setIncidentSearchParams,
    setReportsSearchParams,
    setPeopleSearchParams,
    setVehiclesSearchParams,
    setPropertySearchParams,
    setOrganizationSearchParams,
    handleTabChange: originalHandleTabChange,
    handleSortChange,
    handleChangePage,
    handleChangeRowsPerPage: originalHandleChangeRowsPerPage,
    handleSearch,
  } = useMultiTabSearch({
    initialTab: "Cases",
    onSearchParamsChange: (tabId, params) => {
      // Update the appropriate search params when they change
      if (tabId === "Cases") {
        setCasesSearchParams(params);
      } else if (tabId === "Incidents") {
        setIncidentSearchParams(params);
      } else if (tabId === "Reports") {
        setReportsSearchParams(params);
      } else if (tabId === "People") {
        setPeopleSearchParams(params);
      } else if (tabId === "Vehicles") {
        setVehiclesSearchParams(params);
      } else if (tabId === "Property") {
        setPropertySearchParams(params);
      } else if (tabId === "Organizations") {
        setOrganizationSearchParams(params);
      }
    },
  });

  // Wrapper that handles both event and number inputs
  const handleChangeRowsPerPage = (input: React.ChangeEvent<HTMLInputElement> | number) => {
    const newRowsPerPage = typeof input === 'number' ? input : parseInt(input.target.value, 10);
    const syntheticEvent = {
      target: { value: newRowsPerPage.toString() }
    } as React.ChangeEvent<HTMLInputElement>;
    originalHandleChangeRowsPerPage(syntheticEvent);
  };

  // Individual filter states (still needed for the filter components)
  const [selectedStatuses, setSelectedStatuses] = useState<SituationStatus[]>(
    []
  );
  const [selectedTypes, setSelectedTypes] = useState<SituationType[]>([]);
  const [selectedTriggerSources, setSelectedTriggerSources] = useState<
    TriggerSource[]
  >([]);
  const [selectedPriorities, setSelectedPriorities] = useState<number[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedSearchFields, setSelectedSearchFields] = useState<string[]>(
    []
  );
  const [fieldQueries, setFieldQueries] = useState<
    { field: string; query: string }[]
  >([]);
  const [dateFilters, setDateFilters] = useState<{
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
    incidentTime?: { from: string; to: string };
    resolvedTime?: { from: string; to: string };
    dueTime?: { from: string; to: string };
  }>({});

  // Cases filter states
  const [selectedCaseStatuses, setSelectedCaseStatuses] = useState<CaseStatus[]>([]);
  const [selectedCaseTypes, setSelectedCaseTypes] = useState<CaseType[]>([]);
  const [selectedCasePriorities, setSelectedCasePriorities] = useState<number[]>([]);
  const [selectedCaseTags, setSelectedCaseTags] = useState<string[]>([]);
  const [selectedCaseSearchFields, setSelectedCaseSearchFields] = useState<string[]>([]);
  const [caseFieldQueries, setCaseFieldQueries] = useState<{ field: string; query: string }[]>([]);
  const [casesDateFilters, setCasesDateFilters] = useState<{
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
    dueDate?: { from: string; to: string };
    resolvedTime?: { from: string; to: string };
    closeTime?: { from: string; to: string };
  }>({});

  // Reports filter states
  const [selectedReportStatuses, setSelectedReportStatuses] = useState<
    ReportStatus[]
  >([]);
  const [selectedReportsSearchFields, setSelectedReportsSearchFields] =
    useState<string[]>([]);
  const [selectedReportTypes, setSelectedReportTypes] = useState<any[]>([]);
  const [reportsDateFilters, setReportsDateFilters] = useState<{
    createdAt?: { from: string; to: string };
    updatedAt?: { from: string; to: string };
    assignedAt?: { from: string; to: string };
    completedAt?: { from: string; to: string };
  }>({});

  // Entity filter states (shared across People, Vehicles, Property)
  const [selectedEntityStatuses, setSelectedEntityStatuses] = useState<
    RecordStatus[]
  >([]);
  const [selectedEntityTags, setSelectedEntityTags] = useState<string[]>([]);
  const [selectedEntitySearchFields, setSelectedEntitySearchFields] = useState<
    string[]
  >([]);
  const [entityFieldQueries, setEntityFieldQueries] = useState<
    { field: string; query: string }[]
  >([]);
  const [entityDateFilters, setEntityDateFilters] = useState<{
    createTime?: { from: string; to: string };
    updateTime?: { from: string; to: string };
  }>({});

  const [totalItems, setTotalItems] = useState(0);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Function to reset all filters to their default state
  const resetAllFilters = () => {
    // Reset cases filters
    setSelectedCaseStatuses([]);
    setSelectedCaseTypes([]);
    setSelectedCasePriorities([]);
    setSelectedCaseTags([]);
    setSelectedCaseSearchFields([]);
    setCaseFieldQueries([]);
    setCasesDateFilters({});

    // Reset incident filters
    setSelectedStatuses([]);
    setSelectedTypes([]);
    setSelectedTriggerSources([]);
    setSelectedPriorities([]);
    setSelectedTags([]);
    setSelectedSearchFields([]);
    setFieldQueries([]);
    setDateFilters({});

    // Reset reports filters
    setSelectedReportStatuses([]);
    setSelectedReportsSearchFields([]);
    setSelectedReportTypes([]);
    setReportsDateFilters({});

    // Reset entity filters
    setSelectedEntityStatuses([]);
    setSelectedEntityTags([]);
    setSelectedEntitySearchFields([]);
    setEntityFieldQueries([]);
    setEntityDateFilters({});

    // Reset search value
    setSearchValue("");

    // Let the useMultiTabSearch hook handle search parameter resets
    // to prevent double API calls
  };

  // Custom tab change handler that resets filters
  const handleTabChange = (newTab: string) => {
    // Call the original tab change handler without resetting filters
    // Each tab maintains its own filter state
    originalHandleTabChange(newTab);
  };

  // API hooks - removed unnecessary memoization, let the hooks handle their own optimization
  const { data, isLoading, isError, error } = useSearchSituations(
    incidentSearchParams as any,
    {
      enabled: hasInitialized && activeTab === "Incidents",
      refetchOnWindowFocus: false,
    }
  );

  const {
    data: casesData,
    isLoading: casesIsLoading,
    isError: casesIsError,
    error: casesError,
  } = useSearchCases(casesSearchParams as any, {
    enabled: hasInitialized && activeTab === "Cases",
    refetchOnWindowFocus: false,
  });

  const {
    data: reportsData,
    isLoading: reportsIsLoading,
    isError: reportsIsError,
    error: reportsError,
  } = useSearchReports(reportsSearchParams as any, {
    enabled: hasInitialized && activeTab === "Reports",
    refetchOnWindowFocus: false,
  });

  // Entity API hooks - removed unnecessary memoization
  const {
    data: peopleData,
    isLoading: peopleIsLoading,
    isError: peopleIsError,
    error: peopleError,
  } = useSearchEntities(
    peopleSearchParams as any,
    {
      enabled: hasInitialized && activeTab === "People",
      refetchOnWindowFocus: false,
    } as any
  );

  const {
    data: vehiclesData,
    isLoading: vehiclesIsLoading,
    isError: vehiclesIsError,
    error: vehiclesError,
  } = useSearchEntities(
    vehiclesSearchParams as any,
    {
      enabled: hasInitialized && activeTab === "Vehicles",
      refetchOnWindowFocus: false,
    } as any
  );

  const {
    data: propertyData,
    isLoading: propertyIsLoading,
    isError: propertyIsError,
    error: propertyError,
  } = useSearchProperties(
    propertySearchParams as any,
    {
      enabled: hasInitialized && activeTab === "Property",
    }
  );

  const {
    data: organizationData,
    isLoading: organizationIsLoading,
    isError: organizationIsError,
    error: organizationError,
  } = useSearchEntities(
    organizationSearchParams as any,
    {
      enabled: hasInitialized && activeTab === "Organizations",
      refetchOnWindowFocus: false,
    } as any
  );

  // Initialize state from URL parameters and session storage on component mount
  useEffect(() => {
    if (hasInitialized) return;

    setIsRestoring(true);
    let shouldClearSessionState = true;

    // Check if we should restore from session storage (coming back from incident)
    if (shouldRestoreSearchState()) {
      const savedState = restoreSearchState();
      if (savedState) {
        console.log("Restoring from session storage:", savedState);

        // Restore all state from session storage directly (without handlers to avoid side effects)
        setSearchValue(savedState.searchValue);

        // Restore incident-specific state
        setIncidentSearchParams(savedState.searchParams);
        setSelectedStatuses(savedState.selectedStatuses || []);
        setSelectedTypes(savedState.selectedTypes || []);
        setSelectedTriggerSources(savedState.selectedTriggerSources || []);
        setSelectedPriorities(savedState.selectedPriorities || []);
        setSelectedTags(savedState.selectedTags || []);
        setSelectedSearchFields(savedState.selectedSearchFields || []);
        setFieldQueries(savedState.fieldQueries || []);
        setDateFilters(savedState.dateFilters || {});

        // Restore reports-specific state
        if (savedState.reportsSearchParams) {
          setReportsSearchParams(savedState.reportsSearchParams);
        }
        if (savedState.selectedReportStatuses) {
          setSelectedReportStatuses(savedState.selectedReportStatuses);
        }
        if (savedState.selectedReportsSearchFields) {
          setSelectedReportsSearchFields(
            savedState.selectedReportsSearchFields
          );
        }
        if (savedState.selectedReportTypes) {
          setSelectedReportTypes(savedState.selectedReportTypes);
        }
        if (savedState.reportsDateFilters) {
          setReportsDateFilters(savedState.reportsDateFilters);
        }

        // Restore entity-specific state
        if (savedState.peopleSearchParams) {
          setPeopleSearchParams(savedState.peopleSearchParams);
        }
        if (savedState.vehiclesSearchParams) {
          setVehiclesSearchParams(savedState.vehiclesSearchParams);
        }
        if (savedState.propertySearchParams) {
          // Convert EntitySearchParams to SearchPropertiesRequest
          const propertyParams: SearchPropertiesRequest = {
            query: savedState.propertySearchParams.query,
            pageSize: savedState.propertySearchParams.pageSize,
            orderBy: PropertySearchOrderBy.SEARCH_ORDER_BY_CREATED_AT, // Use property-specific order by
            pageToken: savedState.propertySearchParams.pageToken,
          };
          setPropertySearchParams(propertyParams);
        }
        if (savedState.organizationSearchParams) {
          setOrganizationSearchParams(savedState.organizationSearchParams);
        }
        if (savedState.selectedEntityStatuses) {
          setSelectedEntityStatuses(savedState.selectedEntityStatuses);
        }
        if (savedState.selectedEntityTags) {
          setSelectedEntityTags(savedState.selectedEntityTags);
        }
        if (savedState.selectedEntitySearchFields) {
          setSelectedEntitySearchFields(savedState.selectedEntitySearchFields);
        }
        if (savedState.entityFieldQueries) {
          setEntityFieldQueries(savedState.entityFieldQueries);
        }
        if (savedState.entityDateFilters) {
          setEntityDateFilters(savedState.entityDateFilters);
        }

        setHasInitialized(true);
        shouldClearSessionState = false;

        // Store the session state for the separate effect to handle tab/sort/pagination
        sessionStorage.setItem(
          "temp_restoration_state",
          JSON.stringify(savedState)
        );

        setIsRestoring(false);
        return;
      }
    }

    // No saved state, try to parse from URL parameters
    if (urlSearchParams) {
      const urlState = parseURLSearchParams(urlSearchParams);

      if (Object.keys(urlState).length > 0) {
        // Apply URL state directly (without handlers to avoid side effects)
        if (urlState.searchValue) setSearchValue(urlState.searchValue);

        // Handle incident-specific URL state restoration
        if (urlState.selectedTags) setSelectedTags(urlState.selectedTags);
        if (urlState.selectedPriorities)
          setSelectedPriorities(urlState.selectedPriorities);
        if (urlState.selectedStatuses)
          setSelectedStatuses(urlState.selectedStatuses);
        if (urlState.selectedTypes) setSelectedTypes(urlState.selectedTypes);
        if (urlState.selectedTriggerSources)
          setSelectedTriggerSources(urlState.selectedTriggerSources);
        if (urlState.selectedSearchFields)
          setSelectedSearchFields(urlState.selectedSearchFields);
        if (urlState.fieldQueries) setFieldQueries(urlState.fieldQueries);
        if (urlState.dateFilters) setDateFilters(urlState.dateFilters);

        // Handle reports-specific URL state restoration
        if (urlState.selectedReportStatuses)
          setSelectedReportStatuses(urlState.selectedReportStatuses);
        if (urlState.selectedReportsSearchFields)
          setSelectedReportsSearchFields(urlState.selectedReportsSearchFields);
        if (urlState.selectedReportTypes)
          setSelectedReportTypes(urlState.selectedReportTypes);
        if (urlState.reportsDateFilters)
          setReportsDateFilters(urlState.reportsDateFilters);

        // Handle entity-specific URL state restoration
        if (urlState.selectedEntityStatuses)
          setSelectedEntityStatuses(urlState.selectedEntityStatuses);
        if (urlState.selectedEntityTags)
          setSelectedEntityTags(urlState.selectedEntityTags);
        if (urlState.selectedEntitySearchFields)
          setSelectedEntitySearchFields(urlState.selectedEntitySearchFields);
        if (urlState.entityFieldQueries)
          setEntityFieldQueries(urlState.entityFieldQueries);
        if (urlState.entityDateFilters)
          setEntityDateFilters(urlState.entityDateFilters);

        // Build search params with filters for the active tab
        if (urlState.activeTab === "Reports") {
          // Build reports search params with filters
          const reportFilters: ReportsFilters = {
            selectedStatuses: urlState.selectedReportStatuses || [],
            selectedSearchFields: urlState.selectedReportsSearchFields || [],
            selectedTypes: urlState.selectedReportTypes || [],
            dateFilters: urlState.reportsDateFilters || {},
          };

          const baseParams = {
            query: urlState.searchValue || "",
            pageSize: urlState.rowsPerPage || 5,
            orderBy: urlState.selectedSort
              ? getReportsSortOrderBy(urlState.selectedSort)
              : ReportsSearchOrderBy.CREATED_AT,
            ascending: false,
          };

          const newParams = buildReportsSearchParams(
            baseParams,
            urlState.searchValue || "",
            reportFilters
          );
          setReportsSearchParams(newParams);
        } else if (
          ["People", "Vehicles", "Property", "Organizations"].includes(urlState.activeTab || "")
        ) {
          // Build entity search params with filters
          const entityFilters: EntityFilters = {
            selectedStatuses: urlState.selectedEntityStatuses || [],
            selectedTags: urlState.selectedEntityTags || [],
            selectedSearchFields: urlState.selectedEntitySearchFields || [],
            fieldQueries: urlState.entityFieldQueries || [],
            dateFilters: urlState.entityDateFilters || {},
          };

          const baseParams = {
            query: urlState.searchValue || "",
            pageSize: urlState.rowsPerPage || 5,
            orderBy: urlState.selectedSort
              ? getEntitySortOrderBy(urlState.selectedSort)
              : EntitySearchOrderBy.CREATED_AT,
            ascending: false,
          };

          let entityType;
          if (urlState.activeTab === "People") {
            entityType = "ENTITY_TYPE_PERSON";
          } else if (urlState.activeTab === "Vehicles") {
            entityType = "ENTITY_TYPE_VEHICLE";
          } else if (urlState.activeTab === "Property") {
            // Property tab uses Property service, not Entity service
            const propertyParams: SearchPropertiesRequest = {
              query: urlState.searchValue || "",
              pageSize: urlState.rowsPerPage || 5,
              orderBy: PropertySearchOrderBy.SEARCH_ORDER_BY_CREATED_AT,
              pageToken: undefined,
            };
            setPropertySearchParams(propertyParams);
          } else if (urlState.activeTab === "Organizations") {
            entityType = "ENTITY_TYPE_ORGANIZATION";
          }

          if (urlState.activeTab === "People") {
            const newParams = buildEntitySearchParams(
              baseParams,
              urlState.searchValue || "",
              entityFilters,
              entityType
            );
            setPeopleSearchParams(newParams);
          } else if (urlState.activeTab === "Vehicles") {
            const newParams = buildEntitySearchParams(
              baseParams,
              urlState.searchValue || "",
              entityFilters,
              entityType
            );
            setVehiclesSearchParams(newParams);
          } else if (urlState.activeTab === "Organizations") {
            const newParams = buildEntitySearchParams(
              baseParams,
              urlState.searchValue || "",
              entityFilters,
              entityType
            );
            setOrganizationSearchParams(newParams);
          }
        } else {
          // Build incident search params with filters
          const incidentFilters: IncidentFilters = {
            selectedStatuses: urlState.selectedStatuses || [],
            selectedTypes: urlState.selectedTypes || [],
            selectedTriggerSources: urlState.selectedTriggerSources || [],
            selectedPriorities: urlState.selectedPriorities || [],
            selectedTags: urlState.selectedTags || [],
            selectedSearchFields: urlState.selectedSearchFields || [],
            fieldQueries: urlState.fieldQueries || [],
            dateFilters: urlState.dateFilters || {},
          };

          const baseParams = {
            query: urlState.searchValue || "",
            pageSize: urlState.rowsPerPage || 5,
            orderBy: urlState.selectedSort
              ? getIncidentSortOrderBy(urlState.selectedSort)
              : SearchOrderBy.CREATE_TIME,
            ascending: false,
          };

          const newParams = buildIncidentSearchParams(
            baseParams,
            urlState.searchValue || "",
            incidentFilters
          );
          setIncidentSearchParams(newParams);
        }

        shouldClearSessionState = false;
      }
    }

    if (shouldClearSessionState) {
      clearSearchState();
    }
    setHasInitialized(true);

    setIsRestoring(false);
  }, [urlSearchParams, hasInitialized]);

  // Separate effect to restore multi-tab search hook state after basic state is restored
  useEffect(() => {
    if (!hasInitialized) return;

    // Check if we have session state to restore (from breadcrumb navigation)
    const tempSessionState = sessionStorage.getItem("temp_restoration_state");
    if (tempSessionState) {
      try {
        const savedState = JSON.parse(tempSessionState);
        console.log("Restoring tab/sort/pagination from session:", savedState);

        // Restore multi-tab search hook state from session storage
        if (savedState.activeTab) {
          originalHandleTabChange(savedState.activeTab);
        }
        if (savedState.selectedSort) {
          handleSortChange(savedState.selectedSort);
        }
        if (savedState.page !== undefined) {
          handleChangePage(savedState.page);
        }
        if (savedState.rowsPerPage) {
          handleChangeRowsPerPage(savedState.rowsPerPage);
        }

        // Clean up temp storage
        sessionStorage.removeItem("temp_restoration_state");

        // Auto-execute search for session restoration
        if (savedState.activeTab === "Reports") {
          const reportFilters: ReportsFilters = {
            selectedStatuses: savedState.selectedReportStatuses || [],
            selectedSearchFields: savedState.selectedReportsSearchFields || [],
            selectedTypes: savedState.selectedReportTypes || [],
            dateFilters: savedState.reportsDateFilters || {},
          };
          handleSearch(reportFilters);
        } else if (
          ["People", "Vehicles", "Property", "Organizations"].includes(savedState.activeTab)
        ) {
          const entityFilters: EntityFilters = {
            selectedStatuses: savedState.selectedEntityStatuses || [],
            selectedTags: savedState.selectedEntityTags || [],
            selectedSearchFields: savedState.selectedEntitySearchFields || [],
            fieldQueries: savedState.entityFieldQueries || [],
            dateFilters: savedState.entityDateFilters || {},
          };
          handleSearch(entityFilters);
        } else {
          const incidentFilters: IncidentFilters = {
            selectedStatuses: savedState.selectedStatuses || [],
            selectedTypes: savedState.selectedTypes || [],
            selectedTriggerSources: savedState.selectedTriggerSources || [],
            selectedPriorities: savedState.selectedPriorities || [],
            selectedTags: savedState.selectedTags || [],
            selectedSearchFields: savedState.selectedSearchFields || [],
            fieldQueries: savedState.fieldQueries || [],
            dateFilters: savedState.dateFilters || {},
          };
          handleSearch(incidentFilters);
        }
      } catch (error) {
        console.warn("Failed to parse temp session state:", error);
        sessionStorage.removeItem("temp_restoration_state");
      }
    } else if (urlSearchParams) {
      // Handle URL-based restoration
      const urlState = parseURLSearchParams(urlSearchParams);

      // Now safely use handlers for multi-tab search hook state
      if (urlState.activeTab) {
        originalHandleTabChange(urlState.activeTab);
      }
      if (urlState.selectedSort) {
        handleSortChange(urlState.selectedSort);
      }
      if (urlState.page !== undefined) {
        handleChangePage(urlState.page);
      }
      if (urlState.rowsPerPage) {
        handleChangeRowsPerPage(urlState.rowsPerPage);
      }

      // Auto-execute search for URL restoration if we have search parameters
      if (Object.keys(urlState).length > 0) {
        if (urlState.activeTab === "Reports") {
          const reportFilters: ReportsFilters = {
            selectedStatuses: urlState.selectedReportStatuses || [],
            selectedSearchFields: urlState.selectedReportsSearchFields || [],
            selectedTypes: urlState.selectedReportTypes || [],
            dateFilters: urlState.reportsDateFilters || {},
          };
          handleSearch(reportFilters);
        } else if (
          ["People", "Vehicles", "Property", "Organizations"].includes(urlState.activeTab || "")
        ) {
          const entityFilters: EntityFilters = {
            selectedStatuses: urlState.selectedEntityStatuses || [],
            selectedTags: urlState.selectedEntityTags || [],
            selectedSearchFields: urlState.selectedEntitySearchFields || [],
            fieldQueries: urlState.entityFieldQueries || [],
            dateFilters: urlState.entityDateFilters || {},
          };
          handleSearch(entityFilters);
        } else {
          const incidentFilters: IncidentFilters = {
            selectedStatuses: urlState.selectedStatuses || [],
            selectedTypes: urlState.selectedTypes || [],
            selectedTriggerSources: urlState.selectedTriggerSources || [],
            selectedPriorities: urlState.selectedPriorities || [],
            selectedTags: urlState.selectedTags || [],
            selectedSearchFields: urlState.selectedSearchFields || [],
            fieldQueries: urlState.fieldQueries || [],
            dateFilters: urlState.dateFilters || {},
          };
          handleSearch(incidentFilters);
        }
      }
    }

    setIsRestoring(false);
  }, [hasInitialized]); // Only run once after initialization

  // Update total items when we get data with total count
  useEffect(() => {
    if (activeTab === "Cases" && casesData) {
      setTotalItems(casesData.totalResults || 0);
    } else if (activeTab === "Incidents" && data) {
      setTotalItems(data.totalResults);
    } else if (activeTab === "Reports" && reportsData) {
      setTotalItems(reportsData.totalResults || 0);
    } else if (activeTab === "People" && peopleData) {
      setTotalItems(peopleData.totalResults || 0);
    } else if (activeTab === "Vehicles" && vehiclesData) {
      setTotalItems(vehiclesData.totalResults || 0);
    } else if (activeTab === "Property" && propertyData) {
      setTotalItems(propertyData.totalCount || 0);
    } else if (activeTab === "Organizations" && organizationData) {
      setTotalItems(organizationData.totalResults || 0);
    }
  }, [casesData, data, reportsData, peopleData, vehiclesData, propertyData, organizationData, activeTab]);

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / rowsPerPage));

  const updateURL = () => {
    const currentState = getCurrentSearchState();
    const urlParams = createURLSearchParams(currentState);
    const newURL = urlParams.toString()
      ? `/search?${urlParams.toString()}`
      : "/search";
    router.replace(newURL, { scroll: false });
    updateBreadcrumb("search", { path: newURL });
  };



  const getCurrentSearchState = (): SearchState => ({
    searchValue,
    activeTab,
    selectedSort,
    page,
    rowsPerPage,
    searchParams: incidentSearchParams,
    reportsSearchParams,
    peopleSearchParams,
    vehiclesSearchParams,
    propertySearchParams: propertySearchParams as any,
    organizationSearchParams,
    selectedStatuses,
    selectedTypes,
    selectedTriggerSources,
    selectedPriorities,
    selectedTags,
    selectedSearchFields,
    fieldQueries,
    dateFilters,
    selectedReportStatuses,
    selectedReportsSearchFields,
    selectedReportTypes,
    reportsDateFilters,
    selectedEntityStatuses,
    selectedEntityTags,
    selectedEntitySearchFields,
    entityFieldQueries,
    entityDateFilters,
  });

  // Track if we're in the middle of restoration to prevent URL updates
  const [isRestoring, setIsRestoring] = useState(false);

  // Create refs to access current state values without creating dependencies
  const stateRef = useRef({
    activeTab,
    searchValue,
    selectedCaseStatuses,
    selectedCaseTypes,
    selectedCasePriorities,
    selectedCaseTags,
    selectedCaseSearchFields,
    caseFieldQueries,
    casesDateFilters,
    selectedStatuses,
    selectedTypes,
    selectedTriggerSources,
    selectedPriorities,
    selectedTags,
    selectedSearchFields,
    fieldQueries,
    dateFilters,
    selectedReportStatuses,
    selectedReportsSearchFields,
    selectedReportTypes,
    reportsDateFilters,
    selectedEntityStatuses,
    selectedEntityTags,
    selectedEntitySearchFields,
    entityFieldQueries,
    entityDateFilters,
  });

  // Update refs when state changes
  useEffect(() => {
    stateRef.current = {
      activeTab,
      searchValue,
      selectedCaseStatuses,
      selectedCaseTypes,
      selectedCasePriorities,
      selectedCaseTags,
      selectedCaseSearchFields,
      caseFieldQueries,
      casesDateFilters,
      selectedStatuses,
      selectedTypes,
      selectedTriggerSources,
      selectedPriorities,
      selectedTags,
      selectedSearchFields,
      fieldQueries,
      dateFilters,
      selectedReportStatuses,
      selectedReportsSearchFields,
      selectedReportTypes,
      reportsDateFilters,
      selectedEntityStatuses,
      selectedEntityTags,
      selectedEntitySearchFields,
      entityFieldQueries,
      entityDateFilters,
    };
  });

  // Execute search with current filters - now uses refs to avoid circular dependencies
  const executeSearch = useCallback(() => {
    const currentState = stateRef.current;

    if (currentState.activeTab === "Cases") {
      const casesFilters: CasesFilters = {
        selectedStatuses: currentState.selectedCaseStatuses,
        selectedTypes: currentState.selectedCaseTypes,
        selectedPriorities: currentState.selectedCasePriorities,
        selectedTags: currentState.selectedCaseTags,
        selectedSearchFields: currentState.selectedCaseSearchFields,
        fieldQueries: currentState.caseFieldQueries,
        dateFilters: currentState.casesDateFilters,
      };
      handleSearch(casesFilters);
    } else if (currentState.activeTab === "Incidents") {
      const incidentFilters: IncidentFilters = {
        selectedStatuses: currentState.selectedStatuses,
        selectedTypes: currentState.selectedTypes,
        selectedTriggerSources: currentState.selectedTriggerSources,
        selectedPriorities: currentState.selectedPriorities,
        selectedTags: currentState.selectedTags,
        selectedSearchFields: currentState.selectedSearchFields,
        fieldQueries: currentState.fieldQueries,
        dateFilters: currentState.dateFilters,
      };
      handleSearch(incidentFilters);
    } else if (currentState.activeTab === "Reports") {
      const reportFilters: ReportsFilters = {
        selectedStatuses: currentState.selectedReportStatuses,
        selectedSearchFields: currentState.selectedReportsSearchFields,
        selectedTypes: currentState.selectedReportTypes,
        dateFilters: currentState.reportsDateFilters,
      };
      handleSearch(reportFilters);
    } else if (["People", "Vehicles", "Property", "Organizations"].includes(currentState.activeTab)) {
      const entityFilters: EntityFilters = {
        selectedStatuses: currentState.selectedEntityStatuses,
        selectedTags: currentState.selectedEntityTags,
        selectedSearchFields: currentState.selectedEntitySearchFields,
        fieldQueries: currentState.entityFieldQueries,
        dateFilters: currentState.entityDateFilters,
      };
      handleSearch(entityFilters);
    }

    // Update URL when search is executed
    updateURL();

    // Track search with the proper URL (construct it from state instead of window.location.href)
    const currentSearchState = getCurrentSearchState();
    const urlParams = createURLSearchParams(currentSearchState);
    const currentUrl = urlParams.toString()
      ? `${window.location.origin}/search?${urlParams.toString()}`
      : `${window.location.origin}/search`;
    trackSearch(currentSearchState, currentUrl);
  }, [handleSearch, updateURL, getCurrentSearchState, trackSearch]); // Only stable dependencies

  // Create a debounced search function to prevent excessive re-renders
  const debounceTimer = useRef<NodeJS.Timeout | undefined>(undefined);

  const debouncedExecuteSearch = useCallback(() => {
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      executeSearch();
    }, 300); // Increased debounce delay for better UX
  }, [executeSearch]);

  // Immediate search execution for user-initiated actions (Enter key, search button)
  const executeSearchImmediately = useCallback(() => {
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }
    executeSearch();
  }, [executeSearch]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  // Single effect to handle significant filter changes with debouncing
  // Note: Search field changes are excluded to prevent re-renders when user changes "search across" fields
  useEffect(() => {
    if (!hasInitialized || isRestoring) return;

    // Only trigger search for the active tab
    const shouldTriggerSearch =
      (activeTab === "Cases") ||
      (activeTab === "Incidents") ||
      (activeTab === "Reports") ||
      (["People", "Vehicles", "Property", "Organizations"].includes(activeTab));

    if (shouldTriggerSearch) {
      debouncedExecuteSearch();
    }

    // Cleanup timeout on unmount
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [
    // Cases filters (excluding selectedCaseSearchFields)
    selectedCaseStatuses,
    selectedCaseTypes,
    selectedCasePriorities,
    selectedCaseTags,
    caseFieldQueries,
    casesDateFilters,
    // Incident filters (excluding selectedSearchFields)
    selectedStatuses,
    selectedTypes,
    selectedTriggerSources,
    selectedPriorities,
    selectedTags,
    fieldQueries,
    dateFilters,
    // Reports filters (excluding selectedReportsSearchFields)
    selectedReportStatuses,
    selectedReportTypes,
    reportsDateFilters,
    // Entity filters (excluding selectedEntitySearchFields)
    selectedEntityStatuses,
    selectedEntityTags,
    entityFieldQueries,
    entityDateFilters,
    // Control flags
    hasInitialized,
    isRestoring,
  ]);

  // Update URL only for navigation-related changes (not search/filter changes)
  useEffect(() => {
    if (!hasInitialized || isRestoring) return;
    updateURL();
  }, [activeTab, selectedSort, page, rowsPerPage, hasInitialized, isRestoring]);

  // Update breadcrumb path when URL parameters change on initial load
  useEffect(() => {
    if (hasInitialized && !isRestoring) {
      const currentState = getCurrentSearchState();
      const urlParams = createURLSearchParams(currentState);
      const currentURL = urlParams.toString()
        ? `/search?${urlParams.toString()}`
        : "/search";
      updateBreadcrumb("search", { path: currentURL });
    }
  }, [hasInitialized, isRestoring, updateBreadcrumb]);

  const tabs = [
    "Cases",
    "Reports",
    "Incidents",
    "People",
    "Vehicles",
    "Property",
    "Organizations",
  ];

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100vh",
        width: "100%",
        bgcolor: colors.grey[50],
        padding: "32px",
        paddingBottom: "24px",
      }}
    >
      {/* Header */}
      <Box sx={{ pb: "24px" }}>
        <Typography style="h1" color={colors.grey[900]}>
          Records
        </Typography>
      </Box>

      {/* Tabs */}
      <TabSelector
        activeTab={activeTab}
        setActiveTab={handleTabChange}
        tabs={tabs}
      />

      {/* Search Bar */}
      <Box sx={{ mb: "24px" }}>
        <SearchBar
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          handleSearch={executeSearchImmediately}
          availableSearchFields={currentTabConfig.searchFields}
          selectedSearchFields={
            activeTab === "Cases"
              ? selectedCaseSearchFields
              : activeTab === "Incidents"
                ? selectedSearchFields
                : activeTab === "Reports"
                  ? selectedReportsSearchFields
                  : selectedEntitySearchFields
          }
          onSearchFieldsChange={
            activeTab === "Cases"
              ? setSelectedCaseSearchFields
              : activeTab === "Incidents"
                ? setSelectedSearchFields
                : activeTab === "Reports"
                  ? setSelectedReportsSearchFields
                  : setSelectedEntitySearchFields
          }
        />
      </Box>

      <Box sx={{ display: "flex", flex: 1, overflow: "hidden" }}>
        {/* Filters Sidebar */}
        {activeTab === "Cases" ? (
          <CasesSearchFilters
            selectedStatuses={selectedCaseStatuses}
            setSelectedStatuses={setSelectedCaseStatuses}
            selectedTypes={selectedCaseTypes}
            setSelectedTypes={setSelectedCaseTypes}
            selectedPriorities={selectedCasePriorities}
            setSelectedPriorities={setSelectedCasePriorities}
            selectedTags={selectedCaseTags}
            setSelectedTags={setSelectedCaseTags}
            dateFilters={casesDateFilters}
            setDateFilters={setCasesDateFilters}
          />
        ) : activeTab === "Incidents" ? (
          <IncidentSearchFilters
            selectedStatuses={selectedStatuses}
            setSelectedStatuses={setSelectedStatuses}
            selectedTypes={selectedTypes}
            setSelectedTypes={setSelectedTypes}
            selectedTags={selectedTags}
            setSelectedTags={setSelectedTags}
            selectedTriggerSources={selectedTriggerSources}
            setSelectedTriggerSources={setSelectedTriggerSources}
            selectedPriorities={selectedPriorities}
            setSelectedPriorities={setSelectedPriorities}
            dateFilters={dateFilters}
            setDateFilters={setDateFilters}
          />
        ) : activeTab === "Reports" ? (
          <ReportsSearchFilters
            selectedStatuses={selectedReportStatuses}
            setSelectedStatuses={setSelectedReportStatuses}
            selectedTypes={selectedReportTypes}
            setSelectedTypes={setSelectedReportTypes}
            dateFilters={reportsDateFilters}
            setDateFilters={setReportsDateFilters}
          />
        ) : activeTab === "People" ? (
          <PeopleSearchFilters
            selectedStatuses={selectedEntityStatuses}
            setSelectedStatuses={setSelectedEntityStatuses}
            selectedTags={selectedEntityTags}
            setSelectedTags={setSelectedEntityTags}
            dateFilters={entityDateFilters}
            setDateFilters={setEntityDateFilters}
          />
        ) : activeTab === "Vehicles" ? (
          <VehiclesSearchFilters
            selectedStatuses={selectedEntityStatuses}
            setSelectedStatuses={setSelectedEntityStatuses}
            selectedTags={selectedEntityTags}
            setSelectedTags={setSelectedEntityTags}
            dateFilters={entityDateFilters}
            setDateFilters={setEntityDateFilters}
          />
        ) : activeTab === "Property" ? (
          <PropertySearchFilters
            selectedStatuses={selectedEntityStatuses}
            setSelectedStatuses={setSelectedEntityStatuses}
            selectedTags={selectedEntityTags}
            setSelectedTags={setSelectedEntityTags}
            dateFilters={entityDateFilters}
            setDateFilters={setEntityDateFilters}
          />
        ) : activeTab === "Organizations" ? (
          <OrganizationSearchFilters
            selectedStatuses={selectedEntityStatuses}
            setSelectedStatuses={setSelectedEntityStatuses}
            selectedTags={selectedEntityTags}
            setSelectedTags={setSelectedEntityTags}
            dateFilters={entityDateFilters}
            setDateFilters={setEntityDateFilters}
          />
        ) : null}

        {/* Results Table */}
        {activeTab === "Cases" ? (
          <CasesResultsTable
            data={{ cases: casesData?.cases }}
            isLoading={casesIsLoading}
            isError={casesIsError}
            error={casesError}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={casesData?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={casesSearchParams.orderBy}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : activeTab === "Incidents" ? (
          <IncidentResultsTable
            data={data}
            isLoading={isLoading}
            isError={isError}
            error={error}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={data?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={selectedSort}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : activeTab === "Reports" ? (
          <ReportsResultsTable
            data={reportsData}
            isLoading={reportsIsLoading}
            isError={reportsIsError}
            error={reportsError}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={reportsData?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={reportsSearchParams.orderBy}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : activeTab === "People" ? (
          <PeopleResultsTable
            data={peopleData}
            isLoading={peopleIsLoading}
            isError={peopleIsError}
            error={peopleError}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={peopleData?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={peopleSearchParams.orderBy}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : activeTab === "Vehicles" ? (
          <VehiclesResultsTable
            data={vehiclesData}
            isLoading={vehiclesIsLoading}
            isError={vehiclesIsError}
            error={vehiclesError}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={vehiclesData?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={vehiclesSearchParams.orderBy}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : activeTab === "Property" ? (
          <PropertyResultsTable
            data={propertyData}
            isLoading={propertyIsLoading}
            isError={propertyIsError}
            error={propertyError}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={propertyData?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={propertySearchParams.orderBy || PropertySearchOrderBy.SEARCH_ORDER_BY_CREATED_AT}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : activeTab === "Organizations" ? (
          <OrganizationResultsTable
            data={organizationData}
            isLoading={organizationIsLoading}
            isError={organizationIsError}
            error={organizationError}
            page={page}
            rowsPerPage={rowsPerPage}
            totalPages={totalPages}
            totalResults={totalItems}
            nextPageToken={organizationData?.nextPageToken}
            handleChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            selectedSort={organizationSearchParams.orderBy}
            onSortChange={handleSortChange}
            getCurrentSearchState={getCurrentSearchState}
          />
        ) : null}
      </Box>
    </Box>
  );
};

export default SituationSearch;
