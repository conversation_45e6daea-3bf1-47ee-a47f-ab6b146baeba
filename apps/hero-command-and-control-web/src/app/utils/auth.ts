'use client';
const CLIENT_ID = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '';
const _cookieBase = `CognitoIdentityServiceProvider.${CLIENT_ID}`;

const _buildCookieAccessTokenKey = (username: string) => `${_cookieBase}.${username}.accessToken`;
const _buildCookieIDTokenKey = (username: string) => `${_cookieBase}.${username}.idToken`;
const _buildCookieRefreshTokenKey = (username: string) => `${_cookieBase}.${username}.refreshToken`;
const _buildCookieScopeKey = (username: string) => `${_cookieBase}.${username}.tokenScopesString`;
const _buildCookieUsernameKey = () => `${_cookieBase}.LastAuthUser`;

export interface Tokens {
    accessToken?: string;
    idToken?: string;
    refreshToken?: string;
    username?: string;
    /**
     * Cognito user unique identifier (JWT `sub` claim). This is decoded from the `idToken` whenever it is available
     * and provided for convenience so callers don't need to manually parse the JWT.
     */
    sub?: string;
}

export interface JWTPayload {
    sub?: string;
    exp?: number;
    iat?: number;
    email?: string;
    username?: string;
    'cognito:username'?: string;
    [key: string]: unknown;
}

/**
 * Decodes a JWT token and returns its payload.
 * @param token - The JWT token to decode
 * @returns The decoded payload object or undefined if decoding fails
 */
export const decodeJWTPayload = (token?: string): JWTPayload | undefined => {
    if (!token) return undefined;
    try {
        const base64Url = token.split('.')[1];
        if (!base64Url) return undefined;
        // JWTs use URL-safe base64 so convert to regular and pad if necessary
        let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const padding = 4 - (base64.length % 4);
        if (padding !== 4) {
            base64 += '='.repeat(padding);
        }
        const jsonPayload = atob(base64);
        return JSON.parse(jsonPayload);
    } catch (_) {
        return undefined;
    }
};

// Keep private version for backward compatibility within this file
const _decodeJWTPayload = decodeJWTPayload;

/**
 * Retrieves a cookie value by its name.
 * @private
 * @param name - The name of the cookie to retrieve
 * @returns The cookie value or undefined if not found
 */
const getCookieValue = (name: string): string | undefined => {
    if (typeof window === 'undefined') {
        return undefined;
    }
    const cookies = document.cookie.split('; ').reduce((acc: Record<string, string>, cookie) => {
        const [key, value] = cookie.split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
    }, {});
    return cookies[name];
};

/**
 * Fetches authentication tokens from browser cookies.
 * Reads Cognito-formatted cookies and extracts access, ID, and refresh tokens.
 * Also decodes the ID token to extract the user's unique identifier (sub).
 * 
 * @returns Tokens object containing username, tokens, and sub if available
 */
export const getTokensFromCookies = (): Tokens => {
    const username = getCookieValue(_buildCookieUsernameKey());

    if (!username) {
        return {};
    }

    const usernameForCookieKey = encodeURIComponent(username);
    const accessToken = getCookieValue(_buildCookieAccessTokenKey(usernameForCookieKey));
    const idToken = getCookieValue(_buildCookieIDTokenKey(usernameForCookieKey));
    const refreshToken = getCookieValue(_buildCookieRefreshTokenKey(usernameForCookieKey));

    // Attempt to decode the JWT `idToken` to obtain the `sub` claim
    const payload = _decodeJWTPayload(idToken);
    const sub = payload && typeof payload['sub'] === 'string' ? payload['sub'] : undefined;

    return {
        username,
        accessToken,
        idToken,
        refreshToken,
        sub,
    };
};

// Helper function to remove secure cookies
const removeSecureCookie = (name: string) => {
    const isProduction = typeof window !== 'undefined' && window.location.protocol === 'https:';
    const secureFlag = isProduction ? 'Secure;' : '';
    const expirationDate = new Date(0).toUTCString();
    document.cookie = `${name}=; expires=${expirationDate}; ${secureFlag} path=/`;
};

/**
 * Removes authentication tokens from browser cookies.
 * Handles cookie deletion across different domain variants (apex and subdomain).
 * Sets expiration to past date to ensure browser removes the cookies.
 * 
 * @returns void
 */
export const removeTokensFromCookies = (): void => {
    // Remove tokens from cookies silently
    const username = getCookieValue(_buildCookieUsernameKey());

    if (!username) {
        return;
    }

    const usernameForCookieKey = encodeURIComponent(username);

    // Remove each cookie with secure attributes
    removeSecureCookie(_buildCookieAccessTokenKey(usernameForCookieKey));
    removeSecureCookie(_buildCookieIDTokenKey(usernameForCookieKey));
    removeSecureCookie(_buildCookieRefreshTokenKey(usernameForCookieKey));
    removeSecureCookie(_buildCookieScopeKey(usernameForCookieKey));
    removeSecureCookie(_buildCookieUsernameKey());

    if (typeof window !== 'undefined') {
        // Try to clear using the full host and apex domain
        const hostParts = window.location.hostname.split('.');
        const variants: string[] = [];
        if (hostParts.length >= 2) {
            // apex like gethero.com
            variants.push(`.${hostParts.slice(-2).join('.')}`);
        }
        // full host like .command.gethero.com (only add if different from apex)
        const fullHostDomain = `.${window.location.hostname}`;
        if (!variants.includes(fullHostDomain)) {
            variants.push(fullHostDomain);
        }

        const isProduction = window.location.protocol === 'https:';
        const secureFlag = isProduction ? 'Secure;' : '';
        const expirationDate = new Date(0).toUTCString();
        
        variants.forEach((d) => {
            const domainAttr = `domain=${d}`;
            document.cookie = `${_buildCookieAccessTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; ${secureFlag} SameSite=Strict; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieIDTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; ${secureFlag} SameSite=Strict; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieRefreshTokenKey(usernameForCookieKey)}=; expires=${expirationDate}; ${secureFlag} SameSite=Strict; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieScopeKey(usernameForCookieKey)}=; expires=${expirationDate}; ${secureFlag} SameSite=Strict; path=/; ${domainAttr}`;
            document.cookie = `${_buildCookieUsernameKey()}=; expires=${expirationDate}; ${secureFlag} SameSite=Strict; path=/; ${domainAttr}`;
        });
    }
};

// ============================================================================
// Token Management Utilities (extracted from AuthContext)
// ============================================================================

// Token expiry cache to avoid repeated decoding
const tokenExpiryCache = new Map<string, number | null>();

/**
 * Decodes a JWT token and extracts the expiration time.
 * Caches the decoded expiry to avoid repeated decoding of the same token.
 * 
 * @param token - The JWT token to decode
 * @returns The expiration time in milliseconds, or null if decoding fails
 */
export const getTokenExpiry = (token: string): number | null => {
    // Check cache first for performance
    if (tokenExpiryCache.has(token)) {
        return tokenExpiryCache.get(token)!;
    }

    try {
        const payload = decodeJWTPayload(token);
        if (!payload || typeof payload.exp !== 'number') {
            tokenExpiryCache.set(token, null);
            return null;
        }
        
        const expiry = payload.exp * 1000; // Convert to milliseconds
        
        // Cache the result
        tokenExpiryCache.set(token, expiry);
        
        // Clear cache if it gets too large (keep last 10 tokens)
        if (tokenExpiryCache.size > 10) {
            const firstKey = tokenExpiryCache.keys().next().value;
            if (firstKey !== undefined) {
                tokenExpiryCache.delete(firstKey);
            }
        }
        
        return expiry;
    } catch (error) {
        tokenExpiryCache.set(token, null);
        return null;
    }
};

/**
 * Stores tokens in localStorage in Amplify's expected format
 * 
 * @param tokens - The tokens to store
 * @throws Error if required tokens are missing
 */
export const syncTokensToLocalStorage = (tokens: Tokens): void => {
    if (!tokens.username || !tokens.accessToken || !tokens.refreshToken) {
        throw new Error('Missing required tokens for localStorage sync');
    }

    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '';
    const keyPrefix = `CognitoIdentityServiceProvider.${clientId}`;
    
    localStorage.setItem(`${keyPrefix}.${tokens.username}.accessToken`, tokens.accessToken);
    localStorage.setItem(`${keyPrefix}.${tokens.username}.idToken`, tokens.idToken || '');
    localStorage.setItem(`${keyPrefix}.${tokens.username}.refreshToken`, tokens.refreshToken);
    localStorage.setItem(`${keyPrefix}.LastAuthUser`, tokens.username);
    localStorage.setItem(`${keyPrefix}.${tokens.username}.clockDrift`, '0');
};

/**
 * Clears all Amplify tokens from localStorage
 */
export const clearAmplifyTokensFromLocalStorage = (): void => {
    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '';
    const keyPrefix = `CognitoIdentityServiceProvider.${clientId}`;
    
    if (typeof window !== 'undefined') {
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith(keyPrefix)) {
                localStorage.removeItem(key);
            }
        });
    }
};

/**
 * Updates browser cookies with new tokens
 * Ensures Lambda@Edge sees fresh tokens in production
 * 
 * @param tokens - The tokens to update in cookies
 * @throws Error if required tokens are missing
 */
export const updateTokenCookies = (tokens: Tokens): void => {
    if (!tokens.accessToken || !tokens.username) {
        throw new Error('Missing required tokens for cookie update');
    }

    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '';
    const cookieBase = `CognitoIdentityServiceProvider.${clientId}`;
    const usernameKey = encodeURIComponent(tokens.username);
    
    const isProduction = typeof window !== 'undefined' && window.location.protocol === 'https:';
    const secureFlag = isProduction ? '; Secure' : '';
    const cookieOptions = `${secureFlag}; Path=/`;
    
    // Update cookies with new tokens
    document.cookie = `${cookieBase}.${usernameKey}.accessToken=${encodeURIComponent(tokens.accessToken)}${cookieOptions}`;
    if (tokens.idToken) {
        document.cookie = `${cookieBase}.${usernameKey}.idToken=${encodeURIComponent(tokens.idToken)}${cookieOptions}`;
    }
    if (tokens.refreshToken) {
        document.cookie = `${cookieBase}.${usernameKey}.refreshToken=${encodeURIComponent(tokens.refreshToken)}${cookieOptions}`;
    }
    document.cookie = `${cookieBase}.LastAuthUser=${tokens.username}${cookieOptions}`;
};

/**
 * Calculates when to refresh a token based on its expiry
 * 
 * @param token - The access token
 * @param bufferMs - Buffer time before expiry (default 5 minutes)
 * @returns Object with refresh timing information
 */
export const calculateTokenRefreshTiming = (token: string, bufferMs: number = 5 * 60 * 1000) => {
    const expiry = getTokenExpiry(token);
    
    if (!expiry) {
        return {
            hasExpiry: false,
            expiryTime: null,
            timeUntilExpiry: 0,
            shouldRefreshAt: Date.now() + (50 * 60 * 1000), // Default 50 minutes
            shouldRefreshNow: false,
            refreshDelayMs: 50 * 60 * 1000
        };
    }

    const now = Date.now();
    const timeUntilExpiry = expiry - now;
    const shouldRefreshAt = Math.max(expiry - bufferMs, now);
    const shouldRefreshNow = timeUntilExpiry <= bufferMs;
    const refreshDelayMs = Math.max(0, shouldRefreshAt - now);

    return {
        hasExpiry: true,
        expiryTime: expiry,
        timeUntilExpiry,
        shouldRefreshAt,
        shouldRefreshNow,
        refreshDelayMs
    };
};

/**
 * Performs complete authentication cleanup
 * Clears cookies, localStorage, sessionStorage, and caches
 * IMPORTANT: React Query cache must be cleared separately by the caller
 * as this utility doesn't have access to the QueryClient instance
 */
export const clearAllAuthData = (): void => {
    // Clear cookies
    removeTokensFromCookies();
    
    // Clear localStorage (Amplify tokens)
    clearAmplifyTokensFromLocalStorage();
    
    // Clear any auth-related sessionStorage
    if (typeof window !== 'undefined') {
        Object.keys(sessionStorage).forEach(key => {
            if (key.includes('auth') || key.includes('CognitoIdentityServiceProvider')) {
                sessionStorage.removeItem(key);
            }
        });
    }
    
    // Clear token expiry cache
    tokenExpiryCache.clear();
    
    // Note: React Query cache should be cleared by the component that has access to QueryClient
    // This prevents cross-user data leakage when switching accounts
};

/**
 * Checks if a token is expired
 * 
 * @param token - The JWT token to check
 * @param bufferSeconds - Optional buffer in seconds before actual expiry
 * @returns true if token is expired or will expire within buffer
 */
export const isTokenExpired = (token?: string, bufferSeconds: number = 0): boolean => {
    if (!token) return true;
    
    const expiry = getTokenExpiry(token);
    if (!expiry) return true;
    
    const now = Date.now();
    const bufferMs = bufferSeconds * 1000;
    
    return now >= (expiry - bufferMs);
};

/**
 * Gets time until token expiry in seconds
 * 
 * @param token - The JWT token
 * @returns Time until expiry in seconds, or 0 if expired/invalid
 */
export const getTimeUntilTokenExpiry = (token?: string): number => {
    if (!token) return 0;
    
    const expiry = getTokenExpiry(token);
    if (!expiry) return 0;
    
    const now = Date.now();
    const timeUntilExpiry = Math.max(0, expiry - now);
    
    return Math.floor(timeUntilExpiry / 1000);
};

/**
 * Retrieves tokens from localStorage (Amplify format)
 * 
 * @returns Tokens object with all available tokens
 */
export const getTokensFromLocalStorage = (): Tokens => {
    const clientId = process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '';
    const keyPrefix = `CognitoIdentityServiceProvider.${clientId}`;
    
    const username = localStorage.getItem(`${keyPrefix}.LastAuthUser`) || undefined;
    if (!username) {
        return {};
    }
    
    const accessToken = localStorage.getItem(`${keyPrefix}.${username}.accessToken`) || undefined;
    const idToken = localStorage.getItem(`${keyPrefix}.${username}.idToken`) || undefined;
    const refreshToken = localStorage.getItem(`${keyPrefix}.${username}.refreshToken`) || undefined;
    
    // Decode sub from idToken if available
    const payload = decodeJWTPayload(idToken);
    const sub = payload?.sub;
    
    return {
        username,
        accessToken,
        idToken,
        refreshToken,
        sub
    };
};
