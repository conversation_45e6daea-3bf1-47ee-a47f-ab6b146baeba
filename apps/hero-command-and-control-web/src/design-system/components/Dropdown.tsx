import { Box, InputAdornment, Popover, TextField } from "@mui/material";
import { matchSorter } from "match-sorter";
import { <PERSON>o } from "next/font/google";
import React, { useEffect, useRef, useState } from "react";
import { colors } from "../tokens";
import { Typography } from "./Typography";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-roboto",
});

export type DropdownOption = {
  value: string;
  label: string;
};

export type DropdownProps = {
  title?: string;
  placeholder?: string;
  options: DropdownOption[];
  value?: string | null;
  onChange?: (value: string | null) => void;
  onBlur?: () => void;
  enableSearch?: boolean;
  readOnly?: boolean;
  disabled?: boolean;
  className?: string;
  errorMessage?: string;
};

export const Dropdown: React.FC<DropdownProps> = ({
  title,
  placeholder = "Select",
  options,
  value = null,
  onChange,
  onBlur,
  enableSearch = false,
  readOnly = false,
  disabled = false,
  className = "",
  errorMessage,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | SVGSVGElement | null>(
    null
  );
  const [searchValue, setSearchValue] = useState<string>("");
  const [filteredOptions, setFilteredOptions] =
    useState<DropdownOption[]>(options);
  const [dropdownWidth, setDropdownWidth] = useState<number>(0);
  const inputRef = useRef<HTMLInputElement>(null);

  const selectedOption = options.find((option) => option.value === value);

  useEffect(() => {
    if (!enableSearch) {
      setFilteredOptions(options);
      return;
    }

    const filtered = matchSorter(options, searchValue, { keys: ["label"] });
    setFilteredOptions(filtered);
  }, [searchValue, options, enableSearch]);

  // Reset filtered options when dropdown closes
  useEffect(() => {
    if (!anchorEl) {
      setFilteredOptions(options);
      setSearchValue("");
    }
  }, [anchorEl, options]);

  const handleInputClick = (
    event: React.MouseEvent<HTMLElement | SVGSVGElement>
  ) => {
    if (!readOnly && !disabled) {
      const target = event.currentTarget;
      setAnchorEl(target);
      // Capture the width of the text field
      if (inputRef.current) {
        setDropdownWidth(inputRef.current.offsetWidth);
      }
      // Reset search when opening
      setSearchValue("");
      setFilteredOptions(options);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSearchValue("");
    // Trigger onBlur when dropdown closes
    if (onBlur) {
      onBlur();
    }
  };

  const handleOptionSelect = (option: DropdownOption) => {
    if (onChange) {
      onChange(option.value);
    }
    handleClose();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleClearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onChange) {
      onChange(null);
    }
  };

  const open = Boolean(anchorEl);

  return (
    <div
      className={`dropdown-container ${className}`}
      style={{ opacity: disabled ? 0.5 : 1 }}
    >
      {title && (
        <div
          className={`dropdown-title-container ${readOnly ? "read-only" : ""}`}
        >
          <Typography style="body4" color={colors.grey[500]}>
            {title}
          </Typography>
        </div>
      )}

      {readOnly ? (
        <div className="dropdown-readonly">
          <div className="readonly-input-field">
            <Typography
              style="body1"
              color={colors.grey[900]}
              lineHeight="16px"
            >
              {selectedOption ? selectedOption.label : "---"}
            </Typography>
          </div>
        </div>
      ) : (
        <div className="input-wrapper">
          <TextField
            placeholder={placeholder}
            value={selectedOption ? selectedOption.label : ""}
            onClick={handleInputClick}
            onBlur={onBlur}
            disabled={disabled}
            inputRef={inputRef}
            fullWidth
            inputProps={{ readOnly: true }}
            InputProps={{
              endAdornment: (
                <>
                  {selectedOption && (
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      style={{
                        marginRight: 8,
                        cursor: "pointer",
                      }}
                      onClick={handleClearSelection}
                    >
                      <path
                        d="M12 4L4 12M4 4L12 12"
                        stroke={colors.grey[400]}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{
                      marginRight: 8,
                      cursor: "pointer",
                      transform: open ? "rotate(180deg)" : "rotate(0deg)",
                      transition: "transform 0.3s ease",
                    }}
                    onClick={handleInputClick}
                  >
                    <path
                      d="M1 1L5 5L9 1"
                      stroke={colors.grey[500]}
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </>
              ),
            }}
            error={!!errorMessage}
            sx={{
              "& .MuiOutlinedInput-root": {
                fontFamily: roboto.style.fontFamily,
                fontSize: 16,
                fontWeight: 500,
                lineHeight: "16px",
                borderRadius: "8px",
                padding: "0",
                cursor: "pointer",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: open
                    ? colors.blue[600]
                    : errorMessage
                      ? colors.rose[600]
                      : colors.grey[200],
                  borderWidth: open ? 2 : 1,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: open
                    ? colors.blue[600]
                    : errorMessage
                      ? colors.rose[600]
                      : colors.grey[400],
                  borderWidth: open ? 2 : 1,
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderWidth: 2,
                  borderColor: errorMessage
                    ? colors.rose[600]
                    : colors.blue[600],
                },
                "& .MuiOutlinedInput-input": {
                  padding: "12px 12px",
                  cursor: "pointer",
                },
              },
              "& .MuiOutlinedInput-input::placeholder": {
                color: colors.grey[400],
                fontWeight: 400,
                opacity: 1,
              },
            }}
          />

          <Popover
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "left",
            }}
            PaperProps={{
              sx: {
                mt: 1,
                borderRadius: "8px",
                boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                border: `1px solid ${colors.grey[200]}`,
                width: dropdownWidth || "auto",
                maxWidth: dropdownWidth || 300,
                maxHeight: enableSearch ? 400 : 300,
                overflow: "auto",
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-track": {
                  background: colors.grey[100],
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: colors.grey[300],
                  borderRadius: "6px",
                },
                scrollbarWidth: "thin",
                scrollbarColor: `${colors.grey[300]} ${colors.grey[100]}`,
              },
            }}
          >
            {enableSearch && (
              <Box
                sx={{
                  p: 2,
                  borderBottom: `1px solid ${colors.grey[100]}`,
                  position: "sticky",
                  top: 0,
                  zIndex: 1,
                  backgroundColor: "white",
                }}
              >
                <TextField
                  placeholder="Search"
                  value={searchValue}
                  onChange={handleSearchChange}
                  fullWidth
                  autoFocus
                  size="small"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z"
                            stroke={colors.grey[500]}
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M14 14L11.1 11.1"
                            stroke={colors.grey[500]}
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "8px",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: colors.grey[200],
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: colors.grey[400],
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: colors.blue[600],
                      },
                    },
                  }}
                />
              </Box>
            )}
            <Box>
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => (
                  <Box
                    key={option.value}
                    onClick={() => handleOptionSelect(option)}
                    sx={{
                      px: 2,
                      py: 1.5,
                      cursor: "pointer",
                      "&:hover": {
                        backgroundColor: colors.grey[100],
                      },
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      gap: 1,
                    }}
                  >
                    <Box
                      sx={{
                        fontWeight: option.value === value ? 700 : 400,
                        overflow: "hidden",
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                        wordBreak: "break-word",
                        flex: 1,
                      }}
                    >
                      <Typography
                        style="body2"
                        color={
                          option.value === value
                            ? colors.grey[900]
                            : colors.grey[500]
                        }
                        lineHeight="20px"
                      >
                        {option.label}
                      </Typography>
                    </Box>
                    {option.value === value && (
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        style={{ flexShrink: 0 }}
                      >
                        <path
                          d="M13.3332 4.66663L6.6665 11.3333L2.6665 7.33329"
                          stroke={colors.blue[600]}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </Box>
                ))
              ) : (
                <Box sx={{ px: 2, py: 2 }}>
                  <Typography style="body2" color={colors.grey[500]}>
                    No options found
                  </Typography>
                </Box>
              )}
            </Box>
          </Popover>
        </div>
      )}

      {errorMessage && (
        <div className="error-message">
          <Typography style="body4" color={colors.rose[600]}>
            {errorMessage}
          </Typography>
        </div>
      )}

      <style jsx>{`
        .dropdown-container {
          display: flex;
          flex-direction: column;
          width: 100%;
          position: relative;
        }
        .dropdown-title-container {
          margin-left: 12px;
          margin-bottom: 12px;
        }
        .dropdown-title-container.read-only {
          margin-left: 0;
        }
        .dropdown-readonly {
          box-sizing: border-box;
          position: relative;
        }
        .readonly-input-field {
          box-sizing: border-box;
          border: 1px solid ${colors.grey[100]};
          border-radius: 8px;
          background-color: ${colors.grey[100]};
          padding: 12px 12px;
          font-family: ${roboto.style.fontFamily};
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          width: 100%;
          cursor: default;
          white-space: pre-wrap;
        }
        .input-wrapper {
          position: relative;
        }
        .error-message {
          margin-top: 8px;
          margin-left: 12px;
        }
      `}</style>
    </div>
  );
};
