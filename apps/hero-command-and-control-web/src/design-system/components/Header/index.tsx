import CheckIcon from "@mui/icons-material/Check";
import LaunchIcon from "@mui/icons-material/Launch";
import {
  Box,
  CircularProgress,
  Typography as MuiTypography,
  Stack,
} from "@mui/material";
import React, { ReactNode, useEffect, useRef, useState } from "react";
import { colors } from "../../tokens";
import { Button, ButtonProps } from "../Button";
import { Label, LabelProps } from "../Label";
import { Typography } from "../Typography";

export interface BreadcrumbItem {
  label: string;
  path?: string;
  onClick?: () => void;
  active?: boolean;
}

export interface MetadataItem {
  label: string;
  value: string;
  onClick?: () => void;
}

export interface HeaderActionButton extends Omit<ButtonProps, "onClick"> {
  id?: string;
  onClick?: (e: React.MouseEvent<HTMLElement>) => void;
}

export interface SaveStatus {
  isSaving: boolean;
  hasUnsavedChanges: boolean;
  source: string;
}

export interface HeaderProps {
  /**
   * Breadcrumb navigation items
   */
  breadcrumbs?: BreadcrumbItem[];
  /**
   * Main title for the header
   */
  title: string | ReactNode;
  /**
   * ID to display (optional)
   */
  id?: string;
  /**
   * Tags/labels to display next to the title
   */
  tags?: (Omit<LabelProps, "size"> | { custom: ReactNode })[];
  /**
   * Metadata items to display (label/value pairs)
   */
  metadata?: MetadataItem[];
  /**
   * Action buttons to display on the right
   */
  actions?: HeaderActionButton[];
  /**
   * Optional status indicator element
   */
  statusIndicator?: ReactNode;
  /**
   * Optional save statuses for tracking saving state
   */
  saveStatuses?: SaveStatus[];
  /**
   * Optional handler for when the header is closed
   */
  onClose?: () => void;
}

// Minimum time to display the "Saved" indicator
const MIN_SAVE_DISPLAY_MS = 1_000 as const;
type IndicatorState = "saving" | "saved" | null;

export const Header: React.FC<HeaderProps> = ({
  breadcrumbs = [],
  title,
  id,
  tags = [],
  metadata = [],
  actions = [],
  statusIndicator,
  saveStatuses = [],
  onClose,
}) => {
  const handleBreadcrumbClick = (item: BreadcrumbItem) => {
    if (item.onClick) {
      item.onClick();
    }
    if (onClose) {
      onClose();
    }
  };

  /** Save-indicator state */
  const [indicator, setIndicator] = useState<IndicatorState>(null);
  const saveStartRef = useRef<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  /** Aggregate save-status flags */
  const isSavingAny = saveStatuses.some((s) => s.isSaving);
  const hasUnsavedAny = saveStatuses.some((s) => s.hasUnsavedChanges);

  // Handle save indicator display logic
  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (isSavingAny) {
      if (indicator !== "saving") {
        setIndicator("saving");
        saveStartRef.current = Date.now();
      }
      return;
    }

    if (hasUnsavedAny) {
      setIndicator(null);
      saveStartRef.current = null;
      return;
    }

    const elapsed =
      saveStartRef.current !== null
        ? Date.now() - saveStartRef.current
        : MIN_SAVE_DISPLAY_MS;

    const remaining = Math.max(MIN_SAVE_DISPLAY_MS - elapsed, 0);

    if (remaining === 0) {
      setIndicator("saved");
      saveStartRef.current = null;
    } else {
      timeoutRef.current = setTimeout(() => {
        setIndicator("saved");
        saveStartRef.current = null;
        timeoutRef.current = null;
      }, remaining);
    }

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [isSavingAny, hasUnsavedAny, indicator]);

  // Render save indicator based on current state
  const renderSaveIndicator = () => {
    switch (indicator) {
      case "saving":
        return (
          <Box
            sx={{ display: "flex", alignItems: "center", gap: 1, opacity: 0.5 }}
          >
            <CircularProgress size={16} />
            <MuiTypography sx={{ fontSize: 16, color: colors.grey[600] }}>
              Saving…
            </MuiTypography>
          </Box>
        );
      case "saved":
        return (
          <Box
            sx={{ display: "flex", alignItems: "center", gap: 1, opacity: 0.5 }}
          >
            <CheckIcon sx={{ fontSize: 20, color: colors.grey[600] }} />
            <MuiTypography sx={{ fontSize: 16, color: colors.grey[600] }}>
              Saved
            </MuiTypography>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider", width: "100%" }}>
      {/* Breadcrumb row */}
      {breadcrumbs.length > 0 && (
        <Box sx={{ display: "flex", alignItems: "center", pb: 1 }}>
          {breadcrumbs.map((item, index) => (
            <React.Fragment key={index}>
              {index > 0 && (
                <MuiTypography
                  sx={{ mx: 1, color: colors.grey[600], fontSize: "12px" }}
                >
                  /
                </MuiTypography>
              )}
              <MuiTypography
                sx={{
                  cursor: item.onClick || item.path ? "pointer" : "default",
                  color: colors.grey[600],
                  "&:hover":
                    item.onClick || item.path
                      ? { color: "text.primary" }
                      : undefined,
                  fontSize: 12,
                  fontWeight: item.active ? "bold" : "normal",
                }}
                onClick={
                  item.onClick || item.path
                    ? () => handleBreadcrumbClick(item)
                    : undefined
                }
              >
                {item.label}
              </MuiTypography>
            </React.Fragment>
          ))}
        </Box>
      )}

      {/* Second Row - Content and Controls */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        {/* Left Content - Title, Tags, and Metadata */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 4 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            {/* Title and Tags */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 3, minWidth: 0 }}>
              {typeof title === "string" ? (
                <Typography style="h1" color={colors.grey[900]}>
                  {title}
                </Typography>
              ) : (
                title
              )}
              {id && (
                <Typography style="h1" color={colors.grey[500]}>
                  {id}
                </Typography>
              )}
              {tags.map((tag, index) => (
                'custom' in tag ? (
                  <Box key={index}>
                    {tag.custom}
                  </Box>
                ) : (
                  <Label key={index} {...tag} size="large" />
                )
              ))}
            </Box>
          </Stack>

          {/* Metadata Items */}
          {metadata.map((item, index) => (
            <Stack key={index} direction="column" alignItems="flex-start">
              <MuiTypography color={colors.grey[400]} sx={{ fontSize: "12px" }}>
                {item.label}
              </MuiTypography>
              {item.onClick ? (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 0.5,
                    cursor: "pointer",
                    "&:hover": {
                      opacity: 0.8,
                    },
                  }}
                  onClick={item.onClick}
                >
                  <MuiTypography
                    color={colors.grey[600]}
                    sx={{
                      fontSize: "14px",
                      textDecoration: "underline",
                      textDecorationColor: colors.grey[400],
                    }}
                  >
                    {item.value}
                  </MuiTypography>
                  <LaunchIcon
                    sx={{
                      fontSize: 14,
                      color: colors.grey[400],
                    }}
                  />
                </Box>
              ) : (
                <MuiTypography
                  color={colors.grey[600]}
                  sx={{ fontSize: "14px" }}
                >
                  {item.value}
                </MuiTypography>
              )}
            </Stack>
          ))}
        </Box>

        {/* Right Actions */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          {statusIndicator ||
            (saveStatuses.length > 0 && renderSaveIndicator())}

          {actions.map((action, index) => (
            <Button
              key={action.id || index}
              {...action}
              onClick={action.onClick}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
};
