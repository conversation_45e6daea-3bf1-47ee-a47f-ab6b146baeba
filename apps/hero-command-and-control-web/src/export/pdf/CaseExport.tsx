import React from "react";
import { Document, Page, View, Text } from "@react-pdf/renderer";
import Header from "./core/ExportHeader";
import Footer from "./core/ExportFooter";
import CaseReport from "./core/CaseReport";
import TableOfContents from "./core/TableOfContents";
import CaseSummary from "./core/CaseSummary";
import IncidentReport from "./report/IncidentReport";
import SupplementalReport from "./report/SupplementalReport";
import Media from "./entities/Media";
import Vehicles from "./entities/Vehicles";
import Property from "./entities/Property";
import DetailedPersons from "./entities/DetailedPersons";
import Organization from "./report/core/Organization";
import { Case, CaseAssetAssociationType } from "proto/hero/cases/v1/cases_pb";
import { commonStyles } from "./utils/pdfStyles";
import { Report, ReportSection } from "proto/hero/reports/v2/reports_pb";
import { ExportOptions } from "../../app/cases/caseComponents/components/CaseSelector";
import { Entity, EntityType } from "proto/hero/entity/v1/entity_pb";
import Offenses from "./entities/Offenses";

interface CaseAdditionalInfo {
  identifiersSection?: {
    ssn?: string;
    studentID?: string;
    driversLicense?: string;
  };
  classificationSection?: {
    dateOfBirth?: string;
  };
  contactInformationSection?: {
    phoneNumber?: string;
    email?: string;
    address?: string;
  };
}

// Redaction utility function for fields that need to be strings
const redactField = (value: string | undefined | null, shouldRedact: boolean): string => {
  if (!value) return '';
  if (!shouldRedact) return value;
  
  // For fields that need to be strings, use black circles
  return '●'.repeat(value.length);
};

// Redaction utility function for description
const redactDescription = (description: string, data: CaseAdditionalInfo, excludeIdentifiers: ExportOptions['excludeIdentifiers']): string => {
  if (!description || !excludeIdentifiers) return description;

  let redacted = description;

  if (excludeIdentifiers.ssn && data?.identifiersSection?.ssn) {
    redacted = redacted.replace(
      data.identifiersSection.ssn,
      '●'.repeat(data.identifiersSection.ssn.length)
    );
  }
  if (excludeIdentifiers.studentId && data?.identifiersSection?.studentID) {
    redacted = redacted.replace(
      data.identifiersSection.studentID,
      '●'.repeat(data.identifiersSection.studentID.length)
    );
  }
  if (excludeIdentifiers.driversLicense && data?.identifiersSection?.driversLicense) {
    redacted = redacted.replace(
      data.identifiersSection.driversLicense,
      '●'.repeat(data.identifiersSection.driversLicense.length)
    );
  }
  if (excludeIdentifiers.dob && data?.classificationSection?.dateOfBirth) {
    redacted = redacted.replace(
      data.classificationSection.dateOfBirth,
      '●'.repeat(data.classificationSection.dateOfBirth.length)
    );
  }
  if (excludeIdentifiers.contactInfo) {
    if (data?.contactInformationSection?.phoneNumber) {
      redacted = redacted.replace(
        data.contactInformationSection.phoneNumber,
        '●'.repeat(data.contactInformationSection.phoneNumber.length)
      );
    }
    if (data?.contactInformationSection?.email) {
      redacted = redacted.replace(
        data.contactInformationSection.email,
        '●'.repeat(data.contactInformationSection.email.length)
      );
    }
  }
  if (excludeIdentifiers.addresses && data?.contactInformationSection?.address) {
    redacted = redacted.replace(
      data.contactInformationSection.address,
      '●'.repeat(data.contactInformationSection.address.length)
    );
  }

  return redacted;
};

// Helper type to handle the raw section data
interface RawReportSection extends Omit<ReportSection, 'content' | 'type'> {
  type: string;
  content?: {
    case: string;
    value: any;
  };
  offenseList?: {
    id: string;
    offenses: Array<{
      id: string;
      offense_type: string;
      data: {
        code_section?: string;
        crime_description?: string;
        offense_status?: string;
        bias_motivation?: string;
        weapon_type?: string;
      };
    }>;
  };
  incidentDetails?: {
    initialType: string;
    incidentStartTime: string;
    incidentEndTime: string;
    incidentLocationStreetAddress: string;
    incidentLocationCity?: string;
    incidentLocationState?: string;
    incidentLocationZipCode?: string;
    incidentLocationType?: string;
    responders: Array<{
      displayName: string;
      role: string;
      assetId?: string;
    }>;
    reportingPerson: {
      firstName: string;
      middleName?: string;
      lastName: string;
      phoneNumber?: string;
      reporterRole?: string;
    };
    finalType: string;
  };
  entityList?: {
    entityRefs: Array<{
      id: string;
      displayName: string;
    }>;
  };
}

// Update ExportOptions interface
interface ExtendedExportOptions {
  includeSections?: {
    caseSummary?: boolean;
    primaryReport?: boolean;
    supplements?: boolean;
    people?: boolean;
    vehicles?: boolean;
    property?: boolean;
    organizations?: boolean;
    media?: boolean;
    offenses?: boolean;
  };
}

interface ExportDocumentProps {
  caseData: Case;
  reports?: Report[];
  entities?: Entity[];
  options?: {
    printedBy?: string;
    reviewer?: string;
    approvedDateTime?: string;
    includeSections?: ExtendedExportOptions['includeSections'];
    excludeIdentifiers?: ExportOptions['excludeIdentifiers'];
  };
  schemas?: {
    person?: any;
    property?: any;
    vehicle?: any;
  };
}

// Helper function to format date/time
const formatDateTime = (dateTimeStr: string | undefined): string => {
  if (!dateTimeStr) return "";
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).replace(',', ' –');
  } catch (e) {
    return dateTimeStr;
  }
};

// Helper function to check if a value is a valid EntityType
const isEntityType = (value: unknown): value is EntityType => {
  return typeof value === 'number' && value in EntityType;
};

// Helper function to convert string entity type to enum value
const getEntityTypeFromString = (typeString: unknown): EntityType => {
  if (isEntityType(typeString)) {
    return typeString;
  }
  
  const strType = String(typeString);
  switch (strType) {
    case 'ENTITY_TYPE_PERSON':
      return EntityType.PERSON;
    case 'ENTITY_TYPE_VEHICLE':
      return EntityType.VEHICLE;
    case 'ENTITY_TYPE_PROPERTY':
      return EntityType.PROPERTY;
    case 'ENTITY_TYPE_OTHER':
      return EntityType.OTHER;
    default:
      return EntityType.UNSPECIFIED;
  }
};

const ExportDocumentContent: React.FC<ExportDocumentProps> = ({
  caseData,
  reports = [],
  entities = [],
  options = {},
  schemas = {}
}) => {
  // Helper function to get entity display name using schema
  const getEntityDisplayName = (entity: Entity): string => {
    if (!entity.data) return "Unknown Entity";
    try {
      const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
      switch (entity.entityType) {
        case EntityType.PERSON:
          if (schemas.person) {
            const firstName = data.classificationSection?.firstName || "";
            const lastName = data.classificationSection?.lastName || "";
            return `${firstName} ${lastName}`.trim() || "Unknown Person";
          }
          return `${data.classificationSection?.firstName || ""} ${data.classificationSection?.lastName || ""}`.trim() || "Unknown Person";
        case EntityType.VEHICLE:
          if (schemas.vehicle) {
            const make = data.vehicleInformationSection?.make || "";
            const model = data.vehicleInformationSection?.model || "";
            const year = data.vehicleInformationSection?.year || "";
            return `${year} ${make} ${model}`.trim() || "Unknown Vehicle";
          }
          return `${data.vehicleInformationSection?.year || ""} ${data.vehicleInformationSection?.make || ""} ${data.vehicleInformationSection?.model || ""}`.trim() || "Unknown Vehicle";
        case EntityType.PROPERTY:
          if (schemas.property) {
            const category = data.propertyInformationSection?.category || "";
            const makeModelBrand = data.propertyInformationSection?.makeModelBrand || "";
            return `${category} ${makeModelBrand}`.trim() || "Unknown Property";
          }
          return `${data.propertyInformationSection?.category || ""} ${data.propertyInformationSection?.makeModelBrand || ""}`.trim() || "Unknown Property";
        case EntityType.ORGANIZATION:
          return data.organizationInformationSection?.organizationName || "Unknown Organization";
        default:
          return "Unknown Entity";
      }
    } catch (error) {
      return "Unknown Entity";
    }
  };

  // Helper function to check if an entity is of a specific type
  const isEntityOfType = (entity: Entity, type: EntityType): boolean => {
    if (typeof entity.entityType === 'number') {
      return entity.entityType === type;
    }
    const typeStr = String(entity.entityType);
    switch (type) {
      case EntityType.PERSON:
        return typeStr === 'ENTITY_TYPE_PERSON';
      case EntityType.VEHICLE:
        return typeStr === 'ENTITY_TYPE_VEHICLE';
      case EntityType.PROPERTY:
        return typeStr === 'ENTITY_TYPE_PROPERTY';
      case EntityType.OTHER:
        return typeStr === 'ENTITY_TYPE_OTHER';
      default:
        return false;
    }
  };

  // Helper function to prepare case summary data
  const prepareCaseSummaryData = (caseData: Case, entities: Entity[] = []) => {
    const officers = caseData.assetAssociations
      ?.filter(assoc => assoc.associationType === CaseAssetAssociationType.ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR)
      .map(assoc => assoc.notes) || [];

    // Get people names from entities
    const people = entities
      .filter(entity => isEntityOfType(entity, EntityType.PERSON))
      .map(entity => {
        const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
        const classificationData = data?.classificationSection || {};
        return `${classificationData.firstName || ''} ${classificationData.lastName || ''}`.trim();
      })
      .filter(name => name !== '');

    // Get vehicle information from entities
    const vehicles = entities
      .filter(entity => isEntityOfType(entity, EntityType.VEHICLE))
      .map(entity => {
        const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
        const vehicleData = data?.vehicleInformationSection || {};
        return `${vehicleData.year || ''} ${vehicleData.make || ''} ${vehicleData.model || ''}`.trim();
      })
      .filter(vehicle => vehicle !== '');

    // Get property information from entities
    const property = entities
      .filter(entity => isEntityOfType(entity, EntityType.PROPERTY))
      .map(entity => {
        const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
        const propertyData = data?.propertyInformationSection || {};
        return `${propertyData.makeModelBrand || ''} ${propertyData.model || ''}`.trim();
      })
      .filter(prop => prop !== '');

    // Get media information from entities
    const media = entities
      .filter(entity => isEntityOfType(entity, EntityType.OTHER))
      .map(entity => getEntityDisplayName(entity))
      .filter(media => media !== '');

    return {
      officers,
      people,
      vehicles,
      property,
      media
    };
  };

  // Helper function to prepare media data
  const prepareMediaData = (caseData: Case, entities: Entity[] = []) => {
    // Use full entity data if available, otherwise fall back to entityRefs
    const mediaEntities = entities
      .filter(entity => isEntityOfType(entity, EntityType.OTHER)) || 
      caseData.entityRefs
        ?.filter(ref => ref.type === EntityType.OTHER.toString()) || [];

    return {
      caseNumber: caseData.id || "",
      items: mediaEntities.map((entity, index) => ({
        id: String(index + 1).padStart(2, '0'),
        title: getEntityDisplayName(entity) || "Media Item",
        evidenceNumber: entity.id,
        type: entity.entityType === EntityType.OTHER ? "Evidence" : "Media",
        mediaDescription: getEntityDisplayName(entity) || "Media evidence item",
        mediaUri: entity.id
      }))
    };
  };

  // Helper function to prepare detailed persons data using schema
  const prepareDetailedPersonsData = (caseData: Case, entities: Entity[] = [], reports: Report[] = []) => {
    // Find the primary report
    const primaryReport = reports.find(report => 
      report.reportType?.toString() === 'REPORT_TYPE_INCIDENT_PRIMARY'
    );

    if (!primaryReport) {
      return {
        caseNumber: caseData.id || "",
        items: []
      };
    }

    // Get people from primary report
    const rawSections = primaryReport.sections as unknown as RawReportSection[];
    const incidentDetailsSection = rawSections?.find(
      section => section.type === 'SECTION_TYPE_INCIDENT_DETAILS'
    );
    const incidentDetails = incidentDetailsSection?.incidentDetails;

    // Get all people IDs from the primary report
    const primaryReportPeopleIds = new Set<string>();

    // Add reporting person if exists
    if (incidentDetails?.reportingPerson) {
      const reportingPersonName = `${incidentDetails.reportingPerson.firstName} ${incidentDetails.reportingPerson.lastName}`.trim();
      entities.forEach(entity => {
        const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
        const classificationData = data?.classificationSection || {};
        const entityName = `${classificationData.firstName || ''} ${classificationData.lastName || ''}`.trim();
        if (entityName === reportingPersonName) {
          primaryReportPeopleIds.add(entity.id);
        }
      });
    }

    // Add people from entityList if it exists
    const entityListSection = rawSections?.find(
      section => section.type === 'SECTION_TYPE_ENTITY_LIST_PEOPLE'
    );
    if (entityListSection?.entityList?.entityRefs) {
      entityListSection.entityList.entityRefs.forEach(ref => {
        primaryReportPeopleIds.add(ref.id);
      });
    }

    // Filter entities to only include people from primary report
    const peopleEntities = entities
      .filter(e => isEntityOfType(e, EntityType.PERSON))
      .filter(e => primaryReportPeopleIds.has(e.id));

    return {
      caseNumber: caseData.id || "",
      items: peopleEntities.map((entity, index) => {
        const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
        const classificationData = data?.classificationSection || {};
        const contactData = data?.contactInformationSection || {};
        const descriptorsData = data?.descriptorsSection || {};
        const identifiersData = data?.identifiersSection || {};
        const injuriesData = data?.injuriesSection || {};
        const arrestsData = data?.arrestsSection || {};

        // Apply redaction to sensitive fields
        const redactedSsn = redactField(identifiersData.ssn, options?.excludeIdentifiers?.ssn || false);
        const redactedStudentId = redactField(identifiersData.studentID, options?.excludeIdentifiers?.studentId || false);
        const redactedDriversLicense = redactField(identifiersData.driversLicense, options?.excludeIdentifiers?.driversLicense || false);
        const redactedDob = redactField(classificationData.dateOfBirth, options?.excludeIdentifiers?.dob || false);
        const redactedPhone = redactField(contactData.phoneNumber, options?.excludeIdentifiers?.contactInfo || false);
        const redactedPhone2 = redactField(contactData.phoneNumber2, options?.excludeIdentifiers?.contactInfo || false);
        const redactedEmail = redactField(contactData.email, options?.excludeIdentifiers?.contactInfo || false);
        const redactedAddress = redactField(contactData.address, options?.excludeIdentifiers?.addresses || false);

        return {
          id: entity.id,
          type: '',
          name: `${classificationData.firstName || ''} ${classificationData.middleName || ''} ${classificationData.lastName || ''}`.trim(),
          alias: classificationData.nicknames || '',
          studentNumber: redactedStudentId,
          licenseNumber: redactedDriversLicense,
          ssn: redactedSsn,
          hasInjuries: !!injuriesData.injuries?.length,
          injuries: injuriesData.injuries?.map((injury: any) => ({
            type: injury.type || '',
            description: injury.description || ''
          })),
          hasArrests: !!arrestsData.arrests?.length,
          arrests: arrestsData.arrests?.map((arrest: any) => ({
            type: arrest.type || '',
            description: arrest.description || '',
            date: arrest.date || '',
            arrestedWith: arrest.arrestedWith || '',
            disposition: arrest.disposition || ''
          })),
          gender: classificationData.gender || '',
          sex: classificationData.sex || '',
          build: descriptorsData.build || '',
          race: classificationData.race || '',
          weight: descriptorsData.weight || '',
          height: descriptorsData.height || '',
          dateOfBirth: redactedDob,
          age: classificationData.age || '',
          isMinor: classificationData.isMinor || false,
          ethnicity: classificationData.ethnicity || '',
          residentStatus: classificationData.residentStatus || '',
          marksScars: descriptorsData.marksScars || '',
          disabilities: descriptorsData.disabilities || '',
          affiliation: classificationData.affiliation || '',
          phone: redactedPhone,
          phone2: redactedPhone2,
          email: redactedEmail,
          address: redactedAddress
        };
      })
    };
  };

  // Prepare vehicles data using schema
  const vehicleEntities = entities.filter(e => isEntityOfType(e, EntityType.VEHICLE));
  const vehiclesData = {
    caseNumber: caseData.id || "",
    items: vehicleEntities.map((entity, index) => {
      const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
      const vehicleData = data?.vehicleInformationSection || {};
      return {
        id: entity.id,
        plateNo: vehicleData.licensePlate || '',
        plateState: '',  // Not available in data
        registrationStatus: '',  // Not available in data
        vin: vehicleData.vIN || '',
        affiliation: vehicleData.role || '',
        make: vehicleData.vehicleType || '',
        model: vehicleData.make || '',
        year: vehicleData.year || '',
        color: vehicleData.color || '',
        style: vehicleData.style || '',
        attributes: vehicleData.description || '',
        owner: {
          name: vehicleData.ownerIfApplicable || '',
          contact: '',
          status: ''
        }
      };
    })
  };

  // Prepare property data using schema
  const propertyEntities = entities.filter(e => isEntityOfType(e, EntityType.PROPERTY));
  const propertyData = {
    caseNumber: caseData.id || "",
    items: propertyEntities.map((entity, index) => {
      const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
      const propertyData = data?.propertyInformationSection || {};
      return {
        id: entity.id,
        data: data,
        propertyNumber: propertyData.nibrsPropertyType || '',
        makeBrand: propertyData.makeModelBrand || '',
        model: '',
        value: propertyData.collectedValue || '',
        status: propertyData.condition || '',
        asOfDate: propertyData.dateCollected || '',
        serial: propertyData.serialNumber || '',
        distinguishingCharacteristics: '',
        media: '',
        description: propertyData.description || '',
        owner: {
          name: propertyData.ownerIfApplicable || '',
          contact: '',
          status: ''
        }
      };
    })
  };

  // Prepare detailed persons data
  const detailedPersonsData = prepareDetailedPersonsData(caseData, entities, reports);
  // Prepare case summary data
  const caseSummaryData = prepareCaseSummaryData(caseData, entities);
  // Prepare media data
  const mediaData = prepareMediaData(caseData, entities);

  // Helper function to prepare organization data
  const prepareOrganizationData = (entities: Entity[], caseNumber: string) => {
    const organizationEntities = entities.filter(entity => entity.entityType === EntityType.ORGANIZATION);
    
    if (organizationEntities.length === 0) return null;

    const items = organizationEntities.map(entity => {
      const data = typeof entity.data === "string" ? JSON.parse(entity.data) : entity.data;
      return {
        id: entity.id,
        type: data.organizationInformationSection?.organizationType || "",
        name: data.organizationInformationSection?.organizationName || "",
        alias: data.organizationInformationSection?.alias || "",
        address: data.organizationInformationSection?.address || "",
        contact: {
          name: data.organizationInformationSection?.contactName || "",
          phone: data.organizationInformationSection?.contactPhone || "",
          email: data.organizationInformationSection?.contactEmail || ""
        }
      };
    });

    return {
      caseNumber,
      items
    };
  };

  // Prepare organization data
  const organizationData = prepareOrganizationData(entities, caseData.id || "");

  // Helper function to prepare offenses data
  const prepareOffensesData = (reports: Report[]) => {
    const offenseSections = reports
      .flatMap(report => report.sections || [])
      .filter(section => String(section.type) === 'SECTION_TYPE_OFFENSE') as unknown as RawReportSection[];

    if (offenseSections.length === 0) return null;

    const items = offenseSections.flatMap(section => {
      const offenseList = section.offenseList;
      if (!offenseList) return [];
      return (offenseList.offenses || []).map(offense => ({
        id: offense.id,
        classification: offense.offense_type,
        codeSection: offense.data?.code_section || '',
        crimeDescription: offense.data?.crime_description || '',
        offenseStatus: offense.data?.offense_status || '',
        biasMotivation: offense.data?.bias_motivation || '',
        weaponType: offense.data?.weapon_type || ''
      }));
    });

    if (items.length === 0) return null;

    return {
      caseNumber: caseData.id || "",
      items
    };
  };

  // Prepare offenses data
  const offensesData = prepareOffensesData(reports);

  // Format current date/time for footer
  const now = new Date();
  const printedDateTime = now.toLocaleString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).replace(',', ' –');

  // Get the latest update for reviewer info
  const latestUpdate = caseData.updates && caseData.updates.length > 0 
    ? caseData.updates[caseData.updates.length - 1] 
    : null;
  const reviewer = options?.reviewer || latestUpdate?.displayName || "";
  const approvedDateTime = options?.approvedDateTime || formatDateTime(latestUpdate?.eventTime);

  // Filter entities by type
  console.log('Raw entities data:', {
    totalEntities: entities.length,
    entities: entities.map(e => ({
      id: e.id,
      entityType: e.entityType,
      entityTypeString: typeof e.entityType === 'string' ? e.entityType : 'not a string',
      isPerson: isEntityOfType(e, EntityType.PERSON),
      isVehicle: isEntityOfType(e, EntityType.VEHICLE),
      isProperty: isEntityOfType(e, EntityType.PROPERTY),
      hasData: !!e.data,
      dataKeys: e.data ? Object.keys(e.data as object) : []
    }))
  });

  console.log('Filtered entities:', {
    peopleCount: entities.filter(e => isEntityOfType(e, EntityType.PERSON)).length,
    vehiclesCount: entities.filter(e => isEntityOfType(e, EntityType.VEHICLE)).length,
    propertiesCount: entities.filter(e => isEntityOfType(e, EntityType.PROPERTY)).length
  });

  // Redact case description if needed
  const redactedDescription = options?.excludeIdentifiers 
    ? redactDescription(caseData.description || "", caseData.additionalInfoJson as CaseAdditionalInfo, options.excludeIdentifiers)
    : caseData.description || "";

  return (
    <Document>
      <Page size="LETTER" style={commonStyles.page}>
        <Header
          schoolName="HERO SAFETY UNIVERSITY"
          departmentName="HSU Police Department"
          addressLine="465 California St San Francisco, CA 94108"
          emailLine="<EMAIL>"
          phoneLine="P: ************"
          faxLine="F: ************"
        />

        <View style={commonStyles.content}>
          <CaseReport
            caseNumber={caseData.id || ""}
            caseTitle={caseData.title || ""}
            caseStatus={caseData.status?.toString() || ""}
            caseType={caseData.type?.toString() || ""}
            createTime={caseData.createTime || ""}
            closeTime={caseData.closeTime || ""}
            resolvedTime={caseData.resolvedTime || ""}
            description={redactedDescription}
            codeSection=""
            classification=""
            crimeDescription=""
            incidentDisposition=""
            printedBy={options?.reviewer || ""}
            approvedDateTime={options?.approvedDateTime || ""}
          />
        </View>

        {options?.includeSections?.caseSummary && (
          <View style={commonStyles.sectionRow} minPresenceAhead={100}>
            <View style={commonStyles.column}>
              <TableOfContents 
                caseData={caseData}
                reports={reports}
                entities={entities}
              />
            </View>
            <View style={commonStyles.column}>
              <CaseSummary data={caseSummaryData} />
            </View>
          </View>
        )}

        {/* Add Offenses section here */}
        {options?.includeSections?.offenses && offensesData && (
          <View style={commonStyles.content}>
            <Offenses caseNumber={offensesData.caseNumber} items={offensesData.items} />
          </View>
        )}

        {/* Render reports if included */}
        {options?.includeSections?.primaryReport && reports.map((report) => {
          const reportType = report.reportType?.toString() || "REPORT_TYPE_UNSPECIFIED";

          return (
            <View key={report.id} style={commonStyles.content} break>
              {reportType === "REPORT_TYPE_INCIDENT_PRIMARY" ? (
                <IncidentReport 
                  report={report}
                  people={entities.filter(e => isEntityOfType(e, EntityType.PERSON))}
                  caseNumber={caseData.id}
                  excludeIdentifiers={options?.excludeIdentifiers}
                />
              ) : reportType === "REPORT_TYPE_INCIDENT_SUPPLEMENTAL" ? (
                <SupplementalReport 
                  report={report}
                  people={entities.filter(e => isEntityOfType(e, EntityType.PERSON))}
                  caseNumber={caseData.id}
                  excludeIdentifiers={options?.excludeIdentifiers}
                />
              ) : null}
            </View>
          );
        })}

        {/* If no reports and primary report is included, show a message */}
        {options?.includeSections?.primaryReport && reports.length === 0 && (
          <View style={commonStyles.content} break>
            <Text style={{ textAlign: 'center', padding: 20 }}>No report found.</Text>
          </View>
        )}

        {/* Render detailed persons if included */}
        {options?.includeSections?.people && detailedPersonsData.items.length > 0 && (
          <View style={commonStyles.content} break>
            <DetailedPersons caseNumber={detailedPersonsData.caseNumber} items={detailedPersonsData.items} />
          </View>
        )}

        {/* Render media if included */}
        {options?.includeSections?.media && mediaData.items.length > 0 && (
          <View style={commonStyles.content} break>
            <Media caseNumber={mediaData.caseNumber} items={mediaData.items} />
          </View>
        )}

        {/* Render vehicles if included */}
        {options?.includeSections?.vehicles && vehiclesData.items.length > 0 && (
          <View style={commonStyles.content} break>
            <Vehicles caseNumber={vehiclesData.caseNumber} items={vehiclesData.items} />
          </View>
        )}

        {/* Render property if included */}
        {options?.includeSections?.property && propertyData.items.length > 0 && (
          <View style={commonStyles.content} break>
            <Property caseNumber={propertyData.caseNumber} items={propertyData.items} />
          </View>
        )}

        {/* Render organizations if included */}
        {options?.includeSections?.organizations && organizationData && organizationData.items.length > 0 && (
          <View style={commonStyles.content} break>
            <Organization caseNumber={organizationData.caseNumber} items={organizationData.items} />
          </View>
        )}

        <Footer
          printedDateTime={printedDateTime}
          printedBy={options?.printedBy || "System User"}
        />
      </Page>
    </Document>
  );
};

const ExportDocument = (props: ExportDocumentProps) => {
  return <ExportDocumentContent {...props} />;
};

export default ExportDocument;