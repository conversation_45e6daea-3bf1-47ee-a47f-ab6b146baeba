// IncidentReport.tsx
import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { Entity, EntityType } from 'proto/hero/entity/v1/entity_pb';
import { Report, ReportSection } from 'proto/hero/reports/v2/reports_pb';
import { SituationType } from 'proto/hero/situations/v2/situations_pb';
import React from 'react';
import { SITUATION_TYPE_OPTIONS } from '../../../app/search/utils/constants';
import { formatCaseNumber } from '../utils/helpers';
import { colors, commonStyles, spacing, typography } from '../utils/pdfStyles';
import { SectionTypeString } from '../utils/sectionTypes';
import Header from './core/Header';
import Narrative from './core/Narrative';
import Officers from './core/Officers';
import Organization from './core/Organization';
import ReportingPerson from './core/ReportingPerson';
import InvolvedPersons from './persons/Persons';

// Helper function to convert situation type to readable string
const getReadableSituationType = (type: string): string => {
  const option = SITUATION_TYPE_OPTIONS.find(opt => opt.enumValue === SituationType[type as keyof typeof SituationType]);
  return option?.label || type;
};

// Helper type to handle the raw section data
interface RawReportSection extends Omit<ReportSection, 'content' | 'type'> {
  type: SectionTypeString;
  incidentDetails?: {
    initialType: string;
    incidentStartTime: string;
    incidentEndTime: string;
    incidentLocationStreetAddress: string;
    incidentLocationCity?: string;
    incidentLocationState?: string;
    incidentLocationZipCode?: string;
    incidentLocationType?: string;
    responders: Array<{
      displayName: string;
      role: string;
      assetId?: string;
    }>;
    reportingPerson: {
      firstName: string;
      middleName?: string;
      lastName: string;
      phoneNumber?: string;
      reporterRole?: string;
    };
    finalType: string;
  };
  narrative?: {
    richText: string;
  };
  entityList?: {
    entityRefs: Array<{
      id: string;
      displayName: string;
    }>;
  };
  offenseList?: {
    offenses: Array<{
      id: string;
      offenseType: string;
      data: any;
      schema: any;
    }>;
  };
}

// Helper type for entity references
interface EntityRef {
  id: string;
  displayName: string;
  type?: string;
  version?: number;
}

const styles = StyleSheet.create({
  wrapper: {
    ...commonStyles.container,
    marginTop: 0,
  },
  content: {
    // No padding or spacing
  },
  sectionHeader: {
    backgroundColor: colors.grey[800],
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
  },
  sectionHeaderText: {
    color: colors.white,
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    letterSpacing: typography.letterSpacing,
    textTransform: 'uppercase',
  },
  caseNumberText: {
    color: colors.white,
    fontFamily: typography.fontFamily,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    letterSpacing: typography.letterSpacing,
  },
});

interface IncidentReportProps {
  report: Report;
  people?: Entity[];
  vehicles?: Entity[];
  properties?: Entity[];
  organizations?: Entity[];
  caseNumber?: string;
  excludeIdentifiers?: {
    studentId?: boolean;
    driversLicense?: boolean;
    contactInfo?: boolean;
  };
}

// Helper function to format date/time
const formatDateTime = (dateTimeStr: string | undefined): string => {
  if (!dateTimeStr) return "";
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).replace(',', ' –');
  } catch (e) {
    return dateTimeStr;
  }
};

// Helper function to get display name from entity data
const getEntityDisplayName = (entity: Entity | EntityRef): string => {
  // Log the entity being processed
  console.log('getEntityDisplayName processing:', {
    entity,
    hasDisplayName: 'displayName' in entity,
    hasData: 'data' in entity,
    entityType: 'entityType' in entity ? entity.entityType : null
  });

  if ('displayName' in entity) {
    return entity.displayName;
  }

  if (!entity.data) return entity.id;
  const data = entity.data as Record<string, any>;

  // Handle different entity types
  switch (entity.entityType) {
    case EntityType.PERSON: {
      const personData = data.classificationSection || {};
      const name = `${personData.firstName || ''} ${personData.middleName || ''} ${personData.lastName || ''}`.trim();
      console.log('Person name construction:', {
        personData,
        constructedName: name
      });
      return name || entity.id;
    }

    case EntityType.VEHICLE: {
      const vehicleData = data.vehicleInformationSection || {};
      return `${vehicleData.year || ''} ${vehicleData.vehicleType || ''} ${vehicleData.make || ''} ${vehicleData.model || ''} - ${vehicleData.licensePlate || ''}`.trim() || entity.id;
    }

    case EntityType.PROPERTY: {
      const propertyData = data.propertyInformationSection || {};
      return `${propertyData.nibrsPropertyType || ''} ${propertyData.makeModelBrand || ''} - ${propertyData.description || ''}`.trim() || entity.id;
    }

    case EntityType.ORGANIZATION: {
      const orgData = data.organizationInformationSection || {};
      return orgData.organizationName || entity.id;
    }

    default:
      return entity.id;
  }
};

const IncidentReport: React.FC<IncidentReportProps> = ({
  report,
  people = [],
  vehicles = [],
  properties = [],
  organizations = [],
  caseNumber,
  excludeIdentifiers = {}
}) => {
  // Add logging for incoming props

  // Cast sections to RawReportSection to handle the raw data structure
  const rawSections = report.sections as unknown as RawReportSection[];

  // Find the sections we need
  const incidentDetailsSection = rawSections?.find(
    section => section.type === 'SECTION_TYPE_INCIDENT_DETAILS'
  );
  const narrativeSection = rawSections?.find(
    section => section.type === 'SECTION_TYPE_NARRATIVE'
  );

  // Get the incident details content
  const incidentDetails = incidentDetailsSection?.incidentDetails || null;

  // Prepare person data for the InvolvedPersons component
  const personItems = people
    .filter(person => {
      const data = person.data as Record<string, any>;
      const classificationData = data?.classificationSection || {};
      const personName = `${classificationData.firstName || ''} ${classificationData.lastName || ''}`.trim();

      // Include reporting person
      if (incidentDetails?.reportingPerson) {
        const reportingPersonName = `${incidentDetails.reportingPerson.firstName} ${incidentDetails.reportingPerson.lastName}`.trim();
        if (personName === reportingPersonName) {
          return true;
        }
      }

      // Include people from entityList if it exists
      const entityListSection = rawSections?.find(
        section => section.type === 'SECTION_TYPE_ENTITY_LIST_PEOPLE'
      );
      if (entityListSection?.entityList?.entityRefs) {
        return entityListSection.entityList.entityRefs.some(ref => ref.id === person.id);
      }

      return false;
    })
    .map((person, index) => {
      const data = person.data as Record<string, any>;
      const classificationData = data?.classificationSection || {};
      const contactData = data?.contactInformationSection || {};
      const identifiersData = data?.identifiersSection || {};

      // Check if this person is the reporting person
      const isReportingPerson = incidentDetails?.reportingPerson &&
        classificationData.firstName === incidentDetails.reportingPerson.firstName &&
        classificationData.lastName === incidentDetails.reportingPerson.lastName;

      return {
        id: `P${String(index + 1).padStart(2, '0')}`,
        name: `${classificationData.firstName || ''} ${classificationData.middleName || ''} ${classificationData.lastName || ''}`.trim(),
        type: isReportingPerson ? incidentDetails?.reportingPerson?.reporterRole || 'Reporter' : 'Involved Person',
        studentNumber: identifiersData.studentID || '',
        licenseNumber: identifiersData.driversLicense || '',
        gender: classificationData.gender || '',
        age: classificationData.age || '',
        race: '',
        ethnicity: '',
        phone: contactData.phoneNumber || '',
        excludeIdentifiers: {
          studentId: excludeIdentifiers.studentId,
          driversLicense: excludeIdentifiers.driversLicense,
          contactInfo: excludeIdentifiers.contactInfo
        }
      };
    });

  // Prepare organization data for the Organization component
  const organizationItems = organizations.map((org, index) => {
    const data = org.data as Record<string, any>;
    const orgData = data?.organizationInformationSection || {};
    return {
      id: org.id,
      type: orgData.organizationType || '',
      name: orgData.organizationName || '',
      alias: orgData.alias || '',
      address: orgData.address || '',
      contact: {
        name: orgData.contactName || '',
        phone: orgData.contactPhone || '',
        email: orgData.contactEmail || ''
      }
    };
  });

  if (!report) {
    console.warn('No report provided to IncidentReport');
    return (
      <View style={styles.wrapper}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionHeaderText}>REPORTS - INCIDENT REPORT</Text>
          <Text style={styles.caseNumberText}>CASE# {formatCaseNumber("")}</Text>
        </View>
        <View style={styles.content}>
          <Text>No report data available.</Text>
        </View>
      </View>
    );
  }


  if (incidentDetails?.responders) {
    console.log('Responders Data:', incidentDetails.responders);
  }

  if (incidentDetails?.reportingPerson) {
    console.log('Reporting Person Data:', incidentDetails.reportingPerson);
  }

  return (
    <View style={styles.wrapper}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionHeaderText}>REPORTS - INCIDENT REPORT</Text>
        <Text style={styles.caseNumberText}>CASE# {formatCaseNumber(caseNumber || report.caseId || "")}</Text>
      </View>
      <View style={styles.content}>
        <Header
          data={{
            title: report.title || "Incident Report",
            caseNumber: formatCaseNumber(caseNumber || report.caseId || ""),
            reportNumber: report.id || "",
            reportType: report.resourceType || "Unknown",
            reportStatus: report.status?.toString() || "",
            createdDateTime: formatDateTime(report.createdAt),
            lastModifiedDateTime: formatDateTime(report.updatedAt),
            location: [
              incidentDetails?.incidentLocationStreetAddress,
              incidentDetails?.incidentLocationCity,
              incidentDetails?.incidentLocationState,
              incidentDetails?.incidentLocationZipCode
            ].filter(Boolean).join(', ') || "Unknown Location",
            startDate: formatDateTime(incidentDetails?.incidentStartTime)?.split(' –')[0] || "Unknown Date",
            endDate: formatDateTime(incidentDetails?.incidentEndTime)?.split(' –')[0] || "Unknown Date",
            startTime: formatDateTime(incidentDetails?.incidentStartTime)?.split(' –')[1] || "Unknown Time",
            endTime: formatDateTime(incidentDetails?.incidentEndTime)?.split(' –')[1] || "Unknown Time",
            initialIncidentType: getReadableSituationType(incidentDetails?.initialType || "Unknown"),
            finalIncidentType: getReadableSituationType(incidentDetails?.finalType || "Unknown")
          }}
        />

        {incidentDetails?.responders && incidentDetails.responders.length > 0 && (
          <Officers
            officers={incidentDetails.responders.map(responder => ({
              name: responder.displayName || "Unknown Officer",
              badge: responder.assetId || "No Badge",
              role: responder.role || "Responder"
            }))}
          />
        )}

        {/* Reporting Person Section */}
        {incidentDetails?.reportingPerson && (
          <ReportingPerson
            name={`${incidentDetails.reportingPerson.firstName || ''} ${incidentDetails.reportingPerson.lastName || ''}`.trim()}
            role={incidentDetails.reportingPerson.reporterRole}
            contact={incidentDetails.reportingPerson.phoneNumber}
            excludeIdentifiers={{
              contactInfo: excludeIdentifiers.contactInfo
            }}
          />
        )}

        {personItems.length > 0 && (
          <InvolvedPersons items={personItems} />
        )}

        {organizationItems.length > 0 && (
          <Organization caseNumber={formatCaseNumber(caseNumber || report.caseId || "")} items={organizationItems} />
        )}

        {narrativeSection?.narrative && (
          <Narrative narrative={narrativeSection.narrative.richText} />
        )}
      </View>
    </View>
  );
};

export default IncidentReport;