import React from "react";
import { ActivityIndicator } from "react-native";
import { Stack, Redirect } from "expo-router";
import { useAuth } from "../AuthContext";

export default function ProtectedLayout() {
  const { isLoading, isAuthenticated } = useAuth();

  if (isLoading) {
    // Show a loading indicator while checking authTokens
    return <ActivityIndicator size="large" color="#0000ff" />;
  }

  // Use stable isAuthenticated state to prevent flicker during token refresh
  if (!isAuthenticated) {
    // If user is not authenticated, redirect to the sign-in screen.
    console.log("User not authenticated, redirecting to sign-in");
    return <Redirect href="/sign-in" />;
  }

  // If we have tokens, user is authenticated -> show the child route(s)
  return (
    <Stack>
      <Stack.Screen name="V2/MapScreen" options={{ headerShown: false }} />
      <Stack.Screen name="MainScreen" options={{ headerShown: false }} />
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen
        name="(overlay)"
        options={{ headerShown: false, presentation: "modal" }}
      />
      <Stack.Screen
        name="(reports)/ReportsScreen"
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="(reports)/Reporting"
        options={{ headerShown: false }}
      />
    </Stack>
  );
}
