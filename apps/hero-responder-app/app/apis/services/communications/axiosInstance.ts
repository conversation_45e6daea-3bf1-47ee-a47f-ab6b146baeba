import axios from "axios";
import { registerAxiosAuth } from '../../axiosAuth';

// Create axios without default baseURL. Enforce configuration via setter below.
const axiosInstance = axios.create({
  timeout: 10000, // Optional timeout
  headers: {
    "Content-Type": "application/json",
  },
});

let isConfigured = false;

const authConfig = registerAxiosAuth(axiosInstance, 'Communications');
export const setAccessGetter = authConfig.setAccessGetter;
export const setRefreshCallback = authConfig.setRefreshCallback;

export const setCommunicationsBaseUrl = (url?: string | null): void => {
  if (url && url.trim()) {
    axiosInstance.defaults.baseURL = url.replace(/\/$/, "");
    isConfigured = true;
  } else {
    delete axiosInstance.defaults.baseURL;
    isConfigured = false;
  }
};

// Hard guard: block requests until baseURL is configured
axiosInstance.interceptors.request.use((config) => {
  if (!isConfigured) {
    const err: any = new Error("BASE_URL_NOT_CONFIGURED");
    err.code = "E_BASE_URL_NOT_CONFIGURED";
    throw err;
  }
  return config;
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle errors globally
    return Promise.reject(error);
  }
);

export default axiosInstance;
