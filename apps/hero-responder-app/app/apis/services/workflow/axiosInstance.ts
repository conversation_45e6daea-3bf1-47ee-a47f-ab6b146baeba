import axios from "axios";
import { registerAxiosAuth } from '../../axiosAuth';

// Create axios without a default baseURL to avoid accidental network calls
const axiosInstance = axios.create({
  timeout: 10000, // Optional timeout
  headers: {
    "Content-Type": "application/json",
  },
});

let isConfigured = false;

const authConfig = registerAxiosAuth(axiosInstance, 'Workflow');
export const setAccessGetter = authConfig.setAccessGetter;
export const setRefreshCallback = authConfig.setRefreshCallback;

// Public setter used after environment discovery (and cleared on logout)
export const setWorkflowBaseUrl = (url?: string | null): void => {
  if (url && url.trim()) {
    axiosInstance.defaults.baseURL = url.replace(/\/$/, "");
    isConfigured = true;
  } else {
    delete axiosInstance.defaults.baseURL;
    isConfigured = false;
  }
};

// Hard guard: block requests until baseURL is configured
axiosInstance.interceptors.request.use((config) => {
  if (!isConfigured) {
    const err: any = new Error("BASE_URL_NOT_CONFIGURED");
    err.code = "E_BASE_URL_NOT_CONFIGURED";
    throw err;
  }
  return config;
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle errors globally
    const url = error.config?.url ? `${error.config.baseURL || ''}${error.config.url}` : 'Unknown URL';
    console.log(`Error calling ${url}: ${error.message || error}`);
    return Promise.reject(error);
  }
);

export default axiosInstance;
