import { S3BucketConfig } from "./s3-config";
import { ServersConfig } from "./server-config";

export interface SnsTopicConfig {
    topicName: string;
    displayName?: string;
}

export interface Environment {
    accountId: string;
    envName: string;
    domain: string;
    primaryRegion: string;
}

export interface SharedEnvironment {
    accountId: string;
    ecrRegion: string;
}

export interface OktaProviderConfig {
    name: string;
    metadataUrl: string;
    issuer: string;
    orgId: string;
}

export interface AppConfig {
    vpc: {
        cidr?: string;
    };
    environment: Environment;
    sharedEnvironment: SharedEnvironment;
    // shared between services, not between environments
    sharedSecrets: {
        tlsCert: {
            arn: string;
        };
    };
    sentryDsn: string;
    twilioAccountSid?: string;
    oktaProviders: OktaProviderConfig[];
    enableUsernameLogin: boolean;
    servers: Partial<ServersConfig>;
    s3buckets: { [key: string]: S3BucketConfig };
    snsTopics: { [key: string]: SnsTopicConfig };
    bastion: {
        instanceClass: string;
        instanceSize: string;
    };
    db: {
        hero: {
            instanceClass: string;
            instanceSize: string;
            multiAz: boolean;
        };
        perms: {
            instanceClass: string;
            instanceSize: string;
            multiAz: boolean;
        };
        auditlog: {
            instanceClass: string;
            instanceSize: string;
            multiAz: boolean;
        };
    };
    openfga: {
        dockerImage: string;
        cpu: number;
        memoryLimitMiB: number;
        desiredCount: number;
        minCapacity: number;
        maxCapacity: number;
    };
    tags: { [key: string]: string };
} 