const { Authenticator } = require('cognito-at-edge');

// Config values are replaced by CDK during deployment
const region = '%%REGION%%';
const userPoolId = '%%USER_POOL_ID%%';
const userPoolAppId = '%%USER_POOL_APP_ID%%';
const userPoolDomain = '%%USER_POOL_DOMAIN%%';
const commandDomain = '%%COMMAND_DOMAIN%%';

// Build the logout redirect URI dynamically from the above
const logoutRedirectUri = `https://${userPoolDomain}/logout?redirect_uri=https%3A%2F%2F${commandDomain}&response_type=code&client_id=${userPoolAppId}&state=%2F`;

const authenticator = new Authenticator({
  region: region,
  userPoolId: userPoolId,
  userPoolAppId: userPoolAppId,
  userPoolDomain: userPoolDomain,
  disableCookieDomain: true,
  logoutConfiguration: {
    logoutUri: '/logout',
    logoutRedirectUri: logoutRedirectUri
  }
});

function nextJSDynamicRouting(request) {
  var uri = request.uri;

  // Don't modify URIs that are part of OAuth flows or contain query parameters
  // This prevents corruption of state parameters in Cognito redirects
  if (uri.includes('?') || uri.includes('state=') || uri.includes('code=')) {
    return request;
  }

  if (uri.endsWith('/')) {
    request.uri += 'index.html';
  } else if (!uri.includes('.')) {
    request.uri += '.html';
  }
  return request;
}

exports.handler = async (event) => {
  const authResponse = await authenticator.handle(event);
  // If authentication fails, return the response immediately
  if (authResponse.status && authResponse.status !== 200) {
    return authResponse;
  }

  // Modify the request for Next.js dynamic routing
  return nextJSDynamicRouting(authResponse);
};