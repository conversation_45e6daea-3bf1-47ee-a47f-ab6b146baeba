#!/usr/bin/env bash
set -euo pipefail

# ---------------------------------------
# Config (override with flags or env)
# ---------------------------------------
AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID:-************}"
AWS_REGION="${AWS_REGION:-us-west-2}"
SERVICES_DIR="${SERVICES_DIR:-./services}"
CONFIG_FILE="${CONFIG_FILE:-./infra/config/servers.json}" # kept for compat (unused)
SERVICES_TO_SKIP="${SERVICES_TO_SKIP:-}"   # space-separated list: "svcA svcB"
PUSH_IMAGES="${PUSH_IMAGES:-1}"            # 1=push to ECR, 0=do not push
BUILD_IMAGES="${BUILD_IMAGES:-1}"          # 1=build services, 0=retag only
DRY_RUN="${DRY_RUN:-0}"                    # 1=show actions only
AUTO_SKIP_IF_UNCHANGED="${AUTO_SKIP_IF_UNCHANGED:-}"   # space-separated list: "svcA svcB"


ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

# ---------------------------------------
# Args / flags (legacy positional supported)
# ---------------------------------------
SPECIFIC_SERVICE=""
SUFFIX=""

usage() {
  cat <<EOF
Usage: $0 [--service NAME] [--suffix NAME] [--skip "svcA svcB"] [--auto-skip "svcA svcB"] [--account ID] [--region REGION] [--no-push] [--no-build] [--dry-run]
       $0 [SERVICE] [SUFFIX]   # legacy positional: service optional, suffix optional

ENV:
  AWS_ACCOUNT_ID, AWS_REGION, SERVICES_DIR, CONFIG_FILE, SERVICES_TO_SKIP, PUSH_IMAGES, BUILD_IMAGES, DRY_RUN
EOF
}

positional=()
while [[ $# -gt 0 ]]; do
  case "$1" in
    -s|--service) SPECIFIC_SERVICE="${2:?}"; shift 2;;
    -x|--suffix)  SUFFIX="${2:?}"; shift 2;;
    --skip)       SERVICES_TO_SKIP="${2:-}"; shift 2;;
    --auto-skip)  AUTO_SKIP_IF_UNCHANGED="${2:-}"; shift 2;;
    --account)    AWS_ACCOUNT_ID="${2:?}"; shift 2;;
    --region)     AWS_REGION="${2:?}"; shift 2;;
    --no-push)    PUSH_IMAGES="0"; shift;;
    --no-build)   BUILD_IMAGES="0"; shift;;
    --dry-run)    DRY_RUN="1"; shift;;
    -h|--help)    usage; exit 0;;
    --)           shift; break;;
    -*)           echo "Unknown flag: $1" >&2; usage; exit 1;;
    *)            positional+=("$1"); shift;;
  esac
done

# Legacy positional compatibility
if (( ${#positional[@]} == 2 )); then
  SPECIFIC_SERVICE="${SPECIFIC_SERVICE:-${positional[0]}}"
  SUFFIX="${SUFFIX:-${positional[1]}}"
elif (( ${#positional[@]} == 1 )); then
  if [[ -d "${SERVICES_DIR}/${positional[0]}" ]]; then
    SPECIFIC_SERVICE="${SPECIFIC_SERVICE:-${positional[0]}}"
  else
    SUFFIX="${SUFFIX:-${positional[0]}}"
  fi
fi

ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

# ---------------------------------------
# Helpers
# ---------------------------------------
need() { command -v "$1" >/dev/null 2>&1 || { echo "Missing dependency: $1" >&2; exit 1; }; }
in_skip_list() { [[ " ${SERVICES_TO_SKIP} " =~ (^|[[:space:]])"$1"($|[[:space:]]) ]]; }
in_auto_skip_list() { [[ " ${AUTO_SKIP_IF_UNCHANGED} " =~ (^|[[:space:]])"$1"($|[[:space:]]) ]]; }
tag_exists() { git rev-parse -q --verify "refs/tags/$1" >/dev/null 2>&1; }


latest_main_v() {
  git tag -l 'v[0-9]*' | grep -E '^v[0-9]+$' | sort -V | tail -n1 || true
}

nearest_main_v_ancestor() {
  # Fast path: use describe, excluding any tag with a hyphen (i.e., non-main tags)
  if base=$(git describe --tags --match "v[0-9]*" --exclude "v*-*" --abbrev=0 2>/dev/null); then
    echo "$base"
    return 0
  fi

  # Fallback: manual scan (handles older git or edge cases)
  local best=""
  while read -r t; do
    # resolve tag to commit (handles annotated tags)
    if git merge-base --is-ancestor "$(git rev-parse -q --verify "${t}^{commit}")" HEAD; then
      best="$t"
    fi
  done < <(git tag -l 'v[0-9]*' | grep -E '^v[0-9]+$' | sort -V)
  echo "$best"
}

highest_suffix_for_base() {
  local base="$1" suffix="$2"
  git tag -l "${base}-${suffix}*" | sort -V | tail -n1 || true
}

calc_new_tag() {
  local suffix="$1"
  local latest main_num base rel last_n next_n

  if [[ -n "$suffix" ]]; then
    base="$(nearest_main_v_ancestor)"
    [[ -n "$base" ]] || { echo "No base vN tag reachable from HEAD; create/merge a main tag (e.g. v1) before using --suffix." >&2; exit 1; }

    rel="$(highest_suffix_for_base "$base" "$suffix")"
    if [[ -z "$rel" ]]; then
      next_n=1
    else
      last_n="${rel##*-}"
      next_n="$(( last_n + 1 ))"
    fi
    echo "$base-${suffix}${next_n}"
  else
    latest="$(latest_main_v)"
    if [[ -z "$latest" ]]; then
      echo "v1"
    else
      main_num="${latest#v}"
      echo "v$(( main_num + 1 ))"
    fi
  fi
}

calc_prev_tag() {
  local suffix="$1"
  local latest base rel

  if [[ -n "$suffix" ]]; then
    base="$(nearest_main_v_ancestor)"
    [[ -n "$base" ]] || { echo "No base vN tag reachable from HEAD for suffix release." >&2; exit 1; }

    rel="$(highest_suffix_for_base "$base" "$suffix")"
    if [[ -n "$rel" ]]; then
      echo "$rel"        # previous suffix tag (vN-suffix-K)
    else
      echo "$base"       # fallback to base main tag (vN)
    fi
  else
    latest="$(latest_main_v)"
    echo "${latest}"     # previous main tag (vN) or empty if first-ever
  fi
}

# ---------------------------------------
# Pre-flight
# ---------------------------------------
need git; need docker; need aws
git fetch --tags --quiet || true

NEW_TAG="$(calc_new_tag "${SUFFIX}")"
PREV_TAG="$(calc_prev_tag "${SUFFIX}")"

echo "New tag:  ${NEW_TAG}"
echo "Prev tag: ${PREV_TAG:-<none>}"

# Tag if needed
if tag_exists "${NEW_TAG}"; then
  echo "Tag ${NEW_TAG} already exists; will reuse."
else
  if [[ "${DRY_RUN}" == "1" ]]; then
    echo "[DRY RUN] git tag ${NEW_TAG} && git push origin ${NEW_TAG}"
  else
    git tag "${NEW_TAG}"
    git push origin "${NEW_TAG}"
  fi
fi

# ECR login (only if pushing and not dry-run)
if [[ "${PUSH_IMAGES}" == "1" && "${DRY_RUN}" == "0" ]]; then
  aws ecr get-login-password --region "${AWS_REGION}" \
    | docker login --username AWS --password-stdin "${ECR_REGISTRY}"
fi

# ---------------------------------------
# Build / Retag (uses PREV_TAG)
# ---------------------------------------
process_service() {
  local service_path="$1"
  local service_name repo_name remote_repo
  service_name="$(basename "$service_path")"
  repo_name="${service_name}-service"
  remote_repo="${ECR_REGISTRY}/${repo_name}"

  

  if [[ ! -f "${service_path}/Dockerfile" ]]; then
    echo "Skipping ${service_name}: no Dockerfile at ${service_path}/Dockerfile"
    return 0
  fi

  # Auto-skip (unchanged since PREV_TAG) -> treat as "requested skip" to retag
  local allow_build="${BUILD_IMAGES}"
  local requested_skip="0"

  # existing manual skip list still works
  if in_skip_list "${service_name}"; then
    requested_skip="1"
  fi

  # new: auto-skip if unchanged and we have a PREV_TAG to diff against
  if [[ -n "${PREV_TAG}" ]] && in_auto_skip_list "${service_name}"; then
    if git diff --quiet "${PREV_TAG}" HEAD -- "${service_path}/"; then
      echo "No changes in ${service_name} since ${PREV_TAG}; will retag instead of build."
      requested_skip="1"
    fi
  fi

  if [[ "${allow_build}" == "1" && "${requested_skip}" == "0" ]]; then
    # BUILD
    if [[ "${DRY_RUN}" == "1" ]]; then
      echo "[DRY RUN] docker build --platform linux/amd64 -t ${repo_name}:${NEW_TAG} -f ${service_path}/Dockerfile ."
      echo "[DRY RUN] docker tag ${repo_name}:${NEW_TAG} ${remote_repo}:${NEW_TAG}"
      [[ "${PUSH_IMAGES}" == "1" ]] && echo "[DRY RUN] docker push ${remote_repo}:${NEW_TAG}"
      return 0
    fi
    echo "Building ${repo_name}:${NEW_TAG} (linux/amd64)"
    docker build --platform linux/amd64 \
      --build-arg AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID}" \
      -t "${repo_name}:${NEW_TAG}" \
      -f "${service_path}/Dockerfile" \
      .
    docker tag "${repo_name}:${NEW_TAG}" "${remote_repo}:${NEW_TAG}"
    [[ "${PUSH_IMAGES}" == "1" ]] && docker push "${remote_repo}:${NEW_TAG}"
    return 0
  fi

  # RETAG path (from PREV_TAG)
  if [[ -z "${PREV_TAG}" ]]; then
    if [[ "${allow_build}" == "1" ]]; then
      echo "No PREV_TAG available; building ${repo_name}:${NEW_TAG}."
      if [[ "${DRY_RUN}" == "1" ]]; then
        echo "[DRY RUN] docker build --platform linux/amd64 -t ${repo_name}:${NEW_TAG} -f ${service_path}/Dockerfile ."
        echo "[DRY RUN] docker tag ${repo_name}:${NEW_TAG} ${remote_repo}:${NEW_TAG}"
        [[ "${PUSH_IMAGES}" == "1" ]] && echo "[DRY RUN] docker push ${remote_repo}:${NEW_TAG}"
        return 0
      fi
      docker build --platform linux/amd64 \
        --build-arg AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID}" \
        -t "${repo_name}:${NEW_TAG}" \
        -f "${service_path}/Dockerfile" \
        .
      docker tag "${repo_name}:${NEW_TAG}" "${remote_repo}:${NEW_TAG}"
      [[ "${PUSH_IMAGES}" == "1" ]] && docker push "${remote_repo}:${NEW_TAG}"
      return 0
    else
      echo "No PREV_TAG available and --no-build in effect for ${repo_name}; cannot proceed." >&2
      return 1
    fi
  fi

  if [[ "${DRY_RUN}" == "1" ]]; then
    echo "[DRY RUN] docker pull ${remote_repo}:${PREV_TAG} || <fallback build if allowed>"
    echo "[DRY RUN] docker tag  ${remote_repo}:${PREV_TAG} ${remote_repo}:${NEW_TAG}"
    [[ "${PUSH_IMAGES}" == "1" ]] && echo "[DRY RUN] docker push ${remote_repo}:${NEW_TAG}"
    return 0
  fi

  if docker pull "${remote_repo}:${PREV_TAG}" >/dev/null 2>&1; then
    docker tag "${remote_repo}:${PREV_TAG}" "${remote_repo}:${NEW_TAG}"
    [[ "${PUSH_IMAGES}" == "1" ]] && docker push "${remote_repo}:${NEW_TAG}"
    echo "Retagged ${repo_name}:${PREV_TAG} -> ${NEW_TAG}"
  else
    if [[ "${allow_build}" == "1" ]]; then
      echo "Image ${remote_repo}:${PREV_TAG} not found; building ${repo_name}:${NEW_TAG}."
      docker build --platform linux/amd64 \
        --build-arg AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID}" \
        -t "${repo_name}:${NEW_TAG}" \
        -f "${service_path}/Dockerfile" \
        .
      docker tag "${repo_name}:${NEW_TAG}" "${remote_repo}:${NEW_TAG}"
      [[ "${PUSH_IMAGES}" == "1" ]] && docker push "${remote_repo}:${NEW_TAG}"
    else
      echo "Image ${remote_repo}:${PREV_TAG} not found and --no-build in effect." >&2
      return 1
    fi
  fi
}

# ---------------------------------------
# Execute (one or all)
# ---------------------------------------
if [[ -n "$SPECIFIC_SERVICE" ]]; then
  svc_path="${SERVICES_DIR}/${SPECIFIC_SERVICE}"
  [[ -d "$svc_path" ]] || { echo "Service not found: ${svc_path}" >&2; exit 1; }

  # Explain skip reasons up front
  reasons=()
  [[ ! -f "${svc_path}/Dockerfile" ]] && reasons+=("no-Dockerfile")
  in_skip_list "${SPECIFIC_SERVICE}" && reasons+=("manual-skip")
  if [[ -n "${PREV_TAG:-}" ]] && in_auto_skip_list "${SPECIFIC_SERVICE}"; then
    if git diff --quiet "${PREV_TAG}" HEAD -- "${svc_path}/"; then
      reasons+=("unchanged@${PREV_TAG}")
    fi
  fi

  prefix=""
  [[ "${DRY_RUN}" == "1" ]] && prefix="[DRY RUN] "

  if ((${#reasons[@]})); then
    echo "${prefix}Skipping build for ${SPECIFIC_SERVICE}: ${reasons[*]} (retag if possible)."
  fi
  echo "${prefix}Processing only: ${SPECIFIC_SERVICE}"
  process_service "$svc_path"

else
  prefix=""
  [[ "${DRY_RUN}" == "1" ]] && prefix="[DRY RUN] "
  echo "${prefix}Processing all services in ${SERVICES_DIR}"

  shopt -s nullglob
  for svc in "${SERVICES_DIR}"/*; do
    [[ -d "$svc" ]] || continue
    service_name="$(basename "$svc")"

    # Explain skip reasons per service
    reasons=()
    [[ ! -f "${svc}/Dockerfile" ]] && reasons+=("no-Dockerfile")
    in_skip_list "${service_name}" && reasons+=("manual-skip")
    if [[ -n "${PREV_TAG:-}" ]] && in_auto_skip_list "${service_name}"; then
      if git diff --quiet "${PREV_TAG}" HEAD -- "${svc}/"; then
        reasons+=("unchanged@${PREV_TAG}")
      fi
    fi

    if ((${#reasons[@]})); then
      echo "${prefix}Skipping build for ${service_name}: ${reasons[*]} (retag if possible)."
    fi

    process_service "$svc"
  done
fi

[[ "${DRY_RUN}" == "1" ]] && echo "[DRY RUN] Done. Tag would be: ${NEW_TAG}" || echo "Done. Tag used: ${NEW_TAG}"
