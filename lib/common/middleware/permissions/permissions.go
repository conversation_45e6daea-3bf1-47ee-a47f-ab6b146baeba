package permissions

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	clients "common/clients/services"
	"common/herosentry"

	pb "proto/hero/permissions/v1" // generated by protoc-gen-go

	"connectrpc.com/connect"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
)

// PermissionsMiddleware creates a middleware handler for checking permissions.
func PermissionsMiddleware(next http.Handler) http.Handler {
	permissionServiceURL := os.Getenv("PERMS_SERVICE_URL")
	if permissionServiceURL == "" {
		log.Fatal("PERMS_SERVICE_URL environment variable is not set")
	}

	permissionClient := clients.NewPermissionClient(
		permissionServiceURL,
		herosentry.RPCClientInterceptor(),
	)

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		protectionLevel := getRequiredProtectionLevel(r.URL.Path)
		// If the protection level is public, skip the permissions check.
		// This is just used by the permission check endpoints themselves,
		// to avoid a circular loop .
		if protectionLevel == pb.ProtectionLevel_OPEN {
			next.ServeHTTP(w, r)
			return
		}

		service, method := getServiceAndMethod(r.URL.Path)

		serviceSplit := strings.Split(service, ".")
		if len(serviceSplit) < 3 {
			log.Printf("PermissionsMiddleware: Could not determine service and method from request: %s with service: %s and method: %s", r.URL.Path, service, method)
			http.Error(w, "Internal Server Error: Could not determine service and method", http.StatusInternalServerError)
			return
		}
		serviceName := serviceSplit[len(serviceSplit)-1] // e.g., hero.permissions.v1.PermissionService -> PermissionService
		serviceNameShortSplit := strings.Split(serviceName, "Service")
		if len(serviceNameShortSplit) < 2 {
			log.Printf("PermissionsMiddleware: Service name is not valid: %s", serviceName)
			http.Error(w, "Internal Server Error: Service name is not valid", http.StatusInternalServerError)
			return
		}
		category := serviceNameShortSplit[0] // PermissionService -> Permission
		action := method                     // e.g., CheckPermission

		if category == "" || action == "" {
			log.Printf("PermissionsMiddleware: Could not determine service and method from request")
			http.Error(w, "Internal Server Error: Could not determine service and method", http.StatusInternalServerError)
			return
		}

		requestID, err := getRequestIDDynamic(r)
		if err != nil {
			log.Printf("PermissionsMiddleware: Error getting request ID: %v", err)
			http.Error(w, "Internal Server Error: Could not get request ID", http.StatusInternalServerError)
			return
		}

		// --- Define spec for logging ---
		spec := fmt.Sprintf("/%s/%s", service, method)

		// IMPORTANT: For RPC requests that will be handled by RPC interceptors,
		// we should NOT create a span here. The RPC interceptor will handle it.
		// This prevents duplicate transactions and ensures proper trace propagation.

		// Check if this is an RPC request
		// Connect RPC uses standard application/json but with specific paths
		// RPC requests have paths like /hero.service.v1.ServiceName/MethodName
		isRPCRequest := strings.HasPrefix(r.URL.Path, "/") && strings.Count(r.URL.Path, "/") == 2 && strings.Contains(r.URL.Path, ".")

		var ctx context.Context
		var span herosentry.Span
		var finishSpan func()

		if isRPCRequest {
			// For RPC requests, create a root transaction that will be used by all middleware
			// The RPC interceptor will see this transaction and use it instead of creating a new one
			// Explicitly set operation type to "rpc.server" for incoming RPC requests
			var finishTransaction func()
			ctx, finishTransaction = herosentry.StartTransaction(r.Context(), fmt.Sprintf("%s.%s", serviceName, method), "rpc.server")
			defer finishTransaction()

			// Update the request with the new context
			r = r.WithContext(ctx)
		} else {
			// For non-RPC requests, handle as before
			ctx, span, finishSpan = herosentry.StartSpan(r.Context(), fmt.Sprintf("%s.%s", serviceName, method))
			defer finishSpan()

			// Tag this span to indicate it includes a permission check
			if span != nil {
				span.SetTag("permission.check", "true")
				span.SetTag("permission.service", serviceName)
				span.SetTag("permission.method", method)
			}

			// Update the request with the new context
			r = r.WithContext(ctx)
		}

		// --- Call Permission Service ---
		checkReq := connect.NewRequest(&pb.CheckPermissionRequest{
			Category: category,
			Action:   action,
			ObjectId: requestID,
		})

		checkRes, err := permissionClient.CheckPermission(ctx, checkReq)

		if err != nil {
			log.Printf("PermissionsMiddleware: Error calling CheckPermission for procedure %s: %v", spec, err)

			// Capture permission service errors for monitoring
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal, fmt.Sprintf("Permission service error for %s", spec))

			// Handle different connect error codes if needed
			connectErr := &connect.Error{}
			if errors.As(err, &connectErr) {
				// Map connect error codes to HTTP status codes if desired
				switch connectErr.Code() {
				case connect.CodeUnauthenticated:
					http.Error(w, "Internal Server Error: Permission service authentication failed", http.StatusInternalServerError) // Or potentially 503
				case connect.CodePermissionDenied: // Should not happen from CheckPermission itself, but maybe infra issue
					http.Error(w, "Internal Server Error: Permission denied by upstream service", http.StatusInternalServerError)
				default:
					http.Error(w, "Internal Server Error: Permission check failed", http.StatusInternalServerError)
				}
			} else {
				// Non-connect error (e.g., network)
				http.Error(w, "Internal Server Error: Permission check failed", http.StatusInternalServerError)
			}
			return
		}

		if !checkRes.Msg.Allowed {
			// Capture permission denial for monitoring
			denialErr := fmt.Errorf("permission denied for %s %s", category, action)
			herosentry.CaptureException(ctx, denialErr, herosentry.ErrorTypeForbidden, fmt.Sprintf("Permission denied for service %s method %s", serviceName, method))

			// Add context to current span if exists
			if span := herosentry.CurrentSpan(ctx); span != nil {
				span.SetTag("permission.denied", "true")
				span.SetTag("permission.service", serviceName)
				span.SetTag("permission.method", method)
				span.SetTag("permission.category", category)
				span.SetTag("permission.action", action)
			}

			http.Error(w, "Forbidden: You do not have permission to perform this action", http.StatusForbidden)
			return
		}

		// Permission granted - continue with the request
		// CRITICAL: Pass the context with the span to the next handler
		// This ensures the RPC interceptor will detect the existing span
		// and continue the trace instead of creating a new transaction
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// Retrieve required roles for an RPC method dynamically
func getRequiredProtectionLevel(fullMethodName string) pb.ProtectionLevel {
	parts := splitMethodName(fullMethodName)
	if len(parts) != 2 {
		return pb.ProtectionLevel_PERMISSIONED // Default to permissioned if method name is not valid
	}

	protoMethodName := parts[0] + "." + parts[1]
	md, err := protoregistry.GlobalFiles.FindDescriptorByName(protoreflect.FullName(protoMethodName))
	if err != nil {
		return pb.ProtectionLevel_PERMISSIONED // Default to permissioned if method name is not valid
	}

	opts := md.(protoreflect.MethodDescriptor).Options()
	accessControl, ok := proto.GetExtension(opts, pb.E_AccessControl).(*pb.AccessControl)
	if !ok || accessControl == nil {
		return pb.ProtectionLevel_PERMISSIONED // Default to permissioned if method name is not valid
	}
	return accessControl.GetRequiredProtectionLevel()
}
