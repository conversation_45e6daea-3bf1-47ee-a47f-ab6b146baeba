// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/assets/v2/assets.proto (package hero.assets.v2, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/assets/v2/assets.proto.
 */
export const file_hero_assets_v2_assets: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message hero.assets.v2.GetZelloChannelsRequest
 */
export type GetZelloChannelsRequest = Message<"hero.assets.v2.GetZelloChannelsRequest"> & {
};

/**
 * Describes the message hero.assets.v2.GetZelloChannelsRequest.
 * Use `create(GetZelloChannelsRequestSchema)` to create a new message.
 */
export const GetZelloChannelsRequestSchema: GenMessage<GetZelloChannelsRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 0);

/**
 * @generated from message hero.assets.v2.GetZelloChannelsResponse
 */
export type GetZelloChannelsResponse = Message<"hero.assets.v2.GetZelloChannelsResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v2.ZelloChannel zello_channels = 1;
   */
  zelloChannels: ZelloChannel[];
};

/**
 * Describes the message hero.assets.v2.GetZelloChannelsResponse.
 * Use `create(GetZelloChannelsResponseSchema)` to create a new message.
 */
export const GetZelloChannelsResponseSchema: GenMessage<GetZelloChannelsResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 1);

/**
 * @generated from message hero.assets.v2.ZelloChannel
 */
export type ZelloChannel = Message<"hero.assets.v2.ZelloChannel"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string zello_channel_id = 3;
   */
  zelloChannelId: string;

  /**
   * @generated from field: string display_name = 4;
   */
  displayName: string;
};

/**
 * Describes the message hero.assets.v2.ZelloChannel.
 * Use `create(ZelloChannelSchema)` to create a new message.
 */
export const ZelloChannelSchema: GenMessage<ZelloChannel> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 2);

/**
 * @generated from message hero.assets.v2.Asset
 */
export type Asset = Message<"hero.assets.v2.Asset"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string cognito_jwt_sub = 3;
   */
  cognitoJwtSub: string;

  /**
   * @generated from field: string name = 4;
   */
  name: string;

  /**
   * @generated from field: hero.assets.v2.AssetType type = 5;
   */
  type: AssetType;

  /**
   * @generated from field: hero.assets.v2.AssetStatus status = 6;
   */
  status: AssetStatus;

  /**
   * Last known location
   *
   * @generated from field: double latitude = 7;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 8;
   */
  longitude: number;

  /**
   * ISO8601 timestamp for last location update
   *
   * @generated from field: string location_update_time = 9;
   */
  locationUpdateTime: string;

  /**
   * @generated from field: string contact_no = 10;
   */
  contactNo: string;

  /**
   * @generated from field: string contact_email = 11;
   */
  contactEmail: string;

  /**
   * ISO8601 timestamp when created
   *
   * @generated from field: string create_time = 12;
   */
  createTime: string;

  /**
   * ISO8601 timestamp for last update
   *
   * @generated from field: string update_time = 13;
   */
  updateTime: string;

  /**
   * For asset this will be fixed "ASSET"
   *
   * @generated from field: string resource_type = 14;
   */
  resourceType: string;

  /**
   * This needs to be a valid JSON string 
   * For cameras, this will contain camera-specific details like:
   * {
   *   "camera": {
   *     "ip_address": "*************",
   *     "rtsp_url": "rtsp://*************/stream",
   *     "credentials": {
   *       "username": "admin",
   *       "password": "encrypted_password",
   *       "use_tls": true,
   *       "certificate_path": "/path/to/cert"
   *     },
   *     "manufacturer": "Hikvision",
   *     "model": "DS-2CD2185FWD-I",
   *     "firmware_version": "V5.5.53",
   *     "capabilities": ["PTZ", "HD", "NightVision"],
   *     "stream_type": "RTSP",
   *     "metadata": {
   *       "resolution": "1080p",
   *       "fps": "30",
   *       "storage": "SD"
   *     }
   *   }
   * }
   *
   * @generated from field: string additional_info_json = 16;
   */
  additionalInfoJson: string;

  /**
   * Last timestamp when status got changed
   *
   * ISO8601 timestamp when status changed
   *
   * @generated from field: string status_changed_time = 17;
   */
  statusChangedTime: string;

  /**
   * Flag to indicate if this is an internal asset
   *
   * @generated from field: bool is_internal = 18;
   */
  isInternal: boolean;

  /**
   * Timestamp when the unit last logged on (transitioned OFFLINE -> AVAILABLE)
   *
   * @generated from field: string active_since_time = 19;
   */
  activeSinceTime: string;

  /**
   * Timestamp when the unit last logged off (transitioned Any -> OFFLINE)
   *
   * @generated from field: string last_active_time = 20;
   */
  lastActiveTime: string;
};

/**
 * Describes the message hero.assets.v2.Asset.
 * Use `create(AssetSchema)` to create a new message.
 */
export const AssetSchema: GenMessage<Asset> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 3);

/**
 * @generated from message hero.assets.v2.GetAssetRequest
 */
export type GetAssetRequest = Message<"hero.assets.v2.GetAssetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.assets.v2.GetAssetRequest.
 * Use `create(GetAssetRequestSchema)` to create a new message.
 */
export const GetAssetRequestSchema: GenMessage<GetAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 4);

/**
 * @generated from message hero.assets.v2.GetAssetResponse
 */
export type GetAssetResponse = Message<"hero.assets.v2.GetAssetResponse"> & {
  /**
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.GetAssetResponse.
 * Use `create(GetAssetResponseSchema)` to create a new message.
 */
export const GetAssetResponseSchema: GenMessage<GetAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 5);

/**
 * @generated from message hero.assets.v2.GetAssetByCognitoSubRequest
 */
export type GetAssetByCognitoSubRequest = Message<"hero.assets.v2.GetAssetByCognitoSubRequest"> & {
  /**
   * @generated from field: string cognito_jwt_sub = 1;
   */
  cognitoJwtSub: string;
};

/**
 * Describes the message hero.assets.v2.GetAssetByCognitoSubRequest.
 * Use `create(GetAssetByCognitoSubRequestSchema)` to create a new message.
 */
export const GetAssetByCognitoSubRequestSchema: GenMessage<GetAssetByCognitoSubRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 6);

/**
 * @generated from message hero.assets.v2.GetAssetByCognitoSubResponse
 */
export type GetAssetByCognitoSubResponse = Message<"hero.assets.v2.GetAssetByCognitoSubResponse"> & {
  /**
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.GetAssetByCognitoSubResponse.
 * Use `create(GetAssetByCognitoSubResponseSchema)` to create a new message.
 */
export const GetAssetByCognitoSubResponseSchema: GenMessage<GetAssetByCognitoSubResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 7);

/**
 * @generated from message hero.assets.v2.CreateAssetRequest
 */
export type CreateAssetRequest = Message<"hero.assets.v2.CreateAssetRequest"> & {
  /**
   * The Asset provided here should not include an id, which will be generated
   *
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.CreateAssetRequest.
 * Use `create(CreateAssetRequestSchema)` to create a new message.
 */
export const CreateAssetRequestSchema: GenMessage<CreateAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 8);

/**
 * @generated from message hero.assets.v2.CreateAssetResponse
 */
export type CreateAssetResponse = Message<"hero.assets.v2.CreateAssetResponse"> & {
  /**
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.CreateAssetResponse.
 * Use `create(CreateAssetResponseSchema)` to create a new message.
 */
export const CreateAssetResponseSchema: GenMessage<CreateAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 9);

/**
 * Request message to list assets with pagination, filtering, and ordering options.
 *
 * @generated from message hero.assets.v2.ListAssetsRequest
 */
export type ListAssetsRequest = Message<"hero.assets.v2.ListAssetsRequest"> & {
  /**
   * Maximum number of assets to return in the response.
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * A token identifying a specific page of results to retrieve.
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Optional filter: Returns only assets of the specified type.
   *
   * @generated from field: hero.assets.v2.AssetType type = 3;
   */
  type: AssetType;

  /**
   * Optional filter: Returns only assets matching the specified status.
   *
   * @generated from field: hero.assets.v2.AssetStatus status = 4;
   */
  status: AssetStatus;

  /**
   * Optional: Specifies the ordering of returned assets (e.g., "name asc" or "create_time desc").
   *
   * @generated from field: string order_by = 5;
   */
  orderBy: string;
};

/**
 * Describes the message hero.assets.v2.ListAssetsRequest.
 * Use `create(ListAssetsRequestSchema)` to create a new message.
 */
export const ListAssetsRequestSchema: GenMessage<ListAssetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 10);

/**
 * @generated from message hero.assets.v2.ListAssetsResponse
 */
export type ListAssetsResponse = Message<"hero.assets.v2.ListAssetsResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v2.Asset assets = 1;
   */
  assets: Asset[];

  /**
   * Token to retrieve the next page of results, if any
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.assets.v2.ListAssetsResponse.
 * Use `create(ListAssetsResponseSchema)` to create a new message.
 */
export const ListAssetsResponseSchema: GenMessage<ListAssetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 11);

/**
 * @generated from message hero.assets.v2.DeleteAssetRequest
 */
export type DeleteAssetRequest = Message<"hero.assets.v2.DeleteAssetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.assets.v2.DeleteAssetRequest.
 * Use `create(DeleteAssetRequestSchema)` to create a new message.
 */
export const DeleteAssetRequestSchema: GenMessage<DeleteAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 12);

/**
 * @generated from message hero.assets.v2.DeleteAssetResponse
 */
export type DeleteAssetResponse = Message<"hero.assets.v2.DeleteAssetResponse"> & {
};

/**
 * Describes the message hero.assets.v2.DeleteAssetResponse.
 * Use `create(DeleteAssetResponseSchema)` to create a new message.
 */
export const DeleteAssetResponseSchema: GenMessage<DeleteAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 13);

/**
 * @generated from message hero.assets.v2.UpdateAssetRequest
 */
export type UpdateAssetRequest = Message<"hero.assets.v2.UpdateAssetRequest"> & {
  /**
   * The asset field should include the id of the asset to update along with new field values
   *
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.UpdateAssetRequest.
 * Use `create(UpdateAssetRequestSchema)` to create a new message.
 */
export const UpdateAssetRequestSchema: GenMessage<UpdateAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 14);

/**
 * @generated from message hero.assets.v2.UpdateAssetResponse
 */
export type UpdateAssetResponse = Message<"hero.assets.v2.UpdateAssetResponse"> & {
  /**
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.UpdateAssetResponse.
 * Use `create(UpdateAssetResponseSchema)` to create a new message.
 */
export const UpdateAssetResponseSchema: GenMessage<UpdateAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 15);

/**
 * @generated from message hero.assets.v2.AddAdditionalInfoRequest
 */
export type AddAdditionalInfoRequest = Message<"hero.assets.v2.AddAdditionalInfoRequest"> & {
  /**
   * The ID of the asset to update.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * A JSON string containing the additional info to be merged.
   *
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.assets.v2.AddAdditionalInfoRequest.
 * Use `create(AddAdditionalInfoRequestSchema)` to create a new message.
 */
export const AddAdditionalInfoRequestSchema: GenMessage<AddAdditionalInfoRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 16);

/**
 * @generated from message hero.assets.v2.AddAdditionalInfoResponse
 */
export type AddAdditionalInfoResponse = Message<"hero.assets.v2.AddAdditionalInfoResponse"> & {
  /**
   * The id of the updated asset 
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * The updated additional_info_json object.
   *
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.assets.v2.AddAdditionalInfoResponse.
 * Use `create(AddAdditionalInfoResponseSchema)` to create a new message.
 */
export const AddAdditionalInfoResponseSchema: GenMessage<AddAdditionalInfoResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 17);

/**
 * @generated from message hero.assets.v2.ZelloCreds
 */
export type ZelloCreds = Message<"hero.assets.v2.ZelloCreds"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string asset_id = 3;
   */
  assetId: string;

  /**
   * @generated from field: string username = 4;
   */
  username: string;

  /**
   * @generated from field: string password = 5;
   */
  password: string;

  /**
   * @generated from field: string encrypted_password = 6;
   */
  encryptedPassword: string;
};

/**
 * Describes the message hero.assets.v2.ZelloCreds.
 * Use `create(ZelloCredsSchema)` to create a new message.
 */
export const ZelloCredsSchema: GenMessage<ZelloCreds> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 18);

/**
 * @generated from message hero.assets.v2.GetAssetPrivateRequest
 */
export type GetAssetPrivateRequest = Message<"hero.assets.v2.GetAssetPrivateRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.assets.v2.GetAssetPrivateRequest.
 * Use `create(GetAssetPrivateRequestSchema)` to create a new message.
 */
export const GetAssetPrivateRequestSchema: GenMessage<GetAssetPrivateRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 19);

/**
 * @generated from message hero.assets.v2.GetAssetPrivateResponse
 */
export type GetAssetPrivateResponse = Message<"hero.assets.v2.GetAssetPrivateResponse"> & {
  /**
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;

  /**
   * @generated from field: hero.assets.v2.ZelloCreds zello_creds = 2;
   */
  zelloCreds?: ZelloCreds;
};

/**
 * Describes the message hero.assets.v2.GetAssetPrivateResponse.
 * Use `create(GetAssetPrivateResponseSchema)` to create a new message.
 */
export const GetAssetPrivateResponseSchema: GenMessage<GetAssetPrivateResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 20);

/**
 * Request to get all assets associated with a phone number
 *
 * @generated from message hero.assets.v2.ListAssetsByPhoneNumberRequest
 */
export type ListAssetsByPhoneNumberRequest = Message<"hero.assets.v2.ListAssetsByPhoneNumberRequest"> & {
  /**
   * @generated from field: string phone_number = 1;
   */
  phoneNumber: string;
};

/**
 * Describes the message hero.assets.v2.ListAssetsByPhoneNumberRequest.
 * Use `create(ListAssetsByPhoneNumberRequestSchema)` to create a new message.
 */
export const ListAssetsByPhoneNumberRequestSchema: GenMessage<ListAssetsByPhoneNumberRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 21);

/**
 * Response containing all assets that have the requested phone number
 *
 * @generated from message hero.assets.v2.ListAssetsByPhoneNumberResponse
 */
export type ListAssetsByPhoneNumberResponse = Message<"hero.assets.v2.ListAssetsByPhoneNumberResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v2.Asset assets = 1;
   */
  assets: Asset[];
};

/**
 * Describes the message hero.assets.v2.ListAssetsByPhoneNumberResponse.
 * Use `create(ListAssetsByPhoneNumberResponseSchema)` to create a new message.
 */
export const ListAssetsByPhoneNumberResponseSchema: GenMessage<ListAssetsByPhoneNumberResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 22);

/**
 * ---------------------------------------------------------------------------
 * SEARCH MESSAGES
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.assets.v2.DateRange
 */
export type DateRange = Message<"hero.assets.v2.DateRange"> & {
  /**
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.assets.v2.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 23);

/**
 * @generated from message hero.assets.v2.FieldQuery
 */
export type FieldQuery = Message<"hero.assets.v2.FieldQuery"> & {
  /**
   * The field to search in (id, name, contact_no, contact_email)
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * The query text for this specific field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.assets.v2.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 24);

/**
 * @generated from message hero.assets.v2.HighlightResult
 */
export type HighlightResult = Message<"hero.assets.v2.HighlightResult"> & {
  /**
   * Field name that had a match
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * Highlighted fragments with matched terms
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.assets.v2.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 25);

/**
 * @generated from message hero.assets.v2.SearchAssetsRequest
 */
export type SearchAssetsRequest = Message<"hero.assets.v2.SearchAssetsRequest"> & {
  /**
   * Free-text / fuzzy query (id, name, contact_no, contact_email)
   *
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * Limit search scope (leave empty = all)
   *
   * @generated from field: repeated string search_fields = 2;
   */
  searchFields: string[];

  /**
   * Field-specific query terms
   *
   * @generated from field: repeated hero.assets.v2.FieldQuery field_queries = 3;
   */
  fieldQueries: FieldQuery[];

  /**
   * ─────────────── Fixed-value filters ───────────────
   *
   * @generated from field: repeated hero.assets.v2.AssetType type = 4;
   */
  type: AssetType[];

  /**
   * @generated from field: repeated hero.assets.v2.AssetStatus status = 5;
   */
  status: AssetStatus[];

  /**
   * ─────────────── Date-range filters (inclusive RFC3339) ───────────────
   *
   * @generated from field: hero.assets.v2.DateRange create_time = 6;
   */
  createTime?: DateRange;

  /**
   * @generated from field: hero.assets.v2.DateRange update_time = 7;
   */
  updateTime?: DateRange;

  /**
   * @generated from field: hero.assets.v2.DateRange status_changed_time = 8;
   */
  statusChangedTime?: DateRange;

  /**
   * @generated from field: hero.assets.v2.DateRange location_update_time = 9;
   */
  locationUpdateTime?: DateRange;

  /**
   * ─────────────── Geo bounding box ───────────────
   *
   * @generated from field: double min_latitude = 10;
   */
  minLatitude: number;

  /**
   * @generated from field: double max_latitude = 11;
   */
  maxLatitude: number;

  /**
   * @generated from field: double min_longitude = 12;
   */
  minLongitude: number;

  /**
   * @generated from field: double max_longitude = 13;
   */
  maxLongitude: number;

  /**
   * ─────────────── Pagination & sorting ───────────────
   *
   * @generated from field: int32 page_size = 14;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 15;
   */
  pageToken: string;

  /**
   * ─────────────── Enum based ordering ───────────────
   *
   * defaults to RELEVANCE
   *
   * @generated from field: hero.assets.v2.SearchOrderBy order_by = 16;
   */
  orderBy: SearchOrderBy;

  /**
   * true = ASC, false = DESC
   *
   * @generated from field: bool ascending = 17;
   */
  ascending: boolean;
};

/**
 * Describes the message hero.assets.v2.SearchAssetsRequest.
 * Use `create(SearchAssetsRequestSchema)` to create a new message.
 */
export const SearchAssetsRequestSchema: GenMessage<SearchAssetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 26);

/**
 * @generated from message hero.assets.v2.SearchAssetsResponse
 */
export type SearchAssetsResponse = Message<"hero.assets.v2.SearchAssetsResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v2.Asset assets = 1;
   */
  assets: Asset[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * New field for highlight information
   *
   * Key is asset ID
   *
   * @generated from field: map<string, hero.assets.v2.HighlightResult> highlights = 3;
   */
  highlights: { [key: string]: HighlightResult };

  /**
   * @generated from field: int32 total_results = 4;
   */
  totalResults: number;
};

/**
 * Describes the message hero.assets.v2.SearchAssetsResponse.
 * Use `create(SearchAssetsResponseSchema)` to create a new message.
 */
export const SearchAssetsResponseSchema: GenMessage<SearchAssetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 27);

/**
 * @generated from message hero.assets.v2.SetAssetInternalStatusRequest
 */
export type SetAssetInternalStatusRequest = Message<"hero.assets.v2.SetAssetInternalStatusRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: bool is_internal = 2;
   */
  isInternal: boolean;
};

/**
 * Describes the message hero.assets.v2.SetAssetInternalStatusRequest.
 * Use `create(SetAssetInternalStatusRequestSchema)` to create a new message.
 */
export const SetAssetInternalStatusRequestSchema: GenMessage<SetAssetInternalStatusRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 28);

/**
 * @generated from message hero.assets.v2.SetAssetInternalStatusResponse
 */
export type SetAssetInternalStatusResponse = Message<"hero.assets.v2.SetAssetInternalStatusResponse"> & {
  /**
   * @generated from field: hero.assets.v2.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v2.SetAssetInternalStatusResponse.
 * Use `create(SetAssetInternalStatusResponseSchema)` to create a new message.
 */
export const SetAssetInternalStatusResponseSchema: GenMessage<SetAssetInternalStatusResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v2_assets, 29);

/**
 * @generated from enum hero.assets.v2.AssetType
 */
export enum AssetType {
  /**
   * @generated from enum value: ASSET_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: ASSET_TYPE_MEMBER = 1;
   */
  MEMBER = 1,

  /**
   * @generated from enum value: ASSET_TYPE_DISPATCHER = 2;
   */
  DISPATCHER = 2,

  /**
   * @generated from enum value: ASSET_TYPE_RESPONDER = 3;
   */
  RESPONDER = 3,

  /**
   * @generated from enum value: ASSET_TYPE_CAMERA = 4;
   */
  CAMERA = 4,

  /**
   * @generated from enum value: ASSET_TYPE_BOT = 5;
   */
  BOT = 5,

  /**
   * @generated from enum value: ASSET_TYPE_SUPERVISOR = 6;
   */
  SUPERVISOR = 6,

  /**
   * @generated from enum value: ASSET_TYPE_TEST = 7;
   */
  TEST = 7,
}

/**
 * Describes the enum hero.assets.v2.AssetType.
 */
export const AssetTypeSchema: GenEnum<AssetType> = /*@__PURE__*/
  enumDesc(file_hero_assets_v2_assets, 0);

/**
 * @generated from enum hero.assets.v2.AssetStatus
 */
export enum AssetStatus {
  /**
   * @generated from enum value: ASSET_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: ASSET_STATUS_AVAILABLE = 1;
   */
  AVAILABLE = 1,

  /**
   * @generated from enum value: ASSET_STATUS_OFFLINE = 2;
   */
  OFFLINE = 2,

  /**
   * @generated from enum value: ASSET_STATUS_BUSY = 3;
   */
  BUSY = 3,

  /**
   * @generated from enum value: ASSET_STATUS_DEACTIVATED = 4;
   */
  DEACTIVATED = 4,

  /**
   * @generated from enum value: ASSET_STATUS_RESERVED = 5;
   */
  RESERVED = 5,

  /**
   * @generated from enum value: ASSET_STATUS_MAINTENANCE = 6;
   */
  MAINTENANCE = 6,

  /**
   * @generated from enum value: ASSET_STATUS_ON_BREAK = 7;
   */
  ON_BREAK = 7,
}

/**
 * Describes the enum hero.assets.v2.AssetStatus.
 */
export const AssetStatusSchema: GenEnum<AssetStatus> = /*@__PURE__*/
  enumDesc(file_hero_assets_v2_assets, 1);

/**
 * Enum for the order by field in the search assets request.
 *
 * @generated from enum hero.assets.v2.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_NAME = 2;
   */
  NAME = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATE_TIME = 3;
   */
  CREATE_TIME = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATE_TIME = 4;
   */
  UPDATE_TIME = 4,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_STATUS_CHANGED_TIME = 5;
   */
  STATUS_CHANGED_TIME = 5,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_LOCATION_UPDATE_TIME = 6;
   */
  LOCATION_UPDATE_TIME = 6,
}

/**
 * Describes the enum hero.assets.v2.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_assets_v2_assets, 2);

/**
 * @generated from service hero.assets.v2.AssetRegistryService
 */
export const AssetRegistryService: GenService<{
  /**
   * @generated from rpc hero.assets.v2.AssetRegistryService.GetAsset
   */
  getAsset: {
    methodKind: "unary";
    input: typeof GetAssetRequestSchema;
    output: typeof GetAssetResponseSchema;
  },
  /**
   * Fetch an asset by its Cognito JWT `sub` claim
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.GetAssetByCognitoSub
   */
  getAssetByCognitoSub: {
    methodKind: "unary";
    input: typeof GetAssetByCognitoSubRequestSchema;
    output: typeof GetAssetByCognitoSubResponseSchema;
  },
  /**
   * Register a new Asset to the Registry
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.CreateAsset
   */
  createAsset: {
    methodKind: "unary";
    input: typeof CreateAssetRequestSchema;
    output: typeof CreateAssetResponseSchema;
  },
  /**
   * @generated from rpc hero.assets.v2.AssetRegistryService.CreateResponderAsset
   */
  createResponderAsset: {
    methodKind: "unary";
    input: typeof CreateAssetRequestSchema;
    output: typeof CreateAssetResponseSchema;
  },
  /**
   * List all Assets, with their current Orders
   *
   * option (hero.auth.v1.access_control) = {
   *     required_roles: []
   * };
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.ListAssets
   */
  listAssets: {
    methodKind: "unary";
    input: typeof ListAssetsRequestSchema;
    output: typeof ListAssetsResponseSchema;
  },
  /**
   * Delete an Asset 
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.DeleteAsset
   */
  deleteAsset: {
    methodKind: "unary";
    input: typeof DeleteAssetRequestSchema;
    output: typeof DeleteAssetResponseSchema;
  },
  /**
   * Update an Asset 
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.UpdateAsset
   */
  updateAsset: {
    methodKind: "unary";
    input: typeof UpdateAssetRequestSchema;
    output: typeof UpdateAssetResponseSchema;
  },
  /**
   * Add additional info to an asset by merging provided JSON into the existing additional_info_json.
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.AddAdditionalInfo
   */
  addAdditionalInfo: {
    methodKind: "unary";
    input: typeof AddAdditionalInfoRequestSchema;
    output: typeof AddAdditionalInfoResponseSchema;
  },
  /**
   * GetAssetPrivate is a special RPC to fetch an Asset and its private details
   * (e.g., Zello credentials) for use in secure communications
   * It is intended to only be accessed by the Asset itself, or by Admins
   *
   * option (hero.auth.v1.access_control) = {
   *     required_roles: []
   * };
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.GetAssetPrivate
   */
  getAssetPrivate: {
    methodKind: "unary";
    input: typeof GetAssetPrivateRequestSchema;
    output: typeof GetAssetPrivateResponseSchema;
  },
  /**
   * option (hero.auth.v1.access_control) = {
   *     required_roles: []
   * };
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.GetZelloChannels
   */
  getZelloChannels: {
    methodKind: "unary";
    input: typeof GetZelloChannelsRequestSchema;
    output: typeof GetZelloChannelsResponseSchema;
  },
  /**
   * Get all assets associated with a phone number
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.ListAssetsByPhoneNumber
   */
  listAssetsByPhoneNumber: {
    methodKind: "unary";
    input: typeof ListAssetsByPhoneNumberRequestSchema;
    output: typeof ListAssetsByPhoneNumberResponseSchema;
  },
  /**
   * Search assets with advanced filtering and text search capabilities
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.SearchAssets
   */
  searchAssets: {
    methodKind: "unary";
    input: typeof SearchAssetsRequestSchema;
    output: typeof SearchAssetsResponseSchema;
  },
  /**
   * Set whether an asset is internal or external
   *
   * @generated from rpc hero.assets.v2.AssetRegistryService.SetAssetInternalStatus
   */
  setAssetInternalStatus: {
    methodKind: "unary";
    input: typeof SetAssetInternalStatusRequestSchema;
    output: typeof SetAssetInternalStatusResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_assets_v2_assets, 0);

