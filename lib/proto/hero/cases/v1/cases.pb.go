// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/cases/v1/cases.proto

package cases

import (
	_ "proto/hero/assets/v2"
	v1 "proto/hero/entity/v1"
	_ "proto/hero/permissions/v1"
	v11 "proto/hero/property/v1"
	_ "proto/hero/reports/v2"
	v2 "proto/hero/situations/v2"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// * Overall lifecycle of a case
type CaseStatus int32

const (
	CaseStatus_CASE_STATUS_UNSPECIFIED                CaseStatus = 0
	CaseStatus_CASE_STATUS_NEW                        CaseStatus = 1  // Logged, awaiting triage
	CaseStatus_CASE_STATUS_OPEN                       CaseStatus = 2  // Actively being scoped/triaged
	CaseStatus_CASE_STATUS_UNDER_REVIEW               CaseStatus = 3  // Submitted for managerial review
	CaseStatus_CASE_STATUS_INVESTIGATING              CaseStatus = 4  // Active investigative work
	CaseStatus_CASE_STATUS_PENDING_INFORMATION        CaseStatus = 5  // Blocked, waiting on data
	CaseStatus_CASE_STATUS_ON_HOLD                    CaseStatus = 6  // Manually paused
	CaseStatus_CASE_STATUS_ESCALATED                  CaseStatus = 7  // Handed to higher authority
	CaseStatus_CASE_STATUS_RESOLVED                   CaseStatus = 8  // Root issue fixed
	CaseStatus_CASE_STATUS_CLOSED                     CaseStatus = 9  // Administratively closed
	CaseStatus_CASE_STATUS_ARCHIVED                   CaseStatus = 10 // Long‑term retention (readonly)
	CaseStatus_CASE_STATUS_ASSIGNED_FOR_INVESTIGATION CaseStatus = 11 // Assigned to investigator, awaiting start
)

// Enum value maps for CaseStatus.
var (
	CaseStatus_name = map[int32]string{
		0:  "CASE_STATUS_UNSPECIFIED",
		1:  "CASE_STATUS_NEW",
		2:  "CASE_STATUS_OPEN",
		3:  "CASE_STATUS_UNDER_REVIEW",
		4:  "CASE_STATUS_INVESTIGATING",
		5:  "CASE_STATUS_PENDING_INFORMATION",
		6:  "CASE_STATUS_ON_HOLD",
		7:  "CASE_STATUS_ESCALATED",
		8:  "CASE_STATUS_RESOLVED",
		9:  "CASE_STATUS_CLOSED",
		10: "CASE_STATUS_ARCHIVED",
		11: "CASE_STATUS_ASSIGNED_FOR_INVESTIGATION",
	}
	CaseStatus_value = map[string]int32{
		"CASE_STATUS_UNSPECIFIED":                0,
		"CASE_STATUS_NEW":                        1,
		"CASE_STATUS_OPEN":                       2,
		"CASE_STATUS_UNDER_REVIEW":               3,
		"CASE_STATUS_INVESTIGATING":              4,
		"CASE_STATUS_PENDING_INFORMATION":        5,
		"CASE_STATUS_ON_HOLD":                    6,
		"CASE_STATUS_ESCALATED":                  7,
		"CASE_STATUS_RESOLVED":                   8,
		"CASE_STATUS_CLOSED":                     9,
		"CASE_STATUS_ARCHIVED":                   10,
		"CASE_STATUS_ASSIGNED_FOR_INVESTIGATION": 11,
	}
)

func (x CaseStatus) Enum() *CaseStatus {
	p := new(CaseStatus)
	*p = x
	return p
}

func (x CaseStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_cases_v1_cases_proto_enumTypes[0].Descriptor()
}

func (CaseStatus) Type() protoreflect.EnumType {
	return &file_hero_cases_v1_cases_proto_enumTypes[0]
}

func (x CaseStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseStatus.Descriptor instead.
func (CaseStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{0}
}

// * High‑level category / playbook selector
type CaseType int32

const (
	CaseType_CASE_TYPE_UNSPECIFIED        CaseType = 0
	CaseType_CASE_TYPE_SECURITY_INCIDENT  CaseType = 1
	CaseType_CASE_TYPE_SAFETY_INCIDENT    CaseType = 2
	CaseType_CASE_TYPE_OPERATIONAL_TASK   CaseType = 3
	CaseType_CASE_TYPE_CUSTOMER_COMPLAINT CaseType = 4
	CaseType_CASE_TYPE_INVESTIGATION      CaseType = 5
	CaseType_CASE_TYPE_COMPLIANCE_REVIEW  CaseType = 6
	CaseType_CASE_TYPE_INSURANCE_CLAIM    CaseType = 7
	CaseType_CASE_TYPE_ADMINISTRATIVE     CaseType = 8
	CaseType_CASE_TYPE_OTHER              CaseType = 99
)

// Enum value maps for CaseType.
var (
	CaseType_name = map[int32]string{
		0:  "CASE_TYPE_UNSPECIFIED",
		1:  "CASE_TYPE_SECURITY_INCIDENT",
		2:  "CASE_TYPE_SAFETY_INCIDENT",
		3:  "CASE_TYPE_OPERATIONAL_TASK",
		4:  "CASE_TYPE_CUSTOMER_COMPLAINT",
		5:  "CASE_TYPE_INVESTIGATION",
		6:  "CASE_TYPE_COMPLIANCE_REVIEW",
		7:  "CASE_TYPE_INSURANCE_CLAIM",
		8:  "CASE_TYPE_ADMINISTRATIVE",
		99: "CASE_TYPE_OTHER",
	}
	CaseType_value = map[string]int32{
		"CASE_TYPE_UNSPECIFIED":        0,
		"CASE_TYPE_SECURITY_INCIDENT":  1,
		"CASE_TYPE_SAFETY_INCIDENT":    2,
		"CASE_TYPE_OPERATIONAL_TASK":   3,
		"CASE_TYPE_CUSTOMER_COMPLAINT": 4,
		"CASE_TYPE_INVESTIGATION":      5,
		"CASE_TYPE_COMPLIANCE_REVIEW":  6,
		"CASE_TYPE_INSURANCE_CLAIM":    7,
		"CASE_TYPE_ADMINISTRATIVE":     8,
		"CASE_TYPE_OTHER":              99,
	}
)

func (x CaseType) Enum() *CaseType {
	p := new(CaseType)
	*p = x
	return p
}

func (x CaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_cases_v1_cases_proto_enumTypes[1].Descriptor()
}

func (CaseType) Type() protoreflect.EnumType {
	return &file_hero_cases_v1_cases_proto_enumTypes[1]
}

func (x CaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseType.Descriptor instead.
func (CaseType) EnumDescriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{1}
}

// * Role an asset plays within the case
type CaseAssetAssociationType int32

const (
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_UNSPECIFIED          CaseAssetAssociationType = 0
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR CaseAssetAssociationType = 1
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_INVESTIGATOR         CaseAssetAssociationType = 2
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_OBSERVER             CaseAssetAssociationType = 3
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_APPROVER             CaseAssetAssociationType = 4
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY          CaseAssetAssociationType = 5
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_WITNESS              CaseAssetAssociationType = 6
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_SUBJECT              CaseAssetAssociationType = 7
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_REPORTER             CaseAssetAssociationType = 8
	CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_COLLABORATOR         CaseAssetAssociationType = 9
)

// Enum value maps for CaseAssetAssociationType.
var (
	CaseAssetAssociationType_name = map[int32]string{
		0: "ASSET_ASSOCIATION_TYPE_UNSPECIFIED",
		1: "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
		2: "ASSET_ASSOCIATION_TYPE_INVESTIGATOR",
		3: "ASSET_ASSOCIATION_TYPE_OBSERVER",
		4: "ASSET_ASSOCIATION_TYPE_APPROVER",
		5: "ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY",
		6: "ASSET_ASSOCIATION_TYPE_WITNESS",
		7: "ASSET_ASSOCIATION_TYPE_SUBJECT",
		8: "ASSET_ASSOCIATION_TYPE_REPORTER",
		9: "ASSET_ASSOCIATION_TYPE_COLLABORATOR",
	}
	CaseAssetAssociationType_value = map[string]int32{
		"ASSET_ASSOCIATION_TYPE_UNSPECIFIED":          0,
		"ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR": 1,
		"ASSET_ASSOCIATION_TYPE_INVESTIGATOR":         2,
		"ASSET_ASSOCIATION_TYPE_OBSERVER":             3,
		"ASSET_ASSOCIATION_TYPE_APPROVER":             4,
		"ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY":          5,
		"ASSET_ASSOCIATION_TYPE_WITNESS":              6,
		"ASSET_ASSOCIATION_TYPE_SUBJECT":              7,
		"ASSET_ASSOCIATION_TYPE_REPORTER":             8,
		"ASSET_ASSOCIATION_TYPE_COLLABORATOR":         9,
	}
)

func (x CaseAssetAssociationType) Enum() *CaseAssetAssociationType {
	p := new(CaseAssetAssociationType)
	*p = x
	return p
}

func (x CaseAssetAssociationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseAssetAssociationType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_cases_v1_cases_proto_enumTypes[2].Descriptor()
}

func (CaseAssetAssociationType) Type() protoreflect.EnumType {
	return &file_hero_cases_v1_cases_proto_enumTypes[2]
}

func (x CaseAssetAssociationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseAssetAssociationType.Descriptor instead.
func (CaseAssetAssociationType) EnumDescriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{2}
}

// * Type of change recorded in the audit log
type CaseAuditAction int32

const (
	CaseAuditAction_CASE_AUDIT_ACTION_UNSPECIFIED CaseAuditAction = 0
	CaseAuditAction_CASE_AUDIT_ACTION_CREATE      CaseAuditAction = 1 // Case was created
	CaseAuditAction_CASE_AUDIT_ACTION_UPDATE      CaseAuditAction = 2 // Generic field edit
	CaseAuditAction_CASE_AUDIT_ACTION_STATUS      CaseAuditAction = 3 // Status transition
	CaseAuditAction_CASE_AUDIT_ACTION_RELATION    CaseAuditAction = 4 // Link/unlink other object
	CaseAuditAction_CASE_AUDIT_ACTION_ASSOCIATION CaseAuditAction = 5 // Asset association mutation
	CaseAuditAction_CASE_AUDIT_ACTION_DELETE      CaseAuditAction = 6 // Hard delete
	CaseAuditAction_CASE_AUDIT_ACTION_VIEW        CaseAuditAction = 7 // View case details
)

// Enum value maps for CaseAuditAction.
var (
	CaseAuditAction_name = map[int32]string{
		0: "CASE_AUDIT_ACTION_UNSPECIFIED",
		1: "CASE_AUDIT_ACTION_CREATE",
		2: "CASE_AUDIT_ACTION_UPDATE",
		3: "CASE_AUDIT_ACTION_STATUS",
		4: "CASE_AUDIT_ACTION_RELATION",
		5: "CASE_AUDIT_ACTION_ASSOCIATION",
		6: "CASE_AUDIT_ACTION_DELETE",
		7: "CASE_AUDIT_ACTION_VIEW",
	}
	CaseAuditAction_value = map[string]int32{
		"CASE_AUDIT_ACTION_UNSPECIFIED": 0,
		"CASE_AUDIT_ACTION_CREATE":      1,
		"CASE_AUDIT_ACTION_UPDATE":      2,
		"CASE_AUDIT_ACTION_STATUS":      3,
		"CASE_AUDIT_ACTION_RELATION":    4,
		"CASE_AUDIT_ACTION_ASSOCIATION": 5,
		"CASE_AUDIT_ACTION_DELETE":      6,
		"CASE_AUDIT_ACTION_VIEW":        7,
	}
)

func (x CaseAuditAction) Enum() *CaseAuditAction {
	p := new(CaseAuditAction)
	*p = x
	return p
}

func (x CaseAuditAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseAuditAction) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_cases_v1_cases_proto_enumTypes[3].Descriptor()
}

func (CaseAuditAction) Type() protoreflect.EnumType {
	return &file_hero_cases_v1_cases_proto_enumTypes[3]
}

func (x CaseAuditAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseAuditAction.Descriptor instead.
func (CaseAuditAction) EnumDescriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{3}
}

// * Controls what content reviewers may legally disclose
type ReleaseStatus int32

const (
	ReleaseStatus_RELEASE_STATUS_UNSPECIFIED          ReleaseStatus = 0
	ReleaseStatus_RELEASE_STATUS_PUBLIC               ReleaseStatus = 1 // Safe for public release
	ReleaseStatus_RELEASE_STATUS_INTERNAL             ReleaseStatus = 2 // Company‑internal only
	ReleaseStatus_RELEASE_STATUS_LAW_ENFORCEMENT_ONLY ReleaseStatus = 3 // Shareable with LE partners
	ReleaseStatus_RELEASE_STATUS_DO_NOT_RELEASE       ReleaseStatus = 4 // Withhold from all external requests
)

// Enum value maps for ReleaseStatus.
var (
	ReleaseStatus_name = map[int32]string{
		0: "RELEASE_STATUS_UNSPECIFIED",
		1: "RELEASE_STATUS_PUBLIC",
		2: "RELEASE_STATUS_INTERNAL",
		3: "RELEASE_STATUS_LAW_ENFORCEMENT_ONLY",
		4: "RELEASE_STATUS_DO_NOT_RELEASE",
	}
	ReleaseStatus_value = map[string]int32{
		"RELEASE_STATUS_UNSPECIFIED":          0,
		"RELEASE_STATUS_PUBLIC":               1,
		"RELEASE_STATUS_INTERNAL":             2,
		"RELEASE_STATUS_LAW_ENFORCEMENT_ONLY": 3,
		"RELEASE_STATUS_DO_NOT_RELEASE":       4,
	}
)

func (x ReleaseStatus) Enum() *ReleaseStatus {
	p := new(ReleaseStatus)
	*p = x
	return p
}

func (x ReleaseStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReleaseStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_cases_v1_cases_proto_enumTypes[4].Descriptor()
}

func (ReleaseStatus) Type() protoreflect.EnumType {
	return &file_hero_cases_v1_cases_proto_enumTypes[4]
}

func (x ReleaseStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReleaseStatus.Descriptor instead.
func (ReleaseStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{4}
}

// * Enum for the order by field in the search cases request
type SearchOrderBy int32

const (
	SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED SearchOrderBy = 0
	SearchOrderBy_SEARCH_ORDER_BY_RELEVANCE   SearchOrderBy = 1
	SearchOrderBy_SEARCH_ORDER_BY_CREATE_TIME SearchOrderBy = 2
	SearchOrderBy_SEARCH_ORDER_BY_UPDATE_TIME SearchOrderBy = 3
	SearchOrderBy_SEARCH_ORDER_BY_PRIORITY    SearchOrderBy = 4
	SearchOrderBy_SEARCH_ORDER_BY_STATUS      SearchOrderBy = 5
	SearchOrderBy_SEARCH_ORDER_BY_DUE_DATE    SearchOrderBy = 6
)

// Enum value maps for SearchOrderBy.
var (
	SearchOrderBy_name = map[int32]string{
		0: "SEARCH_ORDER_BY_UNSPECIFIED",
		1: "SEARCH_ORDER_BY_RELEVANCE",
		2: "SEARCH_ORDER_BY_CREATE_TIME",
		3: "SEARCH_ORDER_BY_UPDATE_TIME",
		4: "SEARCH_ORDER_BY_PRIORITY",
		5: "SEARCH_ORDER_BY_STATUS",
		6: "SEARCH_ORDER_BY_DUE_DATE",
	}
	SearchOrderBy_value = map[string]int32{
		"SEARCH_ORDER_BY_UNSPECIFIED": 0,
		"SEARCH_ORDER_BY_RELEVANCE":   1,
		"SEARCH_ORDER_BY_CREATE_TIME": 2,
		"SEARCH_ORDER_BY_UPDATE_TIME": 3,
		"SEARCH_ORDER_BY_PRIORITY":    4,
		"SEARCH_ORDER_BY_STATUS":      5,
		"SEARCH_ORDER_BY_DUE_DATE":    6,
	}
)

func (x SearchOrderBy) Enum() *SearchOrderBy {
	p := new(SearchOrderBy)
	*p = x
	return p
}

func (x SearchOrderBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchOrderBy) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_cases_v1_cases_proto_enumTypes[5].Descriptor()
}

func (SearchOrderBy) Type() protoreflect.EnumType {
	return &file_hero_cases_v1_cases_proto_enumTypes[5]
}

func (x SearchOrderBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchOrderBy.Descriptor instead.
func (SearchOrderBy) EnumDescriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{5}
}

// * File reference for case attachments, linking to filerepository service
type CaseFileReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                          // Unique identifier for this file reference within the case update
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                    // Reference to the case this file belongs to
	FileId        string                 `protobuf:"bytes,3,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`                    // FileMetadata.id from filerepository service - REQUIRED
	Caption       string                 `protobuf:"bytes,4,opt,name=caption,proto3" json:"caption,omitempty"`                                // Optional caption/description for the file
	DisplayName   string                 `protobuf:"bytes,5,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`     // Optional display name (fallback to original filename)
	DisplayOrder  int32                  `protobuf:"varint,6,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"` // Order for displaying files in UI (0-based)
	FileCategory  string                 `protobuf:"bytes,7,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`  // Category of the file (e.g., "evidence_photo", "evidence_video", "evidence_audio", "evidence_document", "correspondence", "other")
	Metadata      *structpb.Struct       `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`                              // Additional metadata about the file reference
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaseFileReference) Reset() {
	*x = CaseFileReference{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseFileReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseFileReference) ProtoMessage() {}

func (x *CaseFileReference) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseFileReference.ProtoReflect.Descriptor instead.
func (*CaseFileReference) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{0}
}

func (x *CaseFileReference) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CaseFileReference) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseFileReference) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *CaseFileReference) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *CaseFileReference) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CaseFileReference) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *CaseFileReference) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *CaseFileReference) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// * Free‑form timeline entry (used for narrative updates)
type CaseUpdateEntry struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Message      string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`                                                                     // Human-readable event text
	EventTime    string                 `protobuf:"bytes,2,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`                                                // ISO-8601 timestamp when the event occurred
	UpdateSource v2.UpdateSource        `protobuf:"varint,3,opt,name=update_source,json=updateSource,proto3,enum=hero.situations.v2.UpdateSource" json:"update_source,omitempty"` // System / human
	UpdaterId    string                 `protobuf:"bytes,4,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`                                                // Asset or system id
	EventType    string                 `protobuf:"bytes,5,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`                                                // Optional: "EVIDENCE_ADDED", etc.
	DisplayName  string                 `protobuf:"bytes,6,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`                                          // Cached for convenience
	// Arbitrary structured payload for machine-readable details
	Data *structpb.Struct `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
	// File attachments for this case update entry
	FileAttachments []*CaseFileReference `protobuf:"bytes,8,rep,name=file_attachments,json=fileAttachments,proto3" json:"file_attachments,omitempty"` // References to files in filerepository
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CaseUpdateEntry) Reset() {
	*x = CaseUpdateEntry{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseUpdateEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseUpdateEntry) ProtoMessage() {}

func (x *CaseUpdateEntry) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseUpdateEntry.ProtoReflect.Descriptor instead.
func (*CaseUpdateEntry) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{1}
}

func (x *CaseUpdateEntry) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CaseUpdateEntry) GetEventTime() string {
	if x != nil {
		return x.EventTime
	}
	return ""
}

func (x *CaseUpdateEntry) GetUpdateSource() v2.UpdateSource {
	if x != nil {
		return x.UpdateSource
	}
	return v2.UpdateSource(0)
}

func (x *CaseUpdateEntry) GetUpdaterId() string {
	if x != nil {
		return x.UpdaterId
	}
	return ""
}

func (x *CaseUpdateEntry) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *CaseUpdateEntry) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CaseUpdateEntry) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CaseUpdateEntry) GetFileAttachments() []*CaseFileReference {
	if x != nil {
		return x.FileAttachments
	}
	return nil
}

// * Immutable record of a status change
type CaseStatusUpdateEntry struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Timestamp      string                 `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                 // When change occurred
	NewStatus      CaseStatus             `protobuf:"varint,2,opt,name=new_status,json=newStatus,proto3,enum=hero.cases.v1.CaseStatus" json:"new_status,omitempty"` // Target state
	PreviousStatus CaseStatus             `protobuf:"varint,3,opt,name=previous_status,json=previousStatus,proto3,enum=hero.cases.v1.CaseStatus" json:"previous_status,omitempty"`
	Note           string                 `protobuf:"bytes,4,opt,name=note,proto3" json:"note,omitempty"` // Reason / context
	UpdaterId      string                 `protobuf:"bytes,5,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdateSource   v2.UpdateSource        `protobuf:"varint,6,opt,name=update_source,json=updateSource,proto3,enum=hero.situations.v2.UpdateSource" json:"update_source,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CaseStatusUpdateEntry) Reset() {
	*x = CaseStatusUpdateEntry{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseStatusUpdateEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseStatusUpdateEntry) ProtoMessage() {}

func (x *CaseStatusUpdateEntry) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseStatusUpdateEntry.ProtoReflect.Descriptor instead.
func (*CaseStatusUpdateEntry) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{2}
}

func (x *CaseStatusUpdateEntry) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *CaseStatusUpdateEntry) GetNewStatus() CaseStatus {
	if x != nil {
		return x.NewStatus
	}
	return CaseStatus_CASE_STATUS_UNSPECIFIED
}

func (x *CaseStatusUpdateEntry) GetPreviousStatus() CaseStatus {
	if x != nil {
		return x.PreviousStatus
	}
	return CaseStatus_CASE_STATUS_UNSPECIFIED
}

func (x *CaseStatusUpdateEntry) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *CaseStatusUpdateEntry) GetUpdaterId() string {
	if x != nil {
		return x.UpdaterId
	}
	return ""
}

func (x *CaseStatusUpdateEntry) GetUpdateSource() v2.UpdateSource {
	if x != nil {
		return x.UpdateSource
	}
	return v2.UpdateSource(0)
}

// * Link between a case and an asset with a defined role
type CaseAssetAssociation struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	Id              string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                          // Unique association id
	CaseId          string                   `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`    // Redundant FK for audits
	AssetId         string                   `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // hero.assets.v2.Asset.id
	AssociationType CaseAssetAssociationType `protobuf:"varint,4,opt,name=association_type,json=associationType,proto3,enum=hero.cases.v1.CaseAssetAssociationType" json:"association_type,omitempty"`
	AssignedAt      string                   `protobuf:"bytes,5,opt,name=assigned_at,json=assignedAt,proto3" json:"assigned_at,omitempty"`                  // When link was created
	Notes           string                   `protobuf:"bytes,6,opt,name=notes,proto3" json:"notes,omitempty"`                                              // Free‑text notes
	AssignerAssetId string                   `protobuf:"bytes,7,opt,name=assigner_asset_id,json=assignerAssetId,proto3" json:"assigner_asset_id,omitempty"` // Who made/changed link
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CaseAssetAssociation) Reset() {
	*x = CaseAssetAssociation{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseAssetAssociation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseAssetAssociation) ProtoMessage() {}

func (x *CaseAssetAssociation) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseAssetAssociation.ProtoReflect.Descriptor instead.
func (*CaseAssetAssociation) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{3}
}

func (x *CaseAssetAssociation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CaseAssetAssociation) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseAssetAssociation) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *CaseAssetAssociation) GetAssociationType() CaseAssetAssociationType {
	if x != nil {
		return x.AssociationType
	}
	return CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_UNSPECIFIED
}

func (x *CaseAssetAssociation) GetAssignedAt() string {
	if x != nil {
		return x.AssignedAt
	}
	return ""
}

func (x *CaseAssetAssociation) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *CaseAssetAssociation) GetAssignerAssetId() string {
	if x != nil {
		return x.AssignerAssetId
	}
	return ""
}

// * Append‑only audit ledger row
type CaseAuditLogEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // UUID
	CaseId        string                 `protobuf:"bytes,2,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Action        CaseAuditAction        `protobuf:"varint,3,opt,name=action,proto3,enum=hero.cases.v1.CaseAuditAction" json:"action,omitempty"`
	ActorAssetId  string                 `protobuf:"bytes,4,opt,name=actor_asset_id,json=actorAssetId,proto3" json:"actor_asset_id,omitempty"` // "SYSTEM" if automated
	Timestamp     string                 `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FieldPath     string                 `protobuf:"bytes,6,opt,name=field_path,json=fieldPath,proto3" json:"field_path,omitempty"` // JSONPath‑ish (e.g., "tags[2]")
	OldValue      string                 `protobuf:"bytes,7,opt,name=old_value,json=oldValue,proto3" json:"old_value,omitempty"`    // Previous value (JSON encoded)
	NewValue      string                 `protobuf:"bytes,8,opt,name=new_value,json=newValue,proto3" json:"new_value,omitempty"`    // New value (JSON encoded)
	Note          string                 `protobuf:"bytes,9,opt,name=note,proto3" json:"note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaseAuditLogEntry) Reset() {
	*x = CaseAuditLogEntry{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseAuditLogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseAuditLogEntry) ProtoMessage() {}

func (x *CaseAuditLogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseAuditLogEntry.ProtoReflect.Descriptor instead.
func (*CaseAuditLogEntry) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{4}
}

func (x *CaseAuditLogEntry) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CaseAuditLogEntry) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseAuditLogEntry) GetAction() CaseAuditAction {
	if x != nil {
		return x.Action
	}
	return CaseAuditAction_CASE_AUDIT_ACTION_UNSPECIFIED
}

func (x *CaseAuditLogEntry) GetActorAssetId() string {
	if x != nil {
		return x.ActorAssetId
	}
	return ""
}

func (x *CaseAuditLogEntry) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *CaseAuditLogEntry) GetFieldPath() string {
	if x != nil {
		return x.FieldPath
	}
	return ""
}

func (x *CaseAuditLogEntry) GetOldValue() string {
	if x != nil {
		return x.OldValue
	}
	return ""
}

func (x *CaseAuditLogEntry) GetNewValue() string {
	if x != nil {
		return x.NewValue
	}
	return ""
}

func (x *CaseAuditLogEntry) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

// * Full snapshot captured at a specific business version
type CaseSnapshot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Version       int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	CaseSnapshot  *Case                  `protobuf:"bytes,3,opt,name=case_snapshot,json=caseSnapshot,proto3" json:"case_snapshot,omitempty"` // Entire object
	Timestamp     string                 `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CaseSnapshot) Reset() {
	*x = CaseSnapshot{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CaseSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseSnapshot) ProtoMessage() {}

func (x *CaseSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseSnapshot.ProtoReflect.Descriptor instead.
func (*CaseSnapshot) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{5}
}

func (x *CaseSnapshot) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseSnapshot) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *CaseSnapshot) GetCaseSnapshot() *Case {
	if x != nil {
		return x.CaseSnapshot
	}
	return nil
}

func (x *CaseSnapshot) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

// * Date range for filtering (inclusive)
type DateRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"` // RFC3339 timestamp, inclusive
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`     // RFC3339 timestamp, inclusive
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{6}
}

func (x *DateRange) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *DateRange) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

// * Field-specific query (limits a search term to one field)
type FieldQuery struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"` // e.g. "title", "description", "tags", "asset_display_name"
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"` // the term to match in that field
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldQuery) Reset() {
	*x = FieldQuery{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldQuery) ProtoMessage() {}

func (x *FieldQuery) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldQuery.ProtoReflect.Descriptor instead.
func (*FieldQuery) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{7}
}

func (x *FieldQuery) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FieldQuery) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

// * Highlighted fragments for a given field in each case
type HighlightResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`         // the field name where matches occurred
	Fragments     []string               `protobuf:"bytes,2,rep,name=fragments,proto3" json:"fragments,omitempty"` // snippets with matches, e.g. ["…urgent…", "…critical…"]
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HighlightResult) Reset() {
	*x = HighlightResult{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HighlightResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HighlightResult) ProtoMessage() {}

func (x *HighlightResult) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HighlightResult.ProtoReflect.Descriptor instead.
func (*HighlightResult) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{8}
}

func (x *HighlightResult) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *HighlightResult) GetFragments() []string {
	if x != nil {
		return x.Fragments
	}
	return nil
}

// -----------------------------------------------------------------------------
// CORE CASE OBJECT
// ---------------------------------------------------------------------------
type Case struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Identity and classification
	Id          string     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                        // UUID‑v4
	OrgId       int32      `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`                    // Tenant / customer identifier
	Type        CaseType   `protobuf:"varint,3,opt,name=type,proto3,enum=hero.cases.v1.CaseType" json:"type,omitempty"`       // Category
	Title       string     `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`                                  // Short human title
	Description string     `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                      // Long description / synopsis
	Status      CaseStatus `protobuf:"varint,6,opt,name=status,proto3,enum=hero.cases.v1.CaseStatus" json:"status,omitempty"` // Current lifecycle state
	Priority    int32      `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                           // 1‑5 scale (higher = more urgent)
	// Relationships
	SituationIds      []string                 `protobuf:"bytes,8,rep,name=situation_ids,json=situationIds,proto3" json:"situation_ids,omitempty"`                 // Related situations
	ReportIds         []string                 `protobuf:"bytes,9,rep,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"`                          // Linked reports
	EntityRefs        []*v1.Reference          `protobuf:"bytes,10,rep,name=entity_refs,json=entityRefs,proto3" json:"entity_refs,omitempty"`                      // People, vehicles, etc. (legacy entity service)
	PropertyRefs      []*v11.PropertyReference `protobuf:"bytes,29,rep,name=property_refs,json=propertyRefs,proto3" json:"property_refs,omitempty"`                // Property service properties
	AssetAssociations []*CaseAssetAssociation  `protobuf:"bytes,11,rep,name=asset_associations,json=assetAssociations,proto3" json:"asset_associations,omitempty"` // All involved assets
	RelatedCaseIds    []string                 `protobuf:"bytes,12,rep,name=related_case_ids,json=relatedCaseIds,proto3" json:"related_case_ids,omitempty"`        // Parent / child / peer
	// History
	Updates       []*CaseUpdateEntry       `protobuf:"bytes,13,rep,name=updates,proto3" json:"updates,omitempty"`                                  // Narrative log
	StatusUpdates []*CaseStatusUpdateEntry `protobuf:"bytes,14,rep,name=status_updates,json=statusUpdates,proto3" json:"status_updates,omitempty"` // State changes
	// Metadata & custom fields
	Tags               []string         `protobuf:"bytes,15,rep,name=tags,proto3" json:"tags,omitempty"`                                                         // Search facets
	AdditionalInfoJson *structpb.Struct `protobuf:"bytes,16,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"` // Arbitrary KV
	ResourceType       string           `protobuf:"bytes,17,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`                     // Constant "CASE" for polymorphic UIs
	Version            int32            `protobuf:"varint,18,opt,name=version,proto3" json:"version,omitempty"`                                                  // Business snapshot version
	// Timeline
	CreateTime   string `protobuf:"bytes,19,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime   string `protobuf:"bytes,20,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	DueDate      string `protobuf:"bytes,21,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"` // SLA target date
	ResolvedTime string `protobuf:"bytes,22,opt,name=resolved_time,json=resolvedTime,proto3" json:"resolved_time,omitempty"`
	CloseTime    string `protobuf:"bytes,23,opt,name=close_time,json=closeTime,proto3" json:"close_time,omitempty"`
	// Audit helpers
	CreatedByAssetId string `protobuf:"bytes,24,opt,name=created_by_asset_id,json=createdByAssetId,proto3" json:"created_by_asset_id,omitempty"`
	UpdatedByAssetId string `protobuf:"bytes,25,opt,name=updated_by_asset_id,json=updatedByAssetId,proto3" json:"updated_by_asset_id,omitempty"`
	// Watcher list – assets who want notifications only
	WatcherAssetIds []string `protobuf:"bytes,26,rep,name=watcher_asset_ids,json=watcherAssetIds,proto3" json:"watcher_asset_ids,omitempty"`
	// Optimistic‑lock token (auto‑incremented on every write)
	Etag int64 `protobuf:"varint,27,opt,name=etag,proto3" json:"etag,omitempty"`
	// Disclosure control flag (default un‑set = follow org policy)
	ReleaseStatus ReleaseStatus `protobuf:"varint,28,opt,name=release_status,json=releaseStatus,proto3,enum=hero.cases.v1.ReleaseStatus" json:"release_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Case) Reset() {
	*x = Case{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Case) ProtoMessage() {}

func (x *Case) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Case.ProtoReflect.Descriptor instead.
func (*Case) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{9}
}

func (x *Case) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Case) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *Case) GetType() CaseType {
	if x != nil {
		return x.Type
	}
	return CaseType_CASE_TYPE_UNSPECIFIED
}

func (x *Case) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Case) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Case) GetStatus() CaseStatus {
	if x != nil {
		return x.Status
	}
	return CaseStatus_CASE_STATUS_UNSPECIFIED
}

func (x *Case) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Case) GetSituationIds() []string {
	if x != nil {
		return x.SituationIds
	}
	return nil
}

func (x *Case) GetReportIds() []string {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

func (x *Case) GetEntityRefs() []*v1.Reference {
	if x != nil {
		return x.EntityRefs
	}
	return nil
}

func (x *Case) GetPropertyRefs() []*v11.PropertyReference {
	if x != nil {
		return x.PropertyRefs
	}
	return nil
}

func (x *Case) GetAssetAssociations() []*CaseAssetAssociation {
	if x != nil {
		return x.AssetAssociations
	}
	return nil
}

func (x *Case) GetRelatedCaseIds() []string {
	if x != nil {
		return x.RelatedCaseIds
	}
	return nil
}

func (x *Case) GetUpdates() []*CaseUpdateEntry {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *Case) GetStatusUpdates() []*CaseStatusUpdateEntry {
	if x != nil {
		return x.StatusUpdates
	}
	return nil
}

func (x *Case) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Case) GetAdditionalInfoJson() *structpb.Struct {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return nil
}

func (x *Case) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Case) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Case) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Case) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Case) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *Case) GetResolvedTime() string {
	if x != nil {
		return x.ResolvedTime
	}
	return ""
}

func (x *Case) GetCloseTime() string {
	if x != nil {
		return x.CloseTime
	}
	return ""
}

func (x *Case) GetCreatedByAssetId() string {
	if x != nil {
		return x.CreatedByAssetId
	}
	return ""
}

func (x *Case) GetUpdatedByAssetId() string {
	if x != nil {
		return x.UpdatedByAssetId
	}
	return ""
}

func (x *Case) GetWatcherAssetIds() []string {
	if x != nil {
		return x.WatcherAssetIds
	}
	return nil
}

func (x *Case) GetEtag() int64 {
	if x != nil {
		return x.Etag
	}
	return 0
}

func (x *Case) GetReleaseStatus() ReleaseStatus {
	if x != nil {
		return x.ReleaseStatus
	}
	return ReleaseStatus_RELEASE_STATUS_UNSPECIFIED
}

// -----------------------------------------------------------------------------
// CRUD & LISTING PAYLOADS
// ---------------------------------------------------------------------------
type CreateCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Case_         *Case                  `protobuf:"bytes,1,opt,name=case_,json=case,proto3" json:"case_,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCaseRequest) Reset() {
	*x = CreateCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCaseRequest) ProtoMessage() {}

func (x *CreateCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCaseRequest.ProtoReflect.Descriptor instead.
func (*CreateCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{10}
}

func (x *CreateCaseRequest) GetCase_() *Case {
	if x != nil {
		return x.Case_
	}
	return nil
}

type CreateCaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Case_         *Case                  `protobuf:"bytes,1,opt,name=case_,json=case,proto3" json:"case_,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCaseResponse) Reset() {
	*x = CreateCaseResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCaseResponse) ProtoMessage() {}

func (x *CreateCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCaseResponse.ProtoReflect.Descriptor instead.
func (*CreateCaseResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{11}
}

func (x *CreateCaseResponse) GetCase_() *Case {
	if x != nil {
		return x.Case_
	}
	return nil
}

type GetCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCaseRequest) Reset() {
	*x = GetCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseRequest) ProtoMessage() {}

func (x *GetCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseRequest.ProtoReflect.Descriptor instead.
func (*GetCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{12}
}

func (x *GetCaseRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Case_         *Case                  `protobuf:"bytes,1,opt,name=case_,json=case,proto3" json:"case_,omitempty"` // Must include last-seen etag
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCaseRequest) Reset() {
	*x = UpdateCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCaseRequest) ProtoMessage() {}

func (x *UpdateCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCaseRequest.ProtoReflect.Descriptor instead.
func (*UpdateCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateCaseRequest) GetCase_() *Case {
	if x != nil {
		return x.Case_
	}
	return nil
}

type DeleteCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCaseRequest) Reset() {
	*x = DeleteCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCaseRequest) ProtoMessage() {}

func (x *DeleteCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCaseRequest.ProtoReflect.Descriptor instead.
func (*DeleteCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteCaseRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ListCasesRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PageSize           int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`                                // Max returned per page
	PageToken          string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`                              // Cursor from previous call
	Status             CaseStatus             `protobuf:"varint,3,opt,name=status,proto3,enum=hero.cases.v1.CaseStatus" json:"status,omitempty"`                      // Filter by lifecycle
	Type               CaseType               `protobuf:"varint,4,opt,name=type,proto3,enum=hero.cases.v1.CaseType" json:"type,omitempty"`                            // Filter by category
	StakeholderAssetId string                 `protobuf:"bytes,5,opt,name=stakeholder_asset_id,json=stakeholderAssetId,proto3" json:"stakeholder_asset_id,omitempty"` // "Cases where asset X involved"
	SituationId        string                 `protobuf:"bytes,6,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`                        // Linked to a specific situation
	ReportId           string                 `protobuf:"bytes,7,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`                                 // Linked to a specific report
	Tags               []string               `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`                                                         // At least one tag matches
	OrderBy            string                 `protobuf:"bytes,9,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`                                    // "update_time desc", etc.
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListCasesRequest) Reset() {
	*x = ListCasesRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesRequest) ProtoMessage() {}

func (x *ListCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesRequest.ProtoReflect.Descriptor instead.
func (*ListCasesRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{15}
}

func (x *ListCasesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCasesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCasesRequest) GetStatus() CaseStatus {
	if x != nil {
		return x.Status
	}
	return CaseStatus_CASE_STATUS_UNSPECIFIED
}

func (x *ListCasesRequest) GetType() CaseType {
	if x != nil {
		return x.Type
	}
	return CaseType_CASE_TYPE_UNSPECIFIED
}

func (x *ListCasesRequest) GetStakeholderAssetId() string {
	if x != nil {
		return x.StakeholderAssetId
	}
	return ""
}

func (x *ListCasesRequest) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *ListCasesRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ListCasesRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ListCasesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListCasesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cases         []*Case                `protobuf:"bytes,1,rep,name=cases,proto3" json:"cases,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCasesResponse) Reset() {
	*x = ListCasesResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesResponse) ProtoMessage() {}

func (x *ListCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesResponse.ProtoReflect.Descriptor instead.
func (*ListCasesResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{16}
}

func (x *ListCasesResponse) GetCases() []*Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *ListCasesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type BatchGetCasesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetCasesRequest) Reset() {
	*x = BatchGetCasesRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCasesRequest) ProtoMessage() {}

func (x *BatchGetCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCasesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetCasesRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{17}
}

func (x *BatchGetCasesRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type BatchGetCasesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cases         []*Case                `protobuf:"bytes,1,rep,name=cases,proto3" json:"cases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetCasesResponse) Reset() {
	*x = BatchGetCasesResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCasesResponse) ProtoMessage() {}

func (x *BatchGetCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCasesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetCasesResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{18}
}

func (x *BatchGetCasesResponse) GetCases() []*Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

// Shortcut list requests (server‑side indexed lookups)
type ListCasesBySituationIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SituationId   string                 `protobuf:"bytes,1,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCasesBySituationIdRequest) Reset() {
	*x = ListCasesBySituationIdRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesBySituationIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesBySituationIdRequest) ProtoMessage() {}

func (x *ListCasesBySituationIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesBySituationIdRequest.ProtoReflect.Descriptor instead.
func (*ListCasesBySituationIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{19}
}

func (x *ListCasesBySituationIdRequest) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *ListCasesBySituationIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCasesBySituationIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListCasesByReportIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportId      string                 `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCasesByReportIdRequest) Reset() {
	*x = ListCasesByReportIdRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesByReportIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesByReportIdRequest) ProtoMessage() {}

func (x *ListCasesByReportIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesByReportIdRequest.ProtoReflect.Descriptor instead.
func (*ListCasesByReportIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{20}
}

func (x *ListCasesByReportIdRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ListCasesByReportIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCasesByReportIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListCasesByAssetIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssetId       string                 `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCasesByAssetIdRequest) Reset() {
	*x = ListCasesByAssetIdRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesByAssetIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesByAssetIdRequest) ProtoMessage() {}

func (x *ListCasesByAssetIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesByAssetIdRequest.ProtoReflect.Descriptor instead.
func (*ListCasesByAssetIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{21}
}

func (x *ListCasesByAssetIdRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ListCasesByAssetIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCasesByAssetIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListCasesByEntityIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntityId      string                 `protobuf:"bytes,1,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCasesByEntityIdRequest) Reset() {
	*x = ListCasesByEntityIdRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesByEntityIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesByEntityIdRequest) ProtoMessage() {}

func (x *ListCasesByEntityIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesByEntityIdRequest.ProtoReflect.Descriptor instead.
func (*ListCasesByEntityIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{22}
}

func (x *ListCasesByEntityIdRequest) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *ListCasesByEntityIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCasesByEntityIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListCasesByPropertyIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PropertyId    string                 `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCasesByPropertyIdRequest) Reset() {
	*x = ListCasesByPropertyIdRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCasesByPropertyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCasesByPropertyIdRequest) ProtoMessage() {}

func (x *ListCasesByPropertyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCasesByPropertyIdRequest.ProtoReflect.Descriptor instead.
func (*ListCasesByPropertyIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{23}
}

func (x *ListCasesByPropertyIdRequest) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *ListCasesByPropertyIdRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCasesByPropertyIdRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// -----------------------------------------------------------------------------
// RELATIONSHIP MUTATORS
// ---------------------------------------------------------------------------
type AddSituationToCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	SituationId   string                 `protobuf:"bytes,2,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddSituationToCaseRequest) Reset() {
	*x = AddSituationToCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddSituationToCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSituationToCaseRequest) ProtoMessage() {}

func (x *AddSituationToCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSituationToCaseRequest.ProtoReflect.Descriptor instead.
func (*AddSituationToCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{24}
}

func (x *AddSituationToCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddSituationToCaseRequest) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

type RemoveSituationFromCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	SituationId   string                 `protobuf:"bytes,2,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveSituationFromCaseRequest) Reset() {
	*x = RemoveSituationFromCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveSituationFromCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveSituationFromCaseRequest) ProtoMessage() {}

func (x *RemoveSituationFromCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveSituationFromCaseRequest.ProtoReflect.Descriptor instead.
func (*RemoveSituationFromCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{25}
}

func (x *RemoveSituationFromCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemoveSituationFromCaseRequest) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

type AddReportToCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	ReportId      string                 `protobuf:"bytes,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddReportToCaseRequest) Reset() {
	*x = AddReportToCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddReportToCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddReportToCaseRequest) ProtoMessage() {}

func (x *AddReportToCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddReportToCaseRequest.ProtoReflect.Descriptor instead.
func (*AddReportToCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{26}
}

func (x *AddReportToCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddReportToCaseRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type RemoveReportFromCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	ReportId      string                 `protobuf:"bytes,2,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveReportFromCaseRequest) Reset() {
	*x = RemoveReportFromCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveReportFromCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveReportFromCaseRequest) ProtoMessage() {}

func (x *RemoveReportFromCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveReportFromCaseRequest.ProtoReflect.Descriptor instead.
func (*RemoveReportFromCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{27}
}

func (x *RemoveReportFromCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemoveReportFromCaseRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

type AddEntityRefToCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	EntityRef     *v1.Reference          `protobuf:"bytes,2,opt,name=entity_ref,json=entityRef,proto3" json:"entity_ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEntityRefToCaseRequest) Reset() {
	*x = AddEntityRefToCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEntityRefToCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEntityRefToCaseRequest) ProtoMessage() {}

func (x *AddEntityRefToCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEntityRefToCaseRequest.ProtoReflect.Descriptor instead.
func (*AddEntityRefToCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{28}
}

func (x *AddEntityRefToCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddEntityRefToCaseRequest) GetEntityRef() *v1.Reference {
	if x != nil {
		return x.EntityRef
	}
	return nil
}

type RemoveEntityRefFromCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	EntityRef     *v1.Reference          `protobuf:"bytes,2,opt,name=entity_ref,json=entityRef,proto3" json:"entity_ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveEntityRefFromCaseRequest) Reset() {
	*x = RemoveEntityRefFromCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveEntityRefFromCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveEntityRefFromCaseRequest) ProtoMessage() {}

func (x *RemoveEntityRefFromCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveEntityRefFromCaseRequest.ProtoReflect.Descriptor instead.
func (*RemoveEntityRefFromCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{29}
}

func (x *RemoveEntityRefFromCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemoveEntityRefFromCaseRequest) GetEntityRef() *v1.Reference {
	if x != nil {
		return x.EntityRef
	}
	return nil
}

// Property reference mutators (mirror entity refs but for property service)
type AddPropertyRefToCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PropertyRef   *v11.PropertyReference `protobuf:"bytes,2,opt,name=property_ref,json=propertyRef,proto3" json:"property_ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPropertyRefToCaseRequest) Reset() {
	*x = AddPropertyRefToCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPropertyRefToCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPropertyRefToCaseRequest) ProtoMessage() {}

func (x *AddPropertyRefToCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPropertyRefToCaseRequest.ProtoReflect.Descriptor instead.
func (*AddPropertyRefToCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{30}
}

func (x *AddPropertyRefToCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddPropertyRefToCaseRequest) GetPropertyRef() *v11.PropertyReference {
	if x != nil {
		return x.PropertyRef
	}
	return nil
}

type RemovePropertyRefFromCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PropertyRef   *v11.PropertyReference `protobuf:"bytes,2,opt,name=property_ref,json=propertyRef,proto3" json:"property_ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemovePropertyRefFromCaseRequest) Reset() {
	*x = RemovePropertyRefFromCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemovePropertyRefFromCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemovePropertyRefFromCaseRequest) ProtoMessage() {}

func (x *RemovePropertyRefFromCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemovePropertyRefFromCaseRequest.ProtoReflect.Descriptor instead.
func (*RemovePropertyRefFromCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{31}
}

func (x *RemovePropertyRefFromCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemovePropertyRefFromCaseRequest) GetPropertyRef() *v11.PropertyReference {
	if x != nil {
		return x.PropertyRef
	}
	return nil
}

type LinkRelatedCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	RelatedCaseId string                 `protobuf:"bytes,2,opt,name=related_case_id,json=relatedCaseId,proto3" json:"related_case_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LinkRelatedCaseRequest) Reset() {
	*x = LinkRelatedCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinkRelatedCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkRelatedCaseRequest) ProtoMessage() {}

func (x *LinkRelatedCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkRelatedCaseRequest.ProtoReflect.Descriptor instead.
func (*LinkRelatedCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{32}
}

func (x *LinkRelatedCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *LinkRelatedCaseRequest) GetRelatedCaseId() string {
	if x != nil {
		return x.RelatedCaseId
	}
	return ""
}

type UnlinkRelatedCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	RelatedCaseId string                 `protobuf:"bytes,2,opt,name=related_case_id,json=relatedCaseId,proto3" json:"related_case_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnlinkRelatedCaseRequest) Reset() {
	*x = UnlinkRelatedCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnlinkRelatedCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlinkRelatedCaseRequest) ProtoMessage() {}

func (x *UnlinkRelatedCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlinkRelatedCaseRequest.ProtoReflect.Descriptor instead.
func (*UnlinkRelatedCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{33}
}

func (x *UnlinkRelatedCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *UnlinkRelatedCaseRequest) GetRelatedCaseId() string {
	if x != nil {
		return x.RelatedCaseId
	}
	return ""
}

// -----------------------------------------------------------------------------
// ASSET ASSOCIATION MUTATORS
// ---------------------------------------------------------------------------
type AssociateAssetToCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Association   *CaseAssetAssociation  `protobuf:"bytes,2,opt,name=association,proto3" json:"association,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssociateAssetToCaseRequest) Reset() {
	*x = AssociateAssetToCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssociateAssetToCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssociateAssetToCaseRequest) ProtoMessage() {}

func (x *AssociateAssetToCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssociateAssetToCaseRequest.ProtoReflect.Descriptor instead.
func (*AssociateAssetToCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{34}
}

func (x *AssociateAssetToCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AssociateAssetToCaseRequest) GetAssociation() *CaseAssetAssociation {
	if x != nil {
		return x.Association
	}
	return nil
}

type AssociateAssetToCaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Association   *CaseAssetAssociation  `protobuf:"bytes,1,opt,name=association,proto3" json:"association,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssociateAssetToCaseResponse) Reset() {
	*x = AssociateAssetToCaseResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssociateAssetToCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssociateAssetToCaseResponse) ProtoMessage() {}

func (x *AssociateAssetToCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssociateAssetToCaseResponse.ProtoReflect.Descriptor instead.
func (*AssociateAssetToCaseResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{35}
}

func (x *AssociateAssetToCaseResponse) GetAssociation() *CaseAssetAssociation {
	if x != nil {
		return x.Association
	}
	return nil
}

// Dedicated message for assigning a case to an investigator
type AssignCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AssetId       string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // The asset to assign as primary investigator
	Notes         string                 `protobuf:"bytes,3,opt,name=notes,proto3" json:"notes,omitempty"`                    // Optional notes about the assignment
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignCaseRequest) Reset() {
	*x = AssignCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignCaseRequest) ProtoMessage() {}

func (x *AssignCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignCaseRequest.ProtoReflect.Descriptor instead.
func (*AssignCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{36}
}

func (x *AssignCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AssignCaseRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *AssignCaseRequest) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type AssignCaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Case_         *Case                  `protobuf:"bytes,1,opt,name=case_,json=case,proto3" json:"case_,omitempty"`
	Association   *CaseAssetAssociation  `protobuf:"bytes,2,opt,name=association,proto3" json:"association,omitempty"`
	OrderId       string                 `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"` // The created order ID for tracking
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignCaseResponse) Reset() {
	*x = AssignCaseResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignCaseResponse) ProtoMessage() {}

func (x *AssignCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignCaseResponse.ProtoReflect.Descriptor instead.
func (*AssignCaseResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{37}
}

func (x *AssignCaseResponse) GetCase_() *Case {
	if x != nil {
		return x.Case_
	}
	return nil
}

func (x *AssignCaseResponse) GetAssociation() *CaseAssetAssociation {
	if x != nil {
		return x.Association
	}
	return nil
}

func (x *AssignCaseResponse) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type UpdateAssetAssociationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Association   *CaseAssetAssociation  `protobuf:"bytes,2,opt,name=association,proto3" json:"association,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssetAssociationRequest) Reset() {
	*x = UpdateAssetAssociationRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssetAssociationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetAssociationRequest) ProtoMessage() {}

func (x *UpdateAssetAssociationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetAssociationRequest.ProtoReflect.Descriptor instead.
func (*UpdateAssetAssociationRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateAssetAssociationRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *UpdateAssetAssociationRequest) GetAssociation() *CaseAssetAssociation {
	if x != nil {
		return x.Association
	}
	return nil
}

type UpdateAssetAssociationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Association   *CaseAssetAssociation  `protobuf:"bytes,1,opt,name=association,proto3" json:"association,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssetAssociationResponse) Reset() {
	*x = UpdateAssetAssociationResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssetAssociationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetAssociationResponse) ProtoMessage() {}

func (x *UpdateAssetAssociationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetAssociationResponse.ProtoReflect.Descriptor instead.
func (*UpdateAssetAssociationResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateAssetAssociationResponse) GetAssociation() *CaseAssetAssociation {
	if x != nil {
		return x.Association
	}
	return nil
}

type DisassociateAssetFromCaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AssociationId string                 `protobuf:"bytes,2,opt,name=association_id,json=associationId,proto3" json:"association_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisassociateAssetFromCaseRequest) Reset() {
	*x = DisassociateAssetFromCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisassociateAssetFromCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisassociateAssetFromCaseRequest) ProtoMessage() {}

func (x *DisassociateAssetFromCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisassociateAssetFromCaseRequest.ProtoReflect.Descriptor instead.
func (*DisassociateAssetFromCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{40}
}

func (x *DisassociateAssetFromCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *DisassociateAssetFromCaseRequest) GetAssociationId() string {
	if x != nil {
		return x.AssociationId
	}
	return ""
}

type ListAssetAssociationsForCaseRequest struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	CaseId          string                   `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PageSize        int32                    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken       string                   `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	AssociationType CaseAssetAssociationType `protobuf:"varint,4,opt,name=association_type,json=associationType,proto3,enum=hero.cases.v1.CaseAssetAssociationType" json:"association_type,omitempty"` // Optional filter
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListAssetAssociationsForCaseRequest) Reset() {
	*x = ListAssetAssociationsForCaseRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAssetAssociationsForCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetAssociationsForCaseRequest) ProtoMessage() {}

func (x *ListAssetAssociationsForCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetAssociationsForCaseRequest.ProtoReflect.Descriptor instead.
func (*ListAssetAssociationsForCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{41}
}

func (x *ListAssetAssociationsForCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListAssetAssociationsForCaseRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAssetAssociationsForCaseRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListAssetAssociationsForCaseRequest) GetAssociationType() CaseAssetAssociationType {
	if x != nil {
		return x.AssociationType
	}
	return CaseAssetAssociationType_ASSET_ASSOCIATION_TYPE_UNSPECIFIED
}

type ListAssetAssociationsForCaseResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Associations  []*CaseAssetAssociation `protobuf:"bytes,1,rep,name=associations,proto3" json:"associations,omitempty"`
	NextPageToken string                  `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAssetAssociationsForCaseResponse) Reset() {
	*x = ListAssetAssociationsForCaseResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAssetAssociationsForCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetAssociationsForCaseResponse) ProtoMessage() {}

func (x *ListAssetAssociationsForCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetAssociationsForCaseResponse.ProtoReflect.Descriptor instead.
func (*ListAssetAssociationsForCaseResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{42}
}

func (x *ListAssetAssociationsForCaseResponse) GetAssociations() []*CaseAssetAssociation {
	if x != nil {
		return x.Associations
	}
	return nil
}

func (x *ListAssetAssociationsForCaseResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// -----------------------------------------------------------------------------
// WATCHER LIST MUTATORS
// ---------------------------------------------------------------------------
type AddWatcherRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AssetId       string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddWatcherRequest) Reset() {
	*x = AddWatcherRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddWatcherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddWatcherRequest) ProtoMessage() {}

func (x *AddWatcherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddWatcherRequest.ProtoReflect.Descriptor instead.
func (*AddWatcherRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{43}
}

func (x *AddWatcherRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddWatcherRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type RemoveWatcherRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AssetId       string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveWatcherRequest) Reset() {
	*x = RemoveWatcherRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveWatcherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveWatcherRequest) ProtoMessage() {}

func (x *RemoveWatcherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveWatcherRequest.ProtoReflect.Descriptor instead.
func (*RemoveWatcherRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{44}
}

func (x *RemoveWatcherRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemoveWatcherRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

// -----------------------------------------------------------------------------
// STATUS / UPDATES / TAGS
// ---------------------------------------------------------------------------
type UpdateCaseStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Status        CaseStatus             `protobuf:"varint,2,opt,name=status,proto3,enum=hero.cases.v1.CaseStatus" json:"status,omitempty"`
	Note          string                 `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCaseStatusRequest) Reset() {
	*x = UpdateCaseStatusRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCaseStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCaseStatusRequest) ProtoMessage() {}

func (x *UpdateCaseStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCaseStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateCaseStatusRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{45}
}

func (x *UpdateCaseStatusRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *UpdateCaseStatusRequest) GetStatus() CaseStatus {
	if x != nil {
		return x.Status
	}
	return CaseStatus_CASE_STATUS_UNSPECIFIED
}

func (x *UpdateCaseStatusRequest) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

type UpdateCaseStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Case_         *Case                  `protobuf:"bytes,1,opt,name=case_,json=case,proto3" json:"case_,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCaseStatusResponse) Reset() {
	*x = UpdateCaseStatusResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCaseStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCaseStatusResponse) ProtoMessage() {}

func (x *UpdateCaseStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCaseStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateCaseStatusResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{46}
}

func (x *UpdateCaseStatusResponse) GetCase_() *Case {
	if x != nil {
		return x.Case_
	}
	return nil
}

type AddCaseUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Update        *CaseUpdateEntry       `protobuf:"bytes,2,opt,name=update,proto3" json:"update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCaseUpdateRequest) Reset() {
	*x = AddCaseUpdateRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCaseUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCaseUpdateRequest) ProtoMessage() {}

func (x *AddCaseUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCaseUpdateRequest.ProtoReflect.Descriptor instead.
func (*AddCaseUpdateRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{47}
}

func (x *AddCaseUpdateRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddCaseUpdateRequest) GetUpdate() *CaseUpdateEntry {
	if x != nil {
		return x.Update
	}
	return nil
}

type RemoveCaseUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Update        *CaseUpdateEntry       `protobuf:"bytes,2,opt,name=update,proto3" json:"update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveCaseUpdateRequest) Reset() {
	*x = RemoveCaseUpdateRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveCaseUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveCaseUpdateRequest) ProtoMessage() {}

func (x *RemoveCaseUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveCaseUpdateRequest.ProtoReflect.Descriptor instead.
func (*RemoveCaseUpdateRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{48}
}

func (x *RemoveCaseUpdateRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemoveCaseUpdateRequest) GetUpdate() *CaseUpdateEntry {
	if x != nil {
		return x.Update
	}
	return nil
}

type ListCaseUpdatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	StartTime     string                 `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       string                 `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"` // Optional range filter
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseUpdatesRequest) Reset() {
	*x = ListCaseUpdatesRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseUpdatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseUpdatesRequest) ProtoMessage() {}

func (x *ListCaseUpdatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseUpdatesRequest.ProtoReflect.Descriptor instead.
func (*ListCaseUpdatesRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{49}
}

func (x *ListCaseUpdatesRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListCaseUpdatesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCaseUpdatesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCaseUpdatesRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ListCaseUpdatesRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type ListCaseUpdatesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Updates       []*CaseUpdateEntry     `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseUpdatesResponse) Reset() {
	*x = ListCaseUpdatesResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseUpdatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseUpdatesResponse) ProtoMessage() {}

func (x *ListCaseUpdatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseUpdatesResponse.ProtoReflect.Descriptor instead.
func (*ListCaseUpdatesResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{50}
}

func (x *ListCaseUpdatesResponse) GetUpdates() []*CaseUpdateEntry {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *ListCaseUpdatesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type ListCaseFileAttachmentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	FileCategory  string                 `protobuf:"bytes,4,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"` // Optional filter by category
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseFileAttachmentsRequest) Reset() {
	*x = ListCaseFileAttachmentsRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseFileAttachmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseFileAttachmentsRequest) ProtoMessage() {}

func (x *ListCaseFileAttachmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseFileAttachmentsRequest.ProtoReflect.Descriptor instead.
func (*ListCaseFileAttachmentsRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{51}
}

func (x *ListCaseFileAttachmentsRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListCaseFileAttachmentsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCaseFileAttachmentsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCaseFileAttachmentsRequest) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

type ListCaseFileAttachmentsResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	FileAttachments []*CaseFileReference   `protobuf:"bytes,1,rep,name=file_attachments,json=fileAttachments,proto3" json:"file_attachments,omitempty"`
	NextPageToken   string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListCaseFileAttachmentsResponse) Reset() {
	*x = ListCaseFileAttachmentsResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseFileAttachmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseFileAttachmentsResponse) ProtoMessage() {}

func (x *ListCaseFileAttachmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseFileAttachmentsResponse.ProtoReflect.Descriptor instead.
func (*ListCaseFileAttachmentsResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{52}
}

func (x *ListCaseFileAttachmentsResponse) GetFileAttachments() []*CaseFileReference {
	if x != nil {
		return x.FileAttachments
	}
	return nil
}

func (x *ListCaseFileAttachmentsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type ListCaseStatusHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseStatusHistoryRequest) Reset() {
	*x = ListCaseStatusHistoryRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseStatusHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseStatusHistoryRequest) ProtoMessage() {}

func (x *ListCaseStatusHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseStatusHistoryRequest.ProtoReflect.Descriptor instead.
func (*ListCaseStatusHistoryRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{53}
}

func (x *ListCaseStatusHistoryRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListCaseStatusHistoryRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCaseStatusHistoryRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListCaseStatusHistoryResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	StatusUpdates []*CaseStatusUpdateEntry `protobuf:"bytes,1,rep,name=status_updates,json=statusUpdates,proto3" json:"status_updates,omitempty"`
	NextPageToken string                   `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseStatusHistoryResponse) Reset() {
	*x = ListCaseStatusHistoryResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseStatusHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseStatusHistoryResponse) ProtoMessage() {}

func (x *ListCaseStatusHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseStatusHistoryResponse.ProtoReflect.Descriptor instead.
func (*ListCaseStatusHistoryResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{54}
}

func (x *ListCaseStatusHistoryResponse) GetStatusUpdates() []*CaseStatusUpdateEntry {
	if x != nil {
		return x.StatusUpdates
	}
	return nil
}

func (x *ListCaseStatusHistoryResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type AddCaseTagRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Tag           string                 `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCaseTagRequest) Reset() {
	*x = AddCaseTagRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCaseTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCaseTagRequest) ProtoMessage() {}

func (x *AddCaseTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCaseTagRequest.ProtoReflect.Descriptor instead.
func (*AddCaseTagRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{55}
}

func (x *AddCaseTagRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddCaseTagRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type RemoveCaseTagRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Tag           string                 `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveCaseTagRequest) Reset() {
	*x = RemoveCaseTagRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveCaseTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveCaseTagRequest) ProtoMessage() {}

func (x *RemoveCaseTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveCaseTagRequest.ProtoReflect.Descriptor instead.
func (*RemoveCaseTagRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{56}
}

func (x *RemoveCaseTagRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *RemoveCaseTagRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type AddAdditionalInfoRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	CaseId             string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AdditionalInfoJson string                 `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddAdditionalInfoRequest) Reset() {
	*x = AddAdditionalInfoRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdditionalInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdditionalInfoRequest) ProtoMessage() {}

func (x *AddAdditionalInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdditionalInfoRequest.ProtoReflect.Descriptor instead.
func (*AddAdditionalInfoRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{57}
}

func (x *AddAdditionalInfoRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddAdditionalInfoRequest) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

type AddAdditionalInfoResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	CaseId             string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	AdditionalInfoJson string                 `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddAdditionalInfoResponse) Reset() {
	*x = AddAdditionalInfoResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdditionalInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdditionalInfoResponse) ProtoMessage() {}

func (x *AddAdditionalInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdditionalInfoResponse.ProtoReflect.Descriptor instead.
func (*AddAdditionalInfoResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{58}
}

func (x *AddAdditionalInfoResponse) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *AddAdditionalInfoResponse) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

// -----------------------------------------------------------------------------
// AUDIT / VERSIONING
// ---------------------------------------------------------------------------
type GetCaseVersionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	Version       int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCaseVersionRequest) Reset() {
	*x = GetCaseVersionRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCaseVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseVersionRequest) ProtoMessage() {}

func (x *GetCaseVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseVersionRequest.ProtoReflect.Descriptor instead.
func (*GetCaseVersionRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{59}
}

func (x *GetCaseVersionRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *GetCaseVersionRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type ListCaseVersionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseVersionsRequest) Reset() {
	*x = ListCaseVersionsRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseVersionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseVersionsRequest) ProtoMessage() {}

func (x *ListCaseVersionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseVersionsRequest.ProtoReflect.Descriptor instead.
func (*ListCaseVersionsRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{60}
}

func (x *ListCaseVersionsRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

type ListCaseVersionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Versions      []int32                `protobuf:"varint,1,rep,packed,name=versions,proto3" json:"versions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseVersionsResponse) Reset() {
	*x = ListCaseVersionsResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseVersionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseVersionsResponse) ProtoMessage() {}

func (x *ListCaseVersionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseVersionsResponse.ProtoReflect.Descriptor instead.
func (*ListCaseVersionsResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{61}
}

func (x *ListCaseVersionsResponse) GetVersions() []int32 {
	if x != nil {
		return x.Versions
	}
	return nil
}

type ListCaseAuditLogRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaseId        string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	FilterAction  CaseAuditAction        `protobuf:"varint,4,opt,name=filter_action,json=filterAction,proto3,enum=hero.cases.v1.CaseAuditAction" json:"filter_action,omitempty"` // Optional action filter
	StartTs       string                 `protobuf:"bytes,5,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs         string                 `protobuf:"bytes,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"` // Optional date range
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseAuditLogRequest) Reset() {
	*x = ListCaseAuditLogRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseAuditLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseAuditLogRequest) ProtoMessage() {}

func (x *ListCaseAuditLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseAuditLogRequest.ProtoReflect.Descriptor instead.
func (*ListCaseAuditLogRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{62}
}

func (x *ListCaseAuditLogRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListCaseAuditLogRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCaseAuditLogRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCaseAuditLogRequest) GetFilterAction() CaseAuditAction {
	if x != nil {
		return x.FilterAction
	}
	return CaseAuditAction_CASE_AUDIT_ACTION_UNSPECIFIED
}

func (x *ListCaseAuditLogRequest) GetStartTs() string {
	if x != nil {
		return x.StartTs
	}
	return ""
}

func (x *ListCaseAuditLogRequest) GetEndTs() string {
	if x != nil {
		return x.EndTs
	}
	return ""
}

type ListCaseAuditLogResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Entries       []*CaseAuditLogEntry   `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCaseAuditLogResponse) Reset() {
	*x = ListCaseAuditLogResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCaseAuditLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCaseAuditLogResponse) ProtoMessage() {}

func (x *ListCaseAuditLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCaseAuditLogResponse.ProtoReflect.Descriptor instead.
func (*ListCaseAuditLogResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{63}
}

func (x *ListCaseAuditLogResponse) GetEntries() []*CaseAuditLogEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *ListCaseAuditLogResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// * Request for searching cases with advanced filtering and text search capabilities
//
// HYBRID SEARCH STRATEGY for optimal performance + API compliance:
// - When search_fields is empty or includes all fields → Uses search_vector column (fastest)
// - When search_fields is restricted → Uses field-specific vectors (respects API contract)
//
// The search_vector includes weighted content from:
//   - Case ID and title (highest weight 'A')
//   - Case description (high weight 'B')
//
// Supported search_fields and field_queries.field values:
//   - "id" (case unique identifier)
//   - "title" (case title)
//   - "description" (case description)
//
// For related data searches (tags, assets, entities), use the dedicated exact-match filter arrays.
type SearchCasesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ────── Free-text & scoped field queries ──────
	Query        string        `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`                                   // full-text search (hybrid: search_vector or field-specific)
	SearchFields []string      `protobuf:"bytes,2,rep,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"` // restrict query to specific fields: id, title, description
	FieldQueries []*FieldQuery `protobuf:"bytes,3,rep,name=field_queries,json=fieldQueries,proto3" json:"field_queries,omitempty"` // ILIKE pattern matching on: id, title, description
	// ────── Case filters (exact match) ──────
	Status            []CaseStatus    `protobuf:"varint,4,rep,packed,name=status,proto3,enum=hero.cases.v1.CaseStatus" json:"status,omitempty"`                                        // cases.status IN (...)
	Type              []CaseType      `protobuf:"varint,5,rep,packed,name=type,proto3,enum=hero.cases.v1.CaseType" json:"type,omitempty"`                                              // cases.type IN (...)
	Priority          []int32         `protobuf:"varint,6,rep,packed,name=priority,proto3" json:"priority,omitempty"`                                                                  // cases.priority IN (...)
	Tags              []string        `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                                                                  // cases.tags @> ARRAY[...]
	CreatedByAssetIds []string        `protobuf:"bytes,8,rep,name=created_by_asset_ids,json=createdByAssetIds,proto3" json:"created_by_asset_ids,omitempty"`                           // cases.created_by_asset_id IN (...)
	UpdatedByAssetIds []string        `protobuf:"bytes,9,rep,name=updated_by_asset_ids,json=updatedByAssetIds,proto3" json:"updated_by_asset_ids,omitempty"`                           // cases.updated_by_asset_id IN (...)
	ReleaseStatus     []ReleaseStatus `protobuf:"varint,10,rep,packed,name=release_status,json=releaseStatus,proto3,enum=hero.cases.v1.ReleaseStatus" json:"release_status,omitempty"` // cases.release_status IN (...)
	// ────── Relationship filters (exact match) ──────
	SituationIds     []string                   `protobuf:"bytes,11,rep,name=situation_ids,json=situationIds,proto3" json:"situation_ids,omitempty"`                                                                 // cases.situation_ids @> ARRAY[...]
	ReportIds        []string                   `protobuf:"bytes,12,rep,name=report_ids,json=reportIds,proto3" json:"report_ids,omitempty"`                                                                          // cases.report_ids @> ARRAY[...]
	RelatedCaseIds   []string                   `protobuf:"bytes,13,rep,name=related_case_ids,json=relatedCaseIds,proto3" json:"related_case_ids,omitempty"`                                                         // cases.related_case_ids @> ARRAY[...]
	AssetIds         []string                   `protobuf:"bytes,14,rep,name=asset_ids,json=assetIds,proto3" json:"asset_ids,omitempty"`                                                                             // asset_associations[].asset_id IN (...)
	AssociationTypes []CaseAssetAssociationType `protobuf:"varint,15,rep,packed,name=association_types,json=associationTypes,proto3,enum=hero.cases.v1.CaseAssetAssociationType" json:"association_types,omitempty"` // asset_associations[].association_type IN (...)
	EntityRefIds     []string                   `protobuf:"bytes,16,rep,name=entity_ref_ids,json=entityRefIds,proto3" json:"entity_ref_ids,omitempty"`                                                               // entity_refs[].id IN (...)
	EntityRefTypes   []string                   `protobuf:"bytes,17,rep,name=entity_ref_types,json=entityRefTypes,proto3" json:"entity_ref_types,omitempty"`                                                         // entity_refs[].type IN (...)
	WatcherAssetIds  []string                   `protobuf:"bytes,18,rep,name=watcher_asset_ids,json=watcherAssetIds,proto3" json:"watcher_asset_ids,omitempty"`                                                      // cases.watcher_asset_ids @> ARRAY[...]
	// ────── Date range filters ──────
	CreateTime   *DateRange `protobuf:"bytes,19,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`       // cases.create_time BETWEEN ...
	UpdateTime   *DateRange `protobuf:"bytes,20,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`       // cases.update_time BETWEEN ...
	DueDate      *DateRange `protobuf:"bytes,21,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`                // cases.due_date BETWEEN ...
	ResolvedTime *DateRange `protobuf:"bytes,22,opt,name=resolved_time,json=resolvedTime,proto3" json:"resolved_time,omitempty"` // cases.resolved_time BETWEEN ...
	CloseTime    *DateRange `protobuf:"bytes,23,opt,name=close_time,json=closeTime,proto3" json:"close_time,omitempty"`          // cases.close_time BETWEEN ...
	// ────── Pagination & sorting ──────
	PageSize      int32         `protobuf:"varint,24,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string        `protobuf:"bytes,25,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`                             // cursor
	OrderBy       SearchOrderBy `protobuf:"varint,26,opt,name=order_by,json=orderBy,proto3,enum=hero.cases.v1.SearchOrderBy" json:"order_by,omitempty"` // default = RELEVANCE
	Ascending     bool          `protobuf:"varint,27,opt,name=ascending,proto3" json:"ascending,omitempty"`                                             // default = false (DESC)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCasesRequest) Reset() {
	*x = SearchCasesRequest{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCasesRequest) ProtoMessage() {}

func (x *SearchCasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCasesRequest.ProtoReflect.Descriptor instead.
func (*SearchCasesRequest) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{64}
}

func (x *SearchCasesRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchCasesRequest) GetSearchFields() []string {
	if x != nil {
		return x.SearchFields
	}
	return nil
}

func (x *SearchCasesRequest) GetFieldQueries() []*FieldQuery {
	if x != nil {
		return x.FieldQueries
	}
	return nil
}

func (x *SearchCasesRequest) GetStatus() []CaseStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SearchCasesRequest) GetType() []CaseType {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *SearchCasesRequest) GetPriority() []int32 {
	if x != nil {
		return x.Priority
	}
	return nil
}

func (x *SearchCasesRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SearchCasesRequest) GetCreatedByAssetIds() []string {
	if x != nil {
		return x.CreatedByAssetIds
	}
	return nil
}

func (x *SearchCasesRequest) GetUpdatedByAssetIds() []string {
	if x != nil {
		return x.UpdatedByAssetIds
	}
	return nil
}

func (x *SearchCasesRequest) GetReleaseStatus() []ReleaseStatus {
	if x != nil {
		return x.ReleaseStatus
	}
	return nil
}

func (x *SearchCasesRequest) GetSituationIds() []string {
	if x != nil {
		return x.SituationIds
	}
	return nil
}

func (x *SearchCasesRequest) GetReportIds() []string {
	if x != nil {
		return x.ReportIds
	}
	return nil
}

func (x *SearchCasesRequest) GetRelatedCaseIds() []string {
	if x != nil {
		return x.RelatedCaseIds
	}
	return nil
}

func (x *SearchCasesRequest) GetAssetIds() []string {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *SearchCasesRequest) GetAssociationTypes() []CaseAssetAssociationType {
	if x != nil {
		return x.AssociationTypes
	}
	return nil
}

func (x *SearchCasesRequest) GetEntityRefIds() []string {
	if x != nil {
		return x.EntityRefIds
	}
	return nil
}

func (x *SearchCasesRequest) GetEntityRefTypes() []string {
	if x != nil {
		return x.EntityRefTypes
	}
	return nil
}

func (x *SearchCasesRequest) GetWatcherAssetIds() []string {
	if x != nil {
		return x.WatcherAssetIds
	}
	return nil
}

func (x *SearchCasesRequest) GetCreateTime() *DateRange {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *SearchCasesRequest) GetUpdateTime() *DateRange {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *SearchCasesRequest) GetDueDate() *DateRange {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *SearchCasesRequest) GetResolvedTime() *DateRange {
	if x != nil {
		return x.ResolvedTime
	}
	return nil
}

func (x *SearchCasesRequest) GetCloseTime() *DateRange {
	if x != nil {
		return x.CloseTime
	}
	return nil
}

func (x *SearchCasesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchCasesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *SearchCasesRequest) GetOrderBy() SearchOrderBy {
	if x != nil {
		return x.OrderBy
	}
	return SearchOrderBy_SEARCH_ORDER_BY_UNSPECIFIED
}

func (x *SearchCasesRequest) GetAscending() bool {
	if x != nil {
		return x.Ascending
	}
	return false
}

type SearchCasesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The page of cases that matched the query (already ordered & trimmed).
	Cases []*Case `protobuf:"bytes,1,rep,name=cases,proto3" json:"cases,omitempty"`
	// Cursor for fetching the next page; empty when you're on the last page.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// Per-case highlight information keyed by case ID.
	// Each HighlightResult lists the field name and one-or-more matched fragments
	// (e.g.  "…urgent investigation…", "…security incident…").
	Highlights map[string]*HighlightResult `protobuf:"bytes,3,rep,name=highlights,proto3" json:"highlights,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Total number of hits *before* pagination—useful for UI counters.
	TotalResults  int32 `protobuf:"varint,4,opt,name=total_results,json=totalResults,proto3" json:"total_results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCasesResponse) Reset() {
	*x = SearchCasesResponse{}
	mi := &file_hero_cases_v1_cases_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCasesResponse) ProtoMessage() {}

func (x *SearchCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_cases_v1_cases_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCasesResponse.ProtoReflect.Descriptor instead.
func (*SearchCasesResponse) Descriptor() ([]byte, []int) {
	return file_hero_cases_v1_cases_proto_rawDescGZIP(), []int{65}
}

func (x *SearchCasesResponse) GetCases() []*Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *SearchCasesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchCasesResponse) GetHighlights() map[string]*HighlightResult {
	if x != nil {
		return x.Highlights
	}
	return nil
}

func (x *SearchCasesResponse) GetTotalResults() int32 {
	if x != nil {
		return x.TotalResults
	}
	return 0
}

var File_hero_cases_v1_cases_proto protoreflect.FileDescriptor

var file_hero_cases_v1_cases_proto_rawDesc = []byte{
	0x0a, 0x19, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x23, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x76, 0x32, 0x2f, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x02, 0x0a, 0x11,
	0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x33, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xec, 0x02, 0x0a, 0x0f, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x0d,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x4b, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0f, 0x66,
	0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xad,
	0x02, 0x0a, 0x15, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x38, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x42, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x91,
	0x02, 0x0a, 0x14, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x10, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x22, 0xa5, 0x02, 0x0a, 0x11, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x36, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1d, 0x0a,
	0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09,
	0x6f, 0x6c, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x77,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65,
	0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x0c, 0x43,
	0x61, 0x73, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38,
	0x0a, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x73, 0x65,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x2f, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x22, 0x38, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x22, 0x45, 0x0a, 0x0f, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72,
	0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xd8, 0x09, 0x0a, 0x04, 0x43, 0x61, 0x73,
	0x65, 0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80,
	0xb5, 0x18, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x2b,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x64, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x72, 0x65, 0x66, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x66, 0x73, 0x12, 0x48, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x72,
	0x65, 0x66, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0c,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x73, 0x12, 0x52, 0x0a, 0x12,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x07, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x49, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x12, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x2d, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x2d, 0x0a, 0x13, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x77, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x77, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x74,
	0x61, 0x67, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x74, 0x61, 0x67, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x3d, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61,
	0x73, 0x65, 0x22, 0x3e, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61,
	0x73, 0x65, 0x22, 0x26, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3d, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x22, 0x29, 0x0a, 0x11, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01,
	0x52, 0x02, 0x69, 0x64, 0x22, 0xcf, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x61, 0x6b, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x22, 0x66, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52,
	0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x28,
	0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x42, 0x0a, 0x15, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x29, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x22, 0x7e, 0x0a, 0x1d,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x53, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x75, 0x0a, 0x1a,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x72, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x75, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7b,
	0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5d, 0x0a, 0x19, 0x41,
	0x64, 0x64, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x1e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f,
	0x6d, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80,
	0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x54,
	0x0a, 0x16, 0x41, 0x64, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x1b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x22,
	0x74, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66, 0x54,
	0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80,
	0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0a, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x66, 0x22, 0x79, 0x0a, 0x1e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66,
	0x22, 0x84, 0x01, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x66, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x46, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x22, 0x89, 0x01, 0x0a, 0x20, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x46, 0x72, 0x6f,
	0x6d, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80,
	0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0c, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x66, 0x22, 0x5f, 0x0a, 0x16, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04,
	0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x22, 0x61, 0x0a, 0x18, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x1b, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x65, 0x0a,
	0x1c, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a,
	0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01,
	0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x12, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x28, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x61, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a,
	0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a,
	0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x67, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x68, 0x0a,
	0x20, 0x44, 0x69, 0x73, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xd4, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x52, 0x0a, 0x10, 0x61, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x97,
	0x01, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x73, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x4d, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04,
	0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0x44, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x04, 0x63, 0x61, 0x73, 0x65,
	0x22, 0x6d, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22,
	0x70, 0x0a, 0x17, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18,
	0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x22, 0xad, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80,
	0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x7b, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x07,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa0,
	0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x22, 0x96, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78,
	0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x79, 0x0a, 0x1c, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18,
	0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x94, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x44, 0x0a, 0x11,
	0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74,
	0x61, 0x67, 0x22, 0x47, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18,
	0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x22, 0x6b, 0x0a, 0x18, 0x41,
	0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x6c, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x50, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x38, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x22, 0x36, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xeb, 0x01, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x52, 0x06, 0x63,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x43, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x73, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x22, 0x7e, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c,
	0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xd9, 0x09, 0x0a, 0x12, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0c, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x51, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x54, 0x0a, 0x11,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x10, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x66,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x66, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x77, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x77,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x39,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x37, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x61, 0x73, 0x63, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x22, 0xc0, 0x02, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x05,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x52, 0x0a, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x5d, 0x0a, 0x0f, 0x48, 0x69, 0x67, 0x68,
	0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x34, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x69, 0x67,
	0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0xe2, 0x02, 0x0a, 0x0a, 0x43, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x53, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x1c,
	0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19,
	0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x45,
	0x53, 0x54, 0x49, 0x47, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x23, 0x0a, 0x1f, 0x43,
	0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05,
	0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4f, 0x4e, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x53,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x07, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x44, 0x10, 0x08, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c,
	0x4f, 0x53, 0x45, 0x44, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x44, 0x10, 0x0a,
	0x12, 0x2a, 0x0a, 0x26, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x56,
	0x45, 0x53, 0x54, 0x49, 0x47, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x2a, 0xb7, 0x02, 0x0a,
	0x08, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x53,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x41,
	0x53, 0x4b, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x49, 0x47, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x49,
	0x4d, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x08, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0x63, 0x2a, 0xa4, 0x03, 0x0a, 0x18, 0x43, 0x61, 0x73, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53,
	0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x49, 0x47, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x49, 0x47, 0x41,
	0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41,
	0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4f, 0x42, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x04, 0x12,
	0x26, 0x0a, 0x22, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59,
	0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x57, 0x49, 0x54, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x07, 0x12,
	0x23, 0x0a, 0x1f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x45, 0x52, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x41, 0x53,
	0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43,
	0x4f, 0x4c, 0x4c, 0x41, 0x42, 0x4f, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x09, 0x2a, 0x8b, 0x02,
	0x0a, 0x0f, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44,
	0x49, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02,
	0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x03, 0x12, 0x1e,
	0x0a, 0x1a, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x21,
	0x0a, 0x1d, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x05, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x06, 0x12,
	0x1a, 0x0a, 0x16, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10, 0x07, 0x2a, 0xb3, 0x01, 0x0a, 0x0d,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x1a, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a,
	0x15, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x4c, 0x45,
	0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x57, 0x5f, 0x45, 0x4e, 0x46, 0x4f,
	0x52, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x03, 0x12, 0x21,
	0x0a, 0x1d, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x44, 0x4f, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x10,
	0x04, 0x2a, 0xe9, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x56, 0x41, 0x4e, 0x43,
	0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54,
	0x59, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x05, 0x12,
	0x1c, 0x0a, 0x18, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x42, 0x59, 0x5f, 0x44, 0x55, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x06, 0x32, 0xbb, 0x1d,
	0x0a, 0x0b, 0x43, 0x61, 0x73, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x51, 0x0a,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x20, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3d, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12,
	0x43, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x20, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61,
	0x73, 0x65, 0x12, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4e, 0x0a, 0x09,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0d,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x23, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x53, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x62, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x42, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x28, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x42,
	0x79, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x53, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x17, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x53, 0x69, 0x74, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x12, 0x25, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x12,
	0x2a, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x72, 0x6f, 0x6d,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x53, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66,
	0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x66, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x17, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x66,
	0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x12, 0x2a, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x54, 0x6f, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x61, 0x0a,
	0x19, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52,
	0x65, 0x66, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x12, 0x2f, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x65, 0x66, 0x46, 0x72, 0x6f, 0x6d,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x4d, 0x0a, 0x0f, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x73, 0x65, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12,
	0x51, 0x0a, 0x11, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x73, 0x65, 0x12, 0x51, 0x0a, 0x0a, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x14, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x12, 0x2a, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a,
	0x19, 0x44, 0x69, 0x73, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x73, 0x65, 0x12, 0x2f, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x61, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f,
	0x72, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a,
	0x0a, 0x41, 0x64, 0x64, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x20, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x73, 0x65, 0x12, 0x49, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x63, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x49, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x4f, 0x0a,
	0x10, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x60,
	0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x78, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2d, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x15, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43,
	0x0a, 0x0a, 0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x54, 0x61, 0x67, 0x12, 0x20, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x43, 0x61, 0x73, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x73,
	0x65, 0x54, 0x61, 0x67, 0x12, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x54,
	0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x12, 0x66,
	0x0a, 0x11, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x73, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x63, 0x0a, 0x10, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x63, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x4c, 0x6f, 0x67, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x61, 0x73, 0x65, 0x73, 0x12, 0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x1b, 0x5a, 0x19, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x63, 0x61, 0x73, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_cases_v1_cases_proto_rawDescOnce sync.Once
	file_hero_cases_v1_cases_proto_rawDescData = file_hero_cases_v1_cases_proto_rawDesc
)

func file_hero_cases_v1_cases_proto_rawDescGZIP() []byte {
	file_hero_cases_v1_cases_proto_rawDescOnce.Do(func() {
		file_hero_cases_v1_cases_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_cases_v1_cases_proto_rawDescData)
	})
	return file_hero_cases_v1_cases_proto_rawDescData
}

var file_hero_cases_v1_cases_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_hero_cases_v1_cases_proto_msgTypes = make([]protoimpl.MessageInfo, 67)
var file_hero_cases_v1_cases_proto_goTypes = []any{
	(CaseStatus)(0),                              // 0: hero.cases.v1.CaseStatus
	(CaseType)(0),                                // 1: hero.cases.v1.CaseType
	(CaseAssetAssociationType)(0),                // 2: hero.cases.v1.CaseAssetAssociationType
	(CaseAuditAction)(0),                         // 3: hero.cases.v1.CaseAuditAction
	(ReleaseStatus)(0),                           // 4: hero.cases.v1.ReleaseStatus
	(SearchOrderBy)(0),                           // 5: hero.cases.v1.SearchOrderBy
	(*CaseFileReference)(nil),                    // 6: hero.cases.v1.CaseFileReference
	(*CaseUpdateEntry)(nil),                      // 7: hero.cases.v1.CaseUpdateEntry
	(*CaseStatusUpdateEntry)(nil),                // 8: hero.cases.v1.CaseStatusUpdateEntry
	(*CaseAssetAssociation)(nil),                 // 9: hero.cases.v1.CaseAssetAssociation
	(*CaseAuditLogEntry)(nil),                    // 10: hero.cases.v1.CaseAuditLogEntry
	(*CaseSnapshot)(nil),                         // 11: hero.cases.v1.CaseSnapshot
	(*DateRange)(nil),                            // 12: hero.cases.v1.DateRange
	(*FieldQuery)(nil),                           // 13: hero.cases.v1.FieldQuery
	(*HighlightResult)(nil),                      // 14: hero.cases.v1.HighlightResult
	(*Case)(nil),                                 // 15: hero.cases.v1.Case
	(*CreateCaseRequest)(nil),                    // 16: hero.cases.v1.CreateCaseRequest
	(*CreateCaseResponse)(nil),                   // 17: hero.cases.v1.CreateCaseResponse
	(*GetCaseRequest)(nil),                       // 18: hero.cases.v1.GetCaseRequest
	(*UpdateCaseRequest)(nil),                    // 19: hero.cases.v1.UpdateCaseRequest
	(*DeleteCaseRequest)(nil),                    // 20: hero.cases.v1.DeleteCaseRequest
	(*ListCasesRequest)(nil),                     // 21: hero.cases.v1.ListCasesRequest
	(*ListCasesResponse)(nil),                    // 22: hero.cases.v1.ListCasesResponse
	(*BatchGetCasesRequest)(nil),                 // 23: hero.cases.v1.BatchGetCasesRequest
	(*BatchGetCasesResponse)(nil),                // 24: hero.cases.v1.BatchGetCasesResponse
	(*ListCasesBySituationIdRequest)(nil),        // 25: hero.cases.v1.ListCasesBySituationIdRequest
	(*ListCasesByReportIdRequest)(nil),           // 26: hero.cases.v1.ListCasesByReportIdRequest
	(*ListCasesByAssetIdRequest)(nil),            // 27: hero.cases.v1.ListCasesByAssetIdRequest
	(*ListCasesByEntityIdRequest)(nil),           // 28: hero.cases.v1.ListCasesByEntityIdRequest
	(*ListCasesByPropertyIdRequest)(nil),         // 29: hero.cases.v1.ListCasesByPropertyIdRequest
	(*AddSituationToCaseRequest)(nil),            // 30: hero.cases.v1.AddSituationToCaseRequest
	(*RemoveSituationFromCaseRequest)(nil),       // 31: hero.cases.v1.RemoveSituationFromCaseRequest
	(*AddReportToCaseRequest)(nil),               // 32: hero.cases.v1.AddReportToCaseRequest
	(*RemoveReportFromCaseRequest)(nil),          // 33: hero.cases.v1.RemoveReportFromCaseRequest
	(*AddEntityRefToCaseRequest)(nil),            // 34: hero.cases.v1.AddEntityRefToCaseRequest
	(*RemoveEntityRefFromCaseRequest)(nil),       // 35: hero.cases.v1.RemoveEntityRefFromCaseRequest
	(*AddPropertyRefToCaseRequest)(nil),          // 36: hero.cases.v1.AddPropertyRefToCaseRequest
	(*RemovePropertyRefFromCaseRequest)(nil),     // 37: hero.cases.v1.RemovePropertyRefFromCaseRequest
	(*LinkRelatedCaseRequest)(nil),               // 38: hero.cases.v1.LinkRelatedCaseRequest
	(*UnlinkRelatedCaseRequest)(nil),             // 39: hero.cases.v1.UnlinkRelatedCaseRequest
	(*AssociateAssetToCaseRequest)(nil),          // 40: hero.cases.v1.AssociateAssetToCaseRequest
	(*AssociateAssetToCaseResponse)(nil),         // 41: hero.cases.v1.AssociateAssetToCaseResponse
	(*AssignCaseRequest)(nil),                    // 42: hero.cases.v1.AssignCaseRequest
	(*AssignCaseResponse)(nil),                   // 43: hero.cases.v1.AssignCaseResponse
	(*UpdateAssetAssociationRequest)(nil),        // 44: hero.cases.v1.UpdateAssetAssociationRequest
	(*UpdateAssetAssociationResponse)(nil),       // 45: hero.cases.v1.UpdateAssetAssociationResponse
	(*DisassociateAssetFromCaseRequest)(nil),     // 46: hero.cases.v1.DisassociateAssetFromCaseRequest
	(*ListAssetAssociationsForCaseRequest)(nil),  // 47: hero.cases.v1.ListAssetAssociationsForCaseRequest
	(*ListAssetAssociationsForCaseResponse)(nil), // 48: hero.cases.v1.ListAssetAssociationsForCaseResponse
	(*AddWatcherRequest)(nil),                    // 49: hero.cases.v1.AddWatcherRequest
	(*RemoveWatcherRequest)(nil),                 // 50: hero.cases.v1.RemoveWatcherRequest
	(*UpdateCaseStatusRequest)(nil),              // 51: hero.cases.v1.UpdateCaseStatusRequest
	(*UpdateCaseStatusResponse)(nil),             // 52: hero.cases.v1.UpdateCaseStatusResponse
	(*AddCaseUpdateRequest)(nil),                 // 53: hero.cases.v1.AddCaseUpdateRequest
	(*RemoveCaseUpdateRequest)(nil),              // 54: hero.cases.v1.RemoveCaseUpdateRequest
	(*ListCaseUpdatesRequest)(nil),               // 55: hero.cases.v1.ListCaseUpdatesRequest
	(*ListCaseUpdatesResponse)(nil),              // 56: hero.cases.v1.ListCaseUpdatesResponse
	(*ListCaseFileAttachmentsRequest)(nil),       // 57: hero.cases.v1.ListCaseFileAttachmentsRequest
	(*ListCaseFileAttachmentsResponse)(nil),      // 58: hero.cases.v1.ListCaseFileAttachmentsResponse
	(*ListCaseStatusHistoryRequest)(nil),         // 59: hero.cases.v1.ListCaseStatusHistoryRequest
	(*ListCaseStatusHistoryResponse)(nil),        // 60: hero.cases.v1.ListCaseStatusHistoryResponse
	(*AddCaseTagRequest)(nil),                    // 61: hero.cases.v1.AddCaseTagRequest
	(*RemoveCaseTagRequest)(nil),                 // 62: hero.cases.v1.RemoveCaseTagRequest
	(*AddAdditionalInfoRequest)(nil),             // 63: hero.cases.v1.AddAdditionalInfoRequest
	(*AddAdditionalInfoResponse)(nil),            // 64: hero.cases.v1.AddAdditionalInfoResponse
	(*GetCaseVersionRequest)(nil),                // 65: hero.cases.v1.GetCaseVersionRequest
	(*ListCaseVersionsRequest)(nil),              // 66: hero.cases.v1.ListCaseVersionsRequest
	(*ListCaseVersionsResponse)(nil),             // 67: hero.cases.v1.ListCaseVersionsResponse
	(*ListCaseAuditLogRequest)(nil),              // 68: hero.cases.v1.ListCaseAuditLogRequest
	(*ListCaseAuditLogResponse)(nil),             // 69: hero.cases.v1.ListCaseAuditLogResponse
	(*SearchCasesRequest)(nil),                   // 70: hero.cases.v1.SearchCasesRequest
	(*SearchCasesResponse)(nil),                  // 71: hero.cases.v1.SearchCasesResponse
	nil,                                          // 72: hero.cases.v1.SearchCasesResponse.HighlightsEntry
	(*structpb.Struct)(nil),                      // 73: google.protobuf.Struct
	(v2.UpdateSource)(0),                         // 74: hero.situations.v2.UpdateSource
	(*v1.Reference)(nil),                         // 75: hero.entity.v1.Reference
	(*v11.PropertyReference)(nil),                // 76: hero.property.v1.PropertyReference
	(*emptypb.Empty)(nil),                        // 77: google.protobuf.Empty
}
var file_hero_cases_v1_cases_proto_depIdxs = []int32{
	73,  // 0: hero.cases.v1.CaseFileReference.metadata:type_name -> google.protobuf.Struct
	74,  // 1: hero.cases.v1.CaseUpdateEntry.update_source:type_name -> hero.situations.v2.UpdateSource
	73,  // 2: hero.cases.v1.CaseUpdateEntry.data:type_name -> google.protobuf.Struct
	6,   // 3: hero.cases.v1.CaseUpdateEntry.file_attachments:type_name -> hero.cases.v1.CaseFileReference
	0,   // 4: hero.cases.v1.CaseStatusUpdateEntry.new_status:type_name -> hero.cases.v1.CaseStatus
	0,   // 5: hero.cases.v1.CaseStatusUpdateEntry.previous_status:type_name -> hero.cases.v1.CaseStatus
	74,  // 6: hero.cases.v1.CaseStatusUpdateEntry.update_source:type_name -> hero.situations.v2.UpdateSource
	2,   // 7: hero.cases.v1.CaseAssetAssociation.association_type:type_name -> hero.cases.v1.CaseAssetAssociationType
	3,   // 8: hero.cases.v1.CaseAuditLogEntry.action:type_name -> hero.cases.v1.CaseAuditAction
	15,  // 9: hero.cases.v1.CaseSnapshot.case_snapshot:type_name -> hero.cases.v1.Case
	1,   // 10: hero.cases.v1.Case.type:type_name -> hero.cases.v1.CaseType
	0,   // 11: hero.cases.v1.Case.status:type_name -> hero.cases.v1.CaseStatus
	75,  // 12: hero.cases.v1.Case.entity_refs:type_name -> hero.entity.v1.Reference
	76,  // 13: hero.cases.v1.Case.property_refs:type_name -> hero.property.v1.PropertyReference
	9,   // 14: hero.cases.v1.Case.asset_associations:type_name -> hero.cases.v1.CaseAssetAssociation
	7,   // 15: hero.cases.v1.Case.updates:type_name -> hero.cases.v1.CaseUpdateEntry
	8,   // 16: hero.cases.v1.Case.status_updates:type_name -> hero.cases.v1.CaseStatusUpdateEntry
	73,  // 17: hero.cases.v1.Case.additional_info_json:type_name -> google.protobuf.Struct
	4,   // 18: hero.cases.v1.Case.release_status:type_name -> hero.cases.v1.ReleaseStatus
	15,  // 19: hero.cases.v1.CreateCaseRequest.case_:type_name -> hero.cases.v1.Case
	15,  // 20: hero.cases.v1.CreateCaseResponse.case_:type_name -> hero.cases.v1.Case
	15,  // 21: hero.cases.v1.UpdateCaseRequest.case_:type_name -> hero.cases.v1.Case
	0,   // 22: hero.cases.v1.ListCasesRequest.status:type_name -> hero.cases.v1.CaseStatus
	1,   // 23: hero.cases.v1.ListCasesRequest.type:type_name -> hero.cases.v1.CaseType
	15,  // 24: hero.cases.v1.ListCasesResponse.cases:type_name -> hero.cases.v1.Case
	15,  // 25: hero.cases.v1.BatchGetCasesResponse.cases:type_name -> hero.cases.v1.Case
	75,  // 26: hero.cases.v1.AddEntityRefToCaseRequest.entity_ref:type_name -> hero.entity.v1.Reference
	75,  // 27: hero.cases.v1.RemoveEntityRefFromCaseRequest.entity_ref:type_name -> hero.entity.v1.Reference
	76,  // 28: hero.cases.v1.AddPropertyRefToCaseRequest.property_ref:type_name -> hero.property.v1.PropertyReference
	76,  // 29: hero.cases.v1.RemovePropertyRefFromCaseRequest.property_ref:type_name -> hero.property.v1.PropertyReference
	9,   // 30: hero.cases.v1.AssociateAssetToCaseRequest.association:type_name -> hero.cases.v1.CaseAssetAssociation
	9,   // 31: hero.cases.v1.AssociateAssetToCaseResponse.association:type_name -> hero.cases.v1.CaseAssetAssociation
	15,  // 32: hero.cases.v1.AssignCaseResponse.case_:type_name -> hero.cases.v1.Case
	9,   // 33: hero.cases.v1.AssignCaseResponse.association:type_name -> hero.cases.v1.CaseAssetAssociation
	9,   // 34: hero.cases.v1.UpdateAssetAssociationRequest.association:type_name -> hero.cases.v1.CaseAssetAssociation
	9,   // 35: hero.cases.v1.UpdateAssetAssociationResponse.association:type_name -> hero.cases.v1.CaseAssetAssociation
	2,   // 36: hero.cases.v1.ListAssetAssociationsForCaseRequest.association_type:type_name -> hero.cases.v1.CaseAssetAssociationType
	9,   // 37: hero.cases.v1.ListAssetAssociationsForCaseResponse.associations:type_name -> hero.cases.v1.CaseAssetAssociation
	0,   // 38: hero.cases.v1.UpdateCaseStatusRequest.status:type_name -> hero.cases.v1.CaseStatus
	15,  // 39: hero.cases.v1.UpdateCaseStatusResponse.case_:type_name -> hero.cases.v1.Case
	7,   // 40: hero.cases.v1.AddCaseUpdateRequest.update:type_name -> hero.cases.v1.CaseUpdateEntry
	7,   // 41: hero.cases.v1.RemoveCaseUpdateRequest.update:type_name -> hero.cases.v1.CaseUpdateEntry
	7,   // 42: hero.cases.v1.ListCaseUpdatesResponse.updates:type_name -> hero.cases.v1.CaseUpdateEntry
	6,   // 43: hero.cases.v1.ListCaseFileAttachmentsResponse.file_attachments:type_name -> hero.cases.v1.CaseFileReference
	8,   // 44: hero.cases.v1.ListCaseStatusHistoryResponse.status_updates:type_name -> hero.cases.v1.CaseStatusUpdateEntry
	3,   // 45: hero.cases.v1.ListCaseAuditLogRequest.filter_action:type_name -> hero.cases.v1.CaseAuditAction
	10,  // 46: hero.cases.v1.ListCaseAuditLogResponse.entries:type_name -> hero.cases.v1.CaseAuditLogEntry
	13,  // 47: hero.cases.v1.SearchCasesRequest.field_queries:type_name -> hero.cases.v1.FieldQuery
	0,   // 48: hero.cases.v1.SearchCasesRequest.status:type_name -> hero.cases.v1.CaseStatus
	1,   // 49: hero.cases.v1.SearchCasesRequest.type:type_name -> hero.cases.v1.CaseType
	4,   // 50: hero.cases.v1.SearchCasesRequest.release_status:type_name -> hero.cases.v1.ReleaseStatus
	2,   // 51: hero.cases.v1.SearchCasesRequest.association_types:type_name -> hero.cases.v1.CaseAssetAssociationType
	12,  // 52: hero.cases.v1.SearchCasesRequest.create_time:type_name -> hero.cases.v1.DateRange
	12,  // 53: hero.cases.v1.SearchCasesRequest.update_time:type_name -> hero.cases.v1.DateRange
	12,  // 54: hero.cases.v1.SearchCasesRequest.due_date:type_name -> hero.cases.v1.DateRange
	12,  // 55: hero.cases.v1.SearchCasesRequest.resolved_time:type_name -> hero.cases.v1.DateRange
	12,  // 56: hero.cases.v1.SearchCasesRequest.close_time:type_name -> hero.cases.v1.DateRange
	5,   // 57: hero.cases.v1.SearchCasesRequest.order_by:type_name -> hero.cases.v1.SearchOrderBy
	15,  // 58: hero.cases.v1.SearchCasesResponse.cases:type_name -> hero.cases.v1.Case
	72,  // 59: hero.cases.v1.SearchCasesResponse.highlights:type_name -> hero.cases.v1.SearchCasesResponse.HighlightsEntry
	14,  // 60: hero.cases.v1.SearchCasesResponse.HighlightsEntry.value:type_name -> hero.cases.v1.HighlightResult
	16,  // 61: hero.cases.v1.CaseService.CreateCase:input_type -> hero.cases.v1.CreateCaseRequest
	18,  // 62: hero.cases.v1.CaseService.GetCase:input_type -> hero.cases.v1.GetCaseRequest
	19,  // 63: hero.cases.v1.CaseService.UpdateCase:input_type -> hero.cases.v1.UpdateCaseRequest
	20,  // 64: hero.cases.v1.CaseService.DeleteCase:input_type -> hero.cases.v1.DeleteCaseRequest
	21,  // 65: hero.cases.v1.CaseService.ListCases:input_type -> hero.cases.v1.ListCasesRequest
	23,  // 66: hero.cases.v1.CaseService.BatchGetCases:input_type -> hero.cases.v1.BatchGetCasesRequest
	25,  // 67: hero.cases.v1.CaseService.ListCasesBySituationId:input_type -> hero.cases.v1.ListCasesBySituationIdRequest
	26,  // 68: hero.cases.v1.CaseService.ListCasesByReportId:input_type -> hero.cases.v1.ListCasesByReportIdRequest
	27,  // 69: hero.cases.v1.CaseService.ListCasesByAssetId:input_type -> hero.cases.v1.ListCasesByAssetIdRequest
	28,  // 70: hero.cases.v1.CaseService.ListCasesByEntityId:input_type -> hero.cases.v1.ListCasesByEntityIdRequest
	29,  // 71: hero.cases.v1.CaseService.ListCasesByPropertyId:input_type -> hero.cases.v1.ListCasesByPropertyIdRequest
	30,  // 72: hero.cases.v1.CaseService.AddSituationToCase:input_type -> hero.cases.v1.AddSituationToCaseRequest
	31,  // 73: hero.cases.v1.CaseService.RemoveSituationFromCase:input_type -> hero.cases.v1.RemoveSituationFromCaseRequest
	32,  // 74: hero.cases.v1.CaseService.AddReportToCase:input_type -> hero.cases.v1.AddReportToCaseRequest
	33,  // 75: hero.cases.v1.CaseService.RemoveReportFromCase:input_type -> hero.cases.v1.RemoveReportFromCaseRequest
	34,  // 76: hero.cases.v1.CaseService.AddEntityRefToCase:input_type -> hero.cases.v1.AddEntityRefToCaseRequest
	35,  // 77: hero.cases.v1.CaseService.RemoveEntityRefFromCase:input_type -> hero.cases.v1.RemoveEntityRefFromCaseRequest
	36,  // 78: hero.cases.v1.CaseService.AddPropertyRefToCase:input_type -> hero.cases.v1.AddPropertyRefToCaseRequest
	37,  // 79: hero.cases.v1.CaseService.RemovePropertyRefFromCase:input_type -> hero.cases.v1.RemovePropertyRefFromCaseRequest
	38,  // 80: hero.cases.v1.CaseService.LinkRelatedCase:input_type -> hero.cases.v1.LinkRelatedCaseRequest
	39,  // 81: hero.cases.v1.CaseService.UnlinkRelatedCase:input_type -> hero.cases.v1.UnlinkRelatedCaseRequest
	42,  // 82: hero.cases.v1.CaseService.AssignCase:input_type -> hero.cases.v1.AssignCaseRequest
	40,  // 83: hero.cases.v1.CaseService.AssociateAssetToCase:input_type -> hero.cases.v1.AssociateAssetToCaseRequest
	44,  // 84: hero.cases.v1.CaseService.UpdateAssetAssociation:input_type -> hero.cases.v1.UpdateAssetAssociationRequest
	46,  // 85: hero.cases.v1.CaseService.DisassociateAssetFromCase:input_type -> hero.cases.v1.DisassociateAssetFromCaseRequest
	47,  // 86: hero.cases.v1.CaseService.ListAssetAssociationsForCase:input_type -> hero.cases.v1.ListAssetAssociationsForCaseRequest
	49,  // 87: hero.cases.v1.CaseService.AddWatcher:input_type -> hero.cases.v1.AddWatcherRequest
	50,  // 88: hero.cases.v1.CaseService.RemoveWatcher:input_type -> hero.cases.v1.RemoveWatcherRequest
	51,  // 89: hero.cases.v1.CaseService.UpdateCaseStatus:input_type -> hero.cases.v1.UpdateCaseStatusRequest
	53,  // 90: hero.cases.v1.CaseService.AddCaseUpdate:input_type -> hero.cases.v1.AddCaseUpdateRequest
	54,  // 91: hero.cases.v1.CaseService.RemoveCaseUpdate:input_type -> hero.cases.v1.RemoveCaseUpdateRequest
	55,  // 92: hero.cases.v1.CaseService.ListCaseUpdates:input_type -> hero.cases.v1.ListCaseUpdatesRequest
	57,  // 93: hero.cases.v1.CaseService.ListCaseFileAttachments:input_type -> hero.cases.v1.ListCaseFileAttachmentsRequest
	59,  // 94: hero.cases.v1.CaseService.ListCaseStatusHistory:input_type -> hero.cases.v1.ListCaseStatusHistoryRequest
	61,  // 95: hero.cases.v1.CaseService.AddCaseTag:input_type -> hero.cases.v1.AddCaseTagRequest
	62,  // 96: hero.cases.v1.CaseService.RemoveCaseTag:input_type -> hero.cases.v1.RemoveCaseTagRequest
	63,  // 97: hero.cases.v1.CaseService.AddAdditionalInfo:input_type -> hero.cases.v1.AddAdditionalInfoRequest
	65,  // 98: hero.cases.v1.CaseService.GetCaseVersion:input_type -> hero.cases.v1.GetCaseVersionRequest
	66,  // 99: hero.cases.v1.CaseService.ListCaseVersions:input_type -> hero.cases.v1.ListCaseVersionsRequest
	68,  // 100: hero.cases.v1.CaseService.ListCaseAuditLog:input_type -> hero.cases.v1.ListCaseAuditLogRequest
	70,  // 101: hero.cases.v1.CaseService.SearchCases:input_type -> hero.cases.v1.SearchCasesRequest
	17,  // 102: hero.cases.v1.CaseService.CreateCase:output_type -> hero.cases.v1.CreateCaseResponse
	15,  // 103: hero.cases.v1.CaseService.GetCase:output_type -> hero.cases.v1.Case
	15,  // 104: hero.cases.v1.CaseService.UpdateCase:output_type -> hero.cases.v1.Case
	77,  // 105: hero.cases.v1.CaseService.DeleteCase:output_type -> google.protobuf.Empty
	22,  // 106: hero.cases.v1.CaseService.ListCases:output_type -> hero.cases.v1.ListCasesResponse
	24,  // 107: hero.cases.v1.CaseService.BatchGetCases:output_type -> hero.cases.v1.BatchGetCasesResponse
	22,  // 108: hero.cases.v1.CaseService.ListCasesBySituationId:output_type -> hero.cases.v1.ListCasesResponse
	22,  // 109: hero.cases.v1.CaseService.ListCasesByReportId:output_type -> hero.cases.v1.ListCasesResponse
	22,  // 110: hero.cases.v1.CaseService.ListCasesByAssetId:output_type -> hero.cases.v1.ListCasesResponse
	22,  // 111: hero.cases.v1.CaseService.ListCasesByEntityId:output_type -> hero.cases.v1.ListCasesResponse
	22,  // 112: hero.cases.v1.CaseService.ListCasesByPropertyId:output_type -> hero.cases.v1.ListCasesResponse
	15,  // 113: hero.cases.v1.CaseService.AddSituationToCase:output_type -> hero.cases.v1.Case
	15,  // 114: hero.cases.v1.CaseService.RemoveSituationFromCase:output_type -> hero.cases.v1.Case
	15,  // 115: hero.cases.v1.CaseService.AddReportToCase:output_type -> hero.cases.v1.Case
	15,  // 116: hero.cases.v1.CaseService.RemoveReportFromCase:output_type -> hero.cases.v1.Case
	15,  // 117: hero.cases.v1.CaseService.AddEntityRefToCase:output_type -> hero.cases.v1.Case
	15,  // 118: hero.cases.v1.CaseService.RemoveEntityRefFromCase:output_type -> hero.cases.v1.Case
	15,  // 119: hero.cases.v1.CaseService.AddPropertyRefToCase:output_type -> hero.cases.v1.Case
	15,  // 120: hero.cases.v1.CaseService.RemovePropertyRefFromCase:output_type -> hero.cases.v1.Case
	15,  // 121: hero.cases.v1.CaseService.LinkRelatedCase:output_type -> hero.cases.v1.Case
	15,  // 122: hero.cases.v1.CaseService.UnlinkRelatedCase:output_type -> hero.cases.v1.Case
	43,  // 123: hero.cases.v1.CaseService.AssignCase:output_type -> hero.cases.v1.AssignCaseResponse
	41,  // 124: hero.cases.v1.CaseService.AssociateAssetToCase:output_type -> hero.cases.v1.AssociateAssetToCaseResponse
	45,  // 125: hero.cases.v1.CaseService.UpdateAssetAssociation:output_type -> hero.cases.v1.UpdateAssetAssociationResponse
	77,  // 126: hero.cases.v1.CaseService.DisassociateAssetFromCase:output_type -> google.protobuf.Empty
	48,  // 127: hero.cases.v1.CaseService.ListAssetAssociationsForCase:output_type -> hero.cases.v1.ListAssetAssociationsForCaseResponse
	15,  // 128: hero.cases.v1.CaseService.AddWatcher:output_type -> hero.cases.v1.Case
	15,  // 129: hero.cases.v1.CaseService.RemoveWatcher:output_type -> hero.cases.v1.Case
	52,  // 130: hero.cases.v1.CaseService.UpdateCaseStatus:output_type -> hero.cases.v1.UpdateCaseStatusResponse
	15,  // 131: hero.cases.v1.CaseService.AddCaseUpdate:output_type -> hero.cases.v1.Case
	15,  // 132: hero.cases.v1.CaseService.RemoveCaseUpdate:output_type -> hero.cases.v1.Case
	56,  // 133: hero.cases.v1.CaseService.ListCaseUpdates:output_type -> hero.cases.v1.ListCaseUpdatesResponse
	58,  // 134: hero.cases.v1.CaseService.ListCaseFileAttachments:output_type -> hero.cases.v1.ListCaseFileAttachmentsResponse
	60,  // 135: hero.cases.v1.CaseService.ListCaseStatusHistory:output_type -> hero.cases.v1.ListCaseStatusHistoryResponse
	15,  // 136: hero.cases.v1.CaseService.AddCaseTag:output_type -> hero.cases.v1.Case
	15,  // 137: hero.cases.v1.CaseService.RemoveCaseTag:output_type -> hero.cases.v1.Case
	64,  // 138: hero.cases.v1.CaseService.AddAdditionalInfo:output_type -> hero.cases.v1.AddAdditionalInfoResponse
	11,  // 139: hero.cases.v1.CaseService.GetCaseVersion:output_type -> hero.cases.v1.CaseSnapshot
	67,  // 140: hero.cases.v1.CaseService.ListCaseVersions:output_type -> hero.cases.v1.ListCaseVersionsResponse
	69,  // 141: hero.cases.v1.CaseService.ListCaseAuditLog:output_type -> hero.cases.v1.ListCaseAuditLogResponse
	71,  // 142: hero.cases.v1.CaseService.SearchCases:output_type -> hero.cases.v1.SearchCasesResponse
	102, // [102:143] is the sub-list for method output_type
	61,  // [61:102] is the sub-list for method input_type
	61,  // [61:61] is the sub-list for extension type_name
	61,  // [61:61] is the sub-list for extension extendee
	0,   // [0:61] is the sub-list for field type_name
}

func init() { file_hero_cases_v1_cases_proto_init() }
func file_hero_cases_v1_cases_proto_init() {
	if File_hero_cases_v1_cases_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_cases_v1_cases_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   67,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_cases_v1_cases_proto_goTypes,
		DependencyIndexes: file_hero_cases_v1_cases_proto_depIdxs,
		EnumInfos:         file_hero_cases_v1_cases_proto_enumTypes,
		MessageInfos:      file_hero_cases_v1_cases_proto_msgTypes,
	}.Build()
	File_hero_cases_v1_cases_proto = out.File
	file_hero_cases_v1_cases_proto_rawDesc = nil
	file_hero_cases_v1_cases_proto_goTypes = nil
	file_hero_cases_v1_cases_proto_depIdxs = nil
}
