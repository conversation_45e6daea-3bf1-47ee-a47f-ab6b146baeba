syntax = "proto3";

package hero.cases.v1;

/*  ============================================================================
    NOTE
    ----
    • All "time" fields use ISO‑8601 strings (same pattern as hero.reports.v2).
    • `etag` is an optimistic‑locking token: every successful write must bump
      it; clients must supply `expected_etag` on UpdateCase to avoid lost updates.
    • `version` is a *business* snapshot counter (e.g., after a review round),
      NOT an optimistic‑lock; it can skip numbers or roll back.
    ============================================================================ */

option go_package = "proto/hero/cases/v1;cases";

/*-----------------------------------------------------------------------------
 * IMPORTS
 *---------------------------------------------------------------------------*/
import "google/protobuf/empty.proto";   // for delete RPCs
import "google/protobuf/struct.proto";  // for flexible JSON metadata

/* hero.* protos referenced by ID strings or enums (no binary dependency) */
import "hero/assets/v2/assets.proto";
import "hero/entity/v1/entity.proto";
import "hero/property/v1/property.proto";
import "hero/situations/v2/situations.proto";
import "hero/reports/v2/reports.proto";
import "hero/permissions/v1/permissions.proto";

/*-----------------------------------------------------------------------------
 * ENUMS
 *---------------------------------------------------------------------------*/

/** Overall lifecycle of a case */
enum CaseStatus {
  CASE_STATUS_UNSPECIFIED = 0;
  CASE_STATUS_NEW         = 1;  // Logged, awaiting triage
  CASE_STATUS_OPEN        = 2;  // Actively being scoped/triaged
  CASE_STATUS_UNDER_REVIEW= 3;  // Submitted for managerial review
  CASE_STATUS_INVESTIGATING = 4; // Active investigative work
  CASE_STATUS_PENDING_INFORMATION = 5; // Blocked, waiting on data
  CASE_STATUS_ON_HOLD     = 6;  // Manually paused
  CASE_STATUS_ESCALATED   = 7;  // Handed to higher authority
  CASE_STATUS_RESOLVED    = 8;  // Root issue fixed
  CASE_STATUS_CLOSED      = 9;  // Administratively closed
  CASE_STATUS_ARCHIVED    = 10; // Long‑term retention (readonly)
  CASE_STATUS_ASSIGNED_FOR_INVESTIGATION = 11; // Assigned to investigator, awaiting start
}

/** High‑level category / playbook selector */
enum CaseType {
  CASE_TYPE_UNSPECIFIED       = 0;
  CASE_TYPE_SECURITY_INCIDENT = 1;
  CASE_TYPE_SAFETY_INCIDENT   = 2;
  CASE_TYPE_OPERATIONAL_TASK  = 3;
  CASE_TYPE_CUSTOMER_COMPLAINT= 4;
  CASE_TYPE_INVESTIGATION     = 5;
  CASE_TYPE_COMPLIANCE_REVIEW = 6;
  CASE_TYPE_INSURANCE_CLAIM   = 7;
  CASE_TYPE_ADMINISTRATIVE    = 8;
  CASE_TYPE_OTHER             = 99;
}

/** Role an asset plays within the case */
enum CaseAssetAssociationType {
  ASSET_ASSOCIATION_TYPE_UNSPECIFIED          = 0;
  ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR = 1;
  ASSET_ASSOCIATION_TYPE_INVESTIGATOR         = 2;
  ASSET_ASSOCIATION_TYPE_OBSERVER             = 3;
  ASSET_ASSOCIATION_TYPE_APPROVER             = 4;
  ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY          = 5;
  ASSET_ASSOCIATION_TYPE_WITNESS              = 6;
  ASSET_ASSOCIATION_TYPE_SUBJECT              = 7;
  ASSET_ASSOCIATION_TYPE_REPORTER             = 8;
  ASSET_ASSOCIATION_TYPE_COLLABORATOR         = 9;
}

/** Type of change recorded in the audit log */
enum CaseAuditAction {
  CASE_AUDIT_ACTION_UNSPECIFIED = 0;
  CASE_AUDIT_ACTION_CREATE      = 1; // Case was created
  CASE_AUDIT_ACTION_UPDATE      = 2; // Generic field edit
  CASE_AUDIT_ACTION_STATUS      = 3; // Status transition
  CASE_AUDIT_ACTION_RELATION    = 4; // Link/unlink other object
  CASE_AUDIT_ACTION_ASSOCIATION = 5; // Asset association mutation
  CASE_AUDIT_ACTION_DELETE      = 6; // Hard delete
  CASE_AUDIT_ACTION_VIEW        = 7; // View case details
}

/** Controls what content reviewers may legally disclose */
enum ReleaseStatus {
  RELEASE_STATUS_UNSPECIFIED              = 0;
  RELEASE_STATUS_PUBLIC                   = 1; // Safe for public release
  RELEASE_STATUS_INTERNAL                 = 2; // Company‑internal only
  RELEASE_STATUS_LAW_ENFORCEMENT_ONLY     = 3; // Shareable with LE partners
  RELEASE_STATUS_DO_NOT_RELEASE           = 4; // Withhold from all external requests
}

/** Enum for the order by field in the search cases request */
enum SearchOrderBy {
  SEARCH_ORDER_BY_UNSPECIFIED = 0;
  SEARCH_ORDER_BY_RELEVANCE   = 1;
  SEARCH_ORDER_BY_CREATE_TIME = 2;
  SEARCH_ORDER_BY_UPDATE_TIME = 3;
  SEARCH_ORDER_BY_PRIORITY    = 4;
  SEARCH_ORDER_BY_STATUS      = 5;
  SEARCH_ORDER_BY_DUE_DATE    = 6;
}

/*-----------------------------------------------------------------------------
 * HELPER MESSAGES
 *---------------------------------------------------------------------------*/

/** File reference for case attachments, linking to filerepository service */
message CaseFileReference {
  string id                         = 1; // Unique identifier for this file reference within the case update
  string case_id                    = 2; // Reference to the case this file belongs to
  string file_id                    = 3; // FileMetadata.id from filerepository service - REQUIRED
  string caption                    = 4; // Optional caption/description for the file
  string display_name               = 5; // Optional display name (fallback to original filename)
  int32  display_order              = 6; // Order for displaying files in UI (0-based)
  string file_category              = 7; // Category of the file (e.g., "evidence_photo", "evidence_video", "evidence_audio", "evidence_document", "correspondence", "other")
  google.protobuf.Struct metadata   = 8; // Additional metadata about the file reference
}

/** Free‑form timeline entry (used for narrative updates) */
message CaseUpdateEntry {
  string message        = 1;  // Human-readable event text
  string event_time     = 2;  // ISO-8601 timestamp when the event occurred
  hero.situations.v2.UpdateSource update_source = 3; // System / human
  string updater_id     = 4;  // Asset or system id
  string event_type     = 5;  // Optional: "EVIDENCE_ADDED", etc.
  string display_name   = 6;  // Cached for convenience

  // Arbitrary structured payload for machine-readable details
  google.protobuf.Struct data = 7;
  
  // File attachments for this case update entry
  repeated CaseFileReference file_attachments = 8; // References to files in filerepository
}

/** Immutable record of a status change */
message CaseStatusUpdateEntry {
  string timestamp  = 1;          // When change occurred
  CaseStatus new_status = 2;          // Target state
  CaseStatus previous_status = 3;
  string note           = 4;          // Reason / context
  string updater_id     = 5;
  hero.situations.v2.UpdateSource update_source = 6;
}

/** Link between a case and an asset with a defined role */
message CaseAssetAssociation {
  string id                 = 1;   // Unique association id
  string case_id            = 2;   // Redundant FK for audits
  string asset_id           = 3;   // hero.assets.v2.Asset.id
  CaseAssetAssociationType association_type = 4;
  string assigned_at    = 5;   // When link was created
  string notes              = 6;   // Free‑text notes
  string assigner_asset_id  = 7;   // Who made/changed link
}

/** Append‑only audit ledger row */
message CaseAuditLogEntry {
  string id             = 1;  // UUID
  string case_id        = 2;
  CaseAuditAction action = 3;
  string actor_asset_id = 4;  // "SYSTEM" if automated
  string timestamp  = 5;
  string field_path     = 6;  // JSONPath‑ish (e.g., "tags[2]")
  string old_value      = 7;  // Previous value (JSON encoded)
  string new_value      = 8;  // New value (JSON encoded)
  string note           = 9;
}

/** Full snapshot captured at a specific business version */
message CaseSnapshot {
  string case_id       = 1;
  int32  version       = 2;
  Case   case_snapshot = 3;   // Entire object
  string timestamp = 4;
}

/*-----------------------------------------------------------------------------
 * SEARCH HELPER MESSAGES
 *---------------------------------------------------------------------------*/

/** Date range for filtering (inclusive) */
message DateRange {
  string from = 1;  // RFC3339 timestamp, inclusive
  string to   = 2;  // RFC3339 timestamp, inclusive
}

/** Field-specific query (limits a search term to one field) */
message FieldQuery {
  string field = 1;   // e.g. "title", "description", "tags", "asset_display_name"
  string query = 2;   // the term to match in that field
}

/** Highlighted fragments for a given field in each case */
message HighlightResult {
  string field             = 1;  // the field name where matches occurred
  repeated string fragments = 2; // snippets with matches, e.g. ["…urgent…", "…critical…"]
}

/*-----------------------------------------------------------------------------
 * CORE CASE OBJECT
 *---------------------------------------------------------------------------*/
message Case {
  /* Identity and classification */
  string id           = 1 [(hero.permissions.v1.permission_id) = true]; // UUID‑v4
  int32  org_id       = 2;  // Tenant / customer identifier
  CaseType type       = 3;  // Category
  string title        = 4;  // Short human title
  string description  = 5;  // Long description / synopsis
  CaseStatus status   = 6;  // Current lifecycle state
  int32 priority      = 7;  // 1‑5 scale (higher = more urgent)

  /* Relationships */
  repeated string                   situation_ids      = 8;  // Related situations
  repeated string                   report_ids         = 9;  // Linked reports
  repeated hero.entity.v1.Reference      entity_refs        = 10; // People, vehicles, etc. (legacy entity service)
  repeated hero.property.v1.PropertyReference property_refs = 29; // Property service properties
  repeated CaseAssetAssociation     asset_associations = 11; // All involved assets
  repeated string                   related_case_ids   = 12; // Parent / child / peer

  /* History */
  repeated CaseUpdateEntry       updates        = 13; // Narrative log
  repeated CaseStatusUpdateEntry status_updates = 14; // State changes

  /* Metadata & custom fields */
  repeated string            tags   = 15; // Search facets
  google.protobuf.Struct     additional_info_json = 16; // Arbitrary KV
  string resource_type       = 17; // Constant "CASE" for polymorphic UIs
  int32  version             = 18; // Business snapshot version

  /* Timeline */
  string create_time   = 19;
  string update_time   = 20;
  string due_date      = 21; // SLA target date
  string resolved_time = 22;
  string close_time    = 23;

  /* Audit helpers */
  string created_by_asset_id = 24;
  string updated_by_asset_id = 25;

  /* Watcher list – assets who want notifications only */
  repeated string watcher_asset_ids = 26;

  /* Optimistic‑lock token (auto‑incremented on every write) */
  int64 etag = 27;
  /* Disclosure control flag (default un‑set = follow org policy) */
  ReleaseStatus release_status      = 28;

}

/*-----------------------------------------------------------------------------
 * CRUD & LISTING PAYLOADS
 *---------------------------------------------------------------------------*/
message CreateCaseRequest  { Case case_ = 1; }   // id/etag set by server
message CreateCaseResponse { Case case_ = 1; }

message GetCaseRequest { string id = 1 [(hero.permissions.v1.permission_id) = true]; }

message UpdateCaseRequest {               // Optimistic locking via etag
  Case case_ = 1;                        // Must include last-seen etag
}

message DeleteCaseRequest { string id = 1 [(hero.permissions.v1.permission_id) = true]; }

message ListCasesRequest {                // Generic filter / paging
  int32  page_size             = 1;       // Max returned per page
  string page_token            = 2;       // Cursor from previous call
  CaseStatus status            = 3;       // Filter by lifecycle
  CaseType type                = 4;       // Filter by category
  string stakeholder_asset_id  = 5;       // "Cases where asset X involved"
  string situation_id          = 6;       // Linked to a specific situation
  string report_id             = 7;       // Linked to a specific report
  repeated string tags         = 8;       // At least one tag matches
  string order_by              = 9;       // "update_time desc", etc.
}
message ListCasesResponse { repeated Case cases = 1; string next_page_token = 2; }

message BatchGetCasesRequest  { repeated string ids = 1; }
message BatchGetCasesResponse { repeated Case cases = 1; }

/* Shortcut list requests (server‑side indexed lookups) */
message ListCasesBySituationIdRequest { string situation_id = 1; int32 page_size = 2; string page_token = 3; }
message ListCasesByReportIdRequest    { string report_id    = 1; int32 page_size = 2; string page_token = 3; }
message ListCasesByAssetIdRequest     { string asset_id     = 1; int32 page_size = 2; string page_token = 3; }
message ListCasesByEntityIdRequest    { string entity_id    = 1; int32 page_size = 2; string page_token = 3; }
message ListCasesByPropertyIdRequest  { string property_id  = 1; int32 page_size = 2; string page_token = 3; }

/*-----------------------------------------------------------------------------
 * RELATIONSHIP MUTATORS
 *---------------------------------------------------------------------------*/
message AddSituationToCaseRequest      { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string situation_id = 2; } // Link situation
message RemoveSituationFromCaseRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string situation_id = 2; } // Unlink situation

message AddReportToCaseRequest         { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string report_id = 2; }    // Link report
message RemoveReportFromCaseRequest    { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string report_id = 2; }    // Unlink report

message AddEntityRefToCaseRequest      { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; hero.entity.v1.Reference entity_ref = 2; }
message RemoveEntityRefFromCaseRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; hero.entity.v1.Reference entity_ref = 2; }

// Property reference mutators (mirror entity refs but for property service)
message AddPropertyRefToCaseRequest      { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; hero.property.v1.PropertyReference property_ref = 2; }
message RemovePropertyRefFromCaseRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; hero.property.v1.PropertyReference property_ref = 2; }

message LinkRelatedCaseRequest         { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string related_case_id = 2; } // Make peer/child link
message UnlinkRelatedCaseRequest       { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string related_case_id = 2; }

/*-----------------------------------------------------------------------------
 * ASSET ASSOCIATION MUTATORS
 *---------------------------------------------------------------------------*/
message AssociateAssetToCaseRequest   { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; CaseAssetAssociation association = 2; } // Add or replace role
message AssociateAssetToCaseResponse  { CaseAssetAssociation association = 1; }

// Dedicated message for assigning a case to an investigator
message AssignCaseRequest {
  string case_id = 1 [(hero.permissions.v1.permission_id) = true];
  string asset_id = 2;  // The asset to assign as primary investigator
  string notes = 3;     // Optional notes about the assignment
}
message AssignCaseResponse {
  Case case_ = 1;
  CaseAssetAssociation association = 2;
  string order_id = 3;  // The created order ID for tracking
}

message UpdateAssetAssociationRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; CaseAssetAssociation association = 2; } // Modify role/notes
message UpdateAssetAssociationResponse{ CaseAssetAssociation association = 1; }

message DisassociateAssetFromCaseRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string association_id = 2; } // Remove link

message ListAssetAssociationsForCaseRequest {
  string case_id = 1 [(hero.permissions.v1.permission_id) = true]; int32 page_size = 2; string page_token = 3;
  CaseAssetAssociationType association_type = 4; // Optional filter
}
message ListAssetAssociationsForCaseResponse {
  repeated CaseAssetAssociation associations = 1; string next_page_token = 2;
}

/*-----------------------------------------------------------------------------
 * WATCHER LIST MUTATORS
 *---------------------------------------------------------------------------*/
message AddWatcherRequest    { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string asset_id = 2; } // Subscribe for notifications
message RemoveWatcherRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string asset_id = 2; } // Unsubscribe

/*-----------------------------------------------------------------------------
 * STATUS / UPDATES / TAGS
 *---------------------------------------------------------------------------*/
message UpdateCaseStatusRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; CaseStatus status = 2; string note = 3; }
message UpdateCaseStatusResponse { Case case_ = 1; }

message AddCaseUpdateRequest    { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; CaseUpdateEntry update = 2; }      // Append timeline entry
message RemoveCaseUpdateRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; CaseUpdateEntry update = 2; }      // Remove timeline entry

message ListCaseUpdatesRequest {
  string case_id = 1 [(hero.permissions.v1.permission_id) = true]; int32 page_size = 2; string page_token = 3;
  string start_time = 4; string end_time = 5; // Optional range filter
}
message ListCaseUpdatesResponse { repeated CaseUpdateEntry updates = 1; string next_page_token = 2; }

message ListCaseFileAttachmentsRequest {
  string case_id = 1 [(hero.permissions.v1.permission_id) = true]; int32 page_size = 2; string page_token = 3;
  string file_category = 4; // Optional filter by category
}
message ListCaseFileAttachmentsResponse { repeated CaseFileReference file_attachments = 1; string next_page_token = 2; }

message ListCaseStatusHistoryRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; int32 page_size = 2; string page_token = 3; }
message ListCaseStatusHistoryResponse { repeated CaseStatusUpdateEntry status_updates = 1; string next_page_token = 2; }

message AddCaseTagRequest    { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string tag = 2; } // Add tag
message RemoveCaseTagRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string tag = 2; } // Remove tag

message AddAdditionalInfoRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string additional_info_json = 2; } // Merge JSON
message AddAdditionalInfoResponse { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; string additional_info_json = 2; }

/*-----------------------------------------------------------------------------
 * AUDIT / VERSIONING
 *---------------------------------------------------------------------------*/
message GetCaseVersionRequest   { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; int32 version = 2; } // Fetch snapshot
message ListCaseVersionsRequest { string case_id = 1 [(hero.permissions.v1.permission_id) = true]; }                    // List snapshot numbers
message ListCaseVersionsResponse { repeated int32 versions = 1; }

message ListCaseAuditLogRequest {
  string case_id = 1 [(hero.permissions.v1.permission_id) = true]; int32 page_size = 2; string page_token = 3;
  CaseAuditAction filter_action = 4;  // Optional action filter
  string start_ts = 5; string end_ts = 6; // Optional date range
}
message ListCaseAuditLogResponse { repeated CaseAuditLogEntry entries = 1; string next_page_token = 2; }

/*-----------------------------------------------------------------------------
 * SEARCH MESSAGES
 *---------------------------------------------------------------------------*/

/** Request for searching cases with advanced filtering and text search capabilities
 *
 * HYBRID SEARCH STRATEGY for optimal performance + API compliance:
 * - When search_fields is empty or includes all fields → Uses search_vector column (fastest)
 * - When search_fields is restricted → Uses field-specific vectors (respects API contract)
 *
 * The search_vector includes weighted content from:
 *   - Case ID and title (highest weight 'A')
 *   - Case description (high weight 'B') 
 *
 * Supported search_fields and field_queries.field values:
 *   - "id" (case unique identifier)
 *   - "title" (case title)  
 *   - "description" (case description)
 *
 * For related data searches (tags, assets, entities), use the dedicated exact-match filter arrays.
 */
message SearchCasesRequest {
  // ────── Free-text & scoped field queries ──────
  string               query           = 1;  // full-text search (hybrid: search_vector or field-specific)
  repeated string      search_fields   = 2;  // restrict query to specific fields: id, title, description
  repeated FieldQuery  field_queries   = 3;  // ILIKE pattern matching on: id, title, description

  // ────── Case filters (exact match) ──────
  repeated CaseStatus  status          = 4;  // cases.status IN (...)
  repeated CaseType    type            = 5;  // cases.type IN (...)
  repeated int32       priority        = 6;  // cases.priority IN (...)
  repeated string      tags            = 7;  // cases.tags @> ARRAY[...]
  repeated string      created_by_asset_ids = 8;  // cases.created_by_asset_id IN (...)
  repeated string      updated_by_asset_ids = 9; // cases.updated_by_asset_id IN (...)
  repeated ReleaseStatus release_status = 10; // cases.release_status IN (...)

  // ────── Relationship filters (exact match) ──────
  repeated string      situation_ids   = 11; // cases.situation_ids @> ARRAY[...]
  repeated string      report_ids      = 12; // cases.report_ids @> ARRAY[...]
  repeated string      related_case_ids = 13; // cases.related_case_ids @> ARRAY[...]
  repeated string      asset_ids       = 14; // asset_associations[].asset_id IN (...)
  repeated CaseAssetAssociationType association_types = 15; // asset_associations[].association_type IN (...)
  repeated string      entity_ref_ids  = 16; // entity_refs[].id IN (...)
  repeated string      entity_ref_types = 17; // entity_refs[].type IN (...)
  repeated string      watcher_asset_ids = 18; // cases.watcher_asset_ids @> ARRAY[...]

  // ────── Date range filters ──────
  DateRange            create_time     = 19; // cases.create_time BETWEEN ...
  DateRange            update_time     = 20; // cases.update_time BETWEEN ...
  DateRange            due_date        = 21; // cases.due_date BETWEEN ...
  DateRange            resolved_time   = 22; // cases.resolved_time BETWEEN ...
  DateRange            close_time      = 23; // cases.close_time BETWEEN ...

  // ────── Pagination & sorting ──────
  int32                page_size       = 24;
  string               page_token      = 25; // cursor
  SearchOrderBy        order_by        = 26; // default = RELEVANCE
  bool                 ascending       = 27; // default = false (DESC)
}

message SearchCasesResponse {
  // The page of cases that matched the query (already ordered & trimmed).
  repeated Case cases = 1;

  // Cursor for fetching the next page; empty when you're on the last page.
  string next_page_token = 2;

  // Per-case highlight information keyed by case ID.
  // Each HighlightResult lists the field name and one-or-more matched fragments
  // (e.g.  "…urgent investigation…", "…security incident…").
  map<string, HighlightResult> highlights = 3;

  // Total number of hits *before* pagination—useful for UI counters.
  int32 total_results = 4;
}

/*-----------------------------------------------------------------------------
 * SERVICE
 *---------------------------------------------------------------------------*/
service CaseService {

  /* -------- Core CRUD -------- */
  rpc CreateCase (CreateCaseRequest) returns (CreateCaseResponse); // Generate id, etag = 1
  rpc GetCase    (GetCaseRequest)    returns (Case);               // Full fetch
  rpc UpdateCase (UpdateCaseRequest) returns (Case);               // Requires expected_etag
  rpc DeleteCase (DeleteCaseRequest) returns (google.protobuf.Empty);

  /* -------- Listing -------- */
  rpc ListCases     (ListCasesRequest)     returns (ListCasesResponse);     // Generic list
  rpc BatchGetCases (BatchGetCasesRequest) returns (BatchGetCasesResponse); // Bulk fetch

  rpc ListCasesBySituationId (ListCasesBySituationIdRequest) returns (ListCasesResponse); // Fast lookup helpers
  rpc ListCasesByReportId    (ListCasesByReportIdRequest)    returns (ListCasesResponse);
  rpc ListCasesByAssetId     (ListCasesByAssetIdRequest)     returns (ListCasesResponse);
  rpc ListCasesByEntityId    (ListCasesByEntityIdRequest)    returns (ListCasesResponse);
  rpc ListCasesByPropertyId  (ListCasesByPropertyIdRequest)  returns (ListCasesResponse);

  /* -------- Relationship mutators -------- */
  rpc AddSituationToCase      (AddSituationToCaseRequest)      returns (Case);
  rpc RemoveSituationFromCase (RemoveSituationFromCaseRequest) returns (Case);
  rpc AddReportToCase         (AddReportToCaseRequest)         returns (Case);
  rpc RemoveReportFromCase    (RemoveReportFromCaseRequest)    returns (Case);
  rpc AddEntityRefToCase      (AddEntityRefToCaseRequest)      returns (Case);
  rpc RemoveEntityRefFromCase (RemoveEntityRefFromCaseRequest) returns (Case);
  rpc AddPropertyRefToCase      (AddPropertyRefToCaseRequest)      returns (Case);
  rpc RemovePropertyRefFromCase (RemovePropertyRefFromCaseRequest) returns (Case);
  rpc LinkRelatedCase         (LinkRelatedCaseRequest)         returns (Case);
  rpc UnlinkRelatedCase       (UnlinkRelatedCaseRequest)       returns (Case);

  /* -------- Asset associations -------- */
  rpc AssignCase                   (AssignCaseRequest)                   returns (AssignCaseResponse); // Dedicated API for case assignment with side effects
  rpc AssociateAssetToCase         (AssociateAssetToCaseRequest)         returns (AssociateAssetToCaseResponse);
  rpc UpdateAssetAssociation       (UpdateAssetAssociationRequest)       returns (UpdateAssetAssociationResponse);
  rpc DisassociateAssetFromCase    (DisassociateAssetFromCaseRequest)    returns (google.protobuf.Empty);
  rpc ListAssetAssociationsForCase (ListAssetAssociationsForCaseRequest) returns (ListAssetAssociationsForCaseResponse);

  /* -------- Watcher list -------- */
  rpc AddWatcher    (AddWatcherRequest)    returns (Case);
  rpc RemoveWatcher (RemoveWatcherRequest) returns (Case);

  /* -------- Status / updates / tags -------- */
  rpc UpdateCaseStatus      (UpdateCaseStatusRequest)      returns (UpdateCaseStatusResponse);
  rpc AddCaseUpdate         (AddCaseUpdateRequest)         returns (Case);
  rpc RemoveCaseUpdate      (RemoveCaseUpdateRequest)      returns (Case);
  rpc ListCaseUpdates       (ListCaseUpdatesRequest)       returns (ListCaseUpdatesResponse);
  rpc ListCaseFileAttachments (ListCaseFileAttachmentsRequest) returns (ListCaseFileAttachmentsResponse);
  rpc ListCaseStatusHistory (ListCaseStatusHistoryRequest) returns (ListCaseStatusHistoryResponse);

  rpc AddCaseTag        (AddCaseTagRequest)        returns (Case);
  rpc RemoveCaseTag     (RemoveCaseTagRequest)     returns (Case);
  rpc AddAdditionalInfo (AddAdditionalInfoRequest) returns (AddAdditionalInfoResponse);

  /* -------- Audit / versioning -------- */
  rpc GetCaseVersion   (GetCaseVersionRequest)   returns (CaseSnapshot);
  rpc ListCaseVersions (ListCaseVersionsRequest) returns (ListCaseVersionsResponse);
  rpc ListCaseAuditLog (ListCaseAuditLogRequest) returns (ListCaseAuditLogResponse);

  /* -------- Search -------- */
  rpc SearchCases (SearchCasesRequest) returns (SearchCasesResponse);
}
