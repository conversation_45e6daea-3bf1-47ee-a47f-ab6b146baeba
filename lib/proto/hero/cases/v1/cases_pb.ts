// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/cases/v1/cases.proto (package hero.cases.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty, file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { Reference } from "../../entity/v1/entity_pb";
import { file_hero_entity_v1_entity } from "../../entity/v1/entity_pb";
import type { PropertyReference } from "../../property/v1/property_pb";
import { file_hero_property_v1_property } from "../../property/v1/property_pb";
import type { UpdateSource } from "../../situations/v2/situations_pb";
import { file_hero_situations_v2_situations } from "../../situations/v2/situations_pb";
import { file_hero_reports_v2_reports } from "../../reports/v2/reports_pb";
import { file_hero_permissions_v1_permissions } from "../../permissions/v1/permissions_pb";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/cases/v1/cases.proto.
 */
export const file_hero_cases_v1_cases: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_empty, file_google_protobuf_struct, file_hero_assets_v2_assets, file_hero_entity_v1_entity, file_hero_property_v1_property, file_hero_situations_v2_situations, file_hero_reports_v2_reports, file_hero_permissions_v1_permissions]);

/**
 * * File reference for case attachments, linking to filerepository service 
 *
 * @generated from message hero.cases.v1.CaseFileReference
 */
export type CaseFileReference = Message<"hero.cases.v1.CaseFileReference"> & {
  /**
   * Unique identifier for this file reference within the case update
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Reference to the case this file belongs to
   *
   * @generated from field: string case_id = 2;
   */
  caseId: string;

  /**
   * FileMetadata.id from filerepository service - REQUIRED
   *
   * @generated from field: string file_id = 3;
   */
  fileId: string;

  /**
   * Optional caption/description for the file
   *
   * @generated from field: string caption = 4;
   */
  caption: string;

  /**
   * Optional display name (fallback to original filename)
   *
   * @generated from field: string display_name = 5;
   */
  displayName: string;

  /**
   * Order for displaying files in UI (0-based)
   *
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;

  /**
   * Category of the file (e.g., "evidence_photo", "evidence_video", "evidence_audio", "evidence_document", "correspondence", "other")
   *
   * @generated from field: string file_category = 7;
   */
  fileCategory: string;

  /**
   * Additional metadata about the file reference
   *
   * @generated from field: google.protobuf.Struct metadata = 8;
   */
  metadata?: JsonObject;
};

/**
 * Describes the message hero.cases.v1.CaseFileReference.
 * Use `create(CaseFileReferenceSchema)` to create a new message.
 */
export const CaseFileReferenceSchema: GenMessage<CaseFileReference> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 0);

/**
 * * Free‑form timeline entry (used for narrative updates) 
 *
 * @generated from message hero.cases.v1.CaseUpdateEntry
 */
export type CaseUpdateEntry = Message<"hero.cases.v1.CaseUpdateEntry"> & {
  /**
   * Human-readable event text
   *
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * ISO-8601 timestamp when the event occurred
   *
   * @generated from field: string event_time = 2;
   */
  eventTime: string;

  /**
   * System / human
   *
   * @generated from field: hero.situations.v2.UpdateSource update_source = 3;
   */
  updateSource: UpdateSource;

  /**
   * Asset or system id
   *
   * @generated from field: string updater_id = 4;
   */
  updaterId: string;

  /**
   * Optional: "EVIDENCE_ADDED", etc.
   *
   * @generated from field: string event_type = 5;
   */
  eventType: string;

  /**
   * Cached for convenience
   *
   * @generated from field: string display_name = 6;
   */
  displayName: string;

  /**
   * Arbitrary structured payload for machine-readable details
   *
   * @generated from field: google.protobuf.Struct data = 7;
   */
  data?: JsonObject;

  /**
   * File attachments for this case update entry
   *
   * References to files in filerepository
   *
   * @generated from field: repeated hero.cases.v1.CaseFileReference file_attachments = 8;
   */
  fileAttachments: CaseFileReference[];
};

/**
 * Describes the message hero.cases.v1.CaseUpdateEntry.
 * Use `create(CaseUpdateEntrySchema)` to create a new message.
 */
export const CaseUpdateEntrySchema: GenMessage<CaseUpdateEntry> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 1);

/**
 * * Immutable record of a status change 
 *
 * @generated from message hero.cases.v1.CaseStatusUpdateEntry
 */
export type CaseStatusUpdateEntry = Message<"hero.cases.v1.CaseStatusUpdateEntry"> & {
  /**
   * When change occurred
   *
   * @generated from field: string timestamp = 1;
   */
  timestamp: string;

  /**
   * Target state
   *
   * @generated from field: hero.cases.v1.CaseStatus new_status = 2;
   */
  newStatus: CaseStatus;

  /**
   * @generated from field: hero.cases.v1.CaseStatus previous_status = 3;
   */
  previousStatus: CaseStatus;

  /**
   * Reason / context
   *
   * @generated from field: string note = 4;
   */
  note: string;

  /**
   * @generated from field: string updater_id = 5;
   */
  updaterId: string;

  /**
   * @generated from field: hero.situations.v2.UpdateSource update_source = 6;
   */
  updateSource: UpdateSource;
};

/**
 * Describes the message hero.cases.v1.CaseStatusUpdateEntry.
 * Use `create(CaseStatusUpdateEntrySchema)` to create a new message.
 */
export const CaseStatusUpdateEntrySchema: GenMessage<CaseStatusUpdateEntry> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 2);

/**
 * * Link between a case and an asset with a defined role 
 *
 * @generated from message hero.cases.v1.CaseAssetAssociation
 */
export type CaseAssetAssociation = Message<"hero.cases.v1.CaseAssetAssociation"> & {
  /**
   * Unique association id
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Redundant FK for audits
   *
   * @generated from field: string case_id = 2;
   */
  caseId: string;

  /**
   * hero.assets.v2.Asset.id
   *
   * @generated from field: string asset_id = 3;
   */
  assetId: string;

  /**
   * @generated from field: hero.cases.v1.CaseAssetAssociationType association_type = 4;
   */
  associationType: CaseAssetAssociationType;

  /**
   * When link was created
   *
   * @generated from field: string assigned_at = 5;
   */
  assignedAt: string;

  /**
   * Free‑text notes
   *
   * @generated from field: string notes = 6;
   */
  notes: string;

  /**
   * Who made/changed link
   *
   * @generated from field: string assigner_asset_id = 7;
   */
  assignerAssetId: string;
};

/**
 * Describes the message hero.cases.v1.CaseAssetAssociation.
 * Use `create(CaseAssetAssociationSchema)` to create a new message.
 */
export const CaseAssetAssociationSchema: GenMessage<CaseAssetAssociation> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 3);

/**
 * * Append‑only audit ledger row 
 *
 * @generated from message hero.cases.v1.CaseAuditLogEntry
 */
export type CaseAuditLogEntry = Message<"hero.cases.v1.CaseAuditLogEntry"> & {
  /**
   * UUID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string case_id = 2;
   */
  caseId: string;

  /**
   * @generated from field: hero.cases.v1.CaseAuditAction action = 3;
   */
  action: CaseAuditAction;

  /**
   * "SYSTEM" if automated
   *
   * @generated from field: string actor_asset_id = 4;
   */
  actorAssetId: string;

  /**
   * @generated from field: string timestamp = 5;
   */
  timestamp: string;

  /**
   * JSONPath‑ish (e.g., "tags[2]")
   *
   * @generated from field: string field_path = 6;
   */
  fieldPath: string;

  /**
   * Previous value (JSON encoded)
   *
   * @generated from field: string old_value = 7;
   */
  oldValue: string;

  /**
   * New value (JSON encoded)
   *
   * @generated from field: string new_value = 8;
   */
  newValue: string;

  /**
   * @generated from field: string note = 9;
   */
  note: string;
};

/**
 * Describes the message hero.cases.v1.CaseAuditLogEntry.
 * Use `create(CaseAuditLogEntrySchema)` to create a new message.
 */
export const CaseAuditLogEntrySchema: GenMessage<CaseAuditLogEntry> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 4);

/**
 * * Full snapshot captured at a specific business version 
 *
 * @generated from message hero.cases.v1.CaseSnapshot
 */
export type CaseSnapshot = Message<"hero.cases.v1.CaseSnapshot"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;

  /**
   * Entire object
   *
   * @generated from field: hero.cases.v1.Case case_snapshot = 3;
   */
  caseSnapshot?: Case;

  /**
   * @generated from field: string timestamp = 4;
   */
  timestamp: string;
};

/**
 * Describes the message hero.cases.v1.CaseSnapshot.
 * Use `create(CaseSnapshotSchema)` to create a new message.
 */
export const CaseSnapshotSchema: GenMessage<CaseSnapshot> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 5);

/**
 * * Date range for filtering (inclusive) 
 *
 * @generated from message hero.cases.v1.DateRange
 */
export type DateRange = Message<"hero.cases.v1.DateRange"> & {
  /**
   * RFC3339 timestamp, inclusive
   *
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * RFC3339 timestamp, inclusive
   *
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.cases.v1.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 6);

/**
 * * Field-specific query (limits a search term to one field) 
 *
 * @generated from message hero.cases.v1.FieldQuery
 */
export type FieldQuery = Message<"hero.cases.v1.FieldQuery"> & {
  /**
   * e.g. "title", "description", "tags", "asset_display_name"
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * the term to match in that field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.cases.v1.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 7);

/**
 * * Highlighted fragments for a given field in each case 
 *
 * @generated from message hero.cases.v1.HighlightResult
 */
export type HighlightResult = Message<"hero.cases.v1.HighlightResult"> & {
  /**
   * the field name where matches occurred
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * snippets with matches, e.g. ["…urgent…", "…critical…"]
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.cases.v1.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 8);

/**
 * -----------------------------------------------------------------------------
 * CORE CASE OBJECT
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.Case
 */
export type Case = Message<"hero.cases.v1.Case"> & {
  /**
   * Identity and classification 
   *
   * UUID‑v4
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Tenant / customer identifier
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Category
   *
   * @generated from field: hero.cases.v1.CaseType type = 3;
   */
  type: CaseType;

  /**
   * Short human title
   *
   * @generated from field: string title = 4;
   */
  title: string;

  /**
   * Long description / synopsis
   *
   * @generated from field: string description = 5;
   */
  description: string;

  /**
   * Current lifecycle state
   *
   * @generated from field: hero.cases.v1.CaseStatus status = 6;
   */
  status: CaseStatus;

  /**
   * 1‑5 scale (higher = more urgent)
   *
   * @generated from field: int32 priority = 7;
   */
  priority: number;

  /**
   * Relationships 
   *
   * Related situations
   *
   * @generated from field: repeated string situation_ids = 8;
   */
  situationIds: string[];

  /**
   * Linked reports
   *
   * @generated from field: repeated string report_ids = 9;
   */
  reportIds: string[];

  /**
   * People, vehicles, etc. (legacy entity service)
   *
   * @generated from field: repeated hero.entity.v1.Reference entity_refs = 10;
   */
  entityRefs: Reference[];

  /**
   * Property service properties
   *
   * @generated from field: repeated hero.property.v1.PropertyReference property_refs = 29;
   */
  propertyRefs: PropertyReference[];

  /**
   * All involved assets
   *
   * @generated from field: repeated hero.cases.v1.CaseAssetAssociation asset_associations = 11;
   */
  assetAssociations: CaseAssetAssociation[];

  /**
   * Parent / child / peer
   *
   * @generated from field: repeated string related_case_ids = 12;
   */
  relatedCaseIds: string[];

  /**
   * History 
   *
   * Narrative log
   *
   * @generated from field: repeated hero.cases.v1.CaseUpdateEntry updates = 13;
   */
  updates: CaseUpdateEntry[];

  /**
   * State changes
   *
   * @generated from field: repeated hero.cases.v1.CaseStatusUpdateEntry status_updates = 14;
   */
  statusUpdates: CaseStatusUpdateEntry[];

  /**
   * Metadata & custom fields 
   *
   * Search facets
   *
   * @generated from field: repeated string tags = 15;
   */
  tags: string[];

  /**
   * Arbitrary KV
   *
   * @generated from field: google.protobuf.Struct additional_info_json = 16;
   */
  additionalInfoJson?: JsonObject;

  /**
   * Constant "CASE" for polymorphic UIs
   *
   * @generated from field: string resource_type = 17;
   */
  resourceType: string;

  /**
   * Business snapshot version
   *
   * @generated from field: int32 version = 18;
   */
  version: number;

  /**
   * Timeline 
   *
   * @generated from field: string create_time = 19;
   */
  createTime: string;

  /**
   * @generated from field: string update_time = 20;
   */
  updateTime: string;

  /**
   * SLA target date
   *
   * @generated from field: string due_date = 21;
   */
  dueDate: string;

  /**
   * @generated from field: string resolved_time = 22;
   */
  resolvedTime: string;

  /**
   * @generated from field: string close_time = 23;
   */
  closeTime: string;

  /**
   * Audit helpers 
   *
   * @generated from field: string created_by_asset_id = 24;
   */
  createdByAssetId: string;

  /**
   * @generated from field: string updated_by_asset_id = 25;
   */
  updatedByAssetId: string;

  /**
   * Watcher list – assets who want notifications only 
   *
   * @generated from field: repeated string watcher_asset_ids = 26;
   */
  watcherAssetIds: string[];

  /**
   * Optimistic‑lock token (auto‑incremented on every write) 
   *
   * @generated from field: int64 etag = 27;
   */
  etag: bigint;

  /**
   * Disclosure control flag (default un‑set = follow org policy) 
   *
   * @generated from field: hero.cases.v1.ReleaseStatus release_status = 28;
   */
  releaseStatus: ReleaseStatus;
};

/**
 * Describes the message hero.cases.v1.Case.
 * Use `create(CaseSchema)` to create a new message.
 */
export const CaseSchema: GenMessage<Case> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 9);

/**
 * -----------------------------------------------------------------------------
 * CRUD & LISTING PAYLOADS
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.CreateCaseRequest
 */
export type CreateCaseRequest = Message<"hero.cases.v1.CreateCaseRequest"> & {
  /**
   * @generated from field: hero.cases.v1.Case case_ = 1;
   */
  case?: Case;
};

/**
 * Describes the message hero.cases.v1.CreateCaseRequest.
 * Use `create(CreateCaseRequestSchema)` to create a new message.
 */
export const CreateCaseRequestSchema: GenMessage<CreateCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 10);

/**
 * @generated from message hero.cases.v1.CreateCaseResponse
 */
export type CreateCaseResponse = Message<"hero.cases.v1.CreateCaseResponse"> & {
  /**
   * @generated from field: hero.cases.v1.Case case_ = 1;
   */
  case?: Case;
};

/**
 * Describes the message hero.cases.v1.CreateCaseResponse.
 * Use `create(CreateCaseResponseSchema)` to create a new message.
 */
export const CreateCaseResponseSchema: GenMessage<CreateCaseResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 11);

/**
 * @generated from message hero.cases.v1.GetCaseRequest
 */
export type GetCaseRequest = Message<"hero.cases.v1.GetCaseRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.cases.v1.GetCaseRequest.
 * Use `create(GetCaseRequestSchema)` to create a new message.
 */
export const GetCaseRequestSchema: GenMessage<GetCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 12);

/**
 * Optimistic locking via etag
 *
 * @generated from message hero.cases.v1.UpdateCaseRequest
 */
export type UpdateCaseRequest = Message<"hero.cases.v1.UpdateCaseRequest"> & {
  /**
   * Must include last-seen etag
   *
   * @generated from field: hero.cases.v1.Case case_ = 1;
   */
  case?: Case;
};

/**
 * Describes the message hero.cases.v1.UpdateCaseRequest.
 * Use `create(UpdateCaseRequestSchema)` to create a new message.
 */
export const UpdateCaseRequestSchema: GenMessage<UpdateCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 13);

/**
 * @generated from message hero.cases.v1.DeleteCaseRequest
 */
export type DeleteCaseRequest = Message<"hero.cases.v1.DeleteCaseRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.cases.v1.DeleteCaseRequest.
 * Use `create(DeleteCaseRequestSchema)` to create a new message.
 */
export const DeleteCaseRequestSchema: GenMessage<DeleteCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 14);

/**
 * Generic filter / paging
 *
 * @generated from message hero.cases.v1.ListCasesRequest
 */
export type ListCasesRequest = Message<"hero.cases.v1.ListCasesRequest"> & {
  /**
   * Max returned per page
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * Cursor from previous call
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Filter by lifecycle
   *
   * @generated from field: hero.cases.v1.CaseStatus status = 3;
   */
  status: CaseStatus;

  /**
   * Filter by category
   *
   * @generated from field: hero.cases.v1.CaseType type = 4;
   */
  type: CaseType;

  /**
   * "Cases where asset X involved"
   *
   * @generated from field: string stakeholder_asset_id = 5;
   */
  stakeholderAssetId: string;

  /**
   * Linked to a specific situation
   *
   * @generated from field: string situation_id = 6;
   */
  situationId: string;

  /**
   * Linked to a specific report
   *
   * @generated from field: string report_id = 7;
   */
  reportId: string;

  /**
   * At least one tag matches
   *
   * @generated from field: repeated string tags = 8;
   */
  tags: string[];

  /**
   * "update_time desc", etc.
   *
   * @generated from field: string order_by = 9;
   */
  orderBy: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesRequest.
 * Use `create(ListCasesRequestSchema)` to create a new message.
 */
export const ListCasesRequestSchema: GenMessage<ListCasesRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 15);

/**
 * @generated from message hero.cases.v1.ListCasesResponse
 */
export type ListCasesResponse = Message<"hero.cases.v1.ListCasesResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.Case cases = 1;
   */
  cases: Case[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesResponse.
 * Use `create(ListCasesResponseSchema)` to create a new message.
 */
export const ListCasesResponseSchema: GenMessage<ListCasesResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 16);

/**
 * @generated from message hero.cases.v1.BatchGetCasesRequest
 */
export type BatchGetCasesRequest = Message<"hero.cases.v1.BatchGetCasesRequest"> & {
  /**
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];
};

/**
 * Describes the message hero.cases.v1.BatchGetCasesRequest.
 * Use `create(BatchGetCasesRequestSchema)` to create a new message.
 */
export const BatchGetCasesRequestSchema: GenMessage<BatchGetCasesRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 17);

/**
 * @generated from message hero.cases.v1.BatchGetCasesResponse
 */
export type BatchGetCasesResponse = Message<"hero.cases.v1.BatchGetCasesResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.Case cases = 1;
   */
  cases: Case[];
};

/**
 * Describes the message hero.cases.v1.BatchGetCasesResponse.
 * Use `create(BatchGetCasesResponseSchema)` to create a new message.
 */
export const BatchGetCasesResponseSchema: GenMessage<BatchGetCasesResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 18);

/**
 * Shortcut list requests (server‑side indexed lookups) 
 *
 * @generated from message hero.cases.v1.ListCasesBySituationIdRequest
 */
export type ListCasesBySituationIdRequest = Message<"hero.cases.v1.ListCasesBySituationIdRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesBySituationIdRequest.
 * Use `create(ListCasesBySituationIdRequestSchema)` to create a new message.
 */
export const ListCasesBySituationIdRequestSchema: GenMessage<ListCasesBySituationIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 19);

/**
 * @generated from message hero.cases.v1.ListCasesByReportIdRequest
 */
export type ListCasesByReportIdRequest = Message<"hero.cases.v1.ListCasesByReportIdRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesByReportIdRequest.
 * Use `create(ListCasesByReportIdRequestSchema)` to create a new message.
 */
export const ListCasesByReportIdRequestSchema: GenMessage<ListCasesByReportIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 20);

/**
 * @generated from message hero.cases.v1.ListCasesByAssetIdRequest
 */
export type ListCasesByAssetIdRequest = Message<"hero.cases.v1.ListCasesByAssetIdRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesByAssetIdRequest.
 * Use `create(ListCasesByAssetIdRequestSchema)` to create a new message.
 */
export const ListCasesByAssetIdRequestSchema: GenMessage<ListCasesByAssetIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 21);

/**
 * @generated from message hero.cases.v1.ListCasesByEntityIdRequest
 */
export type ListCasesByEntityIdRequest = Message<"hero.cases.v1.ListCasesByEntityIdRequest"> & {
  /**
   * @generated from field: string entity_id = 1;
   */
  entityId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesByEntityIdRequest.
 * Use `create(ListCasesByEntityIdRequestSchema)` to create a new message.
 */
export const ListCasesByEntityIdRequestSchema: GenMessage<ListCasesByEntityIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 22);

/**
 * @generated from message hero.cases.v1.ListCasesByPropertyIdRequest
 */
export type ListCasesByPropertyIdRequest = Message<"hero.cases.v1.ListCasesByPropertyIdRequest"> & {
  /**
   * @generated from field: string property_id = 1;
   */
  propertyId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCasesByPropertyIdRequest.
 * Use `create(ListCasesByPropertyIdRequestSchema)` to create a new message.
 */
export const ListCasesByPropertyIdRequestSchema: GenMessage<ListCasesByPropertyIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 23);

/**
 * -----------------------------------------------------------------------------
 * RELATIONSHIP MUTATORS
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.AddSituationToCaseRequest
 */
export type AddSituationToCaseRequest = Message<"hero.cases.v1.AddSituationToCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string situation_id = 2;
   */
  situationId: string;
};

/**
 * Describes the message hero.cases.v1.AddSituationToCaseRequest.
 * Use `create(AddSituationToCaseRequestSchema)` to create a new message.
 */
export const AddSituationToCaseRequestSchema: GenMessage<AddSituationToCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 24);

/**
 * @generated from message hero.cases.v1.RemoveSituationFromCaseRequest
 */
export type RemoveSituationFromCaseRequest = Message<"hero.cases.v1.RemoveSituationFromCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string situation_id = 2;
   */
  situationId: string;
};

/**
 * Describes the message hero.cases.v1.RemoveSituationFromCaseRequest.
 * Use `create(RemoveSituationFromCaseRequestSchema)` to create a new message.
 */
export const RemoveSituationFromCaseRequestSchema: GenMessage<RemoveSituationFromCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 25);

/**
 * @generated from message hero.cases.v1.AddReportToCaseRequest
 */
export type AddReportToCaseRequest = Message<"hero.cases.v1.AddReportToCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string report_id = 2;
   */
  reportId: string;
};

/**
 * Describes the message hero.cases.v1.AddReportToCaseRequest.
 * Use `create(AddReportToCaseRequestSchema)` to create a new message.
 */
export const AddReportToCaseRequestSchema: GenMessage<AddReportToCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 26);

/**
 * @generated from message hero.cases.v1.RemoveReportFromCaseRequest
 */
export type RemoveReportFromCaseRequest = Message<"hero.cases.v1.RemoveReportFromCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string report_id = 2;
   */
  reportId: string;
};

/**
 * Describes the message hero.cases.v1.RemoveReportFromCaseRequest.
 * Use `create(RemoveReportFromCaseRequestSchema)` to create a new message.
 */
export const RemoveReportFromCaseRequestSchema: GenMessage<RemoveReportFromCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 27);

/**
 * @generated from message hero.cases.v1.AddEntityRefToCaseRequest
 */
export type AddEntityRefToCaseRequest = Message<"hero.cases.v1.AddEntityRefToCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.entity.v1.Reference entity_ref = 2;
   */
  entityRef?: Reference;
};

/**
 * Describes the message hero.cases.v1.AddEntityRefToCaseRequest.
 * Use `create(AddEntityRefToCaseRequestSchema)` to create a new message.
 */
export const AddEntityRefToCaseRequestSchema: GenMessage<AddEntityRefToCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 28);

/**
 * @generated from message hero.cases.v1.RemoveEntityRefFromCaseRequest
 */
export type RemoveEntityRefFromCaseRequest = Message<"hero.cases.v1.RemoveEntityRefFromCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.entity.v1.Reference entity_ref = 2;
   */
  entityRef?: Reference;
};

/**
 * Describes the message hero.cases.v1.RemoveEntityRefFromCaseRequest.
 * Use `create(RemoveEntityRefFromCaseRequestSchema)` to create a new message.
 */
export const RemoveEntityRefFromCaseRequestSchema: GenMessage<RemoveEntityRefFromCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 29);

/**
 * Property reference mutators (mirror entity refs but for property service)
 *
 * @generated from message hero.cases.v1.AddPropertyRefToCaseRequest
 */
export type AddPropertyRefToCaseRequest = Message<"hero.cases.v1.AddPropertyRefToCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.property.v1.PropertyReference property_ref = 2;
   */
  propertyRef?: PropertyReference;
};

/**
 * Describes the message hero.cases.v1.AddPropertyRefToCaseRequest.
 * Use `create(AddPropertyRefToCaseRequestSchema)` to create a new message.
 */
export const AddPropertyRefToCaseRequestSchema: GenMessage<AddPropertyRefToCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 30);

/**
 * @generated from message hero.cases.v1.RemovePropertyRefFromCaseRequest
 */
export type RemovePropertyRefFromCaseRequest = Message<"hero.cases.v1.RemovePropertyRefFromCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.property.v1.PropertyReference property_ref = 2;
   */
  propertyRef?: PropertyReference;
};

/**
 * Describes the message hero.cases.v1.RemovePropertyRefFromCaseRequest.
 * Use `create(RemovePropertyRefFromCaseRequestSchema)` to create a new message.
 */
export const RemovePropertyRefFromCaseRequestSchema: GenMessage<RemovePropertyRefFromCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 31);

/**
 * @generated from message hero.cases.v1.LinkRelatedCaseRequest
 */
export type LinkRelatedCaseRequest = Message<"hero.cases.v1.LinkRelatedCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string related_case_id = 2;
   */
  relatedCaseId: string;
};

/**
 * Describes the message hero.cases.v1.LinkRelatedCaseRequest.
 * Use `create(LinkRelatedCaseRequestSchema)` to create a new message.
 */
export const LinkRelatedCaseRequestSchema: GenMessage<LinkRelatedCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 32);

/**
 * @generated from message hero.cases.v1.UnlinkRelatedCaseRequest
 */
export type UnlinkRelatedCaseRequest = Message<"hero.cases.v1.UnlinkRelatedCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string related_case_id = 2;
   */
  relatedCaseId: string;
};

/**
 * Describes the message hero.cases.v1.UnlinkRelatedCaseRequest.
 * Use `create(UnlinkRelatedCaseRequestSchema)` to create a new message.
 */
export const UnlinkRelatedCaseRequestSchema: GenMessage<UnlinkRelatedCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 33);

/**
 * -----------------------------------------------------------------------------
 * ASSET ASSOCIATION MUTATORS
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.AssociateAssetToCaseRequest
 */
export type AssociateAssetToCaseRequest = Message<"hero.cases.v1.AssociateAssetToCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.cases.v1.CaseAssetAssociation association = 2;
   */
  association?: CaseAssetAssociation;
};

/**
 * Describes the message hero.cases.v1.AssociateAssetToCaseRequest.
 * Use `create(AssociateAssetToCaseRequestSchema)` to create a new message.
 */
export const AssociateAssetToCaseRequestSchema: GenMessage<AssociateAssetToCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 34);

/**
 * @generated from message hero.cases.v1.AssociateAssetToCaseResponse
 */
export type AssociateAssetToCaseResponse = Message<"hero.cases.v1.AssociateAssetToCaseResponse"> & {
  /**
   * @generated from field: hero.cases.v1.CaseAssetAssociation association = 1;
   */
  association?: CaseAssetAssociation;
};

/**
 * Describes the message hero.cases.v1.AssociateAssetToCaseResponse.
 * Use `create(AssociateAssetToCaseResponseSchema)` to create a new message.
 */
export const AssociateAssetToCaseResponseSchema: GenMessage<AssociateAssetToCaseResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 35);

/**
 * Dedicated message for assigning a case to an investigator
 *
 * @generated from message hero.cases.v1.AssignCaseRequest
 */
export type AssignCaseRequest = Message<"hero.cases.v1.AssignCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * The asset to assign as primary investigator
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * Optional notes about the assignment
   *
   * @generated from field: string notes = 3;
   */
  notes: string;
};

/**
 * Describes the message hero.cases.v1.AssignCaseRequest.
 * Use `create(AssignCaseRequestSchema)` to create a new message.
 */
export const AssignCaseRequestSchema: GenMessage<AssignCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 36);

/**
 * @generated from message hero.cases.v1.AssignCaseResponse
 */
export type AssignCaseResponse = Message<"hero.cases.v1.AssignCaseResponse"> & {
  /**
   * @generated from field: hero.cases.v1.Case case_ = 1;
   */
  case?: Case;

  /**
   * @generated from field: hero.cases.v1.CaseAssetAssociation association = 2;
   */
  association?: CaseAssetAssociation;

  /**
   * The created order ID for tracking
   *
   * @generated from field: string order_id = 3;
   */
  orderId: string;
};

/**
 * Describes the message hero.cases.v1.AssignCaseResponse.
 * Use `create(AssignCaseResponseSchema)` to create a new message.
 */
export const AssignCaseResponseSchema: GenMessage<AssignCaseResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 37);

/**
 * @generated from message hero.cases.v1.UpdateAssetAssociationRequest
 */
export type UpdateAssetAssociationRequest = Message<"hero.cases.v1.UpdateAssetAssociationRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.cases.v1.CaseAssetAssociation association = 2;
   */
  association?: CaseAssetAssociation;
};

/**
 * Describes the message hero.cases.v1.UpdateAssetAssociationRequest.
 * Use `create(UpdateAssetAssociationRequestSchema)` to create a new message.
 */
export const UpdateAssetAssociationRequestSchema: GenMessage<UpdateAssetAssociationRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 38);

/**
 * @generated from message hero.cases.v1.UpdateAssetAssociationResponse
 */
export type UpdateAssetAssociationResponse = Message<"hero.cases.v1.UpdateAssetAssociationResponse"> & {
  /**
   * @generated from field: hero.cases.v1.CaseAssetAssociation association = 1;
   */
  association?: CaseAssetAssociation;
};

/**
 * Describes the message hero.cases.v1.UpdateAssetAssociationResponse.
 * Use `create(UpdateAssetAssociationResponseSchema)` to create a new message.
 */
export const UpdateAssetAssociationResponseSchema: GenMessage<UpdateAssetAssociationResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 39);

/**
 * @generated from message hero.cases.v1.DisassociateAssetFromCaseRequest
 */
export type DisassociateAssetFromCaseRequest = Message<"hero.cases.v1.DisassociateAssetFromCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string association_id = 2;
   */
  associationId: string;
};

/**
 * Describes the message hero.cases.v1.DisassociateAssetFromCaseRequest.
 * Use `create(DisassociateAssetFromCaseRequestSchema)` to create a new message.
 */
export const DisassociateAssetFromCaseRequestSchema: GenMessage<DisassociateAssetFromCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 40);

/**
 * @generated from message hero.cases.v1.ListAssetAssociationsForCaseRequest
 */
export type ListAssetAssociationsForCaseRequest = Message<"hero.cases.v1.ListAssetAssociationsForCaseRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filter
   *
   * @generated from field: hero.cases.v1.CaseAssetAssociationType association_type = 4;
   */
  associationType: CaseAssetAssociationType;
};

/**
 * Describes the message hero.cases.v1.ListAssetAssociationsForCaseRequest.
 * Use `create(ListAssetAssociationsForCaseRequestSchema)` to create a new message.
 */
export const ListAssetAssociationsForCaseRequestSchema: GenMessage<ListAssetAssociationsForCaseRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 41);

/**
 * @generated from message hero.cases.v1.ListAssetAssociationsForCaseResponse
 */
export type ListAssetAssociationsForCaseResponse = Message<"hero.cases.v1.ListAssetAssociationsForCaseResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.CaseAssetAssociation associations = 1;
   */
  associations: CaseAssetAssociation[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListAssetAssociationsForCaseResponse.
 * Use `create(ListAssetAssociationsForCaseResponseSchema)` to create a new message.
 */
export const ListAssetAssociationsForCaseResponseSchema: GenMessage<ListAssetAssociationsForCaseResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 42);

/**
 * -----------------------------------------------------------------------------
 * WATCHER LIST MUTATORS
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.AddWatcherRequest
 */
export type AddWatcherRequest = Message<"hero.cases.v1.AddWatcherRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.cases.v1.AddWatcherRequest.
 * Use `create(AddWatcherRequestSchema)` to create a new message.
 */
export const AddWatcherRequestSchema: GenMessage<AddWatcherRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 43);

/**
 * @generated from message hero.cases.v1.RemoveWatcherRequest
 */
export type RemoveWatcherRequest = Message<"hero.cases.v1.RemoveWatcherRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.cases.v1.RemoveWatcherRequest.
 * Use `create(RemoveWatcherRequestSchema)` to create a new message.
 */
export const RemoveWatcherRequestSchema: GenMessage<RemoveWatcherRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 44);

/**
 * -----------------------------------------------------------------------------
 * STATUS / UPDATES / TAGS
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.UpdateCaseStatusRequest
 */
export type UpdateCaseStatusRequest = Message<"hero.cases.v1.UpdateCaseStatusRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.cases.v1.CaseStatus status = 2;
   */
  status: CaseStatus;

  /**
   * @generated from field: string note = 3;
   */
  note: string;
};

/**
 * Describes the message hero.cases.v1.UpdateCaseStatusRequest.
 * Use `create(UpdateCaseStatusRequestSchema)` to create a new message.
 */
export const UpdateCaseStatusRequestSchema: GenMessage<UpdateCaseStatusRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 45);

/**
 * @generated from message hero.cases.v1.UpdateCaseStatusResponse
 */
export type UpdateCaseStatusResponse = Message<"hero.cases.v1.UpdateCaseStatusResponse"> & {
  /**
   * @generated from field: hero.cases.v1.Case case_ = 1;
   */
  case?: Case;
};

/**
 * Describes the message hero.cases.v1.UpdateCaseStatusResponse.
 * Use `create(UpdateCaseStatusResponseSchema)` to create a new message.
 */
export const UpdateCaseStatusResponseSchema: GenMessage<UpdateCaseStatusResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 46);

/**
 * @generated from message hero.cases.v1.AddCaseUpdateRequest
 */
export type AddCaseUpdateRequest = Message<"hero.cases.v1.AddCaseUpdateRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.cases.v1.CaseUpdateEntry update = 2;
   */
  update?: CaseUpdateEntry;
};

/**
 * Describes the message hero.cases.v1.AddCaseUpdateRequest.
 * Use `create(AddCaseUpdateRequestSchema)` to create a new message.
 */
export const AddCaseUpdateRequestSchema: GenMessage<AddCaseUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 47);

/**
 * @generated from message hero.cases.v1.RemoveCaseUpdateRequest
 */
export type RemoveCaseUpdateRequest = Message<"hero.cases.v1.RemoveCaseUpdateRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: hero.cases.v1.CaseUpdateEntry update = 2;
   */
  update?: CaseUpdateEntry;
};

/**
 * Describes the message hero.cases.v1.RemoveCaseUpdateRequest.
 * Use `create(RemoveCaseUpdateRequestSchema)` to create a new message.
 */
export const RemoveCaseUpdateRequestSchema: GenMessage<RemoveCaseUpdateRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 48);

/**
 * @generated from message hero.cases.v1.ListCaseUpdatesRequest
 */
export type ListCaseUpdatesRequest = Message<"hero.cases.v1.ListCaseUpdatesRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * @generated from field: string start_time = 4;
   */
  startTime: string;

  /**
   * Optional range filter
   *
   * @generated from field: string end_time = 5;
   */
  endTime: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseUpdatesRequest.
 * Use `create(ListCaseUpdatesRequestSchema)` to create a new message.
 */
export const ListCaseUpdatesRequestSchema: GenMessage<ListCaseUpdatesRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 49);

/**
 * @generated from message hero.cases.v1.ListCaseUpdatesResponse
 */
export type ListCaseUpdatesResponse = Message<"hero.cases.v1.ListCaseUpdatesResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.CaseUpdateEntry updates = 1;
   */
  updates: CaseUpdateEntry[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseUpdatesResponse.
 * Use `create(ListCaseUpdatesResponseSchema)` to create a new message.
 */
export const ListCaseUpdatesResponseSchema: GenMessage<ListCaseUpdatesResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 50);

/**
 * @generated from message hero.cases.v1.ListCaseFileAttachmentsRequest
 */
export type ListCaseFileAttachmentsRequest = Message<"hero.cases.v1.ListCaseFileAttachmentsRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional filter by category
   *
   * @generated from field: string file_category = 4;
   */
  fileCategory: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseFileAttachmentsRequest.
 * Use `create(ListCaseFileAttachmentsRequestSchema)` to create a new message.
 */
export const ListCaseFileAttachmentsRequestSchema: GenMessage<ListCaseFileAttachmentsRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 51);

/**
 * @generated from message hero.cases.v1.ListCaseFileAttachmentsResponse
 */
export type ListCaseFileAttachmentsResponse = Message<"hero.cases.v1.ListCaseFileAttachmentsResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.CaseFileReference file_attachments = 1;
   */
  fileAttachments: CaseFileReference[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseFileAttachmentsResponse.
 * Use `create(ListCaseFileAttachmentsResponseSchema)` to create a new message.
 */
export const ListCaseFileAttachmentsResponseSchema: GenMessage<ListCaseFileAttachmentsResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 52);

/**
 * @generated from message hero.cases.v1.ListCaseStatusHistoryRequest
 */
export type ListCaseStatusHistoryRequest = Message<"hero.cases.v1.ListCaseStatusHistoryRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseStatusHistoryRequest.
 * Use `create(ListCaseStatusHistoryRequestSchema)` to create a new message.
 */
export const ListCaseStatusHistoryRequestSchema: GenMessage<ListCaseStatusHistoryRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 53);

/**
 * @generated from message hero.cases.v1.ListCaseStatusHistoryResponse
 */
export type ListCaseStatusHistoryResponse = Message<"hero.cases.v1.ListCaseStatusHistoryResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.CaseStatusUpdateEntry status_updates = 1;
   */
  statusUpdates: CaseStatusUpdateEntry[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseStatusHistoryResponse.
 * Use `create(ListCaseStatusHistoryResponseSchema)` to create a new message.
 */
export const ListCaseStatusHistoryResponseSchema: GenMessage<ListCaseStatusHistoryResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 54);

/**
 * @generated from message hero.cases.v1.AddCaseTagRequest
 */
export type AddCaseTagRequest = Message<"hero.cases.v1.AddCaseTagRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string tag = 2;
   */
  tag: string;
};

/**
 * Describes the message hero.cases.v1.AddCaseTagRequest.
 * Use `create(AddCaseTagRequestSchema)` to create a new message.
 */
export const AddCaseTagRequestSchema: GenMessage<AddCaseTagRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 55);

/**
 * @generated from message hero.cases.v1.RemoveCaseTagRequest
 */
export type RemoveCaseTagRequest = Message<"hero.cases.v1.RemoveCaseTagRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string tag = 2;
   */
  tag: string;
};

/**
 * Describes the message hero.cases.v1.RemoveCaseTagRequest.
 * Use `create(RemoveCaseTagRequestSchema)` to create a new message.
 */
export const RemoveCaseTagRequestSchema: GenMessage<RemoveCaseTagRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 56);

/**
 * @generated from message hero.cases.v1.AddAdditionalInfoRequest
 */
export type AddAdditionalInfoRequest = Message<"hero.cases.v1.AddAdditionalInfoRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.cases.v1.AddAdditionalInfoRequest.
 * Use `create(AddAdditionalInfoRequestSchema)` to create a new message.
 */
export const AddAdditionalInfoRequestSchema: GenMessage<AddAdditionalInfoRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 57);

/**
 * @generated from message hero.cases.v1.AddAdditionalInfoResponse
 */
export type AddAdditionalInfoResponse = Message<"hero.cases.v1.AddAdditionalInfoResponse"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string additional_info_json = 2;
   */
  additionalInfoJson: string;
};

/**
 * Describes the message hero.cases.v1.AddAdditionalInfoResponse.
 * Use `create(AddAdditionalInfoResponseSchema)` to create a new message.
 */
export const AddAdditionalInfoResponseSchema: GenMessage<AddAdditionalInfoResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 58);

/**
 * -----------------------------------------------------------------------------
 * AUDIT / VERSIONING
 * ---------------------------------------------------------------------------
 *
 * @generated from message hero.cases.v1.GetCaseVersionRequest
 */
export type GetCaseVersionRequest = Message<"hero.cases.v1.GetCaseVersionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.cases.v1.GetCaseVersionRequest.
 * Use `create(GetCaseVersionRequestSchema)` to create a new message.
 */
export const GetCaseVersionRequestSchema: GenMessage<GetCaseVersionRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 59);

/**
 * @generated from message hero.cases.v1.ListCaseVersionsRequest
 */
export type ListCaseVersionsRequest = Message<"hero.cases.v1.ListCaseVersionsRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseVersionsRequest.
 * Use `create(ListCaseVersionsRequestSchema)` to create a new message.
 */
export const ListCaseVersionsRequestSchema: GenMessage<ListCaseVersionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 60);

/**
 * @generated from message hero.cases.v1.ListCaseVersionsResponse
 */
export type ListCaseVersionsResponse = Message<"hero.cases.v1.ListCaseVersionsResponse"> & {
  /**
   * @generated from field: repeated int32 versions = 1;
   */
  versions: number[];
};

/**
 * Describes the message hero.cases.v1.ListCaseVersionsResponse.
 * Use `create(ListCaseVersionsResponseSchema)` to create a new message.
 */
export const ListCaseVersionsResponseSchema: GenMessage<ListCaseVersionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 61);

/**
 * @generated from message hero.cases.v1.ListCaseAuditLogRequest
 */
export type ListCaseAuditLogRequest = Message<"hero.cases.v1.ListCaseAuditLogRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * Optional action filter
   *
   * @generated from field: hero.cases.v1.CaseAuditAction filter_action = 4;
   */
  filterAction: CaseAuditAction;

  /**
   * @generated from field: string start_ts = 5;
   */
  startTs: string;

  /**
   * Optional date range
   *
   * @generated from field: string end_ts = 6;
   */
  endTs: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseAuditLogRequest.
 * Use `create(ListCaseAuditLogRequestSchema)` to create a new message.
 */
export const ListCaseAuditLogRequestSchema: GenMessage<ListCaseAuditLogRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 62);

/**
 * @generated from message hero.cases.v1.ListCaseAuditLogResponse
 */
export type ListCaseAuditLogResponse = Message<"hero.cases.v1.ListCaseAuditLogResponse"> & {
  /**
   * @generated from field: repeated hero.cases.v1.CaseAuditLogEntry entries = 1;
   */
  entries: CaseAuditLogEntry[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.cases.v1.ListCaseAuditLogResponse.
 * Use `create(ListCaseAuditLogResponseSchema)` to create a new message.
 */
export const ListCaseAuditLogResponseSchema: GenMessage<ListCaseAuditLogResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 63);

/**
 * * Request for searching cases with advanced filtering and text search capabilities
 *
 * HYBRID SEARCH STRATEGY for optimal performance + API compliance:
 * - When search_fields is empty or includes all fields → Uses search_vector column (fastest)
 * - When search_fields is restricted → Uses field-specific vectors (respects API contract)
 *
 * The search_vector includes weighted content from:
 *   - Case ID and title (highest weight 'A')
 *   - Case description (high weight 'B') 
 *
 * Supported search_fields and field_queries.field values:
 *   - "id" (case unique identifier)
 *   - "title" (case title)  
 *   - "description" (case description)
 *
 * For related data searches (tags, assets, entities), use the dedicated exact-match filter arrays.
 *
 * @generated from message hero.cases.v1.SearchCasesRequest
 */
export type SearchCasesRequest = Message<"hero.cases.v1.SearchCasesRequest"> & {
  /**
   * ────── Free-text & scoped field queries ──────
   *
   * full-text search (hybrid: search_vector or field-specific)
   *
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * restrict query to specific fields: id, title, description
   *
   * @generated from field: repeated string search_fields = 2;
   */
  searchFields: string[];

  /**
   * ILIKE pattern matching on: id, title, description
   *
   * @generated from field: repeated hero.cases.v1.FieldQuery field_queries = 3;
   */
  fieldQueries: FieldQuery[];

  /**
   * ────── Case filters (exact match) ──────
   *
   * cases.status IN (...)
   *
   * @generated from field: repeated hero.cases.v1.CaseStatus status = 4;
   */
  status: CaseStatus[];

  /**
   * cases.type IN (...)
   *
   * @generated from field: repeated hero.cases.v1.CaseType type = 5;
   */
  type: CaseType[];

  /**
   * cases.priority IN (...)
   *
   * @generated from field: repeated int32 priority = 6;
   */
  priority: number[];

  /**
   * cases.tags @> ARRAY[...]
   *
   * @generated from field: repeated string tags = 7;
   */
  tags: string[];

  /**
   * cases.created_by_asset_id IN (...)
   *
   * @generated from field: repeated string created_by_asset_ids = 8;
   */
  createdByAssetIds: string[];

  /**
   * cases.updated_by_asset_id IN (...)
   *
   * @generated from field: repeated string updated_by_asset_ids = 9;
   */
  updatedByAssetIds: string[];

  /**
   * cases.release_status IN (...)
   *
   * @generated from field: repeated hero.cases.v1.ReleaseStatus release_status = 10;
   */
  releaseStatus: ReleaseStatus[];

  /**
   * ────── Relationship filters (exact match) ──────
   *
   * cases.situation_ids @> ARRAY[...]
   *
   * @generated from field: repeated string situation_ids = 11;
   */
  situationIds: string[];

  /**
   * cases.report_ids @> ARRAY[...]
   *
   * @generated from field: repeated string report_ids = 12;
   */
  reportIds: string[];

  /**
   * cases.related_case_ids @> ARRAY[...]
   *
   * @generated from field: repeated string related_case_ids = 13;
   */
  relatedCaseIds: string[];

  /**
   * asset_associations[].asset_id IN (...)
   *
   * @generated from field: repeated string asset_ids = 14;
   */
  assetIds: string[];

  /**
   * asset_associations[].association_type IN (...)
   *
   * @generated from field: repeated hero.cases.v1.CaseAssetAssociationType association_types = 15;
   */
  associationTypes: CaseAssetAssociationType[];

  /**
   * entity_refs[].id IN (...)
   *
   * @generated from field: repeated string entity_ref_ids = 16;
   */
  entityRefIds: string[];

  /**
   * entity_refs[].type IN (...)
   *
   * @generated from field: repeated string entity_ref_types = 17;
   */
  entityRefTypes: string[];

  /**
   * cases.watcher_asset_ids @> ARRAY[...]
   *
   * @generated from field: repeated string watcher_asset_ids = 18;
   */
  watcherAssetIds: string[];

  /**
   * ────── Date range filters ──────
   *
   * cases.create_time BETWEEN ...
   *
   * @generated from field: hero.cases.v1.DateRange create_time = 19;
   */
  createTime?: DateRange;

  /**
   * cases.update_time BETWEEN ...
   *
   * @generated from field: hero.cases.v1.DateRange update_time = 20;
   */
  updateTime?: DateRange;

  /**
   * cases.due_date BETWEEN ...
   *
   * @generated from field: hero.cases.v1.DateRange due_date = 21;
   */
  dueDate?: DateRange;

  /**
   * cases.resolved_time BETWEEN ...
   *
   * @generated from field: hero.cases.v1.DateRange resolved_time = 22;
   */
  resolvedTime?: DateRange;

  /**
   * cases.close_time BETWEEN ...
   *
   * @generated from field: hero.cases.v1.DateRange close_time = 23;
   */
  closeTime?: DateRange;

  /**
   * ────── Pagination & sorting ──────
   *
   * @generated from field: int32 page_size = 24;
   */
  pageSize: number;

  /**
   * cursor
   *
   * @generated from field: string page_token = 25;
   */
  pageToken: string;

  /**
   * default = RELEVANCE
   *
   * @generated from field: hero.cases.v1.SearchOrderBy order_by = 26;
   */
  orderBy: SearchOrderBy;

  /**
   * default = false (DESC)
   *
   * @generated from field: bool ascending = 27;
   */
  ascending: boolean;
};

/**
 * Describes the message hero.cases.v1.SearchCasesRequest.
 * Use `create(SearchCasesRequestSchema)` to create a new message.
 */
export const SearchCasesRequestSchema: GenMessage<SearchCasesRequest> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 64);

/**
 * @generated from message hero.cases.v1.SearchCasesResponse
 */
export type SearchCasesResponse = Message<"hero.cases.v1.SearchCasesResponse"> & {
  /**
   * The page of cases that matched the query (already ordered & trimmed).
   *
   * @generated from field: repeated hero.cases.v1.Case cases = 1;
   */
  cases: Case[];

  /**
   * Cursor for fetching the next page; empty when you're on the last page.
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * Per-case highlight information keyed by case ID.
   * Each HighlightResult lists the field name and one-or-more matched fragments
   * (e.g.  "…urgent investigation…", "…security incident…").
   *
   * @generated from field: map<string, hero.cases.v1.HighlightResult> highlights = 3;
   */
  highlights: { [key: string]: HighlightResult };

  /**
   * Total number of hits *before* pagination—useful for UI counters.
   *
   * @generated from field: int32 total_results = 4;
   */
  totalResults: number;
};

/**
 * Describes the message hero.cases.v1.SearchCasesResponse.
 * Use `create(SearchCasesResponseSchema)` to create a new message.
 */
export const SearchCasesResponseSchema: GenMessage<SearchCasesResponse> = /*@__PURE__*/
  messageDesc(file_hero_cases_v1_cases, 65);

/**
 * * Overall lifecycle of a case 
 *
 * @generated from enum hero.cases.v1.CaseStatus
 */
export enum CaseStatus {
  /**
   * @generated from enum value: CASE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Logged, awaiting triage
   *
   * @generated from enum value: CASE_STATUS_NEW = 1;
   */
  NEW = 1,

  /**
   * Actively being scoped/triaged
   *
   * @generated from enum value: CASE_STATUS_OPEN = 2;
   */
  OPEN = 2,

  /**
   * Submitted for managerial review
   *
   * @generated from enum value: CASE_STATUS_UNDER_REVIEW = 3;
   */
  UNDER_REVIEW = 3,

  /**
   * Active investigative work
   *
   * @generated from enum value: CASE_STATUS_INVESTIGATING = 4;
   */
  INVESTIGATING = 4,

  /**
   * Blocked, waiting on data
   *
   * @generated from enum value: CASE_STATUS_PENDING_INFORMATION = 5;
   */
  PENDING_INFORMATION = 5,

  /**
   * Manually paused
   *
   * @generated from enum value: CASE_STATUS_ON_HOLD = 6;
   */
  ON_HOLD = 6,

  /**
   * Handed to higher authority
   *
   * @generated from enum value: CASE_STATUS_ESCALATED = 7;
   */
  ESCALATED = 7,

  /**
   * Root issue fixed
   *
   * @generated from enum value: CASE_STATUS_RESOLVED = 8;
   */
  RESOLVED = 8,

  /**
   * Administratively closed
   *
   * @generated from enum value: CASE_STATUS_CLOSED = 9;
   */
  CLOSED = 9,

  /**
   * Long‑term retention (readonly)
   *
   * @generated from enum value: CASE_STATUS_ARCHIVED = 10;
   */
  ARCHIVED = 10,

  /**
   * Assigned to investigator, awaiting start
   *
   * @generated from enum value: CASE_STATUS_ASSIGNED_FOR_INVESTIGATION = 11;
   */
  ASSIGNED_FOR_INVESTIGATION = 11,
}

/**
 * Describes the enum hero.cases.v1.CaseStatus.
 */
export const CaseStatusSchema: GenEnum<CaseStatus> = /*@__PURE__*/
  enumDesc(file_hero_cases_v1_cases, 0);

/**
 * * High‑level category / playbook selector 
 *
 * @generated from enum hero.cases.v1.CaseType
 */
export enum CaseType {
  /**
   * @generated from enum value: CASE_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CASE_TYPE_SECURITY_INCIDENT = 1;
   */
  SECURITY_INCIDENT = 1,

  /**
   * @generated from enum value: CASE_TYPE_SAFETY_INCIDENT = 2;
   */
  SAFETY_INCIDENT = 2,

  /**
   * @generated from enum value: CASE_TYPE_OPERATIONAL_TASK = 3;
   */
  OPERATIONAL_TASK = 3,

  /**
   * @generated from enum value: CASE_TYPE_CUSTOMER_COMPLAINT = 4;
   */
  CUSTOMER_COMPLAINT = 4,

  /**
   * @generated from enum value: CASE_TYPE_INVESTIGATION = 5;
   */
  INVESTIGATION = 5,

  /**
   * @generated from enum value: CASE_TYPE_COMPLIANCE_REVIEW = 6;
   */
  COMPLIANCE_REVIEW = 6,

  /**
   * @generated from enum value: CASE_TYPE_INSURANCE_CLAIM = 7;
   */
  INSURANCE_CLAIM = 7,

  /**
   * @generated from enum value: CASE_TYPE_ADMINISTRATIVE = 8;
   */
  ADMINISTRATIVE = 8,

  /**
   * @generated from enum value: CASE_TYPE_OTHER = 99;
   */
  OTHER = 99,
}

/**
 * Describes the enum hero.cases.v1.CaseType.
 */
export const CaseTypeSchema: GenEnum<CaseType> = /*@__PURE__*/
  enumDesc(file_hero_cases_v1_cases, 1);

/**
 * * Role an asset plays within the case 
 *
 * @generated from enum hero.cases.v1.CaseAssetAssociationType
 */
export enum CaseAssetAssociationType {
  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_UNSPECIFIED = 0;
   */
  ASSET_ASSOCIATION_TYPE_UNSPECIFIED = 0,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR = 1;
   */
  ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR = 1,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_INVESTIGATOR = 2;
   */
  ASSET_ASSOCIATION_TYPE_INVESTIGATOR = 2,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_OBSERVER = 3;
   */
  ASSET_ASSOCIATION_TYPE_OBSERVER = 3,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_APPROVER = 4;
   */
  ASSET_ASSOCIATION_TYPE_APPROVER = 4,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY = 5;
   */
  ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY = 5,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_WITNESS = 6;
   */
  ASSET_ASSOCIATION_TYPE_WITNESS = 6,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_SUBJECT = 7;
   */
  ASSET_ASSOCIATION_TYPE_SUBJECT = 7,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_REPORTER = 8;
   */
  ASSET_ASSOCIATION_TYPE_REPORTER = 8,

  /**
   * @generated from enum value: ASSET_ASSOCIATION_TYPE_COLLABORATOR = 9;
   */
  ASSET_ASSOCIATION_TYPE_COLLABORATOR = 9,
}

/**
 * Describes the enum hero.cases.v1.CaseAssetAssociationType.
 */
export const CaseAssetAssociationTypeSchema: GenEnum<CaseAssetAssociationType> = /*@__PURE__*/
  enumDesc(file_hero_cases_v1_cases, 2);

/**
 * * Type of change recorded in the audit log 
 *
 * @generated from enum hero.cases.v1.CaseAuditAction
 */
export enum CaseAuditAction {
  /**
   * @generated from enum value: CASE_AUDIT_ACTION_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Case was created
   *
   * @generated from enum value: CASE_AUDIT_ACTION_CREATE = 1;
   */
  CREATE = 1,

  /**
   * Generic field edit
   *
   * @generated from enum value: CASE_AUDIT_ACTION_UPDATE = 2;
   */
  UPDATE = 2,

  /**
   * Status transition
   *
   * @generated from enum value: CASE_AUDIT_ACTION_STATUS = 3;
   */
  STATUS = 3,

  /**
   * Link/unlink other object
   *
   * @generated from enum value: CASE_AUDIT_ACTION_RELATION = 4;
   */
  RELATION = 4,

  /**
   * Asset association mutation
   *
   * @generated from enum value: CASE_AUDIT_ACTION_ASSOCIATION = 5;
   */
  ASSOCIATION = 5,

  /**
   * Hard delete
   *
   * @generated from enum value: CASE_AUDIT_ACTION_DELETE = 6;
   */
  DELETE = 6,

  /**
   * View case details
   *
   * @generated from enum value: CASE_AUDIT_ACTION_VIEW = 7;
   */
  VIEW = 7,
}

/**
 * Describes the enum hero.cases.v1.CaseAuditAction.
 */
export const CaseAuditActionSchema: GenEnum<CaseAuditAction> = /*@__PURE__*/
  enumDesc(file_hero_cases_v1_cases, 3);

/**
 * * Controls what content reviewers may legally disclose 
 *
 * @generated from enum hero.cases.v1.ReleaseStatus
 */
export enum ReleaseStatus {
  /**
   * @generated from enum value: RELEASE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Safe for public release
   *
   * @generated from enum value: RELEASE_STATUS_PUBLIC = 1;
   */
  PUBLIC = 1,

  /**
   * Company‑internal only
   *
   * @generated from enum value: RELEASE_STATUS_INTERNAL = 2;
   */
  INTERNAL = 2,

  /**
   * Shareable with LE partners
   *
   * @generated from enum value: RELEASE_STATUS_LAW_ENFORCEMENT_ONLY = 3;
   */
  LAW_ENFORCEMENT_ONLY = 3,

  /**
   * Withhold from all external requests
   *
   * @generated from enum value: RELEASE_STATUS_DO_NOT_RELEASE = 4;
   */
  DO_NOT_RELEASE = 4,
}

/**
 * Describes the enum hero.cases.v1.ReleaseStatus.
 */
export const ReleaseStatusSchema: GenEnum<ReleaseStatus> = /*@__PURE__*/
  enumDesc(file_hero_cases_v1_cases, 4);

/**
 * * Enum for the order by field in the search cases request 
 *
 * @generated from enum hero.cases.v1.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATE_TIME = 2;
   */
  CREATE_TIME = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATE_TIME = 3;
   */
  UPDATE_TIME = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_PRIORITY = 4;
   */
  PRIORITY = 4,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_STATUS = 5;
   */
  STATUS = 5,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_DUE_DATE = 6;
   */
  DUE_DATE = 6,
}

/**
 * Describes the enum hero.cases.v1.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_cases_v1_cases, 5);

/**
 * -----------------------------------------------------------------------------
 * SERVICE
 * ---------------------------------------------------------------------------
 *
 * @generated from service hero.cases.v1.CaseService
 */
export const CaseService: GenService<{
  /**
   * -------- Core CRUD -------- 
   *
   * Generate id, etag = 1
   *
   * @generated from rpc hero.cases.v1.CaseService.CreateCase
   */
  createCase: {
    methodKind: "unary";
    input: typeof CreateCaseRequestSchema;
    output: typeof CreateCaseResponseSchema;
  },
  /**
   * Full fetch
   *
   * @generated from rpc hero.cases.v1.CaseService.GetCase
   */
  getCase: {
    methodKind: "unary";
    input: typeof GetCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * Requires expected_etag
   *
   * @generated from rpc hero.cases.v1.CaseService.UpdateCase
   */
  updateCase: {
    methodKind: "unary";
    input: typeof UpdateCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.DeleteCase
   */
  deleteCase: {
    methodKind: "unary";
    input: typeof DeleteCaseRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * -------- Listing -------- 
   *
   * Generic list
   *
   * @generated from rpc hero.cases.v1.CaseService.ListCases
   */
  listCases: {
    methodKind: "unary";
    input: typeof ListCasesRequestSchema;
    output: typeof ListCasesResponseSchema;
  },
  /**
   * Bulk fetch
   *
   * @generated from rpc hero.cases.v1.CaseService.BatchGetCases
   */
  batchGetCases: {
    methodKind: "unary";
    input: typeof BatchGetCasesRequestSchema;
    output: typeof BatchGetCasesResponseSchema;
  },
  /**
   * Fast lookup helpers
   *
   * @generated from rpc hero.cases.v1.CaseService.ListCasesBySituationId
   */
  listCasesBySituationId: {
    methodKind: "unary";
    input: typeof ListCasesBySituationIdRequestSchema;
    output: typeof ListCasesResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCasesByReportId
   */
  listCasesByReportId: {
    methodKind: "unary";
    input: typeof ListCasesByReportIdRequestSchema;
    output: typeof ListCasesResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCasesByAssetId
   */
  listCasesByAssetId: {
    methodKind: "unary";
    input: typeof ListCasesByAssetIdRequestSchema;
    output: typeof ListCasesResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCasesByEntityId
   */
  listCasesByEntityId: {
    methodKind: "unary";
    input: typeof ListCasesByEntityIdRequestSchema;
    output: typeof ListCasesResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCasesByPropertyId
   */
  listCasesByPropertyId: {
    methodKind: "unary";
    input: typeof ListCasesByPropertyIdRequestSchema;
    output: typeof ListCasesResponseSchema;
  },
  /**
   * -------- Relationship mutators -------- 
   *
   * @generated from rpc hero.cases.v1.CaseService.AddSituationToCase
   */
  addSituationToCase: {
    methodKind: "unary";
    input: typeof AddSituationToCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemoveSituationFromCase
   */
  removeSituationFromCase: {
    methodKind: "unary";
    input: typeof RemoveSituationFromCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AddReportToCase
   */
  addReportToCase: {
    methodKind: "unary";
    input: typeof AddReportToCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemoveReportFromCase
   */
  removeReportFromCase: {
    methodKind: "unary";
    input: typeof RemoveReportFromCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AddEntityRefToCase
   */
  addEntityRefToCase: {
    methodKind: "unary";
    input: typeof AddEntityRefToCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemoveEntityRefFromCase
   */
  removeEntityRefFromCase: {
    methodKind: "unary";
    input: typeof RemoveEntityRefFromCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AddPropertyRefToCase
   */
  addPropertyRefToCase: {
    methodKind: "unary";
    input: typeof AddPropertyRefToCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemovePropertyRefFromCase
   */
  removePropertyRefFromCase: {
    methodKind: "unary";
    input: typeof RemovePropertyRefFromCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.LinkRelatedCase
   */
  linkRelatedCase: {
    methodKind: "unary";
    input: typeof LinkRelatedCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.UnlinkRelatedCase
   */
  unlinkRelatedCase: {
    methodKind: "unary";
    input: typeof UnlinkRelatedCaseRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * -------- Asset associations -------- 
   *
   * Dedicated API for case assignment with side effects
   *
   * @generated from rpc hero.cases.v1.CaseService.AssignCase
   */
  assignCase: {
    methodKind: "unary";
    input: typeof AssignCaseRequestSchema;
    output: typeof AssignCaseResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AssociateAssetToCase
   */
  associateAssetToCase: {
    methodKind: "unary";
    input: typeof AssociateAssetToCaseRequestSchema;
    output: typeof AssociateAssetToCaseResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.UpdateAssetAssociation
   */
  updateAssetAssociation: {
    methodKind: "unary";
    input: typeof UpdateAssetAssociationRequestSchema;
    output: typeof UpdateAssetAssociationResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.DisassociateAssetFromCase
   */
  disassociateAssetFromCase: {
    methodKind: "unary";
    input: typeof DisassociateAssetFromCaseRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListAssetAssociationsForCase
   */
  listAssetAssociationsForCase: {
    methodKind: "unary";
    input: typeof ListAssetAssociationsForCaseRequestSchema;
    output: typeof ListAssetAssociationsForCaseResponseSchema;
  },
  /**
   * -------- Watcher list -------- 
   *
   * @generated from rpc hero.cases.v1.CaseService.AddWatcher
   */
  addWatcher: {
    methodKind: "unary";
    input: typeof AddWatcherRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemoveWatcher
   */
  removeWatcher: {
    methodKind: "unary";
    input: typeof RemoveWatcherRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * -------- Status / updates / tags -------- 
   *
   * @generated from rpc hero.cases.v1.CaseService.UpdateCaseStatus
   */
  updateCaseStatus: {
    methodKind: "unary";
    input: typeof UpdateCaseStatusRequestSchema;
    output: typeof UpdateCaseStatusResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AddCaseUpdate
   */
  addCaseUpdate: {
    methodKind: "unary";
    input: typeof AddCaseUpdateRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemoveCaseUpdate
   */
  removeCaseUpdate: {
    methodKind: "unary";
    input: typeof RemoveCaseUpdateRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCaseUpdates
   */
  listCaseUpdates: {
    methodKind: "unary";
    input: typeof ListCaseUpdatesRequestSchema;
    output: typeof ListCaseUpdatesResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCaseFileAttachments
   */
  listCaseFileAttachments: {
    methodKind: "unary";
    input: typeof ListCaseFileAttachmentsRequestSchema;
    output: typeof ListCaseFileAttachmentsResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCaseStatusHistory
   */
  listCaseStatusHistory: {
    methodKind: "unary";
    input: typeof ListCaseStatusHistoryRequestSchema;
    output: typeof ListCaseStatusHistoryResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AddCaseTag
   */
  addCaseTag: {
    methodKind: "unary";
    input: typeof AddCaseTagRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.RemoveCaseTag
   */
  removeCaseTag: {
    methodKind: "unary";
    input: typeof RemoveCaseTagRequestSchema;
    output: typeof CaseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.AddAdditionalInfo
   */
  addAdditionalInfo: {
    methodKind: "unary";
    input: typeof AddAdditionalInfoRequestSchema;
    output: typeof AddAdditionalInfoResponseSchema;
  },
  /**
   * -------- Audit / versioning -------- 
   *
   * @generated from rpc hero.cases.v1.CaseService.GetCaseVersion
   */
  getCaseVersion: {
    methodKind: "unary";
    input: typeof GetCaseVersionRequestSchema;
    output: typeof CaseSnapshotSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCaseVersions
   */
  listCaseVersions: {
    methodKind: "unary";
    input: typeof ListCaseVersionsRequestSchema;
    output: typeof ListCaseVersionsResponseSchema;
  },
  /**
   * @generated from rpc hero.cases.v1.CaseService.ListCaseAuditLog
   */
  listCaseAuditLog: {
    methodKind: "unary";
    input: typeof ListCaseAuditLogRequestSchema;
    output: typeof ListCaseAuditLogResponseSchema;
  },
  /**
   * -------- Search -------- 
   *
   * @generated from rpc hero.cases.v1.CaseService.SearchCases
   */
  searchCases: {
    methodKind: "unary";
    input: typeof SearchCasesRequestSchema;
    output: typeof SearchCasesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_cases_v1_cases, 0);

