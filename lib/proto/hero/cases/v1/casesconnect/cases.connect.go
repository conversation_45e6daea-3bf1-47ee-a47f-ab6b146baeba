// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/cases/v1/cases.proto

package casesconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v1 "proto/hero/cases/v1"
	strings "strings"

	connect "connectrpc.com/connect"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CaseServiceName is the fully-qualified name of the CaseService service.
	CaseServiceName = "hero.cases.v1.CaseService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CaseServiceCreateCaseProcedure is the fully-qualified name of the CaseService's CreateCase RPC.
	CaseServiceCreateCaseProcedure = "/hero.cases.v1.CaseService/CreateCase"
	// CaseServiceGetCaseProcedure is the fully-qualified name of the CaseService's GetCase RPC.
	CaseServiceGetCaseProcedure = "/hero.cases.v1.CaseService/GetCase"
	// CaseServiceUpdateCaseProcedure is the fully-qualified name of the CaseService's UpdateCase RPC.
	CaseServiceUpdateCaseProcedure = "/hero.cases.v1.CaseService/UpdateCase"
	// CaseServiceDeleteCaseProcedure is the fully-qualified name of the CaseService's DeleteCase RPC.
	CaseServiceDeleteCaseProcedure = "/hero.cases.v1.CaseService/DeleteCase"
	// CaseServiceListCasesProcedure is the fully-qualified name of the CaseService's ListCases RPC.
	CaseServiceListCasesProcedure = "/hero.cases.v1.CaseService/ListCases"
	// CaseServiceBatchGetCasesProcedure is the fully-qualified name of the CaseService's BatchGetCases
	// RPC.
	CaseServiceBatchGetCasesProcedure = "/hero.cases.v1.CaseService/BatchGetCases"
	// CaseServiceListCasesBySituationIdProcedure is the fully-qualified name of the CaseService's
	// ListCasesBySituationId RPC.
	CaseServiceListCasesBySituationIdProcedure = "/hero.cases.v1.CaseService/ListCasesBySituationId"
	// CaseServiceListCasesByReportIdProcedure is the fully-qualified name of the CaseService's
	// ListCasesByReportId RPC.
	CaseServiceListCasesByReportIdProcedure = "/hero.cases.v1.CaseService/ListCasesByReportId"
	// CaseServiceListCasesByAssetIdProcedure is the fully-qualified name of the CaseService's
	// ListCasesByAssetId RPC.
	CaseServiceListCasesByAssetIdProcedure = "/hero.cases.v1.CaseService/ListCasesByAssetId"
	// CaseServiceListCasesByEntityIdProcedure is the fully-qualified name of the CaseService's
	// ListCasesByEntityId RPC.
	CaseServiceListCasesByEntityIdProcedure = "/hero.cases.v1.CaseService/ListCasesByEntityId"
	// CaseServiceListCasesByPropertyIdProcedure is the fully-qualified name of the CaseService's
	// ListCasesByPropertyId RPC.
	CaseServiceListCasesByPropertyIdProcedure = "/hero.cases.v1.CaseService/ListCasesByPropertyId"
	// CaseServiceAddSituationToCaseProcedure is the fully-qualified name of the CaseService's
	// AddSituationToCase RPC.
	CaseServiceAddSituationToCaseProcedure = "/hero.cases.v1.CaseService/AddSituationToCase"
	// CaseServiceRemoveSituationFromCaseProcedure is the fully-qualified name of the CaseService's
	// RemoveSituationFromCase RPC.
	CaseServiceRemoveSituationFromCaseProcedure = "/hero.cases.v1.CaseService/RemoveSituationFromCase"
	// CaseServiceAddReportToCaseProcedure is the fully-qualified name of the CaseService's
	// AddReportToCase RPC.
	CaseServiceAddReportToCaseProcedure = "/hero.cases.v1.CaseService/AddReportToCase"
	// CaseServiceRemoveReportFromCaseProcedure is the fully-qualified name of the CaseService's
	// RemoveReportFromCase RPC.
	CaseServiceRemoveReportFromCaseProcedure = "/hero.cases.v1.CaseService/RemoveReportFromCase"
	// CaseServiceAddEntityRefToCaseProcedure is the fully-qualified name of the CaseService's
	// AddEntityRefToCase RPC.
	CaseServiceAddEntityRefToCaseProcedure = "/hero.cases.v1.CaseService/AddEntityRefToCase"
	// CaseServiceRemoveEntityRefFromCaseProcedure is the fully-qualified name of the CaseService's
	// RemoveEntityRefFromCase RPC.
	CaseServiceRemoveEntityRefFromCaseProcedure = "/hero.cases.v1.CaseService/RemoveEntityRefFromCase"
	// CaseServiceAddPropertyRefToCaseProcedure is the fully-qualified name of the CaseService's
	// AddPropertyRefToCase RPC.
	CaseServiceAddPropertyRefToCaseProcedure = "/hero.cases.v1.CaseService/AddPropertyRefToCase"
	// CaseServiceRemovePropertyRefFromCaseProcedure is the fully-qualified name of the CaseService's
	// RemovePropertyRefFromCase RPC.
	CaseServiceRemovePropertyRefFromCaseProcedure = "/hero.cases.v1.CaseService/RemovePropertyRefFromCase"
	// CaseServiceLinkRelatedCaseProcedure is the fully-qualified name of the CaseService's
	// LinkRelatedCase RPC.
	CaseServiceLinkRelatedCaseProcedure = "/hero.cases.v1.CaseService/LinkRelatedCase"
	// CaseServiceUnlinkRelatedCaseProcedure is the fully-qualified name of the CaseService's
	// UnlinkRelatedCase RPC.
	CaseServiceUnlinkRelatedCaseProcedure = "/hero.cases.v1.CaseService/UnlinkRelatedCase"
	// CaseServiceAssignCaseProcedure is the fully-qualified name of the CaseService's AssignCase RPC.
	CaseServiceAssignCaseProcedure = "/hero.cases.v1.CaseService/AssignCase"
	// CaseServiceAssociateAssetToCaseProcedure is the fully-qualified name of the CaseService's
	// AssociateAssetToCase RPC.
	CaseServiceAssociateAssetToCaseProcedure = "/hero.cases.v1.CaseService/AssociateAssetToCase"
	// CaseServiceUpdateAssetAssociationProcedure is the fully-qualified name of the CaseService's
	// UpdateAssetAssociation RPC.
	CaseServiceUpdateAssetAssociationProcedure = "/hero.cases.v1.CaseService/UpdateAssetAssociation"
	// CaseServiceDisassociateAssetFromCaseProcedure is the fully-qualified name of the CaseService's
	// DisassociateAssetFromCase RPC.
	CaseServiceDisassociateAssetFromCaseProcedure = "/hero.cases.v1.CaseService/DisassociateAssetFromCase"
	// CaseServiceListAssetAssociationsForCaseProcedure is the fully-qualified name of the CaseService's
	// ListAssetAssociationsForCase RPC.
	CaseServiceListAssetAssociationsForCaseProcedure = "/hero.cases.v1.CaseService/ListAssetAssociationsForCase"
	// CaseServiceAddWatcherProcedure is the fully-qualified name of the CaseService's AddWatcher RPC.
	CaseServiceAddWatcherProcedure = "/hero.cases.v1.CaseService/AddWatcher"
	// CaseServiceRemoveWatcherProcedure is the fully-qualified name of the CaseService's RemoveWatcher
	// RPC.
	CaseServiceRemoveWatcherProcedure = "/hero.cases.v1.CaseService/RemoveWatcher"
	// CaseServiceUpdateCaseStatusProcedure is the fully-qualified name of the CaseService's
	// UpdateCaseStatus RPC.
	CaseServiceUpdateCaseStatusProcedure = "/hero.cases.v1.CaseService/UpdateCaseStatus"
	// CaseServiceAddCaseUpdateProcedure is the fully-qualified name of the CaseService's AddCaseUpdate
	// RPC.
	CaseServiceAddCaseUpdateProcedure = "/hero.cases.v1.CaseService/AddCaseUpdate"
	// CaseServiceRemoveCaseUpdateProcedure is the fully-qualified name of the CaseService's
	// RemoveCaseUpdate RPC.
	CaseServiceRemoveCaseUpdateProcedure = "/hero.cases.v1.CaseService/RemoveCaseUpdate"
	// CaseServiceListCaseUpdatesProcedure is the fully-qualified name of the CaseService's
	// ListCaseUpdates RPC.
	CaseServiceListCaseUpdatesProcedure = "/hero.cases.v1.CaseService/ListCaseUpdates"
	// CaseServiceListCaseFileAttachmentsProcedure is the fully-qualified name of the CaseService's
	// ListCaseFileAttachments RPC.
	CaseServiceListCaseFileAttachmentsProcedure = "/hero.cases.v1.CaseService/ListCaseFileAttachments"
	// CaseServiceListCaseStatusHistoryProcedure is the fully-qualified name of the CaseService's
	// ListCaseStatusHistory RPC.
	CaseServiceListCaseStatusHistoryProcedure = "/hero.cases.v1.CaseService/ListCaseStatusHistory"
	// CaseServiceAddCaseTagProcedure is the fully-qualified name of the CaseService's AddCaseTag RPC.
	CaseServiceAddCaseTagProcedure = "/hero.cases.v1.CaseService/AddCaseTag"
	// CaseServiceRemoveCaseTagProcedure is the fully-qualified name of the CaseService's RemoveCaseTag
	// RPC.
	CaseServiceRemoveCaseTagProcedure = "/hero.cases.v1.CaseService/RemoveCaseTag"
	// CaseServiceAddAdditionalInfoProcedure is the fully-qualified name of the CaseService's
	// AddAdditionalInfo RPC.
	CaseServiceAddAdditionalInfoProcedure = "/hero.cases.v1.CaseService/AddAdditionalInfo"
	// CaseServiceGetCaseVersionProcedure is the fully-qualified name of the CaseService's
	// GetCaseVersion RPC.
	CaseServiceGetCaseVersionProcedure = "/hero.cases.v1.CaseService/GetCaseVersion"
	// CaseServiceListCaseVersionsProcedure is the fully-qualified name of the CaseService's
	// ListCaseVersions RPC.
	CaseServiceListCaseVersionsProcedure = "/hero.cases.v1.CaseService/ListCaseVersions"
	// CaseServiceListCaseAuditLogProcedure is the fully-qualified name of the CaseService's
	// ListCaseAuditLog RPC.
	CaseServiceListCaseAuditLogProcedure = "/hero.cases.v1.CaseService/ListCaseAuditLog"
	// CaseServiceSearchCasesProcedure is the fully-qualified name of the CaseService's SearchCases RPC.
	CaseServiceSearchCasesProcedure = "/hero.cases.v1.CaseService/SearchCases"
)

// CaseServiceClient is a client for the hero.cases.v1.CaseService service.
type CaseServiceClient interface {
	// -------- Core CRUD --------
	CreateCase(context.Context, *connect.Request[v1.CreateCaseRequest]) (*connect.Response[v1.CreateCaseResponse], error)
	GetCase(context.Context, *connect.Request[v1.GetCaseRequest]) (*connect.Response[v1.Case], error)
	UpdateCase(context.Context, *connect.Request[v1.UpdateCaseRequest]) (*connect.Response[v1.Case], error)
	DeleteCase(context.Context, *connect.Request[v1.DeleteCaseRequest]) (*connect.Response[emptypb.Empty], error)
	// -------- Listing --------
	ListCases(context.Context, *connect.Request[v1.ListCasesRequest]) (*connect.Response[v1.ListCasesResponse], error)
	BatchGetCases(context.Context, *connect.Request[v1.BatchGetCasesRequest]) (*connect.Response[v1.BatchGetCasesResponse], error)
	ListCasesBySituationId(context.Context, *connect.Request[v1.ListCasesBySituationIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByReportId(context.Context, *connect.Request[v1.ListCasesByReportIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByAssetId(context.Context, *connect.Request[v1.ListCasesByAssetIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByEntityId(context.Context, *connect.Request[v1.ListCasesByEntityIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByPropertyId(context.Context, *connect.Request[v1.ListCasesByPropertyIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	// -------- Relationship mutators --------
	AddSituationToCase(context.Context, *connect.Request[v1.AddSituationToCaseRequest]) (*connect.Response[v1.Case], error)
	RemoveSituationFromCase(context.Context, *connect.Request[v1.RemoveSituationFromCaseRequest]) (*connect.Response[v1.Case], error)
	AddReportToCase(context.Context, *connect.Request[v1.AddReportToCaseRequest]) (*connect.Response[v1.Case], error)
	RemoveReportFromCase(context.Context, *connect.Request[v1.RemoveReportFromCaseRequest]) (*connect.Response[v1.Case], error)
	AddEntityRefToCase(context.Context, *connect.Request[v1.AddEntityRefToCaseRequest]) (*connect.Response[v1.Case], error)
	RemoveEntityRefFromCase(context.Context, *connect.Request[v1.RemoveEntityRefFromCaseRequest]) (*connect.Response[v1.Case], error)
	AddPropertyRefToCase(context.Context, *connect.Request[v1.AddPropertyRefToCaseRequest]) (*connect.Response[v1.Case], error)
	RemovePropertyRefFromCase(context.Context, *connect.Request[v1.RemovePropertyRefFromCaseRequest]) (*connect.Response[v1.Case], error)
	LinkRelatedCase(context.Context, *connect.Request[v1.LinkRelatedCaseRequest]) (*connect.Response[v1.Case], error)
	UnlinkRelatedCase(context.Context, *connect.Request[v1.UnlinkRelatedCaseRequest]) (*connect.Response[v1.Case], error)
	// -------- Asset associations --------
	AssignCase(context.Context, *connect.Request[v1.AssignCaseRequest]) (*connect.Response[v1.AssignCaseResponse], error)
	AssociateAssetToCase(context.Context, *connect.Request[v1.AssociateAssetToCaseRequest]) (*connect.Response[v1.AssociateAssetToCaseResponse], error)
	UpdateAssetAssociation(context.Context, *connect.Request[v1.UpdateAssetAssociationRequest]) (*connect.Response[v1.UpdateAssetAssociationResponse], error)
	DisassociateAssetFromCase(context.Context, *connect.Request[v1.DisassociateAssetFromCaseRequest]) (*connect.Response[emptypb.Empty], error)
	ListAssetAssociationsForCase(context.Context, *connect.Request[v1.ListAssetAssociationsForCaseRequest]) (*connect.Response[v1.ListAssetAssociationsForCaseResponse], error)
	// -------- Watcher list --------
	AddWatcher(context.Context, *connect.Request[v1.AddWatcherRequest]) (*connect.Response[v1.Case], error)
	RemoveWatcher(context.Context, *connect.Request[v1.RemoveWatcherRequest]) (*connect.Response[v1.Case], error)
	// -------- Status / updates / tags --------
	UpdateCaseStatus(context.Context, *connect.Request[v1.UpdateCaseStatusRequest]) (*connect.Response[v1.UpdateCaseStatusResponse], error)
	AddCaseUpdate(context.Context, *connect.Request[v1.AddCaseUpdateRequest]) (*connect.Response[v1.Case], error)
	RemoveCaseUpdate(context.Context, *connect.Request[v1.RemoveCaseUpdateRequest]) (*connect.Response[v1.Case], error)
	ListCaseUpdates(context.Context, *connect.Request[v1.ListCaseUpdatesRequest]) (*connect.Response[v1.ListCaseUpdatesResponse], error)
	ListCaseFileAttachments(context.Context, *connect.Request[v1.ListCaseFileAttachmentsRequest]) (*connect.Response[v1.ListCaseFileAttachmentsResponse], error)
	ListCaseStatusHistory(context.Context, *connect.Request[v1.ListCaseStatusHistoryRequest]) (*connect.Response[v1.ListCaseStatusHistoryResponse], error)
	AddCaseTag(context.Context, *connect.Request[v1.AddCaseTagRequest]) (*connect.Response[v1.Case], error)
	RemoveCaseTag(context.Context, *connect.Request[v1.RemoveCaseTagRequest]) (*connect.Response[v1.Case], error)
	AddAdditionalInfo(context.Context, *connect.Request[v1.AddAdditionalInfoRequest]) (*connect.Response[v1.AddAdditionalInfoResponse], error)
	// -------- Audit / versioning --------
	GetCaseVersion(context.Context, *connect.Request[v1.GetCaseVersionRequest]) (*connect.Response[v1.CaseSnapshot], error)
	ListCaseVersions(context.Context, *connect.Request[v1.ListCaseVersionsRequest]) (*connect.Response[v1.ListCaseVersionsResponse], error)
	ListCaseAuditLog(context.Context, *connect.Request[v1.ListCaseAuditLogRequest]) (*connect.Response[v1.ListCaseAuditLogResponse], error)
	// -------- Search --------
	SearchCases(context.Context, *connect.Request[v1.SearchCasesRequest]) (*connect.Response[v1.SearchCasesResponse], error)
}

// NewCaseServiceClient constructs a client for the hero.cases.v1.CaseService service. By default,
// it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and
// sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC()
// or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCaseServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CaseServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	caseServiceMethods := v1.File_hero_cases_v1_cases_proto.Services().ByName("CaseService").Methods()
	return &caseServiceClient{
		createCase: connect.NewClient[v1.CreateCaseRequest, v1.CreateCaseResponse](
			httpClient,
			baseURL+CaseServiceCreateCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("CreateCase")),
			connect.WithClientOptions(opts...),
		),
		getCase: connect.NewClient[v1.GetCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceGetCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("GetCase")),
			connect.WithClientOptions(opts...),
		),
		updateCase: connect.NewClient[v1.UpdateCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceUpdateCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("UpdateCase")),
			connect.WithClientOptions(opts...),
		),
		deleteCase: connect.NewClient[v1.DeleteCaseRequest, emptypb.Empty](
			httpClient,
			baseURL+CaseServiceDeleteCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("DeleteCase")),
			connect.WithClientOptions(opts...),
		),
		listCases: connect.NewClient[v1.ListCasesRequest, v1.ListCasesResponse](
			httpClient,
			baseURL+CaseServiceListCasesProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCases")),
			connect.WithClientOptions(opts...),
		),
		batchGetCases: connect.NewClient[v1.BatchGetCasesRequest, v1.BatchGetCasesResponse](
			httpClient,
			baseURL+CaseServiceBatchGetCasesProcedure,
			connect.WithSchema(caseServiceMethods.ByName("BatchGetCases")),
			connect.WithClientOptions(opts...),
		),
		listCasesBySituationId: connect.NewClient[v1.ListCasesBySituationIdRequest, v1.ListCasesResponse](
			httpClient,
			baseURL+CaseServiceListCasesBySituationIdProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCasesBySituationId")),
			connect.WithClientOptions(opts...),
		),
		listCasesByReportId: connect.NewClient[v1.ListCasesByReportIdRequest, v1.ListCasesResponse](
			httpClient,
			baseURL+CaseServiceListCasesByReportIdProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCasesByReportId")),
			connect.WithClientOptions(opts...),
		),
		listCasesByAssetId: connect.NewClient[v1.ListCasesByAssetIdRequest, v1.ListCasesResponse](
			httpClient,
			baseURL+CaseServiceListCasesByAssetIdProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCasesByAssetId")),
			connect.WithClientOptions(opts...),
		),
		listCasesByEntityId: connect.NewClient[v1.ListCasesByEntityIdRequest, v1.ListCasesResponse](
			httpClient,
			baseURL+CaseServiceListCasesByEntityIdProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCasesByEntityId")),
			connect.WithClientOptions(opts...),
		),
		listCasesByPropertyId: connect.NewClient[v1.ListCasesByPropertyIdRequest, v1.ListCasesResponse](
			httpClient,
			baseURL+CaseServiceListCasesByPropertyIdProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCasesByPropertyId")),
			connect.WithClientOptions(opts...),
		),
		addSituationToCase: connect.NewClient[v1.AddSituationToCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddSituationToCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddSituationToCase")),
			connect.WithClientOptions(opts...),
		),
		removeSituationFromCase: connect.NewClient[v1.RemoveSituationFromCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemoveSituationFromCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemoveSituationFromCase")),
			connect.WithClientOptions(opts...),
		),
		addReportToCase: connect.NewClient[v1.AddReportToCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddReportToCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddReportToCase")),
			connect.WithClientOptions(opts...),
		),
		removeReportFromCase: connect.NewClient[v1.RemoveReportFromCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemoveReportFromCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemoveReportFromCase")),
			connect.WithClientOptions(opts...),
		),
		addEntityRefToCase: connect.NewClient[v1.AddEntityRefToCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddEntityRefToCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddEntityRefToCase")),
			connect.WithClientOptions(opts...),
		),
		removeEntityRefFromCase: connect.NewClient[v1.RemoveEntityRefFromCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemoveEntityRefFromCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemoveEntityRefFromCase")),
			connect.WithClientOptions(opts...),
		),
		addPropertyRefToCase: connect.NewClient[v1.AddPropertyRefToCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddPropertyRefToCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddPropertyRefToCase")),
			connect.WithClientOptions(opts...),
		),
		removePropertyRefFromCase: connect.NewClient[v1.RemovePropertyRefFromCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemovePropertyRefFromCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemovePropertyRefFromCase")),
			connect.WithClientOptions(opts...),
		),
		linkRelatedCase: connect.NewClient[v1.LinkRelatedCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceLinkRelatedCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("LinkRelatedCase")),
			connect.WithClientOptions(opts...),
		),
		unlinkRelatedCase: connect.NewClient[v1.UnlinkRelatedCaseRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceUnlinkRelatedCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("UnlinkRelatedCase")),
			connect.WithClientOptions(opts...),
		),
		assignCase: connect.NewClient[v1.AssignCaseRequest, v1.AssignCaseResponse](
			httpClient,
			baseURL+CaseServiceAssignCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AssignCase")),
			connect.WithClientOptions(opts...),
		),
		associateAssetToCase: connect.NewClient[v1.AssociateAssetToCaseRequest, v1.AssociateAssetToCaseResponse](
			httpClient,
			baseURL+CaseServiceAssociateAssetToCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AssociateAssetToCase")),
			connect.WithClientOptions(opts...),
		),
		updateAssetAssociation: connect.NewClient[v1.UpdateAssetAssociationRequest, v1.UpdateAssetAssociationResponse](
			httpClient,
			baseURL+CaseServiceUpdateAssetAssociationProcedure,
			connect.WithSchema(caseServiceMethods.ByName("UpdateAssetAssociation")),
			connect.WithClientOptions(opts...),
		),
		disassociateAssetFromCase: connect.NewClient[v1.DisassociateAssetFromCaseRequest, emptypb.Empty](
			httpClient,
			baseURL+CaseServiceDisassociateAssetFromCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("DisassociateAssetFromCase")),
			connect.WithClientOptions(opts...),
		),
		listAssetAssociationsForCase: connect.NewClient[v1.ListAssetAssociationsForCaseRequest, v1.ListAssetAssociationsForCaseResponse](
			httpClient,
			baseURL+CaseServiceListAssetAssociationsForCaseProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListAssetAssociationsForCase")),
			connect.WithClientOptions(opts...),
		),
		addWatcher: connect.NewClient[v1.AddWatcherRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddWatcherProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddWatcher")),
			connect.WithClientOptions(opts...),
		),
		removeWatcher: connect.NewClient[v1.RemoveWatcherRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemoveWatcherProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemoveWatcher")),
			connect.WithClientOptions(opts...),
		),
		updateCaseStatus: connect.NewClient[v1.UpdateCaseStatusRequest, v1.UpdateCaseStatusResponse](
			httpClient,
			baseURL+CaseServiceUpdateCaseStatusProcedure,
			connect.WithSchema(caseServiceMethods.ByName("UpdateCaseStatus")),
			connect.WithClientOptions(opts...),
		),
		addCaseUpdate: connect.NewClient[v1.AddCaseUpdateRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddCaseUpdateProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddCaseUpdate")),
			connect.WithClientOptions(opts...),
		),
		removeCaseUpdate: connect.NewClient[v1.RemoveCaseUpdateRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemoveCaseUpdateProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemoveCaseUpdate")),
			connect.WithClientOptions(opts...),
		),
		listCaseUpdates: connect.NewClient[v1.ListCaseUpdatesRequest, v1.ListCaseUpdatesResponse](
			httpClient,
			baseURL+CaseServiceListCaseUpdatesProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCaseUpdates")),
			connect.WithClientOptions(opts...),
		),
		listCaseFileAttachments: connect.NewClient[v1.ListCaseFileAttachmentsRequest, v1.ListCaseFileAttachmentsResponse](
			httpClient,
			baseURL+CaseServiceListCaseFileAttachmentsProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCaseFileAttachments")),
			connect.WithClientOptions(opts...),
		),
		listCaseStatusHistory: connect.NewClient[v1.ListCaseStatusHistoryRequest, v1.ListCaseStatusHistoryResponse](
			httpClient,
			baseURL+CaseServiceListCaseStatusHistoryProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCaseStatusHistory")),
			connect.WithClientOptions(opts...),
		),
		addCaseTag: connect.NewClient[v1.AddCaseTagRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceAddCaseTagProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddCaseTag")),
			connect.WithClientOptions(opts...),
		),
		removeCaseTag: connect.NewClient[v1.RemoveCaseTagRequest, v1.Case](
			httpClient,
			baseURL+CaseServiceRemoveCaseTagProcedure,
			connect.WithSchema(caseServiceMethods.ByName("RemoveCaseTag")),
			connect.WithClientOptions(opts...),
		),
		addAdditionalInfo: connect.NewClient[v1.AddAdditionalInfoRequest, v1.AddAdditionalInfoResponse](
			httpClient,
			baseURL+CaseServiceAddAdditionalInfoProcedure,
			connect.WithSchema(caseServiceMethods.ByName("AddAdditionalInfo")),
			connect.WithClientOptions(opts...),
		),
		getCaseVersion: connect.NewClient[v1.GetCaseVersionRequest, v1.CaseSnapshot](
			httpClient,
			baseURL+CaseServiceGetCaseVersionProcedure,
			connect.WithSchema(caseServiceMethods.ByName("GetCaseVersion")),
			connect.WithClientOptions(opts...),
		),
		listCaseVersions: connect.NewClient[v1.ListCaseVersionsRequest, v1.ListCaseVersionsResponse](
			httpClient,
			baseURL+CaseServiceListCaseVersionsProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCaseVersions")),
			connect.WithClientOptions(opts...),
		),
		listCaseAuditLog: connect.NewClient[v1.ListCaseAuditLogRequest, v1.ListCaseAuditLogResponse](
			httpClient,
			baseURL+CaseServiceListCaseAuditLogProcedure,
			connect.WithSchema(caseServiceMethods.ByName("ListCaseAuditLog")),
			connect.WithClientOptions(opts...),
		),
		searchCases: connect.NewClient[v1.SearchCasesRequest, v1.SearchCasesResponse](
			httpClient,
			baseURL+CaseServiceSearchCasesProcedure,
			connect.WithSchema(caseServiceMethods.ByName("SearchCases")),
			connect.WithClientOptions(opts...),
		),
	}
}

// caseServiceClient implements CaseServiceClient.
type caseServiceClient struct {
	createCase                   *connect.Client[v1.CreateCaseRequest, v1.CreateCaseResponse]
	getCase                      *connect.Client[v1.GetCaseRequest, v1.Case]
	updateCase                   *connect.Client[v1.UpdateCaseRequest, v1.Case]
	deleteCase                   *connect.Client[v1.DeleteCaseRequest, emptypb.Empty]
	listCases                    *connect.Client[v1.ListCasesRequest, v1.ListCasesResponse]
	batchGetCases                *connect.Client[v1.BatchGetCasesRequest, v1.BatchGetCasesResponse]
	listCasesBySituationId       *connect.Client[v1.ListCasesBySituationIdRequest, v1.ListCasesResponse]
	listCasesByReportId          *connect.Client[v1.ListCasesByReportIdRequest, v1.ListCasesResponse]
	listCasesByAssetId           *connect.Client[v1.ListCasesByAssetIdRequest, v1.ListCasesResponse]
	listCasesByEntityId          *connect.Client[v1.ListCasesByEntityIdRequest, v1.ListCasesResponse]
	listCasesByPropertyId        *connect.Client[v1.ListCasesByPropertyIdRequest, v1.ListCasesResponse]
	addSituationToCase           *connect.Client[v1.AddSituationToCaseRequest, v1.Case]
	removeSituationFromCase      *connect.Client[v1.RemoveSituationFromCaseRequest, v1.Case]
	addReportToCase              *connect.Client[v1.AddReportToCaseRequest, v1.Case]
	removeReportFromCase         *connect.Client[v1.RemoveReportFromCaseRequest, v1.Case]
	addEntityRefToCase           *connect.Client[v1.AddEntityRefToCaseRequest, v1.Case]
	removeEntityRefFromCase      *connect.Client[v1.RemoveEntityRefFromCaseRequest, v1.Case]
	addPropertyRefToCase         *connect.Client[v1.AddPropertyRefToCaseRequest, v1.Case]
	removePropertyRefFromCase    *connect.Client[v1.RemovePropertyRefFromCaseRequest, v1.Case]
	linkRelatedCase              *connect.Client[v1.LinkRelatedCaseRequest, v1.Case]
	unlinkRelatedCase            *connect.Client[v1.UnlinkRelatedCaseRequest, v1.Case]
	assignCase                   *connect.Client[v1.AssignCaseRequest, v1.AssignCaseResponse]
	associateAssetToCase         *connect.Client[v1.AssociateAssetToCaseRequest, v1.AssociateAssetToCaseResponse]
	updateAssetAssociation       *connect.Client[v1.UpdateAssetAssociationRequest, v1.UpdateAssetAssociationResponse]
	disassociateAssetFromCase    *connect.Client[v1.DisassociateAssetFromCaseRequest, emptypb.Empty]
	listAssetAssociationsForCase *connect.Client[v1.ListAssetAssociationsForCaseRequest, v1.ListAssetAssociationsForCaseResponse]
	addWatcher                   *connect.Client[v1.AddWatcherRequest, v1.Case]
	removeWatcher                *connect.Client[v1.RemoveWatcherRequest, v1.Case]
	updateCaseStatus             *connect.Client[v1.UpdateCaseStatusRequest, v1.UpdateCaseStatusResponse]
	addCaseUpdate                *connect.Client[v1.AddCaseUpdateRequest, v1.Case]
	removeCaseUpdate             *connect.Client[v1.RemoveCaseUpdateRequest, v1.Case]
	listCaseUpdates              *connect.Client[v1.ListCaseUpdatesRequest, v1.ListCaseUpdatesResponse]
	listCaseFileAttachments      *connect.Client[v1.ListCaseFileAttachmentsRequest, v1.ListCaseFileAttachmentsResponse]
	listCaseStatusHistory        *connect.Client[v1.ListCaseStatusHistoryRequest, v1.ListCaseStatusHistoryResponse]
	addCaseTag                   *connect.Client[v1.AddCaseTagRequest, v1.Case]
	removeCaseTag                *connect.Client[v1.RemoveCaseTagRequest, v1.Case]
	addAdditionalInfo            *connect.Client[v1.AddAdditionalInfoRequest, v1.AddAdditionalInfoResponse]
	getCaseVersion               *connect.Client[v1.GetCaseVersionRequest, v1.CaseSnapshot]
	listCaseVersions             *connect.Client[v1.ListCaseVersionsRequest, v1.ListCaseVersionsResponse]
	listCaseAuditLog             *connect.Client[v1.ListCaseAuditLogRequest, v1.ListCaseAuditLogResponse]
	searchCases                  *connect.Client[v1.SearchCasesRequest, v1.SearchCasesResponse]
}

// CreateCase calls hero.cases.v1.CaseService.CreateCase.
func (c *caseServiceClient) CreateCase(ctx context.Context, req *connect.Request[v1.CreateCaseRequest]) (*connect.Response[v1.CreateCaseResponse], error) {
	return c.createCase.CallUnary(ctx, req)
}

// GetCase calls hero.cases.v1.CaseService.GetCase.
func (c *caseServiceClient) GetCase(ctx context.Context, req *connect.Request[v1.GetCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.getCase.CallUnary(ctx, req)
}

// UpdateCase calls hero.cases.v1.CaseService.UpdateCase.
func (c *caseServiceClient) UpdateCase(ctx context.Context, req *connect.Request[v1.UpdateCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.updateCase.CallUnary(ctx, req)
}

// DeleteCase calls hero.cases.v1.CaseService.DeleteCase.
func (c *caseServiceClient) DeleteCase(ctx context.Context, req *connect.Request[v1.DeleteCaseRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteCase.CallUnary(ctx, req)
}

// ListCases calls hero.cases.v1.CaseService.ListCases.
func (c *caseServiceClient) ListCases(ctx context.Context, req *connect.Request[v1.ListCasesRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return c.listCases.CallUnary(ctx, req)
}

// BatchGetCases calls hero.cases.v1.CaseService.BatchGetCases.
func (c *caseServiceClient) BatchGetCases(ctx context.Context, req *connect.Request[v1.BatchGetCasesRequest]) (*connect.Response[v1.BatchGetCasesResponse], error) {
	return c.batchGetCases.CallUnary(ctx, req)
}

// ListCasesBySituationId calls hero.cases.v1.CaseService.ListCasesBySituationId.
func (c *caseServiceClient) ListCasesBySituationId(ctx context.Context, req *connect.Request[v1.ListCasesBySituationIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return c.listCasesBySituationId.CallUnary(ctx, req)
}

// ListCasesByReportId calls hero.cases.v1.CaseService.ListCasesByReportId.
func (c *caseServiceClient) ListCasesByReportId(ctx context.Context, req *connect.Request[v1.ListCasesByReportIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return c.listCasesByReportId.CallUnary(ctx, req)
}

// ListCasesByAssetId calls hero.cases.v1.CaseService.ListCasesByAssetId.
func (c *caseServiceClient) ListCasesByAssetId(ctx context.Context, req *connect.Request[v1.ListCasesByAssetIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return c.listCasesByAssetId.CallUnary(ctx, req)
}

// ListCasesByEntityId calls hero.cases.v1.CaseService.ListCasesByEntityId.
func (c *caseServiceClient) ListCasesByEntityId(ctx context.Context, req *connect.Request[v1.ListCasesByEntityIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return c.listCasesByEntityId.CallUnary(ctx, req)
}

// ListCasesByPropertyId calls hero.cases.v1.CaseService.ListCasesByPropertyId.
func (c *caseServiceClient) ListCasesByPropertyId(ctx context.Context, req *connect.Request[v1.ListCasesByPropertyIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return c.listCasesByPropertyId.CallUnary(ctx, req)
}

// AddSituationToCase calls hero.cases.v1.CaseService.AddSituationToCase.
func (c *caseServiceClient) AddSituationToCase(ctx context.Context, req *connect.Request[v1.AddSituationToCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.addSituationToCase.CallUnary(ctx, req)
}

// RemoveSituationFromCase calls hero.cases.v1.CaseService.RemoveSituationFromCase.
func (c *caseServiceClient) RemoveSituationFromCase(ctx context.Context, req *connect.Request[v1.RemoveSituationFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.removeSituationFromCase.CallUnary(ctx, req)
}

// AddReportToCase calls hero.cases.v1.CaseService.AddReportToCase.
func (c *caseServiceClient) AddReportToCase(ctx context.Context, req *connect.Request[v1.AddReportToCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.addReportToCase.CallUnary(ctx, req)
}

// RemoveReportFromCase calls hero.cases.v1.CaseService.RemoveReportFromCase.
func (c *caseServiceClient) RemoveReportFromCase(ctx context.Context, req *connect.Request[v1.RemoveReportFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.removeReportFromCase.CallUnary(ctx, req)
}

// AddEntityRefToCase calls hero.cases.v1.CaseService.AddEntityRefToCase.
func (c *caseServiceClient) AddEntityRefToCase(ctx context.Context, req *connect.Request[v1.AddEntityRefToCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.addEntityRefToCase.CallUnary(ctx, req)
}

// RemoveEntityRefFromCase calls hero.cases.v1.CaseService.RemoveEntityRefFromCase.
func (c *caseServiceClient) RemoveEntityRefFromCase(ctx context.Context, req *connect.Request[v1.RemoveEntityRefFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.removeEntityRefFromCase.CallUnary(ctx, req)
}

// AddPropertyRefToCase calls hero.cases.v1.CaseService.AddPropertyRefToCase.
func (c *caseServiceClient) AddPropertyRefToCase(ctx context.Context, req *connect.Request[v1.AddPropertyRefToCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.addPropertyRefToCase.CallUnary(ctx, req)
}

// RemovePropertyRefFromCase calls hero.cases.v1.CaseService.RemovePropertyRefFromCase.
func (c *caseServiceClient) RemovePropertyRefFromCase(ctx context.Context, req *connect.Request[v1.RemovePropertyRefFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.removePropertyRefFromCase.CallUnary(ctx, req)
}

// LinkRelatedCase calls hero.cases.v1.CaseService.LinkRelatedCase.
func (c *caseServiceClient) LinkRelatedCase(ctx context.Context, req *connect.Request[v1.LinkRelatedCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.linkRelatedCase.CallUnary(ctx, req)
}

// UnlinkRelatedCase calls hero.cases.v1.CaseService.UnlinkRelatedCase.
func (c *caseServiceClient) UnlinkRelatedCase(ctx context.Context, req *connect.Request[v1.UnlinkRelatedCaseRequest]) (*connect.Response[v1.Case], error) {
	return c.unlinkRelatedCase.CallUnary(ctx, req)
}

// AssignCase calls hero.cases.v1.CaseService.AssignCase.
func (c *caseServiceClient) AssignCase(ctx context.Context, req *connect.Request[v1.AssignCaseRequest]) (*connect.Response[v1.AssignCaseResponse], error) {
	return c.assignCase.CallUnary(ctx, req)
}

// AssociateAssetToCase calls hero.cases.v1.CaseService.AssociateAssetToCase.
func (c *caseServiceClient) AssociateAssetToCase(ctx context.Context, req *connect.Request[v1.AssociateAssetToCaseRequest]) (*connect.Response[v1.AssociateAssetToCaseResponse], error) {
	return c.associateAssetToCase.CallUnary(ctx, req)
}

// UpdateAssetAssociation calls hero.cases.v1.CaseService.UpdateAssetAssociation.
func (c *caseServiceClient) UpdateAssetAssociation(ctx context.Context, req *connect.Request[v1.UpdateAssetAssociationRequest]) (*connect.Response[v1.UpdateAssetAssociationResponse], error) {
	return c.updateAssetAssociation.CallUnary(ctx, req)
}

// DisassociateAssetFromCase calls hero.cases.v1.CaseService.DisassociateAssetFromCase.
func (c *caseServiceClient) DisassociateAssetFromCase(ctx context.Context, req *connect.Request[v1.DisassociateAssetFromCaseRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.disassociateAssetFromCase.CallUnary(ctx, req)
}

// ListAssetAssociationsForCase calls hero.cases.v1.CaseService.ListAssetAssociationsForCase.
func (c *caseServiceClient) ListAssetAssociationsForCase(ctx context.Context, req *connect.Request[v1.ListAssetAssociationsForCaseRequest]) (*connect.Response[v1.ListAssetAssociationsForCaseResponse], error) {
	return c.listAssetAssociationsForCase.CallUnary(ctx, req)
}

// AddWatcher calls hero.cases.v1.CaseService.AddWatcher.
func (c *caseServiceClient) AddWatcher(ctx context.Context, req *connect.Request[v1.AddWatcherRequest]) (*connect.Response[v1.Case], error) {
	return c.addWatcher.CallUnary(ctx, req)
}

// RemoveWatcher calls hero.cases.v1.CaseService.RemoveWatcher.
func (c *caseServiceClient) RemoveWatcher(ctx context.Context, req *connect.Request[v1.RemoveWatcherRequest]) (*connect.Response[v1.Case], error) {
	return c.removeWatcher.CallUnary(ctx, req)
}

// UpdateCaseStatus calls hero.cases.v1.CaseService.UpdateCaseStatus.
func (c *caseServiceClient) UpdateCaseStatus(ctx context.Context, req *connect.Request[v1.UpdateCaseStatusRequest]) (*connect.Response[v1.UpdateCaseStatusResponse], error) {
	return c.updateCaseStatus.CallUnary(ctx, req)
}

// AddCaseUpdate calls hero.cases.v1.CaseService.AddCaseUpdate.
func (c *caseServiceClient) AddCaseUpdate(ctx context.Context, req *connect.Request[v1.AddCaseUpdateRequest]) (*connect.Response[v1.Case], error) {
	return c.addCaseUpdate.CallUnary(ctx, req)
}

// RemoveCaseUpdate calls hero.cases.v1.CaseService.RemoveCaseUpdate.
func (c *caseServiceClient) RemoveCaseUpdate(ctx context.Context, req *connect.Request[v1.RemoveCaseUpdateRequest]) (*connect.Response[v1.Case], error) {
	return c.removeCaseUpdate.CallUnary(ctx, req)
}

// ListCaseUpdates calls hero.cases.v1.CaseService.ListCaseUpdates.
func (c *caseServiceClient) ListCaseUpdates(ctx context.Context, req *connect.Request[v1.ListCaseUpdatesRequest]) (*connect.Response[v1.ListCaseUpdatesResponse], error) {
	return c.listCaseUpdates.CallUnary(ctx, req)
}

// ListCaseFileAttachments calls hero.cases.v1.CaseService.ListCaseFileAttachments.
func (c *caseServiceClient) ListCaseFileAttachments(ctx context.Context, req *connect.Request[v1.ListCaseFileAttachmentsRequest]) (*connect.Response[v1.ListCaseFileAttachmentsResponse], error) {
	return c.listCaseFileAttachments.CallUnary(ctx, req)
}

// ListCaseStatusHistory calls hero.cases.v1.CaseService.ListCaseStatusHistory.
func (c *caseServiceClient) ListCaseStatusHistory(ctx context.Context, req *connect.Request[v1.ListCaseStatusHistoryRequest]) (*connect.Response[v1.ListCaseStatusHistoryResponse], error) {
	return c.listCaseStatusHistory.CallUnary(ctx, req)
}

// AddCaseTag calls hero.cases.v1.CaseService.AddCaseTag.
func (c *caseServiceClient) AddCaseTag(ctx context.Context, req *connect.Request[v1.AddCaseTagRequest]) (*connect.Response[v1.Case], error) {
	return c.addCaseTag.CallUnary(ctx, req)
}

// RemoveCaseTag calls hero.cases.v1.CaseService.RemoveCaseTag.
func (c *caseServiceClient) RemoveCaseTag(ctx context.Context, req *connect.Request[v1.RemoveCaseTagRequest]) (*connect.Response[v1.Case], error) {
	return c.removeCaseTag.CallUnary(ctx, req)
}

// AddAdditionalInfo calls hero.cases.v1.CaseService.AddAdditionalInfo.
func (c *caseServiceClient) AddAdditionalInfo(ctx context.Context, req *connect.Request[v1.AddAdditionalInfoRequest]) (*connect.Response[v1.AddAdditionalInfoResponse], error) {
	return c.addAdditionalInfo.CallUnary(ctx, req)
}

// GetCaseVersion calls hero.cases.v1.CaseService.GetCaseVersion.
func (c *caseServiceClient) GetCaseVersion(ctx context.Context, req *connect.Request[v1.GetCaseVersionRequest]) (*connect.Response[v1.CaseSnapshot], error) {
	return c.getCaseVersion.CallUnary(ctx, req)
}

// ListCaseVersions calls hero.cases.v1.CaseService.ListCaseVersions.
func (c *caseServiceClient) ListCaseVersions(ctx context.Context, req *connect.Request[v1.ListCaseVersionsRequest]) (*connect.Response[v1.ListCaseVersionsResponse], error) {
	return c.listCaseVersions.CallUnary(ctx, req)
}

// ListCaseAuditLog calls hero.cases.v1.CaseService.ListCaseAuditLog.
func (c *caseServiceClient) ListCaseAuditLog(ctx context.Context, req *connect.Request[v1.ListCaseAuditLogRequest]) (*connect.Response[v1.ListCaseAuditLogResponse], error) {
	return c.listCaseAuditLog.CallUnary(ctx, req)
}

// SearchCases calls hero.cases.v1.CaseService.SearchCases.
func (c *caseServiceClient) SearchCases(ctx context.Context, req *connect.Request[v1.SearchCasesRequest]) (*connect.Response[v1.SearchCasesResponse], error) {
	return c.searchCases.CallUnary(ctx, req)
}

// CaseServiceHandler is an implementation of the hero.cases.v1.CaseService service.
type CaseServiceHandler interface {
	// -------- Core CRUD --------
	CreateCase(context.Context, *connect.Request[v1.CreateCaseRequest]) (*connect.Response[v1.CreateCaseResponse], error)
	GetCase(context.Context, *connect.Request[v1.GetCaseRequest]) (*connect.Response[v1.Case], error)
	UpdateCase(context.Context, *connect.Request[v1.UpdateCaseRequest]) (*connect.Response[v1.Case], error)
	DeleteCase(context.Context, *connect.Request[v1.DeleteCaseRequest]) (*connect.Response[emptypb.Empty], error)
	// -------- Listing --------
	ListCases(context.Context, *connect.Request[v1.ListCasesRequest]) (*connect.Response[v1.ListCasesResponse], error)
	BatchGetCases(context.Context, *connect.Request[v1.BatchGetCasesRequest]) (*connect.Response[v1.BatchGetCasesResponse], error)
	ListCasesBySituationId(context.Context, *connect.Request[v1.ListCasesBySituationIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByReportId(context.Context, *connect.Request[v1.ListCasesByReportIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByAssetId(context.Context, *connect.Request[v1.ListCasesByAssetIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByEntityId(context.Context, *connect.Request[v1.ListCasesByEntityIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	ListCasesByPropertyId(context.Context, *connect.Request[v1.ListCasesByPropertyIdRequest]) (*connect.Response[v1.ListCasesResponse], error)
	// -------- Relationship mutators --------
	AddSituationToCase(context.Context, *connect.Request[v1.AddSituationToCaseRequest]) (*connect.Response[v1.Case], error)
	RemoveSituationFromCase(context.Context, *connect.Request[v1.RemoveSituationFromCaseRequest]) (*connect.Response[v1.Case], error)
	AddReportToCase(context.Context, *connect.Request[v1.AddReportToCaseRequest]) (*connect.Response[v1.Case], error)
	RemoveReportFromCase(context.Context, *connect.Request[v1.RemoveReportFromCaseRequest]) (*connect.Response[v1.Case], error)
	AddEntityRefToCase(context.Context, *connect.Request[v1.AddEntityRefToCaseRequest]) (*connect.Response[v1.Case], error)
	RemoveEntityRefFromCase(context.Context, *connect.Request[v1.RemoveEntityRefFromCaseRequest]) (*connect.Response[v1.Case], error)
	AddPropertyRefToCase(context.Context, *connect.Request[v1.AddPropertyRefToCaseRequest]) (*connect.Response[v1.Case], error)
	RemovePropertyRefFromCase(context.Context, *connect.Request[v1.RemovePropertyRefFromCaseRequest]) (*connect.Response[v1.Case], error)
	LinkRelatedCase(context.Context, *connect.Request[v1.LinkRelatedCaseRequest]) (*connect.Response[v1.Case], error)
	UnlinkRelatedCase(context.Context, *connect.Request[v1.UnlinkRelatedCaseRequest]) (*connect.Response[v1.Case], error)
	// -------- Asset associations --------
	AssignCase(context.Context, *connect.Request[v1.AssignCaseRequest]) (*connect.Response[v1.AssignCaseResponse], error)
	AssociateAssetToCase(context.Context, *connect.Request[v1.AssociateAssetToCaseRequest]) (*connect.Response[v1.AssociateAssetToCaseResponse], error)
	UpdateAssetAssociation(context.Context, *connect.Request[v1.UpdateAssetAssociationRequest]) (*connect.Response[v1.UpdateAssetAssociationResponse], error)
	DisassociateAssetFromCase(context.Context, *connect.Request[v1.DisassociateAssetFromCaseRequest]) (*connect.Response[emptypb.Empty], error)
	ListAssetAssociationsForCase(context.Context, *connect.Request[v1.ListAssetAssociationsForCaseRequest]) (*connect.Response[v1.ListAssetAssociationsForCaseResponse], error)
	// -------- Watcher list --------
	AddWatcher(context.Context, *connect.Request[v1.AddWatcherRequest]) (*connect.Response[v1.Case], error)
	RemoveWatcher(context.Context, *connect.Request[v1.RemoveWatcherRequest]) (*connect.Response[v1.Case], error)
	// -------- Status / updates / tags --------
	UpdateCaseStatus(context.Context, *connect.Request[v1.UpdateCaseStatusRequest]) (*connect.Response[v1.UpdateCaseStatusResponse], error)
	AddCaseUpdate(context.Context, *connect.Request[v1.AddCaseUpdateRequest]) (*connect.Response[v1.Case], error)
	RemoveCaseUpdate(context.Context, *connect.Request[v1.RemoveCaseUpdateRequest]) (*connect.Response[v1.Case], error)
	ListCaseUpdates(context.Context, *connect.Request[v1.ListCaseUpdatesRequest]) (*connect.Response[v1.ListCaseUpdatesResponse], error)
	ListCaseFileAttachments(context.Context, *connect.Request[v1.ListCaseFileAttachmentsRequest]) (*connect.Response[v1.ListCaseFileAttachmentsResponse], error)
	ListCaseStatusHistory(context.Context, *connect.Request[v1.ListCaseStatusHistoryRequest]) (*connect.Response[v1.ListCaseStatusHistoryResponse], error)
	AddCaseTag(context.Context, *connect.Request[v1.AddCaseTagRequest]) (*connect.Response[v1.Case], error)
	RemoveCaseTag(context.Context, *connect.Request[v1.RemoveCaseTagRequest]) (*connect.Response[v1.Case], error)
	AddAdditionalInfo(context.Context, *connect.Request[v1.AddAdditionalInfoRequest]) (*connect.Response[v1.AddAdditionalInfoResponse], error)
	// -------- Audit / versioning --------
	GetCaseVersion(context.Context, *connect.Request[v1.GetCaseVersionRequest]) (*connect.Response[v1.CaseSnapshot], error)
	ListCaseVersions(context.Context, *connect.Request[v1.ListCaseVersionsRequest]) (*connect.Response[v1.ListCaseVersionsResponse], error)
	ListCaseAuditLog(context.Context, *connect.Request[v1.ListCaseAuditLogRequest]) (*connect.Response[v1.ListCaseAuditLogResponse], error)
	// -------- Search --------
	SearchCases(context.Context, *connect.Request[v1.SearchCasesRequest]) (*connect.Response[v1.SearchCasesResponse], error)
}

// NewCaseServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCaseServiceHandler(svc CaseServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	caseServiceMethods := v1.File_hero_cases_v1_cases_proto.Services().ByName("CaseService").Methods()
	caseServiceCreateCaseHandler := connect.NewUnaryHandler(
		CaseServiceCreateCaseProcedure,
		svc.CreateCase,
		connect.WithSchema(caseServiceMethods.ByName("CreateCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceGetCaseHandler := connect.NewUnaryHandler(
		CaseServiceGetCaseProcedure,
		svc.GetCase,
		connect.WithSchema(caseServiceMethods.ByName("GetCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceUpdateCaseHandler := connect.NewUnaryHandler(
		CaseServiceUpdateCaseProcedure,
		svc.UpdateCase,
		connect.WithSchema(caseServiceMethods.ByName("UpdateCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceDeleteCaseHandler := connect.NewUnaryHandler(
		CaseServiceDeleteCaseProcedure,
		svc.DeleteCase,
		connect.WithSchema(caseServiceMethods.ByName("DeleteCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCasesHandler := connect.NewUnaryHandler(
		CaseServiceListCasesProcedure,
		svc.ListCases,
		connect.WithSchema(caseServiceMethods.ByName("ListCases")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceBatchGetCasesHandler := connect.NewUnaryHandler(
		CaseServiceBatchGetCasesProcedure,
		svc.BatchGetCases,
		connect.WithSchema(caseServiceMethods.ByName("BatchGetCases")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCasesBySituationIdHandler := connect.NewUnaryHandler(
		CaseServiceListCasesBySituationIdProcedure,
		svc.ListCasesBySituationId,
		connect.WithSchema(caseServiceMethods.ByName("ListCasesBySituationId")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCasesByReportIdHandler := connect.NewUnaryHandler(
		CaseServiceListCasesByReportIdProcedure,
		svc.ListCasesByReportId,
		connect.WithSchema(caseServiceMethods.ByName("ListCasesByReportId")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCasesByAssetIdHandler := connect.NewUnaryHandler(
		CaseServiceListCasesByAssetIdProcedure,
		svc.ListCasesByAssetId,
		connect.WithSchema(caseServiceMethods.ByName("ListCasesByAssetId")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCasesByEntityIdHandler := connect.NewUnaryHandler(
		CaseServiceListCasesByEntityIdProcedure,
		svc.ListCasesByEntityId,
		connect.WithSchema(caseServiceMethods.ByName("ListCasesByEntityId")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCasesByPropertyIdHandler := connect.NewUnaryHandler(
		CaseServiceListCasesByPropertyIdProcedure,
		svc.ListCasesByPropertyId,
		connect.WithSchema(caseServiceMethods.ByName("ListCasesByPropertyId")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddSituationToCaseHandler := connect.NewUnaryHandler(
		CaseServiceAddSituationToCaseProcedure,
		svc.AddSituationToCase,
		connect.WithSchema(caseServiceMethods.ByName("AddSituationToCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemoveSituationFromCaseHandler := connect.NewUnaryHandler(
		CaseServiceRemoveSituationFromCaseProcedure,
		svc.RemoveSituationFromCase,
		connect.WithSchema(caseServiceMethods.ByName("RemoveSituationFromCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddReportToCaseHandler := connect.NewUnaryHandler(
		CaseServiceAddReportToCaseProcedure,
		svc.AddReportToCase,
		connect.WithSchema(caseServiceMethods.ByName("AddReportToCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemoveReportFromCaseHandler := connect.NewUnaryHandler(
		CaseServiceRemoveReportFromCaseProcedure,
		svc.RemoveReportFromCase,
		connect.WithSchema(caseServiceMethods.ByName("RemoveReportFromCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddEntityRefToCaseHandler := connect.NewUnaryHandler(
		CaseServiceAddEntityRefToCaseProcedure,
		svc.AddEntityRefToCase,
		connect.WithSchema(caseServiceMethods.ByName("AddEntityRefToCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemoveEntityRefFromCaseHandler := connect.NewUnaryHandler(
		CaseServiceRemoveEntityRefFromCaseProcedure,
		svc.RemoveEntityRefFromCase,
		connect.WithSchema(caseServiceMethods.ByName("RemoveEntityRefFromCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddPropertyRefToCaseHandler := connect.NewUnaryHandler(
		CaseServiceAddPropertyRefToCaseProcedure,
		svc.AddPropertyRefToCase,
		connect.WithSchema(caseServiceMethods.ByName("AddPropertyRefToCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemovePropertyRefFromCaseHandler := connect.NewUnaryHandler(
		CaseServiceRemovePropertyRefFromCaseProcedure,
		svc.RemovePropertyRefFromCase,
		connect.WithSchema(caseServiceMethods.ByName("RemovePropertyRefFromCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceLinkRelatedCaseHandler := connect.NewUnaryHandler(
		CaseServiceLinkRelatedCaseProcedure,
		svc.LinkRelatedCase,
		connect.WithSchema(caseServiceMethods.ByName("LinkRelatedCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceUnlinkRelatedCaseHandler := connect.NewUnaryHandler(
		CaseServiceUnlinkRelatedCaseProcedure,
		svc.UnlinkRelatedCase,
		connect.WithSchema(caseServiceMethods.ByName("UnlinkRelatedCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAssignCaseHandler := connect.NewUnaryHandler(
		CaseServiceAssignCaseProcedure,
		svc.AssignCase,
		connect.WithSchema(caseServiceMethods.ByName("AssignCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAssociateAssetToCaseHandler := connect.NewUnaryHandler(
		CaseServiceAssociateAssetToCaseProcedure,
		svc.AssociateAssetToCase,
		connect.WithSchema(caseServiceMethods.ByName("AssociateAssetToCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceUpdateAssetAssociationHandler := connect.NewUnaryHandler(
		CaseServiceUpdateAssetAssociationProcedure,
		svc.UpdateAssetAssociation,
		connect.WithSchema(caseServiceMethods.ByName("UpdateAssetAssociation")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceDisassociateAssetFromCaseHandler := connect.NewUnaryHandler(
		CaseServiceDisassociateAssetFromCaseProcedure,
		svc.DisassociateAssetFromCase,
		connect.WithSchema(caseServiceMethods.ByName("DisassociateAssetFromCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListAssetAssociationsForCaseHandler := connect.NewUnaryHandler(
		CaseServiceListAssetAssociationsForCaseProcedure,
		svc.ListAssetAssociationsForCase,
		connect.WithSchema(caseServiceMethods.ByName("ListAssetAssociationsForCase")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddWatcherHandler := connect.NewUnaryHandler(
		CaseServiceAddWatcherProcedure,
		svc.AddWatcher,
		connect.WithSchema(caseServiceMethods.ByName("AddWatcher")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemoveWatcherHandler := connect.NewUnaryHandler(
		CaseServiceRemoveWatcherProcedure,
		svc.RemoveWatcher,
		connect.WithSchema(caseServiceMethods.ByName("RemoveWatcher")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceUpdateCaseStatusHandler := connect.NewUnaryHandler(
		CaseServiceUpdateCaseStatusProcedure,
		svc.UpdateCaseStatus,
		connect.WithSchema(caseServiceMethods.ByName("UpdateCaseStatus")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddCaseUpdateHandler := connect.NewUnaryHandler(
		CaseServiceAddCaseUpdateProcedure,
		svc.AddCaseUpdate,
		connect.WithSchema(caseServiceMethods.ByName("AddCaseUpdate")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemoveCaseUpdateHandler := connect.NewUnaryHandler(
		CaseServiceRemoveCaseUpdateProcedure,
		svc.RemoveCaseUpdate,
		connect.WithSchema(caseServiceMethods.ByName("RemoveCaseUpdate")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCaseUpdatesHandler := connect.NewUnaryHandler(
		CaseServiceListCaseUpdatesProcedure,
		svc.ListCaseUpdates,
		connect.WithSchema(caseServiceMethods.ByName("ListCaseUpdates")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCaseFileAttachmentsHandler := connect.NewUnaryHandler(
		CaseServiceListCaseFileAttachmentsProcedure,
		svc.ListCaseFileAttachments,
		connect.WithSchema(caseServiceMethods.ByName("ListCaseFileAttachments")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCaseStatusHistoryHandler := connect.NewUnaryHandler(
		CaseServiceListCaseStatusHistoryProcedure,
		svc.ListCaseStatusHistory,
		connect.WithSchema(caseServiceMethods.ByName("ListCaseStatusHistory")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddCaseTagHandler := connect.NewUnaryHandler(
		CaseServiceAddCaseTagProcedure,
		svc.AddCaseTag,
		connect.WithSchema(caseServiceMethods.ByName("AddCaseTag")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceRemoveCaseTagHandler := connect.NewUnaryHandler(
		CaseServiceRemoveCaseTagProcedure,
		svc.RemoveCaseTag,
		connect.WithSchema(caseServiceMethods.ByName("RemoveCaseTag")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceAddAdditionalInfoHandler := connect.NewUnaryHandler(
		CaseServiceAddAdditionalInfoProcedure,
		svc.AddAdditionalInfo,
		connect.WithSchema(caseServiceMethods.ByName("AddAdditionalInfo")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceGetCaseVersionHandler := connect.NewUnaryHandler(
		CaseServiceGetCaseVersionProcedure,
		svc.GetCaseVersion,
		connect.WithSchema(caseServiceMethods.ByName("GetCaseVersion")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCaseVersionsHandler := connect.NewUnaryHandler(
		CaseServiceListCaseVersionsProcedure,
		svc.ListCaseVersions,
		connect.WithSchema(caseServiceMethods.ByName("ListCaseVersions")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceListCaseAuditLogHandler := connect.NewUnaryHandler(
		CaseServiceListCaseAuditLogProcedure,
		svc.ListCaseAuditLog,
		connect.WithSchema(caseServiceMethods.ByName("ListCaseAuditLog")),
		connect.WithHandlerOptions(opts...),
	)
	caseServiceSearchCasesHandler := connect.NewUnaryHandler(
		CaseServiceSearchCasesProcedure,
		svc.SearchCases,
		connect.WithSchema(caseServiceMethods.ByName("SearchCases")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.cases.v1.CaseService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CaseServiceCreateCaseProcedure:
			caseServiceCreateCaseHandler.ServeHTTP(w, r)
		case CaseServiceGetCaseProcedure:
			caseServiceGetCaseHandler.ServeHTTP(w, r)
		case CaseServiceUpdateCaseProcedure:
			caseServiceUpdateCaseHandler.ServeHTTP(w, r)
		case CaseServiceDeleteCaseProcedure:
			caseServiceDeleteCaseHandler.ServeHTTP(w, r)
		case CaseServiceListCasesProcedure:
			caseServiceListCasesHandler.ServeHTTP(w, r)
		case CaseServiceBatchGetCasesProcedure:
			caseServiceBatchGetCasesHandler.ServeHTTP(w, r)
		case CaseServiceListCasesBySituationIdProcedure:
			caseServiceListCasesBySituationIdHandler.ServeHTTP(w, r)
		case CaseServiceListCasesByReportIdProcedure:
			caseServiceListCasesByReportIdHandler.ServeHTTP(w, r)
		case CaseServiceListCasesByAssetIdProcedure:
			caseServiceListCasesByAssetIdHandler.ServeHTTP(w, r)
		case CaseServiceListCasesByEntityIdProcedure:
			caseServiceListCasesByEntityIdHandler.ServeHTTP(w, r)
		case CaseServiceListCasesByPropertyIdProcedure:
			caseServiceListCasesByPropertyIdHandler.ServeHTTP(w, r)
		case CaseServiceAddSituationToCaseProcedure:
			caseServiceAddSituationToCaseHandler.ServeHTTP(w, r)
		case CaseServiceRemoveSituationFromCaseProcedure:
			caseServiceRemoveSituationFromCaseHandler.ServeHTTP(w, r)
		case CaseServiceAddReportToCaseProcedure:
			caseServiceAddReportToCaseHandler.ServeHTTP(w, r)
		case CaseServiceRemoveReportFromCaseProcedure:
			caseServiceRemoveReportFromCaseHandler.ServeHTTP(w, r)
		case CaseServiceAddEntityRefToCaseProcedure:
			caseServiceAddEntityRefToCaseHandler.ServeHTTP(w, r)
		case CaseServiceRemoveEntityRefFromCaseProcedure:
			caseServiceRemoveEntityRefFromCaseHandler.ServeHTTP(w, r)
		case CaseServiceAddPropertyRefToCaseProcedure:
			caseServiceAddPropertyRefToCaseHandler.ServeHTTP(w, r)
		case CaseServiceRemovePropertyRefFromCaseProcedure:
			caseServiceRemovePropertyRefFromCaseHandler.ServeHTTP(w, r)
		case CaseServiceLinkRelatedCaseProcedure:
			caseServiceLinkRelatedCaseHandler.ServeHTTP(w, r)
		case CaseServiceUnlinkRelatedCaseProcedure:
			caseServiceUnlinkRelatedCaseHandler.ServeHTTP(w, r)
		case CaseServiceAssignCaseProcedure:
			caseServiceAssignCaseHandler.ServeHTTP(w, r)
		case CaseServiceAssociateAssetToCaseProcedure:
			caseServiceAssociateAssetToCaseHandler.ServeHTTP(w, r)
		case CaseServiceUpdateAssetAssociationProcedure:
			caseServiceUpdateAssetAssociationHandler.ServeHTTP(w, r)
		case CaseServiceDisassociateAssetFromCaseProcedure:
			caseServiceDisassociateAssetFromCaseHandler.ServeHTTP(w, r)
		case CaseServiceListAssetAssociationsForCaseProcedure:
			caseServiceListAssetAssociationsForCaseHandler.ServeHTTP(w, r)
		case CaseServiceAddWatcherProcedure:
			caseServiceAddWatcherHandler.ServeHTTP(w, r)
		case CaseServiceRemoveWatcherProcedure:
			caseServiceRemoveWatcherHandler.ServeHTTP(w, r)
		case CaseServiceUpdateCaseStatusProcedure:
			caseServiceUpdateCaseStatusHandler.ServeHTTP(w, r)
		case CaseServiceAddCaseUpdateProcedure:
			caseServiceAddCaseUpdateHandler.ServeHTTP(w, r)
		case CaseServiceRemoveCaseUpdateProcedure:
			caseServiceRemoveCaseUpdateHandler.ServeHTTP(w, r)
		case CaseServiceListCaseUpdatesProcedure:
			caseServiceListCaseUpdatesHandler.ServeHTTP(w, r)
		case CaseServiceListCaseFileAttachmentsProcedure:
			caseServiceListCaseFileAttachmentsHandler.ServeHTTP(w, r)
		case CaseServiceListCaseStatusHistoryProcedure:
			caseServiceListCaseStatusHistoryHandler.ServeHTTP(w, r)
		case CaseServiceAddCaseTagProcedure:
			caseServiceAddCaseTagHandler.ServeHTTP(w, r)
		case CaseServiceRemoveCaseTagProcedure:
			caseServiceRemoveCaseTagHandler.ServeHTTP(w, r)
		case CaseServiceAddAdditionalInfoProcedure:
			caseServiceAddAdditionalInfoHandler.ServeHTTP(w, r)
		case CaseServiceGetCaseVersionProcedure:
			caseServiceGetCaseVersionHandler.ServeHTTP(w, r)
		case CaseServiceListCaseVersionsProcedure:
			caseServiceListCaseVersionsHandler.ServeHTTP(w, r)
		case CaseServiceListCaseAuditLogProcedure:
			caseServiceListCaseAuditLogHandler.ServeHTTP(w, r)
		case CaseServiceSearchCasesProcedure:
			caseServiceSearchCasesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCaseServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCaseServiceHandler struct{}

func (UnimplementedCaseServiceHandler) CreateCase(context.Context, *connect.Request[v1.CreateCaseRequest]) (*connect.Response[v1.CreateCaseResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.CreateCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) GetCase(context.Context, *connect.Request[v1.GetCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.GetCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) UpdateCase(context.Context, *connect.Request[v1.UpdateCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.UpdateCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) DeleteCase(context.Context, *connect.Request[v1.DeleteCaseRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.DeleteCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCases(context.Context, *connect.Request[v1.ListCasesRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCases is not implemented"))
}

func (UnimplementedCaseServiceHandler) BatchGetCases(context.Context, *connect.Request[v1.BatchGetCasesRequest]) (*connect.Response[v1.BatchGetCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.BatchGetCases is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCasesBySituationId(context.Context, *connect.Request[v1.ListCasesBySituationIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCasesBySituationId is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCasesByReportId(context.Context, *connect.Request[v1.ListCasesByReportIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCasesByReportId is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCasesByAssetId(context.Context, *connect.Request[v1.ListCasesByAssetIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCasesByAssetId is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCasesByEntityId(context.Context, *connect.Request[v1.ListCasesByEntityIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCasesByEntityId is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCasesByPropertyId(context.Context, *connect.Request[v1.ListCasesByPropertyIdRequest]) (*connect.Response[v1.ListCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCasesByPropertyId is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddSituationToCase(context.Context, *connect.Request[v1.AddSituationToCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddSituationToCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemoveSituationFromCase(context.Context, *connect.Request[v1.RemoveSituationFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemoveSituationFromCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddReportToCase(context.Context, *connect.Request[v1.AddReportToCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddReportToCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemoveReportFromCase(context.Context, *connect.Request[v1.RemoveReportFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemoveReportFromCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddEntityRefToCase(context.Context, *connect.Request[v1.AddEntityRefToCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddEntityRefToCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemoveEntityRefFromCase(context.Context, *connect.Request[v1.RemoveEntityRefFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemoveEntityRefFromCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddPropertyRefToCase(context.Context, *connect.Request[v1.AddPropertyRefToCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddPropertyRefToCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemovePropertyRefFromCase(context.Context, *connect.Request[v1.RemovePropertyRefFromCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemovePropertyRefFromCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) LinkRelatedCase(context.Context, *connect.Request[v1.LinkRelatedCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.LinkRelatedCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) UnlinkRelatedCase(context.Context, *connect.Request[v1.UnlinkRelatedCaseRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.UnlinkRelatedCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) AssignCase(context.Context, *connect.Request[v1.AssignCaseRequest]) (*connect.Response[v1.AssignCaseResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AssignCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) AssociateAssetToCase(context.Context, *connect.Request[v1.AssociateAssetToCaseRequest]) (*connect.Response[v1.AssociateAssetToCaseResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AssociateAssetToCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) UpdateAssetAssociation(context.Context, *connect.Request[v1.UpdateAssetAssociationRequest]) (*connect.Response[v1.UpdateAssetAssociationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.UpdateAssetAssociation is not implemented"))
}

func (UnimplementedCaseServiceHandler) DisassociateAssetFromCase(context.Context, *connect.Request[v1.DisassociateAssetFromCaseRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.DisassociateAssetFromCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListAssetAssociationsForCase(context.Context, *connect.Request[v1.ListAssetAssociationsForCaseRequest]) (*connect.Response[v1.ListAssetAssociationsForCaseResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListAssetAssociationsForCase is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddWatcher(context.Context, *connect.Request[v1.AddWatcherRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddWatcher is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemoveWatcher(context.Context, *connect.Request[v1.RemoveWatcherRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemoveWatcher is not implemented"))
}

func (UnimplementedCaseServiceHandler) UpdateCaseStatus(context.Context, *connect.Request[v1.UpdateCaseStatusRequest]) (*connect.Response[v1.UpdateCaseStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.UpdateCaseStatus is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddCaseUpdate(context.Context, *connect.Request[v1.AddCaseUpdateRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddCaseUpdate is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemoveCaseUpdate(context.Context, *connect.Request[v1.RemoveCaseUpdateRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemoveCaseUpdate is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCaseUpdates(context.Context, *connect.Request[v1.ListCaseUpdatesRequest]) (*connect.Response[v1.ListCaseUpdatesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCaseUpdates is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCaseFileAttachments(context.Context, *connect.Request[v1.ListCaseFileAttachmentsRequest]) (*connect.Response[v1.ListCaseFileAttachmentsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCaseFileAttachments is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCaseStatusHistory(context.Context, *connect.Request[v1.ListCaseStatusHistoryRequest]) (*connect.Response[v1.ListCaseStatusHistoryResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCaseStatusHistory is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddCaseTag(context.Context, *connect.Request[v1.AddCaseTagRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddCaseTag is not implemented"))
}

func (UnimplementedCaseServiceHandler) RemoveCaseTag(context.Context, *connect.Request[v1.RemoveCaseTagRequest]) (*connect.Response[v1.Case], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.RemoveCaseTag is not implemented"))
}

func (UnimplementedCaseServiceHandler) AddAdditionalInfo(context.Context, *connect.Request[v1.AddAdditionalInfoRequest]) (*connect.Response[v1.AddAdditionalInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.AddAdditionalInfo is not implemented"))
}

func (UnimplementedCaseServiceHandler) GetCaseVersion(context.Context, *connect.Request[v1.GetCaseVersionRequest]) (*connect.Response[v1.CaseSnapshot], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.GetCaseVersion is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCaseVersions(context.Context, *connect.Request[v1.ListCaseVersionsRequest]) (*connect.Response[v1.ListCaseVersionsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCaseVersions is not implemented"))
}

func (UnimplementedCaseServiceHandler) ListCaseAuditLog(context.Context, *connect.Request[v1.ListCaseAuditLogRequest]) (*connect.Response[v1.ListCaseAuditLogResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.ListCaseAuditLog is not implemented"))
}

func (UnimplementedCaseServiceHandler) SearchCases(context.Context, *connect.Request[v1.SearchCasesRequest]) (*connect.Response[v1.SearchCasesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.cases.v1.CaseService.SearchCases is not implemented"))
}
