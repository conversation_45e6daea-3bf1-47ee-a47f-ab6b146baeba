// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/communications/v1/conversation.proto

package conversation

import (
	v21 "proto/hero/assets/v2"
	v2 "proto/hero/situations/v2"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CallEventType defines the types of events in a call's lifecycle
type CallEventType int32

const (
	CallEventType_CALL_EVENT_TYPE_UNSPECIFIED CallEventType = 0
	CallEventType_CALL_EVENT_TYPE_QUEUED      CallEventType = 1
	CallEventType_CALL_EVENT_TYPE_DEQUEUED    CallEventType = 2
	CallEventType_CALL_EVENT_TYPE_HELD        CallEventType = 3
	CallEventType_CALL_EVENT_TYPE_RESUMED     CallEventType = 4
	CallEventType_CALL_EVENT_TYPE_ENDED       CallEventType = 5
)

// Enum value maps for CallEventType.
var (
	CallEventType_name = map[int32]string{
		0: "CALL_EVENT_TYPE_UNSPECIFIED",
		1: "CALL_EVENT_TYPE_QUEUED",
		2: "CALL_EVENT_TYPE_DEQUEUED",
		3: "CALL_EVENT_TYPE_HELD",
		4: "CALL_EVENT_TYPE_RESUMED",
		5: "CALL_EVENT_TYPE_ENDED",
	}
	CallEventType_value = map[string]int32{
		"CALL_EVENT_TYPE_UNSPECIFIED": 0,
		"CALL_EVENT_TYPE_QUEUED":      1,
		"CALL_EVENT_TYPE_DEQUEUED":    2,
		"CALL_EVENT_TYPE_HELD":        3,
		"CALL_EVENT_TYPE_RESUMED":     4,
		"CALL_EVENT_TYPE_ENDED":       5,
	}
)

func (x CallEventType) Enum() *CallEventType {
	p := new(CallEventType)
	*p = x
	return p
}

func (x CallEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_communications_v1_conversation_proto_enumTypes[0].Descriptor()
}

func (CallEventType) Type() protoreflect.EnumType {
	return &file_hero_communications_v1_conversation_proto_enumTypes[0]
}

func (x CallEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallEventType.Descriptor instead.
func (CallEventType) EnumDescriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{0}
}

type GetVideoCallAccessTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the user initiating the audio/video call
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The unique identifier of the channel where the communication will take place
	Channel string `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	// Time in seconds for token validity (default: 3600)
	Expire        int32 `protobuf:"varint,3,opt,name=expire,proto3" json:"expire,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVideoCallAccessTokenRequest) Reset() {
	*x = GetVideoCallAccessTokenRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVideoCallAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoCallAccessTokenRequest) ProtoMessage() {}

func (x *GetVideoCallAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoCallAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*GetVideoCallAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{0}
}

func (x *GetVideoCallAccessTokenRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetVideoCallAccessTokenRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *GetVideoCallAccessTokenRequest) GetExpire() int32 {
	if x != nil {
		return x.Expire
	}
	return 0
}

type GetVideoCallAccessTokenResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The Agora Access Token for React Native Video SDK
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// The Agora APP ID
	AppId string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// Call channel
	Channel       string `protobuf:"bytes,3,opt,name=channel,proto3" json:"channel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVideoCallAccessTokenResponse) Reset() {
	*x = GetVideoCallAccessTokenResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVideoCallAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVideoCallAccessTokenResponse) ProtoMessage() {}

func (x *GetVideoCallAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVideoCallAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*GetVideoCallAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{1}
}

func (x *GetVideoCallAccessTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GetVideoCallAccessTokenResponse) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetVideoCallAccessTokenResponse) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

// -----------------------------------------------------
// ChatService
// -----------------------------------------------------
type GetChatUserTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the user
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Expiration time in seconds
	Expire        uint32 `protobuf:"varint,2,opt,name=expire,proto3" json:"expire,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatUserTokenRequest) Reset() {
	*x = GetChatUserTokenRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatUserTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatUserTokenRequest) ProtoMessage() {}

func (x *GetChatUserTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatUserTokenRequest.ProtoReflect.Descriptor instead.
func (*GetChatUserTokenRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{2}
}

func (x *GetChatUserTokenRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetChatUserTokenRequest) GetExpire() uint32 {
	if x != nil {
		return x.Expire
	}
	return 0
}

type GetChatUserTokenResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The generated token for the user
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	AppKey        string `protobuf:"bytes,2,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatUserTokenResponse) Reset() {
	*x = GetChatUserTokenResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatUserTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatUserTokenResponse) ProtoMessage() {}

func (x *GetChatUserTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatUserTokenResponse.ProtoReflect.Descriptor instead.
func (*GetChatUserTokenResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{3}
}

func (x *GetChatUserTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetChatUserTokenResponse) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

type GetChatAppTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Expiration time in seconds
	Expire        uint32 `protobuf:"varint,1,opt,name=expire,proto3" json:"expire,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatAppTokenRequest) Reset() {
	*x = GetChatAppTokenRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatAppTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatAppTokenRequest) ProtoMessage() {}

func (x *GetChatAppTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatAppTokenRequest.ProtoReflect.Descriptor instead.
func (*GetChatAppTokenRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{4}
}

func (x *GetChatAppTokenRequest) GetExpire() uint32 {
	if x != nil {
		return x.Expire
	}
	return 0
}

type GetChatAppTokenResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The generated token for the application
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatAppTokenResponse) Reset() {
	*x = GetChatAppTokenResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatAppTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatAppTokenResponse) ProtoMessage() {}

func (x *GetChatAppTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatAppTokenResponse.ProtoReflect.Descriptor instead.
func (*GetChatAppTokenResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{5}
}

func (x *GetChatAppTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Request message for creating a new group chat
type CreateGroupChatRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the user creating the group
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The desired name for the new group
	GroupName     string `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupChatRequest) Reset() {
	*x = CreateGroupChatRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupChatRequest) ProtoMessage() {}

func (x *CreateGroupChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupChatRequest.ProtoReflect.Descriptor instead.
func (*CreateGroupChatRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{6}
}

func (x *CreateGroupChatRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateGroupChatRequest) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

// Response message for creating a new group chat
type CreateGroupChatResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the created group
	GroupId       string `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupChatResponse) Reset() {
	*x = CreateGroupChatResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupChatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupChatResponse) ProtoMessage() {}

func (x *CreateGroupChatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupChatResponse.ProtoReflect.Descriptor instead.
func (*CreateGroupChatResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{7}
}

func (x *CreateGroupChatResponse) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

// Request message to retrieve GroupChatId
type GetGroupChatIdRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The desired name of the group for which the ID is requested
	GroupName     string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupChatIdRequest) Reset() {
	*x = GetGroupChatIdRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupChatIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupChatIdRequest) ProtoMessage() {}

func (x *GetGroupChatIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupChatIdRequest.ProtoReflect.Descriptor instead.
func (*GetGroupChatIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{8}
}

func (x *GetGroupChatIdRequest) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

// Response message for getting the group chat Id
type GetGroupChatIdResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the group chats retrieved from the DB which share the same name
	GroupIds      []string `protobuf:"bytes,1,rep,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupChatIdResponse) Reset() {
	*x = GetGroupChatIdResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupChatIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupChatIdResponse) ProtoMessage() {}

func (x *GetGroupChatIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupChatIdResponse.ProtoReflect.Descriptor instead.
func (*GetGroupChatIdResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{9}
}

func (x *GetGroupChatIdResponse) GetGroupIds() []string {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

type GetCellularCallAccessTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier for the user or device initiating the call
	Identity string `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
	// Optional expiration time in seconds for the token (backend may provide a default)
	Expire int32 `protobuf:"varint,2,opt,name=expire,proto3" json:"expire,omitempty"`
	// Unique suffix generated by the client for this specific session
	SessionSuffix string `protobuf:"bytes,3,opt,name=session_suffix,json=sessionSuffix,proto3" json:"session_suffix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCellularCallAccessTokenRequest) Reset() {
	*x = GetCellularCallAccessTokenRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCellularCallAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCellularCallAccessTokenRequest) ProtoMessage() {}

func (x *GetCellularCallAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCellularCallAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*GetCellularCallAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{10}
}

func (x *GetCellularCallAccessTokenRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *GetCellularCallAccessTokenRequest) GetExpire() int32 {
	if x != nil {
		return x.Expire
	}
	return 0
}

func (x *GetCellularCallAccessTokenRequest) GetSessionSuffix() string {
	if x != nil {
		return x.SessionSuffix
	}
	return ""
}

type GetCellularCallAccessTokenResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The generated Twilio Access Token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// The identity associated with the token
	Identity      string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCellularCallAccessTokenResponse) Reset() {
	*x = GetCellularCallAccessTokenResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCellularCallAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCellularCallAccessTokenResponse) ProtoMessage() {}

func (x *GetCellularCallAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCellularCallAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*GetCellularCallAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{11}
}

func (x *GetCellularCallAccessTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetCellularCallAccessTokenResponse) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

type HandleCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The caller's phone number
	Caller string `protobuf:"bytes,1,opt,name=caller,proto3" json:"caller,omitempty"`
	// The target phone number or client identifier
	To            string            `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	CallSid       string            `protobuf:"bytes,3,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	Flow          string            `protobuf:"bytes,4,opt,name=flow,proto3" json:"flow,omitempty"`
	Attributes    map[string]string `protobuf:"bytes,5,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleCallRequest) Reset() {
	*x = HandleCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCallRequest) ProtoMessage() {}

func (x *HandleCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCallRequest.ProtoReflect.Descriptor instead.
func (*HandleCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{12}
}

func (x *HandleCallRequest) GetCaller() string {
	if x != nil {
		return x.Caller
	}
	return ""
}

func (x *HandleCallRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *HandleCallRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *HandleCallRequest) GetFlow() string {
	if x != nil {
		return x.Flow
	}
	return ""
}

func (x *HandleCallRequest) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

type HandleCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The generated TwiML response as a string
	Twiml         string `protobuf:"bytes,1,opt,name=twiml,proto3" json:"twiml,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleCallResponse) Reset() {
	*x = HandleCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleCallResponse) ProtoMessage() {}

func (x *HandleCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleCallResponse.ProtoReflect.Descriptor instead.
func (*HandleCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{13}
}

func (x *HandleCallResponse) GetTwiml() string {
	if x != nil {
		return x.Twiml
	}
	return ""
}

// QueuedCall represents a call in the waiting queue or on hold
type QueuedCall struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique Twilio Call SID
	CallSid string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// Caller's phone number
	Caller string `protobuf:"bytes,2,opt,name=caller,proto3" json:"caller,omitempty"`
	// Optional caller name if available
	CallerName string `protobuf:"bytes,3,opt,name=caller_name,json=callerName,proto3" json:"caller_name,omitempty"`
	// When the call was added to the queue
	EnqueueTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=enqueue_time,json=enqueueTime,proto3" json:"enqueue_time,omitempty"`
	// Additional metadata for the call
	Attributes map[string]string `protobuf:"bytes,5,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// History of call events
	History []*CallEvent `protobuf:"bytes,6,rep,name=history,proto3" json:"history,omitempty"`
	// Priority level (higher = more important)
	Priority int32 `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`
	// Asset ID of the resource handling the call
	AssetId string `protobuf:"bytes,8,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Situation ID if associated with a situation
	SituationId string `protobuf:"bytes,9,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	// Notes or description for the call
	Notes string `protobuf:"bytes,10,opt,name=notes,proto3" json:"notes,omitempty"`
	// When the call was connected to an agent and became active
	CallStartTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=call_start_time,json=callStartTime,proto3" json:"call_start_time,omitempty"`
	// When the call was ended
	CallEndTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=call_end_time,json=callEndTime,proto3" json:"call_end_time,omitempty"`
	// When the call was last placed on hold (null if not on hold)
	LastHoldStart *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=last_hold_start,json=lastHoldStart,proto3" json:"last_hold_start,omitempty"`
	// Call direction: "inbound" or "outbound"
	Direction     string `protobuf:"bytes,14,opt,name=direction,proto3" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueuedCall) Reset() {
	*x = QueuedCall{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueuedCall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueuedCall) ProtoMessage() {}

func (x *QueuedCall) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueuedCall.ProtoReflect.Descriptor instead.
func (*QueuedCall) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{14}
}

func (x *QueuedCall) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *QueuedCall) GetCaller() string {
	if x != nil {
		return x.Caller
	}
	return ""
}

func (x *QueuedCall) GetCallerName() string {
	if x != nil {
		return x.CallerName
	}
	return ""
}

func (x *QueuedCall) GetEnqueueTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EnqueueTime
	}
	return nil
}

func (x *QueuedCall) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *QueuedCall) GetHistory() []*CallEvent {
	if x != nil {
		return x.History
	}
	return nil
}

func (x *QueuedCall) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *QueuedCall) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *QueuedCall) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *QueuedCall) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *QueuedCall) GetCallStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CallStartTime
	}
	return nil
}

func (x *QueuedCall) GetCallEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CallEndTime
	}
	return nil
}

func (x *QueuedCall) GetLastHoldStart() *timestamppb.Timestamp {
	if x != nil {
		return x.LastHoldStart
	}
	return nil
}

func (x *QueuedCall) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

type QueueCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The caller's phone number
	Caller string `protobuf:"bytes,1,opt,name=caller,proto3" json:"caller,omitempty"`
	// Optional caller name
	CallerName string `protobuf:"bytes,2,opt,name=caller_name,json=callerName,proto3" json:"caller_name,omitempty"`
	// Optional additional call attributes
	Attributes map[string]string `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Priority level (higher = more important)
	Priority      int32 `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueueCallRequest) Reset() {
	*x = QueueCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueueCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueCallRequest) ProtoMessage() {}

func (x *QueueCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueCallRequest.ProtoReflect.Descriptor instead.
func (*QueueCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{15}
}

func (x *QueueCallRequest) GetCaller() string {
	if x != nil {
		return x.Caller
	}
	return ""
}

func (x *QueueCallRequest) GetCallerName() string {
	if x != nil {
		return x.CallerName
	}
	return ""
}

func (x *QueueCallRequest) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *QueueCallRequest) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type QueueCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Twilio Queue SID
	QueueSid string `protobuf:"bytes,1,opt,name=queue_sid,json=queueSid,proto3" json:"queue_sid,omitempty"`
	// Twilio Call SID
	CallSid string `protobuf:"bytes,2,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// TwiML response for enqueuing
	Twiml string `protobuf:"bytes,3,opt,name=twiml,proto3" json:"twiml,omitempty"`
	// The name of the queue
	QueueName     string `protobuf:"bytes,4,opt,name=queue_name,json=queueName,proto3" json:"queue_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueueCallResponse) Reset() {
	*x = QueueCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueueCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueCallResponse) ProtoMessage() {}

func (x *QueueCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueCallResponse.ProtoReflect.Descriptor instead.
func (*QueueCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{16}
}

func (x *QueueCallResponse) GetQueueSid() string {
	if x != nil {
		return x.QueueSid
	}
	return ""
}

func (x *QueueCallResponse) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *QueueCallResponse) GetTwiml() string {
	if x != nil {
		return x.Twiml
	}
	return ""
}

func (x *QueueCallResponse) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

type DequeueCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the asset (agent) that will handle the call
	AssetId       string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DequeueCallRequest) Reset() {
	*x = DequeueCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DequeueCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DequeueCallRequest) ProtoMessage() {}

func (x *DequeueCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DequeueCallRequest.ProtoReflect.Descriptor instead.
func (*DequeueCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{17}
}

func (x *DequeueCallRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type DequeueCallBySidRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique identifier of the asset (agent) that will handle the call
	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// The unique identifier of the call to dequeue
	CallSid string `protobuf:"bytes,2,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// The session suffix from the client for identity matching
	SessionSuffix string `protobuf:"bytes,3,opt,name=session_suffix,json=sessionSuffix,proto3" json:"session_suffix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DequeueCallBySidRequest) Reset() {
	*x = DequeueCallBySidRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DequeueCallBySidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DequeueCallBySidRequest) ProtoMessage() {}

func (x *DequeueCallBySidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DequeueCallBySidRequest.ProtoReflect.Descriptor instead.
func (*DequeueCallBySidRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{18}
}

func (x *DequeueCallBySidRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *DequeueCallBySidRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *DequeueCallBySidRequest) GetSessionSuffix() string {
	if x != nil {
		return x.SessionSuffix
	}
	return ""
}

type DequeueCallBySidResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the call was successfully claimed and redirected
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// The SID of the call that was claimed
	CallSid string `protobuf:"bytes,2,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// The caller's phone number
	Caller string `protobuf:"bytes,3,opt,name=caller,proto3" json:"caller,omitempty"`
	// The caller's name if available
	CallerName string `protobuf:"bytes,4,opt,name=caller_name,json=callerName,proto3" json:"caller_name,omitempty"`
	// The situation ID if associated with a situation
	SituationId   string `protobuf:"bytes,5,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DequeueCallBySidResponse) Reset() {
	*x = DequeueCallBySidResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DequeueCallBySidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DequeueCallBySidResponse) ProtoMessage() {}

func (x *DequeueCallBySidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DequeueCallBySidResponse.ProtoReflect.Descriptor instead.
func (*DequeueCallBySidResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{19}
}

func (x *DequeueCallBySidResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DequeueCallBySidResponse) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *DequeueCallBySidResponse) GetCaller() string {
	if x != nil {
		return x.Caller
	}
	return ""
}

func (x *DequeueCallBySidResponse) GetCallerName() string {
	if x != nil {
		return x.CallerName
	}
	return ""
}

func (x *DequeueCallBySidResponse) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

type DequeueCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether a call was successfully dequeued
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// Twilio Call SID of the dequeued call
	CallSid string `protobuf:"bytes,2,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// The caller's phone number
	Caller string `protobuf:"bytes,3,opt,name=caller,proto3" json:"caller,omitempty"`
	// Caller name if available
	CallerName string `protobuf:"bytes,4,opt,name=caller_name,json=callerName,proto3" json:"caller_name,omitempty"`
	// Any additional call attributes
	Attributes map[string]string `protobuf:"bytes,5,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Add queue name for twiML response
	QueueName string `protobuf:"bytes,6,opt,name=queue_name,json=queueName,proto3" json:"queue_name,omitempty"`
	// situation ID if associated with a situation
	SituationId string `protobuf:"bytes,7,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	// When the call was connected to an agent and became active
	CallStartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=call_start_time,json=callStartTime,proto3" json:"call_start_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DequeueCallResponse) Reset() {
	*x = DequeueCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DequeueCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DequeueCallResponse) ProtoMessage() {}

func (x *DequeueCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DequeueCallResponse.ProtoReflect.Descriptor instead.
func (*DequeueCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{20}
}

func (x *DequeueCallResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DequeueCallResponse) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *DequeueCallResponse) GetCaller() string {
	if x != nil {
		return x.Caller
	}
	return ""
}

func (x *DequeueCallResponse) GetCallerName() string {
	if x != nil {
		return x.CallerName
	}
	return ""
}

func (x *DequeueCallResponse) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *DequeueCallResponse) GetQueueName() string {
	if x != nil {
		return x.QueueName
	}
	return ""
}

func (x *DequeueCallResponse) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *DequeueCallResponse) GetCallStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CallStartTime
	}
	return nil
}

type GetQueueStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQueueStatusRequest) Reset() {
	*x = GetQueueStatusRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQueueStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueStatusRequest) ProtoMessage() {}

func (x *GetQueueStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueStatusRequest.ProtoReflect.Descriptor instead.
func (*GetQueueStatusRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{21}
}

type GetQueueStatusResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Number of calls waiting in queue
	QueueSize int32 `protobuf:"varint,1,opt,name=queue_size,json=queueSize,proto3" json:"queue_size,omitempty"`
	// Number of calls on hold
	HoldSize int32 `protobuf:"varint,2,opt,name=hold_size,json=holdSize,proto3" json:"hold_size,omitempty"`
	// Info about the next call in queue (if any)
	NextCall *QueuedCall `protobuf:"bytes,3,opt,name=next_call,json=nextCall,proto3" json:"next_call,omitempty"`
	// All calls in the waiting queue
	WaitingCalls []*QueuedCall `protobuf:"bytes,4,rep,name=waiting_calls,json=waitingCalls,proto3" json:"waiting_calls,omitempty"`
	// All calls on hold
	OnHoldCalls   []*QueuedCall `protobuf:"bytes,5,rep,name=on_hold_calls,json=onHoldCalls,proto3" json:"on_hold_calls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQueueStatusResponse) Reset() {
	*x = GetQueueStatusResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQueueStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueStatusResponse) ProtoMessage() {}

func (x *GetQueueStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueStatusResponse.ProtoReflect.Descriptor instead.
func (*GetQueueStatusResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{22}
}

func (x *GetQueueStatusResponse) GetQueueSize() int32 {
	if x != nil {
		return x.QueueSize
	}
	return 0
}

func (x *GetQueueStatusResponse) GetHoldSize() int32 {
	if x != nil {
		return x.HoldSize
	}
	return 0
}

func (x *GetQueueStatusResponse) GetNextCall() *QueuedCall {
	if x != nil {
		return x.NextCall
	}
	return nil
}

func (x *GetQueueStatusResponse) GetWaitingCalls() []*QueuedCall {
	if x != nil {
		return x.WaitingCalls
	}
	return nil
}

func (x *GetQueueStatusResponse) GetOnHoldCalls() []*QueuedCall {
	if x != nil {
		return x.OnHoldCalls
	}
	return nil
}

type HoldCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Call SID to place on hold
	CallSid string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// Asset ID of the agent/dispatcher placing the call on hold
	AssetId string `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Optional reason for hold
	Reason        string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HoldCallRequest) Reset() {
	*x = HoldCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HoldCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HoldCallRequest) ProtoMessage() {}

func (x *HoldCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HoldCallRequest.ProtoReflect.Descriptor instead.
func (*HoldCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{23}
}

func (x *HoldCallRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *HoldCallRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *HoldCallRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type HoldCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the call was successfully placed on hold
	Success       bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HoldCallResponse) Reset() {
	*x = HoldCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HoldCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HoldCallResponse) ProtoMessage() {}

func (x *HoldCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HoldCallResponse.ProtoReflect.Descriptor instead.
func (*HoldCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{24}
}

func (x *HoldCallResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetAssetHeldCallsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Asset ID of the agent/dispatcher
	AssetId       string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetHeldCallsRequest) Reset() {
	*x = GetAssetHeldCallsRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetHeldCallsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetHeldCallsRequest) ProtoMessage() {}

func (x *GetAssetHeldCallsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetHeldCallsRequest.ProtoReflect.Descriptor instead.
func (*GetAssetHeldCallsRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{25}
}

func (x *GetAssetHeldCallsRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type GetAssetHeldCallsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of calls on hold for this asset
	HeldCalls     []*QueuedCall `protobuf:"bytes,1,rep,name=held_calls,json=heldCalls,proto3" json:"held_calls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAssetHeldCallsResponse) Reset() {
	*x = GetAssetHeldCallsResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAssetHeldCallsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetHeldCallsResponse) ProtoMessage() {}

func (x *GetAssetHeldCallsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetHeldCallsResponse.ProtoReflect.Descriptor instead.
func (*GetAssetHeldCallsResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{26}
}

func (x *GetAssetHeldCallsResponse) GetHeldCalls() []*QueuedCall {
	if x != nil {
		return x.HeldCalls
	}
	return nil
}

type ResumeCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Twilio Call SID to resume
	CallSid string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// Asset ID of the agent/dispatcher resuming the call
	AssetId string `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Current session suffix from the client (for cross-session resume support)
	CurrentSessionSuffix string `protobuf:"bytes,3,opt,name=current_session_suffix,json=currentSessionSuffix,proto3" json:"current_session_suffix,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ResumeCallRequest) Reset() {
	*x = ResumeCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResumeCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeCallRequest) ProtoMessage() {}

func (x *ResumeCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeCallRequest.ProtoReflect.Descriptor instead.
func (*ResumeCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{27}
}

func (x *ResumeCallRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *ResumeCallRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ResumeCallRequest) GetCurrentSessionSuffix() string {
	if x != nil {
		return x.CurrentSessionSuffix
	}
	return ""
}

type ResumeCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the call was successfully resumed
	Success       bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResumeCallResponse) Reset() {
	*x = ResumeCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResumeCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeCallResponse) ProtoMessage() {}

func (x *ResumeCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeCallResponse.ProtoReflect.Descriptor instead.
func (*ResumeCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{28}
}

func (x *ResumeCallResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type EndCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Call SID to end
	CallSid string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// Asset ID of the agent/dispatcher ending the call
	AssetId string `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Optional reason for ending call
	Reason        string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndCallRequest) Reset() {
	*x = EndCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndCallRequest) ProtoMessage() {}

func (x *EndCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndCallRequest.ProtoReflect.Descriptor instead.
func (*EndCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{29}
}

func (x *EndCallRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *EndCallRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *EndCallRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type EndCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the call was successfully ended
	Success       bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndCallResponse) Reset() {
	*x = EndCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndCallResponse) ProtoMessage() {}

func (x *EndCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndCallResponse.ProtoReflect.Descriptor instead.
func (*EndCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{30}
}

func (x *EndCallResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// CallEvent represents a single event in a call's history
type CallEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// When the event occurred
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Type of event
	EventType CallEventType `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=hero.conversation.v1.CallEventType" json:"event_type,omitempty"`
	// Asset ID of the agent/dispatcher involved
	AssetId string `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Notes about the event
	Notes         string `protobuf:"bytes,4,opt,name=notes,proto3" json:"notes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallEvent) Reset() {
	*x = CallEvent{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallEvent) ProtoMessage() {}

func (x *CallEvent) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallEvent.ProtoReflect.Descriptor instead.
func (*CallEvent) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{31}
}

func (x *CallEvent) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *CallEvent) GetEventType() CallEventType {
	if x != nil {
		return x.EventType
	}
	return CallEventType_CALL_EVENT_TYPE_UNSPECIFIED
}

func (x *CallEvent) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *CallEvent) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type GetSituationForCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Call SID to get situation for
	CallSid       string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSituationForCallRequest) Reset() {
	*x = GetSituationForCallRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSituationForCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSituationForCallRequest) ProtoMessage() {}

func (x *GetSituationForCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSituationForCallRequest.ProtoReflect.Descriptor instead.
func (*GetSituationForCallRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{32}
}

func (x *GetSituationForCallRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

type GetSituationForCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether situation was found
	Found bool `protobuf:"varint,1,opt,name=found,proto3" json:"found,omitempty"`
	// Situation ID if found
	SituationId string `protobuf:"bytes,2,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	// Full situation object
	Situation     *v2.Situation `protobuf:"bytes,3,opt,name=situation,proto3" json:"situation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSituationForCallResponse) Reset() {
	*x = GetSituationForCallResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSituationForCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSituationForCallResponse) ProtoMessage() {}

func (x *GetSituationForCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSituationForCallResponse.ProtoReflect.Descriptor instead.
func (*GetSituationForCallResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{33}
}

func (x *GetSituationForCallResponse) GetFound() bool {
	if x != nil {
		return x.Found
	}
	return false
}

func (x *GetSituationForCallResponse) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *GetSituationForCallResponse) GetSituation() *v2.Situation {
	if x != nil {
		return x.Situation
	}
	return nil
}

type RevertSelectiveClaimRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The call SID to revert
	CallSid       string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevertSelectiveClaimRequest) Reset() {
	*x = RevertSelectiveClaimRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevertSelectiveClaimRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevertSelectiveClaimRequest) ProtoMessage() {}

func (x *RevertSelectiveClaimRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevertSelectiveClaimRequest.ProtoReflect.Descriptor instead.
func (*RevertSelectiveClaimRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{34}
}

func (x *RevertSelectiveClaimRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

type RevertSelectiveClaimResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the operation was successful
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// Whether the call was actually reverted (false if already in waiting state)
	Reverted      bool `protobuf:"varint,2,opt,name=reverted,proto3" json:"reverted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevertSelectiveClaimResponse) Reset() {
	*x = RevertSelectiveClaimResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevertSelectiveClaimResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevertSelectiveClaimResponse) ProtoMessage() {}

func (x *RevertSelectiveClaimResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevertSelectiveClaimResponse.ProtoReflect.Descriptor instead.
func (*RevertSelectiveClaimResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{35}
}

func (x *RevertSelectiveClaimResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RevertSelectiveClaimResponse) GetReverted() bool {
	if x != nil {
		return x.Reverted
	}
	return false
}

type ListCallsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional start date filter (RFC3339 format)
	StartDate string `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Optional end date filter (RFC3339 format)
	EndDate string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// Page number (1-based)
	Page int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// Number of results per page (default: 50, max: 100)
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Sort order: "asc" or "desc" (default: "desc" - newest first)
	SortOrder string `protobuf:"bytes,5,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// Optional filter by call state
	State string `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	// Optional filter by call direction
	Direction     string `protobuf:"bytes,7,opt,name=direction,proto3" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCallsRequest) Reset() {
	*x = ListCallsRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCallsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCallsRequest) ProtoMessage() {}

func (x *ListCallsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCallsRequest.ProtoReflect.Descriptor instead.
func (*ListCallsRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{36}
}

func (x *ListCallsRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *ListCallsRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *ListCallsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCallsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCallsRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

func (x *ListCallsRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ListCallsRequest) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

type ListCallsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of calls
	Calls []*QueuedCall `protobuf:"bytes,1,rep,name=calls,proto3" json:"calls,omitempty"`
	// Total number of calls matching the filter
	TotalCount int32 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// Next page number if there are more results, null if this is the last page
	NextPage      *int32 `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3,oneof" json:"next_page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCallsResponse) Reset() {
	*x = ListCallsResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCallsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCallsResponse) ProtoMessage() {}

func (x *ListCallsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCallsResponse.ProtoReflect.Descriptor instead.
func (*ListCallsResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{37}
}

func (x *ListCallsResponse) GetCalls() []*QueuedCall {
	if x != nil {
		return x.Calls
	}
	return nil
}

func (x *ListCallsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListCallsResponse) GetNextPage() int32 {
	if x != nil && x.NextPage != nil {
		return *x.NextPage
	}
	return 0
}

// GetHistoryMetadataRequest represents the request parameters for retrieving Zello history metadata
type GetHistoryMetadataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sender        string                 `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"`
	Recipient     string                 `protobuf:"bytes,2,opt,name=recipient,proto3" json:"recipient,omitempty"`
	ViaChannel    string                 `protobuf:"bytes,3,opt,name=via_channel,json=viaChannel,proto3" json:"via_channel,omitempty"`
	IsChannel     *bool                  `protobuf:"varint,4,opt,name=is_channel,json=isChannel,proto3,oneof" json:"is_channel,omitempty"`
	Type          string                 `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Text          string                 `protobuf:"bytes,6,opt,name=text,proto3" json:"text,omitempty"`
	Eid           string                 `protobuf:"bytes,7,opt,name=eid,proto3" json:"eid,omitempty"`
	CallId        int32                  `protobuf:"varint,8,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	StartTs       int64                  `protobuf:"varint,9,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs         int64                  `protobuf:"varint,10,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	StartId       int32                  `protobuf:"varint,11,opt,name=start_id,json=startId,proto3" json:"start_id,omitempty"`
	EndId         int32                  `protobuf:"varint,12,opt,name=end_id,json=endId,proto3" json:"end_id,omitempty"`
	Max           int32                  `protobuf:"varint,13,opt,name=max,proto3" json:"max,omitempty"`
	Start         int32                  `protobuf:"varint,14,opt,name=start,proto3" json:"start,omitempty"`
	Sort          string                 `protobuf:"bytes,15,opt,name=sort,proto3" json:"sort,omitempty"`
	SortOrder     string                 `protobuf:"bytes,16,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHistoryMetadataRequest) Reset() {
	*x = GetHistoryMetadataRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHistoryMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHistoryMetadataRequest) ProtoMessage() {}

func (x *GetHistoryMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHistoryMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetHistoryMetadataRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{38}
}

func (x *GetHistoryMetadataRequest) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetViaChannel() string {
	if x != nil {
		return x.ViaChannel
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetIsChannel() bool {
	if x != nil && x.IsChannel != nil {
		return *x.IsChannel
	}
	return false
}

func (x *GetHistoryMetadataRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetEid() string {
	if x != nil {
		return x.Eid
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetCallId() int32 {
	if x != nil {
		return x.CallId
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetStartTs() int64 {
	if x != nil {
		return x.StartTs
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetEndTs() int64 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetStartId() int32 {
	if x != nil {
		return x.StartId
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetEndId() int32 {
	if x != nil {
		return x.EndId
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *GetHistoryMetadataRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *GetHistoryMetadataRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

// HistoryMessage represents a message in the Zello history
type HistoryMessage struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	Id                      int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                    string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Ts                      int64                  `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	CallId                  int64                  `protobuf:"varint,4,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	CallIdShort             int64                  `protobuf:"varint,5,opt,name=call_id_short,json=callIdShort,proto3" json:"call_id_short,omitempty"`
	Eid                     string                 `protobuf:"bytes,6,opt,name=eid,proto3" json:"eid,omitempty"`
	Sender                  *v21.Asset             `protobuf:"bytes,7,opt,name=sender,proto3" json:"sender,omitempty"`
	Recipient               string                 `protobuf:"bytes,8,opt,name=recipient,proto3" json:"recipient,omitempty"`
	DispatchCallRecipient   string                 `protobuf:"bytes,9,opt,name=dispatch_call_recipient,json=dispatchCallRecipient,proto3" json:"dispatch_call_recipient,omitempty"`
	RecipientType           string                 `protobuf:"bytes,10,opt,name=recipient_type,json=recipientType,proto3" json:"recipient_type,omitempty"`
	MediaKey                string                 `protobuf:"bytes,11,opt,name=media_key,json=mediaKey,proto3" json:"media_key,omitempty"`
	Duration                int64                  `protobuf:"varint,12,opt,name=duration,proto3" json:"duration,omitempty"`
	ChannelUsers            []string               `protobuf:"bytes,13,rep,name=channel_users,json=channelUsers,proto3" json:"channel_users,omitempty"`
	ImageTs                 int64                  `protobuf:"varint,14,opt,name=image_ts,json=imageTs,proto3" json:"image_ts,omitempty"`
	ImageSource             string                 `protobuf:"bytes,15,opt,name=image_source,json=imageSource,proto3" json:"image_source,omitempty"`
	Text                    string                 `protobuf:"bytes,16,opt,name=text,proto3" json:"text,omitempty"`
	Transcription           string                 `protobuf:"bytes,17,opt,name=transcription,proto3" json:"transcription,omitempty"`
	TranscriptionInaccurate bool                   `protobuf:"varint,18,opt,name=transcription_inaccurate,json=transcriptionInaccurate,proto3" json:"transcription_inaccurate,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *HistoryMessage) Reset() {
	*x = HistoryMessage{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistoryMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistoryMessage) ProtoMessage() {}

func (x *HistoryMessage) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistoryMessage.ProtoReflect.Descriptor instead.
func (*HistoryMessage) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{39}
}

func (x *HistoryMessage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HistoryMessage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HistoryMessage) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *HistoryMessage) GetCallId() int64 {
	if x != nil {
		return x.CallId
	}
	return 0
}

func (x *HistoryMessage) GetCallIdShort() int64 {
	if x != nil {
		return x.CallIdShort
	}
	return 0
}

func (x *HistoryMessage) GetEid() string {
	if x != nil {
		return x.Eid
	}
	return ""
}

func (x *HistoryMessage) GetSender() *v21.Asset {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *HistoryMessage) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *HistoryMessage) GetDispatchCallRecipient() string {
	if x != nil {
		return x.DispatchCallRecipient
	}
	return ""
}

func (x *HistoryMessage) GetRecipientType() string {
	if x != nil {
		return x.RecipientType
	}
	return ""
}

func (x *HistoryMessage) GetMediaKey() string {
	if x != nil {
		return x.MediaKey
	}
	return ""
}

func (x *HistoryMessage) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *HistoryMessage) GetChannelUsers() []string {
	if x != nil {
		return x.ChannelUsers
	}
	return nil
}

func (x *HistoryMessage) GetImageTs() int64 {
	if x != nil {
		return x.ImageTs
	}
	return 0
}

func (x *HistoryMessage) GetImageSource() string {
	if x != nil {
		return x.ImageSource
	}
	return ""
}

func (x *HistoryMessage) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *HistoryMessage) GetTranscription() string {
	if x != nil {
		return x.Transcription
	}
	return ""
}

func (x *HistoryMessage) GetTranscriptionInaccurate() bool {
	if x != nil {
		return x.TranscriptionInaccurate
	}
	return false
}

// GetHistoryMetadataResponse represents the response from retrieving Zello history metadata
type GetHistoryMetadataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Total         int64                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Returned      int64                  `protobuf:"varint,4,opt,name=returned,proto3" json:"returned,omitempty"`
	Messages      []*HistoryMessage      `protobuf:"bytes,5,rep,name=messages,proto3" json:"messages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHistoryMetadataResponse) Reset() {
	*x = GetHistoryMetadataResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHistoryMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHistoryMetadataResponse) ProtoMessage() {}

func (x *GetHistoryMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHistoryMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetHistoryMetadataResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{40}
}

func (x *GetHistoryMetadataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetHistoryMetadataResponse) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetHistoryMetadataResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetHistoryMetadataResponse) GetReturned() int64 {
	if x != nil {
		return x.Returned
	}
	return 0
}

func (x *GetHistoryMetadataResponse) GetMessages() []*HistoryMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

// UpdateCallerNameRequest represents a request to update the caller name for a call
type UpdateCallerNameRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The Twilio Call SID to update
	CallSid string `protobuf:"bytes,1,opt,name=call_sid,json=callSid,proto3" json:"call_sid,omitempty"`
	// The caller name to set (can be empty to clear)
	CallerName    string `protobuf:"bytes,2,opt,name=caller_name,json=callerName,proto3" json:"caller_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCallerNameRequest) Reset() {
	*x = UpdateCallerNameRequest{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCallerNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallerNameRequest) ProtoMessage() {}

func (x *UpdateCallerNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallerNameRequest.ProtoReflect.Descriptor instead.
func (*UpdateCallerNameRequest) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{41}
}

func (x *UpdateCallerNameRequest) GetCallSid() string {
	if x != nil {
		return x.CallSid
	}
	return ""
}

func (x *UpdateCallerNameRequest) GetCallerName() string {
	if x != nil {
		return x.CallerName
	}
	return ""
}

// UpdateCallerNameResponse represents the response from updating a caller name
type UpdateCallerNameResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the update was successful
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// Descriptive message about the result
	Message       string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCallerNameResponse) Reset() {
	*x = UpdateCallerNameResponse{}
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCallerNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallerNameResponse) ProtoMessage() {}

func (x *UpdateCallerNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_communications_v1_conversation_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallerNameResponse.ProtoReflect.Descriptor instead.
func (*UpdateCallerNameResponse) Descriptor() ([]byte, []int) {
	return file_hero_communications_v1_conversation_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateCallerNameResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateCallerNameResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_hero_communications_v1_conversation_proto protoreflect.FileDescriptor

var file_hero_communications_v1_conversation_proto_rawDesc = []byte{
	0x0a, 0x29, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x1a, 0x1b, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x23, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x22, 0x75, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x61, 0x6c,
	0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x4a, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x22, 0x49, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x22,
	0x30, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x22, 0x2f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0x50, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x34, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x35, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68,
	0x61, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x22, 0x7e, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75,
	0x66, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22, 0x56, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x22, 0x82, 0x02, 0x0a, 0x11, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c,
	0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x57,
	0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2a, 0x0a, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x77, 0x69, 0x6d, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x77, 0x69,
	0x6d, 0x6c, 0x22, 0xc1, 0x05, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x43, 0x61, 0x6c,
	0x6c, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61,
	0x6c, 0x6c, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x65, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x65, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6e,
	0x6f, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65,
	0x73, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x68, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x48, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfe, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x80, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x71, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x77, 0x69, 0x6d, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x77, 0x69, 0x6d, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x12, 0x44, 0x65,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x17, 0x44,
	0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x53, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x66,
	0x66, 0x69, 0x78, 0x22, 0xab, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43,
	0x61, 0x6c, 0x6c, 0x42, 0x79, 0x53, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0xa3, 0x03, 0x0a, 0x13, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0xa0, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x71, 0x75, 0x65, 0x75, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68,
	0x6f, 0x6c, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x3d, 0x0a, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x63, 0x61, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x08, 0x6e, 0x65,
	0x78, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x45, 0x0a, 0x0d, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52,
	0x0c, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x44, 0x0a,
	0x0d, 0x6f, 0x6e, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x0b, 0x6f, 0x6e, 0x48, 0x6f, 0x6c, 0x64, 0x43, 0x61,
	0x6c, 0x6c, 0x73, 0x22, 0x5f, 0x0a, 0x0f, 0x48, 0x6f, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x22, 0x2c, 0x0a, 0x10, 0x48, 0x6f, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x35, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x65,
	0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x5c, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x68, 0x65, 0x6c, 0x64, 0x5f, 0x63,
	0x61, 0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x09, 0x68, 0x65,
	0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x22, 0x7f, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x75, 0x6d,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22, 0x2e, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x5e, 0x0a, 0x0e, 0x45, 0x6e, 0x64, 0x43,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x2b, 0x0a, 0x0f, 0x45, 0x6e, 0x64, 0x43,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x09, 0x43, 0x61, 0x6c, 0x6c, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x42, 0x0a,
	0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x22, 0x37, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x75, 0x6e,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x09, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73,
	0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x69, 0x74,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x38, 0x0a, 0x1b, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x69, 0x64, 0x22, 0x54, 0x0a, 0x1c, 0x52,
	0x65, 0x76, 0x65, 0x72, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6c,
	0x61, 0x69, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x65,
	0x64, 0x22, 0xd0, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x05, 0x63, 0x61,
	0x6c, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x05, 0x63, 0x61, 0x6c,
	0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x22, 0xb7, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63,
	0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x69, 0x61, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69,
	0x61, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09,
	0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x65, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0xd0, 0x04,
	0x0a, 0x0e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x53, 0x68, 0x6f, 0x72,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x65, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74,
	0x12, 0x36, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x61, 0x6c,
	0x6c, 0x5f, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x52,
	0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x69,
	0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x74, 0x65,
	0x22, 0xbc, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x65, 0x64, 0x12, 0x40, 0x0a,
	0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22,
	0x55, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x6c, 0x6c, 0x53, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6c, 0x6c,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4e, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0xbc, 0x01, 0x0a, 0x0d, 0x43, 0x61, 0x6c, 0x6c, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x41, 0x4c, 0x4c,
	0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x55, 0x45,
	0x55, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x56,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x51, 0x55, 0x45, 0x55, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x45, 0x4c, 0x44, 0x10, 0x03, 0x12, 0x1b, 0x0a,
	0x17, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x55, 0x4d, 0x45, 0x44, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41,
	0x4c, 0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4e,
	0x44, 0x45, 0x44, 0x10, 0x05, 0x32, 0x9b, 0x01, 0x0a, 0x10, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43,
	0x61, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x34, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x61, 0x6c, 0x6c,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x32, 0xcd, 0x03, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x32, 0x80, 0x0c, 0x0a, 0x13, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72,
	0x43, 0x61, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x37, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x65, 0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x6c,
	0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x65,
	0x6c, 0x6c, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a,
	0x0a, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x27, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c,
	0x0a, 0x09, 0x51, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x26, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x0b,
	0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x28, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x71, 0x0a, 0x10, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x42,
	0x79, 0x53, 0x69, 0x64, 0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x53, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x79, 0x53, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x59, 0x0a, 0x08, 0x48, 0x6f, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x25, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x43,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x48, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x73,
	0x12, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x48, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x48, 0x65, 0x6c, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x12,
	0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x56, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x24, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x64, 0x43, 0x61,
	0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6c,
	0x6c, 0x12, 0x30, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x14, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x31,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x74, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x73, 0x12, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x6c, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x87, 0x01, 0x0a, 0x0a, 0x50, 0x54, 0x54, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x2b, 0x5a, 0x29, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x31,
	0x3b, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_communications_v1_conversation_proto_rawDescOnce sync.Once
	file_hero_communications_v1_conversation_proto_rawDescData = file_hero_communications_v1_conversation_proto_rawDesc
)

func file_hero_communications_v1_conversation_proto_rawDescGZIP() []byte {
	file_hero_communications_v1_conversation_proto_rawDescOnce.Do(func() {
		file_hero_communications_v1_conversation_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_communications_v1_conversation_proto_rawDescData)
	})
	return file_hero_communications_v1_conversation_proto_rawDescData
}

var file_hero_communications_v1_conversation_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_hero_communications_v1_conversation_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_hero_communications_v1_conversation_proto_goTypes = []any{
	(CallEventType)(0),                         // 0: hero.conversation.v1.CallEventType
	(*GetVideoCallAccessTokenRequest)(nil),     // 1: hero.conversation.v1.GetVideoCallAccessTokenRequest
	(*GetVideoCallAccessTokenResponse)(nil),    // 2: hero.conversation.v1.GetVideoCallAccessTokenResponse
	(*GetChatUserTokenRequest)(nil),            // 3: hero.conversation.v1.GetChatUserTokenRequest
	(*GetChatUserTokenResponse)(nil),           // 4: hero.conversation.v1.GetChatUserTokenResponse
	(*GetChatAppTokenRequest)(nil),             // 5: hero.conversation.v1.GetChatAppTokenRequest
	(*GetChatAppTokenResponse)(nil),            // 6: hero.conversation.v1.GetChatAppTokenResponse
	(*CreateGroupChatRequest)(nil),             // 7: hero.conversation.v1.CreateGroupChatRequest
	(*CreateGroupChatResponse)(nil),            // 8: hero.conversation.v1.CreateGroupChatResponse
	(*GetGroupChatIdRequest)(nil),              // 9: hero.conversation.v1.GetGroupChatIdRequest
	(*GetGroupChatIdResponse)(nil),             // 10: hero.conversation.v1.GetGroupChatIdResponse
	(*GetCellularCallAccessTokenRequest)(nil),  // 11: hero.conversation.v1.GetCellularCallAccessTokenRequest
	(*GetCellularCallAccessTokenResponse)(nil), // 12: hero.conversation.v1.GetCellularCallAccessTokenResponse
	(*HandleCallRequest)(nil),                  // 13: hero.conversation.v1.HandleCallRequest
	(*HandleCallResponse)(nil),                 // 14: hero.conversation.v1.HandleCallResponse
	(*QueuedCall)(nil),                         // 15: hero.conversation.v1.QueuedCall
	(*QueueCallRequest)(nil),                   // 16: hero.conversation.v1.QueueCallRequest
	(*QueueCallResponse)(nil),                  // 17: hero.conversation.v1.QueueCallResponse
	(*DequeueCallRequest)(nil),                 // 18: hero.conversation.v1.DequeueCallRequest
	(*DequeueCallBySidRequest)(nil),            // 19: hero.conversation.v1.DequeueCallBySidRequest
	(*DequeueCallBySidResponse)(nil),           // 20: hero.conversation.v1.DequeueCallBySidResponse
	(*DequeueCallResponse)(nil),                // 21: hero.conversation.v1.DequeueCallResponse
	(*GetQueueStatusRequest)(nil),              // 22: hero.conversation.v1.GetQueueStatusRequest
	(*GetQueueStatusResponse)(nil),             // 23: hero.conversation.v1.GetQueueStatusResponse
	(*HoldCallRequest)(nil),                    // 24: hero.conversation.v1.HoldCallRequest
	(*HoldCallResponse)(nil),                   // 25: hero.conversation.v1.HoldCallResponse
	(*GetAssetHeldCallsRequest)(nil),           // 26: hero.conversation.v1.GetAssetHeldCallsRequest
	(*GetAssetHeldCallsResponse)(nil),          // 27: hero.conversation.v1.GetAssetHeldCallsResponse
	(*ResumeCallRequest)(nil),                  // 28: hero.conversation.v1.ResumeCallRequest
	(*ResumeCallResponse)(nil),                 // 29: hero.conversation.v1.ResumeCallResponse
	(*EndCallRequest)(nil),                     // 30: hero.conversation.v1.EndCallRequest
	(*EndCallResponse)(nil),                    // 31: hero.conversation.v1.EndCallResponse
	(*CallEvent)(nil),                          // 32: hero.conversation.v1.CallEvent
	(*GetSituationForCallRequest)(nil),         // 33: hero.conversation.v1.GetSituationForCallRequest
	(*GetSituationForCallResponse)(nil),        // 34: hero.conversation.v1.GetSituationForCallResponse
	(*RevertSelectiveClaimRequest)(nil),        // 35: hero.conversation.v1.RevertSelectiveClaimRequest
	(*RevertSelectiveClaimResponse)(nil),       // 36: hero.conversation.v1.RevertSelectiveClaimResponse
	(*ListCallsRequest)(nil),                   // 37: hero.conversation.v1.ListCallsRequest
	(*ListCallsResponse)(nil),                  // 38: hero.conversation.v1.ListCallsResponse
	(*GetHistoryMetadataRequest)(nil),          // 39: hero.conversation.v1.GetHistoryMetadataRequest
	(*HistoryMessage)(nil),                     // 40: hero.conversation.v1.HistoryMessage
	(*GetHistoryMetadataResponse)(nil),         // 41: hero.conversation.v1.GetHistoryMetadataResponse
	(*UpdateCallerNameRequest)(nil),            // 42: hero.conversation.v1.UpdateCallerNameRequest
	(*UpdateCallerNameResponse)(nil),           // 43: hero.conversation.v1.UpdateCallerNameResponse
	nil,                                        // 44: hero.conversation.v1.HandleCallRequest.AttributesEntry
	nil,                                        // 45: hero.conversation.v1.QueuedCall.AttributesEntry
	nil,                                        // 46: hero.conversation.v1.QueueCallRequest.AttributesEntry
	nil,                                        // 47: hero.conversation.v1.DequeueCallResponse.AttributesEntry
	(*timestamppb.Timestamp)(nil),              // 48: google.protobuf.Timestamp
	(*v2.Situation)(nil),                       // 49: hero.situations.v2.Situation
	(*v21.Asset)(nil),                          // 50: hero.assets.v2.Asset
}
var file_hero_communications_v1_conversation_proto_depIdxs = []int32{
	44, // 0: hero.conversation.v1.HandleCallRequest.attributes:type_name -> hero.conversation.v1.HandleCallRequest.AttributesEntry
	48, // 1: hero.conversation.v1.QueuedCall.enqueue_time:type_name -> google.protobuf.Timestamp
	45, // 2: hero.conversation.v1.QueuedCall.attributes:type_name -> hero.conversation.v1.QueuedCall.AttributesEntry
	32, // 3: hero.conversation.v1.QueuedCall.history:type_name -> hero.conversation.v1.CallEvent
	48, // 4: hero.conversation.v1.QueuedCall.call_start_time:type_name -> google.protobuf.Timestamp
	48, // 5: hero.conversation.v1.QueuedCall.call_end_time:type_name -> google.protobuf.Timestamp
	48, // 6: hero.conversation.v1.QueuedCall.last_hold_start:type_name -> google.protobuf.Timestamp
	46, // 7: hero.conversation.v1.QueueCallRequest.attributes:type_name -> hero.conversation.v1.QueueCallRequest.AttributesEntry
	47, // 8: hero.conversation.v1.DequeueCallResponse.attributes:type_name -> hero.conversation.v1.DequeueCallResponse.AttributesEntry
	48, // 9: hero.conversation.v1.DequeueCallResponse.call_start_time:type_name -> google.protobuf.Timestamp
	15, // 10: hero.conversation.v1.GetQueueStatusResponse.next_call:type_name -> hero.conversation.v1.QueuedCall
	15, // 11: hero.conversation.v1.GetQueueStatusResponse.waiting_calls:type_name -> hero.conversation.v1.QueuedCall
	15, // 12: hero.conversation.v1.GetQueueStatusResponse.on_hold_calls:type_name -> hero.conversation.v1.QueuedCall
	15, // 13: hero.conversation.v1.GetAssetHeldCallsResponse.held_calls:type_name -> hero.conversation.v1.QueuedCall
	48, // 14: hero.conversation.v1.CallEvent.timestamp:type_name -> google.protobuf.Timestamp
	0,  // 15: hero.conversation.v1.CallEvent.event_type:type_name -> hero.conversation.v1.CallEventType
	49, // 16: hero.conversation.v1.GetSituationForCallResponse.situation:type_name -> hero.situations.v2.Situation
	15, // 17: hero.conversation.v1.ListCallsResponse.calls:type_name -> hero.conversation.v1.QueuedCall
	50, // 18: hero.conversation.v1.HistoryMessage.sender:type_name -> hero.assets.v2.Asset
	40, // 19: hero.conversation.v1.GetHistoryMetadataResponse.messages:type_name -> hero.conversation.v1.HistoryMessage
	1,  // 20: hero.conversation.v1.VideoCallService.GetVideoCallAccessToken:input_type -> hero.conversation.v1.GetVideoCallAccessTokenRequest
	3,  // 21: hero.conversation.v1.ChatService.GetChatUserToken:input_type -> hero.conversation.v1.GetChatUserTokenRequest
	5,  // 22: hero.conversation.v1.ChatService.GetChatAppToken:input_type -> hero.conversation.v1.GetChatAppTokenRequest
	7,  // 23: hero.conversation.v1.ChatService.CreateGroupChat:input_type -> hero.conversation.v1.CreateGroupChatRequest
	9,  // 24: hero.conversation.v1.ChatService.GetGroupChatId:input_type -> hero.conversation.v1.GetGroupChatIdRequest
	11, // 25: hero.conversation.v1.CellularCallService.GetCellularCallAccessToken:input_type -> hero.conversation.v1.GetCellularCallAccessTokenRequest
	13, // 26: hero.conversation.v1.CellularCallService.HandleCall:input_type -> hero.conversation.v1.HandleCallRequest
	16, // 27: hero.conversation.v1.CellularCallService.QueueCall:input_type -> hero.conversation.v1.QueueCallRequest
	18, // 28: hero.conversation.v1.CellularCallService.DequeueCall:input_type -> hero.conversation.v1.DequeueCallRequest
	19, // 29: hero.conversation.v1.CellularCallService.DequeueCallBySid:input_type -> hero.conversation.v1.DequeueCallBySidRequest
	22, // 30: hero.conversation.v1.CellularCallService.GetQueueStatus:input_type -> hero.conversation.v1.GetQueueStatusRequest
	24, // 31: hero.conversation.v1.CellularCallService.HoldCall:input_type -> hero.conversation.v1.HoldCallRequest
	26, // 32: hero.conversation.v1.CellularCallService.GetAssetHeldCalls:input_type -> hero.conversation.v1.GetAssetHeldCallsRequest
	28, // 33: hero.conversation.v1.CellularCallService.ResumeCall:input_type -> hero.conversation.v1.ResumeCallRequest
	30, // 34: hero.conversation.v1.CellularCallService.EndCall:input_type -> hero.conversation.v1.EndCallRequest
	33, // 35: hero.conversation.v1.CellularCallService.GetSituationForCall:input_type -> hero.conversation.v1.GetSituationForCallRequest
	35, // 36: hero.conversation.v1.CellularCallService.RevertSelectiveClaim:input_type -> hero.conversation.v1.RevertSelectiveClaimRequest
	37, // 37: hero.conversation.v1.CellularCallService.ListCalls:input_type -> hero.conversation.v1.ListCallsRequest
	42, // 38: hero.conversation.v1.CellularCallService.UpdateCallerName:input_type -> hero.conversation.v1.UpdateCallerNameRequest
	39, // 39: hero.conversation.v1.PTTService.GetHistoryMetadata:input_type -> hero.conversation.v1.GetHistoryMetadataRequest
	2,  // 40: hero.conversation.v1.VideoCallService.GetVideoCallAccessToken:output_type -> hero.conversation.v1.GetVideoCallAccessTokenResponse
	4,  // 41: hero.conversation.v1.ChatService.GetChatUserToken:output_type -> hero.conversation.v1.GetChatUserTokenResponse
	6,  // 42: hero.conversation.v1.ChatService.GetChatAppToken:output_type -> hero.conversation.v1.GetChatAppTokenResponse
	8,  // 43: hero.conversation.v1.ChatService.CreateGroupChat:output_type -> hero.conversation.v1.CreateGroupChatResponse
	10, // 44: hero.conversation.v1.ChatService.GetGroupChatId:output_type -> hero.conversation.v1.GetGroupChatIdResponse
	12, // 45: hero.conversation.v1.CellularCallService.GetCellularCallAccessToken:output_type -> hero.conversation.v1.GetCellularCallAccessTokenResponse
	14, // 46: hero.conversation.v1.CellularCallService.HandleCall:output_type -> hero.conversation.v1.HandleCallResponse
	17, // 47: hero.conversation.v1.CellularCallService.QueueCall:output_type -> hero.conversation.v1.QueueCallResponse
	21, // 48: hero.conversation.v1.CellularCallService.DequeueCall:output_type -> hero.conversation.v1.DequeueCallResponse
	20, // 49: hero.conversation.v1.CellularCallService.DequeueCallBySid:output_type -> hero.conversation.v1.DequeueCallBySidResponse
	23, // 50: hero.conversation.v1.CellularCallService.GetQueueStatus:output_type -> hero.conversation.v1.GetQueueStatusResponse
	25, // 51: hero.conversation.v1.CellularCallService.HoldCall:output_type -> hero.conversation.v1.HoldCallResponse
	27, // 52: hero.conversation.v1.CellularCallService.GetAssetHeldCalls:output_type -> hero.conversation.v1.GetAssetHeldCallsResponse
	29, // 53: hero.conversation.v1.CellularCallService.ResumeCall:output_type -> hero.conversation.v1.ResumeCallResponse
	31, // 54: hero.conversation.v1.CellularCallService.EndCall:output_type -> hero.conversation.v1.EndCallResponse
	34, // 55: hero.conversation.v1.CellularCallService.GetSituationForCall:output_type -> hero.conversation.v1.GetSituationForCallResponse
	36, // 56: hero.conversation.v1.CellularCallService.RevertSelectiveClaim:output_type -> hero.conversation.v1.RevertSelectiveClaimResponse
	38, // 57: hero.conversation.v1.CellularCallService.ListCalls:output_type -> hero.conversation.v1.ListCallsResponse
	43, // 58: hero.conversation.v1.CellularCallService.UpdateCallerName:output_type -> hero.conversation.v1.UpdateCallerNameResponse
	41, // 59: hero.conversation.v1.PTTService.GetHistoryMetadata:output_type -> hero.conversation.v1.GetHistoryMetadataResponse
	40, // [40:60] is the sub-list for method output_type
	20, // [20:40] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_hero_communications_v1_conversation_proto_init() }
func file_hero_communications_v1_conversation_proto_init() {
	if File_hero_communications_v1_conversation_proto != nil {
		return
	}
	file_hero_communications_v1_conversation_proto_msgTypes[37].OneofWrappers = []any{}
	file_hero_communications_v1_conversation_proto_msgTypes[38].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_communications_v1_conversation_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   4,
		},
		GoTypes:           file_hero_communications_v1_conversation_proto_goTypes,
		DependencyIndexes: file_hero_communications_v1_conversation_proto_depIdxs,
		EnumInfos:         file_hero_communications_v1_conversation_proto_enumTypes,
		MessageInfos:      file_hero_communications_v1_conversation_proto_msgTypes,
	}.Build()
	File_hero_communications_v1_conversation_proto = out.File
	file_hero_communications_v1_conversation_proto_rawDesc = nil
	file_hero_communications_v1_conversation_proto_goTypes = nil
	file_hero_communications_v1_conversation_proto_depIdxs = nil
}
