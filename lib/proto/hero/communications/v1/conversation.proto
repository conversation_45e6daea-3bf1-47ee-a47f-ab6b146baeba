syntax = "proto3";

package hero.conversation.v1;

option go_package = "proto/hero/communications/v1;conversation";

// Import the Asset and Situations definitions
import "hero/assets/v2/assets.proto";
import "google/protobuf/timestamp.proto";
import "hero/situations/v2/situations.proto";

// -----------------------------------------------------
// VideoCallService
// -----------------------------------------------------
service VideoCallService {
  // Generate an Agora Video Access Token by providing a user ID and channel
  rpc GetVideoCallAccessToken(GetVideoCallAccessTokenRequest) returns (GetVideoCallAccessTokenResponse);
}

message GetVideoCallAccessTokenRequest {
  // The unique identifier of the user initiating the audio/video call
  string user_id = 1;
  // The unique identifier of the channel where the communication will take place
  string channel = 2;
  // Time in seconds for token validity (default: 3600)
  int32 expire = 3;
}

message GetVideoCallAccessTokenResponse {
  // The Agora Access Token for React Native Video SDK
  string access_token = 1;
  // The Agora APP ID 
  string app_id = 2;
  // Call channel 
  string channel = 3;
}

// -----------------------------------------------------
// ChatService
// -----------------------------------------------------
message GetChatUserTokenRequest {
  // The unique identifier of the user
  string user_id = 1; 
  // Expiration time in seconds
  uint32 expire = 2;
}

message GetChatUserTokenResponse {
  // The generated token for the user
  string token = 1;
  string app_key = 2;
}

message GetChatAppTokenRequest {
  // Expiration time in seconds
  uint32 expire = 1;
}

message GetChatAppTokenResponse {
  // The generated token for the application
  string token = 1;
}

// Request message for creating a new group chat
message CreateGroupChatRequest {
  // The unique identifier of the user creating the group
  string user_id = 1;
  // The desired name for the new group
  string group_name = 2;
}

// Response message for creating a new group chat
message CreateGroupChatResponse {
  // The unique identifier of the created group
  string group_id = 1;
}

// Request message to retrieve GroupChatId
message GetGroupChatIdRequest {
  // The desired name of the group for which the ID is requested
  string group_name = 1;
}

// Response message for getting the group chat Id 
message GetGroupChatIdResponse {
  // The unique identifier of the group chats retrieved from the DB which share the same name
  repeated string group_ids = 1;
}

// ChatService defines the RPC methods for generating tokens and managing groups
service ChatService {
  // GetChatUserToken generates a token for a specific user
  rpc GetChatUserToken(GetChatUserTokenRequest) returns (GetChatUserTokenResponse);
  // GetChatAppToken generates a token for the entire application
  rpc GetChatAppToken(GetChatAppTokenRequest) returns (GetChatAppTokenResponse);
  // CreateGroupChat creates a new group chat and returns the group ID if successful
  rpc CreateGroupChat(CreateGroupChatRequest) returns (CreateGroupChatResponse);
  // GetGroupChatId retrieves the group chat ID from the DB by providing a group chat name
  rpc GetGroupChatId(GetGroupChatIdRequest) returns (GetGroupChatIdResponse);
}

// -----------------------------------------------------
// CellularCallService (Twilio)
// -----------------------------------------------------
service CellularCallService {
  // Generate a Twilio Cellular Access Token for voice calls
  rpc GetCellularCallAccessToken(GetCellularCallAccessTokenRequest) returns (GetCellularCallAccessTokenResponse);
  // Handle incoming or outgoing cellular calls and return a TwiML response
  rpc HandleCall(HandleCallRequest) returns (HandleCallResponse);
  
  // === Call Queue Operations ===
  
  // Place a call in the queue with TwiML instructions for waiting
  rpc QueueCall(QueueCallRequest) returns (QueueCallResponse);
  // Connect an asset (agent) to the next call in queue
  rpc DequeueCall(DequeueCallRequest) returns (DequeueCallResponse);
  // Connect an asset (agent) to a specific call by SID
  rpc DequeueCallBySid(DequeueCallBySidRequest) returns (DequeueCallBySidResponse);
  // Get current status of the call queue (size, next caller)
  rpc GetQueueStatus(GetQueueStatusRequest) returns (GetQueueStatusResponse);
  // Place an active call on hold
  rpc HoldCall(HoldCallRequest) returns (HoldCallResponse);
  // Get calls on hold for a specific asset (agent)
  rpc GetAssetHeldCalls(GetAssetHeldCallsRequest) returns (GetAssetHeldCallsResponse);
  // Resume a call from hold
  rpc ResumeCall(ResumeCallRequest) returns (ResumeCallResponse);
  // End an active call
  rpc EndCall(EndCallRequest) returns (EndCallResponse);
  // Get the situation associated with a call
  rpc GetSituationForCall(GetSituationForCallRequest) returns (GetSituationForCallResponse);
  // Revert a call from pending_selective_assignment back to waiting state
  rpc RevertSelectiveClaim(RevertSelectiveClaimRequest) returns (RevertSelectiveClaimResponse);
  // List calls with filtering and pagination
  rpc ListCalls(ListCallsRequest) returns (ListCallsResponse);
  // Update the caller name for a specific call
  rpc UpdateCallerName(UpdateCallerNameRequest) returns (UpdateCallerNameResponse);
}

message GetCellularCallAccessTokenRequest {
  // The unique identifier for the user or device initiating the call
  string identity = 1;
  // Optional expiration time in seconds for the token (backend may provide a default)
  int32 expire = 2;
  // Unique suffix generated by the client for this specific session
  string session_suffix = 3;
}

message GetCellularCallAccessTokenResponse {
  // The generated Twilio Access Token
  string token = 1;
  // The identity associated with the token
  string identity = 2;
}

message HandleCallRequest {
  // The caller's phone number
  string caller = 1;
  // The target phone number or client identifier
  string to = 2;

  string call_sid = 3;

  string flow = 4;

  map<string, string> attributes = 5;
}

message HandleCallResponse {
  // The generated TwiML response as a string
  string twiml = 1;
}

// === Call Queue Message Definitions ===

// QueuedCall represents a call in the waiting queue or on hold
message QueuedCall {
  // Unique Twilio Call SID
  string call_sid = 1;
  // Caller's phone number
  string caller = 2;
  // Optional caller name if available
  string caller_name = 3;
  // When the call was added to the queue
  google.protobuf.Timestamp enqueue_time = 4;
  // Additional metadata for the call
  map<string, string> attributes = 5;
  // History of call events
  repeated CallEvent history = 6;
  // Priority level (higher = more important)
  int32 priority = 7;
  // Asset ID of the resource handling the call
  string asset_id = 8;
  // Situation ID if associated with a situation
  string situation_id = 9;
  // Notes or description for the call
  string notes = 10;
  // When the call was connected to an agent and became active
  google.protobuf.Timestamp call_start_time = 11;
  // When the call was ended
  google.protobuf.Timestamp call_end_time = 12;
  // When the call was last placed on hold (null if not on hold)
  google.protobuf.Timestamp last_hold_start = 13;
  // Call direction: "inbound" or "outbound"
  string direction = 14;
}

message QueueCallRequest {
  // The caller's phone number
  string caller = 1;
  // Optional caller name
  string caller_name = 2;
  // Optional additional call attributes
  map<string, string> attributes = 3;
  // Priority level (higher = more important)
  int32 priority = 4;
}

message QueueCallResponse {
  // Twilio Queue SID
  string queue_sid = 1;
  // Twilio Call SID
  string call_sid = 2;
  // TwiML response for enqueuing
  string twiml = 3;
  // The name of the queue
  string queue_name = 4;
}

message DequeueCallRequest {
  // The unique identifier of the asset (agent) that will handle the call
  string asset_id = 1;
}

message DequeueCallBySidRequest {
  // The unique identifier of the asset (agent) that will handle the call
  string asset_id = 1;
  // The unique identifier of the call to dequeue
  string call_sid = 2;
  // The session suffix from the client for identity matching
  string session_suffix = 3;
}

message DequeueCallBySidResponse {
  // Whether the call was successfully claimed and redirected
  bool success = 1;
  // The SID of the call that was claimed
  string call_sid = 2;
  // The caller's phone number
  string caller = 3;
  // The caller's name if available
  string caller_name = 4;
  // The situation ID if associated with a situation
  string situation_id = 5;
}

message DequeueCallResponse {
  // Whether a call was successfully dequeued
  bool success = 1;
  // Twilio Call SID of the dequeued call
  string call_sid = 2;
  // The caller's phone number
  string caller = 3;
  // Caller name if available
  string caller_name = 4;
  // Any additional call attributes
  map<string, string> attributes = 5;
  // Add queue name for twiML response
  string queue_name = 6;
  // situation ID if associated with a situation
  string situation_id = 7;
  // When the call was connected to an agent and became active
  google.protobuf.Timestamp call_start_time = 8;
}

message GetQueueStatusRequest {
}

message GetQueueStatusResponse {
  // Number of calls waiting in queue
  int32 queue_size = 1;
  // Number of calls on hold
  int32 hold_size = 2;
  // Info about the next call in queue (if any)
  QueuedCall next_call = 3;
  // All calls in the waiting queue
  repeated QueuedCall waiting_calls = 4;
  // All calls on hold
  repeated QueuedCall on_hold_calls = 5;
}

message HoldCallRequest {
  // Call SID to place on hold
  string call_sid = 1;
  // Asset ID of the agent/dispatcher placing the call on hold
  string asset_id = 2;
  // Optional reason for hold
  string reason = 3;
}

message HoldCallResponse {
  // Whether the call was successfully placed on hold
  bool success = 1;
}

message GetAssetHeldCallsRequest {
  // Asset ID of the agent/dispatcher
  string asset_id = 1;
}

message GetAssetHeldCallsResponse {
  // List of calls on hold for this asset
  repeated QueuedCall held_calls = 1;
}

message ResumeCallRequest {
  // Twilio Call SID to resume
  string call_sid = 1;
  // Asset ID of the agent/dispatcher resuming the call
  string asset_id = 2;
  // Current session suffix from the client (for cross-session resume support)
  string current_session_suffix = 3;
}

message ResumeCallResponse {
  // Whether the call was successfully resumed
  bool success = 1;
}

message EndCallRequest {
  // Call SID to end
  string call_sid = 1;
  // Asset ID of the agent/dispatcher ending the call
  string asset_id = 2;
  // Optional reason for ending call
  string reason = 3;
}

message EndCallResponse {
  // Whether the call was successfully ended
  bool success = 1;
}

// === Common Types ===

// CallEvent represents a single event in a call's history
message CallEvent {
  // When the event occurred
  google.protobuf.Timestamp timestamp = 1;
  // Type of event
  CallEventType event_type = 2;
  // Asset ID of the agent/dispatcher involved
  string asset_id = 3;
  // Notes about the event
  string notes = 4;
}

message GetSituationForCallRequest {
  // Call SID to get situation for
  string call_sid = 1;
}

message GetSituationForCallResponse {
  // Whether situation was found
  bool found = 1;
  // Situation ID if found
  string situation_id = 2;
  // Full situation object
  hero.situations.v2.Situation situation = 3;
}

message RevertSelectiveClaimRequest {
  // The call SID to revert
  string call_sid = 1;
}

message RevertSelectiveClaimResponse {
  // Whether the operation was successful
  bool success = 1;
  // Whether the call was actually reverted (false if already in waiting state)
  bool reverted = 2;
}

message ListCallsRequest {
  // Optional start date filter (RFC3339 format)
  string start_date = 1;
  // Optional end date filter (RFC3339 format)
  string end_date = 2;
  // Page number (1-based)
  int32 page = 3;
  // Number of results per page (default: 50, max: 100)
  int32 page_size = 4;
  // Sort order: "asc" or "desc" (default: "desc" - newest first)
  string sort_order = 5;
  // Optional filter by call state
  string state = 6;
  // Optional filter by call direction
  string direction = 7;
}

message ListCallsResponse {
  // List of calls
  repeated QueuedCall calls = 1;
  // Total number of calls matching the filter
  int32 total_count = 2;
  // Next page number if there are more results, null if this is the last page
  optional int32 next_page = 3;
}

// CallEventType defines the types of events in a call's lifecycle
enum CallEventType {
  CALL_EVENT_TYPE_UNSPECIFIED = 0;
  CALL_EVENT_TYPE_QUEUED = 1;
  CALL_EVENT_TYPE_DEQUEUED = 2;
  CALL_EVENT_TYPE_HELD = 3;
  CALL_EVENT_TYPE_RESUMED = 4;
  CALL_EVENT_TYPE_ENDED = 5;
}


service PTTService {
  // Get Zello channel history metadata
  rpc GetHistoryMetadata(GetHistoryMetadataRequest) returns (GetHistoryMetadataResponse) {}
}

// GetHistoryMetadataRequest represents the request parameters for retrieving Zello history metadata
message GetHistoryMetadataRequest {
  string sender = 1;
  string recipient = 2;
  string via_channel = 3;
  optional bool is_channel = 4;
  string type = 5;
  string text = 6;
  string eid = 7;
  int32 call_id = 8;
  int64 start_ts = 9;
  int64 end_ts = 10;
  int32 start_id = 11;
  int32 end_id = 12;
  int32 max = 13;
  int32 start = 14;
  string sort = 15;
  string sort_order = 16;
}

// HistoryMessage represents a message in the Zello history
message HistoryMessage {
  int64 id = 1;
  string type = 2;
  int64 ts = 3;
  int64 call_id = 4;
  int64 call_id_short = 5;
  string eid = 6;
  hero.assets.v2.Asset sender = 7;
  string recipient = 8;
  string dispatch_call_recipient = 9;
  string recipient_type = 10;
  string media_key = 11;
  int64 duration = 12;
  repeated string channel_users = 13;
  int64 image_ts = 14;
  string image_source = 15;
  string text = 16;
  string transcription = 17;
  bool transcription_inaccurate = 18;
}

// GetHistoryMetadataResponse represents the response from retrieving Zello history metadata
message GetHistoryMetadataResponse {
  string status = 1;
  string code = 2;
  int64 total = 3;
  int64 returned = 4;
  repeated HistoryMessage messages = 5;
}

// UpdateCallerNameRequest represents a request to update the caller name for a call
message UpdateCallerNameRequest {
  // The Twilio Call SID to update
  string call_sid = 1;
  // The caller name to set (can be empty to clear)
  string caller_name = 2;
}

// UpdateCallerNameResponse represents the response from updating a caller name
message UpdateCallerNameResponse {
  // Whether the update was successful
  bool success = 1;
  // Descriptive message about the result
  string message = 2;
}
