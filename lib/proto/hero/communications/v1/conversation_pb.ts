// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/communications/v1/conversation.proto (package hero.conversation.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Asset } from "../../assets/v2/assets_pb";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Situation } from "../../situations/v2/situations_pb";
import { file_hero_situations_v2_situations } from "../../situations/v2/situations_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/communications/v1/conversation.proto.
 */
export const file_hero_communications_v1_conversation: GenFile = /*@__PURE__*/
  fileDesc("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", [file_hero_assets_v2_assets, file_google_protobuf_timestamp, file_hero_situations_v2_situations]);

/**
 * @generated from message hero.conversation.v1.GetVideoCallAccessTokenRequest
 */
export type GetVideoCallAccessTokenRequest = Message<"hero.conversation.v1.GetVideoCallAccessTokenRequest"> & {
  /**
   * The unique identifier of the user initiating the audio/video call
   *
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * The unique identifier of the channel where the communication will take place
   *
   * @generated from field: string channel = 2;
   */
  channel: string;

  /**
   * Time in seconds for token validity (default: 3600)
   *
   * @generated from field: int32 expire = 3;
   */
  expire: number;
};

/**
 * Describes the message hero.conversation.v1.GetVideoCallAccessTokenRequest.
 * Use `create(GetVideoCallAccessTokenRequestSchema)` to create a new message.
 */
export const GetVideoCallAccessTokenRequestSchema: GenMessage<GetVideoCallAccessTokenRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 0);

/**
 * @generated from message hero.conversation.v1.GetVideoCallAccessTokenResponse
 */
export type GetVideoCallAccessTokenResponse = Message<"hero.conversation.v1.GetVideoCallAccessTokenResponse"> & {
  /**
   * The Agora Access Token for React Native Video SDK
   *
   * @generated from field: string access_token = 1;
   */
  accessToken: string;

  /**
   * The Agora APP ID 
   *
   * @generated from field: string app_id = 2;
   */
  appId: string;

  /**
   * Call channel 
   *
   * @generated from field: string channel = 3;
   */
  channel: string;
};

/**
 * Describes the message hero.conversation.v1.GetVideoCallAccessTokenResponse.
 * Use `create(GetVideoCallAccessTokenResponseSchema)` to create a new message.
 */
export const GetVideoCallAccessTokenResponseSchema: GenMessage<GetVideoCallAccessTokenResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 1);

/**
 * -----------------------------------------------------
 * ChatService
 * -----------------------------------------------------
 *
 * @generated from message hero.conversation.v1.GetChatUserTokenRequest
 */
export type GetChatUserTokenRequest = Message<"hero.conversation.v1.GetChatUserTokenRequest"> & {
  /**
   * The unique identifier of the user
   *
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * Expiration time in seconds
   *
   * @generated from field: uint32 expire = 2;
   */
  expire: number;
};

/**
 * Describes the message hero.conversation.v1.GetChatUserTokenRequest.
 * Use `create(GetChatUserTokenRequestSchema)` to create a new message.
 */
export const GetChatUserTokenRequestSchema: GenMessage<GetChatUserTokenRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 2);

/**
 * @generated from message hero.conversation.v1.GetChatUserTokenResponse
 */
export type GetChatUserTokenResponse = Message<"hero.conversation.v1.GetChatUserTokenResponse"> & {
  /**
   * The generated token for the user
   *
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: string app_key = 2;
   */
  appKey: string;
};

/**
 * Describes the message hero.conversation.v1.GetChatUserTokenResponse.
 * Use `create(GetChatUserTokenResponseSchema)` to create a new message.
 */
export const GetChatUserTokenResponseSchema: GenMessage<GetChatUserTokenResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 3);

/**
 * @generated from message hero.conversation.v1.GetChatAppTokenRequest
 */
export type GetChatAppTokenRequest = Message<"hero.conversation.v1.GetChatAppTokenRequest"> & {
  /**
   * Expiration time in seconds
   *
   * @generated from field: uint32 expire = 1;
   */
  expire: number;
};

/**
 * Describes the message hero.conversation.v1.GetChatAppTokenRequest.
 * Use `create(GetChatAppTokenRequestSchema)` to create a new message.
 */
export const GetChatAppTokenRequestSchema: GenMessage<GetChatAppTokenRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 4);

/**
 * @generated from message hero.conversation.v1.GetChatAppTokenResponse
 */
export type GetChatAppTokenResponse = Message<"hero.conversation.v1.GetChatAppTokenResponse"> & {
  /**
   * The generated token for the application
   *
   * @generated from field: string token = 1;
   */
  token: string;
};

/**
 * Describes the message hero.conversation.v1.GetChatAppTokenResponse.
 * Use `create(GetChatAppTokenResponseSchema)` to create a new message.
 */
export const GetChatAppTokenResponseSchema: GenMessage<GetChatAppTokenResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 5);

/**
 * Request message for creating a new group chat
 *
 * @generated from message hero.conversation.v1.CreateGroupChatRequest
 */
export type CreateGroupChatRequest = Message<"hero.conversation.v1.CreateGroupChatRequest"> & {
  /**
   * The unique identifier of the user creating the group
   *
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * The desired name for the new group
   *
   * @generated from field: string group_name = 2;
   */
  groupName: string;
};

/**
 * Describes the message hero.conversation.v1.CreateGroupChatRequest.
 * Use `create(CreateGroupChatRequestSchema)` to create a new message.
 */
export const CreateGroupChatRequestSchema: GenMessage<CreateGroupChatRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 6);

/**
 * Response message for creating a new group chat
 *
 * @generated from message hero.conversation.v1.CreateGroupChatResponse
 */
export type CreateGroupChatResponse = Message<"hero.conversation.v1.CreateGroupChatResponse"> & {
  /**
   * The unique identifier of the created group
   *
   * @generated from field: string group_id = 1;
   */
  groupId: string;
};

/**
 * Describes the message hero.conversation.v1.CreateGroupChatResponse.
 * Use `create(CreateGroupChatResponseSchema)` to create a new message.
 */
export const CreateGroupChatResponseSchema: GenMessage<CreateGroupChatResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 7);

/**
 * Request message to retrieve GroupChatId
 *
 * @generated from message hero.conversation.v1.GetGroupChatIdRequest
 */
export type GetGroupChatIdRequest = Message<"hero.conversation.v1.GetGroupChatIdRequest"> & {
  /**
   * The desired name of the group for which the ID is requested
   *
   * @generated from field: string group_name = 1;
   */
  groupName: string;
};

/**
 * Describes the message hero.conversation.v1.GetGroupChatIdRequest.
 * Use `create(GetGroupChatIdRequestSchema)` to create a new message.
 */
export const GetGroupChatIdRequestSchema: GenMessage<GetGroupChatIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 8);

/**
 * Response message for getting the group chat Id 
 *
 * @generated from message hero.conversation.v1.GetGroupChatIdResponse
 */
export type GetGroupChatIdResponse = Message<"hero.conversation.v1.GetGroupChatIdResponse"> & {
  /**
   * The unique identifier of the group chats retrieved from the DB which share the same name
   *
   * @generated from field: repeated string group_ids = 1;
   */
  groupIds: string[];
};

/**
 * Describes the message hero.conversation.v1.GetGroupChatIdResponse.
 * Use `create(GetGroupChatIdResponseSchema)` to create a new message.
 */
export const GetGroupChatIdResponseSchema: GenMessage<GetGroupChatIdResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 9);

/**
 * @generated from message hero.conversation.v1.GetCellularCallAccessTokenRequest
 */
export type GetCellularCallAccessTokenRequest = Message<"hero.conversation.v1.GetCellularCallAccessTokenRequest"> & {
  /**
   * The unique identifier for the user or device initiating the call
   *
   * @generated from field: string identity = 1;
   */
  identity: string;

  /**
   * Optional expiration time in seconds for the token (backend may provide a default)
   *
   * @generated from field: int32 expire = 2;
   */
  expire: number;

  /**
   * Unique suffix generated by the client for this specific session
   *
   * @generated from field: string session_suffix = 3;
   */
  sessionSuffix: string;
};

/**
 * Describes the message hero.conversation.v1.GetCellularCallAccessTokenRequest.
 * Use `create(GetCellularCallAccessTokenRequestSchema)` to create a new message.
 */
export const GetCellularCallAccessTokenRequestSchema: GenMessage<GetCellularCallAccessTokenRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 10);

/**
 * @generated from message hero.conversation.v1.GetCellularCallAccessTokenResponse
 */
export type GetCellularCallAccessTokenResponse = Message<"hero.conversation.v1.GetCellularCallAccessTokenResponse"> & {
  /**
   * The generated Twilio Access Token
   *
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * The identity associated with the token
   *
   * @generated from field: string identity = 2;
   */
  identity: string;
};

/**
 * Describes the message hero.conversation.v1.GetCellularCallAccessTokenResponse.
 * Use `create(GetCellularCallAccessTokenResponseSchema)` to create a new message.
 */
export const GetCellularCallAccessTokenResponseSchema: GenMessage<GetCellularCallAccessTokenResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 11);

/**
 * @generated from message hero.conversation.v1.HandleCallRequest
 */
export type HandleCallRequest = Message<"hero.conversation.v1.HandleCallRequest"> & {
  /**
   * The caller's phone number
   *
   * @generated from field: string caller = 1;
   */
  caller: string;

  /**
   * The target phone number or client identifier
   *
   * @generated from field: string to = 2;
   */
  to: string;

  /**
   * @generated from field: string call_sid = 3;
   */
  callSid: string;

  /**
   * @generated from field: string flow = 4;
   */
  flow: string;

  /**
   * @generated from field: map<string, string> attributes = 5;
   */
  attributes: { [key: string]: string };
};

/**
 * Describes the message hero.conversation.v1.HandleCallRequest.
 * Use `create(HandleCallRequestSchema)` to create a new message.
 */
export const HandleCallRequestSchema: GenMessage<HandleCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 12);

/**
 * @generated from message hero.conversation.v1.HandleCallResponse
 */
export type HandleCallResponse = Message<"hero.conversation.v1.HandleCallResponse"> & {
  /**
   * The generated TwiML response as a string
   *
   * @generated from field: string twiml = 1;
   */
  twiml: string;
};

/**
 * Describes the message hero.conversation.v1.HandleCallResponse.
 * Use `create(HandleCallResponseSchema)` to create a new message.
 */
export const HandleCallResponseSchema: GenMessage<HandleCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 13);

/**
 * QueuedCall represents a call in the waiting queue or on hold
 *
 * @generated from message hero.conversation.v1.QueuedCall
 */
export type QueuedCall = Message<"hero.conversation.v1.QueuedCall"> & {
  /**
   * Unique Twilio Call SID
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;

  /**
   * Caller's phone number
   *
   * @generated from field: string caller = 2;
   */
  caller: string;

  /**
   * Optional caller name if available
   *
   * @generated from field: string caller_name = 3;
   */
  callerName: string;

  /**
   * When the call was added to the queue
   *
   * @generated from field: google.protobuf.Timestamp enqueue_time = 4;
   */
  enqueueTime?: Timestamp;

  /**
   * Additional metadata for the call
   *
   * @generated from field: map<string, string> attributes = 5;
   */
  attributes: { [key: string]: string };

  /**
   * History of call events
   *
   * @generated from field: repeated hero.conversation.v1.CallEvent history = 6;
   */
  history: CallEvent[];

  /**
   * Priority level (higher = more important)
   *
   * @generated from field: int32 priority = 7;
   */
  priority: number;

  /**
   * Asset ID of the resource handling the call
   *
   * @generated from field: string asset_id = 8;
   */
  assetId: string;

  /**
   * Situation ID if associated with a situation
   *
   * @generated from field: string situation_id = 9;
   */
  situationId: string;

  /**
   * Notes or description for the call
   *
   * @generated from field: string notes = 10;
   */
  notes: string;

  /**
   * When the call was connected to an agent and became active
   *
   * @generated from field: google.protobuf.Timestamp call_start_time = 11;
   */
  callStartTime?: Timestamp;

  /**
   * When the call was ended
   *
   * @generated from field: google.protobuf.Timestamp call_end_time = 12;
   */
  callEndTime?: Timestamp;

  /**
   * When the call was last placed on hold (null if not on hold)
   *
   * @generated from field: google.protobuf.Timestamp last_hold_start = 13;
   */
  lastHoldStart?: Timestamp;

  /**
   * Call direction: "inbound" or "outbound"
   *
   * @generated from field: string direction = 14;
   */
  direction: string;
};

/**
 * Describes the message hero.conversation.v1.QueuedCall.
 * Use `create(QueuedCallSchema)` to create a new message.
 */
export const QueuedCallSchema: GenMessage<QueuedCall> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 14);

/**
 * @generated from message hero.conversation.v1.QueueCallRequest
 */
export type QueueCallRequest = Message<"hero.conversation.v1.QueueCallRequest"> & {
  /**
   * The caller's phone number
   *
   * @generated from field: string caller = 1;
   */
  caller: string;

  /**
   * Optional caller name
   *
   * @generated from field: string caller_name = 2;
   */
  callerName: string;

  /**
   * Optional additional call attributes
   *
   * @generated from field: map<string, string> attributes = 3;
   */
  attributes: { [key: string]: string };

  /**
   * Priority level (higher = more important)
   *
   * @generated from field: int32 priority = 4;
   */
  priority: number;
};

/**
 * Describes the message hero.conversation.v1.QueueCallRequest.
 * Use `create(QueueCallRequestSchema)` to create a new message.
 */
export const QueueCallRequestSchema: GenMessage<QueueCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 15);

/**
 * @generated from message hero.conversation.v1.QueueCallResponse
 */
export type QueueCallResponse = Message<"hero.conversation.v1.QueueCallResponse"> & {
  /**
   * Twilio Queue SID
   *
   * @generated from field: string queue_sid = 1;
   */
  queueSid: string;

  /**
   * Twilio Call SID
   *
   * @generated from field: string call_sid = 2;
   */
  callSid: string;

  /**
   * TwiML response for enqueuing
   *
   * @generated from field: string twiml = 3;
   */
  twiml: string;

  /**
   * The name of the queue
   *
   * @generated from field: string queue_name = 4;
   */
  queueName: string;
};

/**
 * Describes the message hero.conversation.v1.QueueCallResponse.
 * Use `create(QueueCallResponseSchema)` to create a new message.
 */
export const QueueCallResponseSchema: GenMessage<QueueCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 16);

/**
 * @generated from message hero.conversation.v1.DequeueCallRequest
 */
export type DequeueCallRequest = Message<"hero.conversation.v1.DequeueCallRequest"> & {
  /**
   * The unique identifier of the asset (agent) that will handle the call
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.conversation.v1.DequeueCallRequest.
 * Use `create(DequeueCallRequestSchema)` to create a new message.
 */
export const DequeueCallRequestSchema: GenMessage<DequeueCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 17);

/**
 * @generated from message hero.conversation.v1.DequeueCallBySidRequest
 */
export type DequeueCallBySidRequest = Message<"hero.conversation.v1.DequeueCallBySidRequest"> & {
  /**
   * The unique identifier of the asset (agent) that will handle the call
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * The unique identifier of the call to dequeue
   *
   * @generated from field: string call_sid = 2;
   */
  callSid: string;

  /**
   * The session suffix from the client for identity matching
   *
   * @generated from field: string session_suffix = 3;
   */
  sessionSuffix: string;
};

/**
 * Describes the message hero.conversation.v1.DequeueCallBySidRequest.
 * Use `create(DequeueCallBySidRequestSchema)` to create a new message.
 */
export const DequeueCallBySidRequestSchema: GenMessage<DequeueCallBySidRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 18);

/**
 * @generated from message hero.conversation.v1.DequeueCallBySidResponse
 */
export type DequeueCallBySidResponse = Message<"hero.conversation.v1.DequeueCallBySidResponse"> & {
  /**
   * Whether the call was successfully claimed and redirected
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * The SID of the call that was claimed
   *
   * @generated from field: string call_sid = 2;
   */
  callSid: string;

  /**
   * The caller's phone number
   *
   * @generated from field: string caller = 3;
   */
  caller: string;

  /**
   * The caller's name if available
   *
   * @generated from field: string caller_name = 4;
   */
  callerName: string;

  /**
   * The situation ID if associated with a situation
   *
   * @generated from field: string situation_id = 5;
   */
  situationId: string;
};

/**
 * Describes the message hero.conversation.v1.DequeueCallBySidResponse.
 * Use `create(DequeueCallBySidResponseSchema)` to create a new message.
 */
export const DequeueCallBySidResponseSchema: GenMessage<DequeueCallBySidResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 19);

/**
 * @generated from message hero.conversation.v1.DequeueCallResponse
 */
export type DequeueCallResponse = Message<"hero.conversation.v1.DequeueCallResponse"> & {
  /**
   * Whether a call was successfully dequeued
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Twilio Call SID of the dequeued call
   *
   * @generated from field: string call_sid = 2;
   */
  callSid: string;

  /**
   * The caller's phone number
   *
   * @generated from field: string caller = 3;
   */
  caller: string;

  /**
   * Caller name if available
   *
   * @generated from field: string caller_name = 4;
   */
  callerName: string;

  /**
   * Any additional call attributes
   *
   * @generated from field: map<string, string> attributes = 5;
   */
  attributes: { [key: string]: string };

  /**
   * Add queue name for twiML response
   *
   * @generated from field: string queue_name = 6;
   */
  queueName: string;

  /**
   * situation ID if associated with a situation
   *
   * @generated from field: string situation_id = 7;
   */
  situationId: string;

  /**
   * When the call was connected to an agent and became active
   *
   * @generated from field: google.protobuf.Timestamp call_start_time = 8;
   */
  callStartTime?: Timestamp;
};

/**
 * Describes the message hero.conversation.v1.DequeueCallResponse.
 * Use `create(DequeueCallResponseSchema)` to create a new message.
 */
export const DequeueCallResponseSchema: GenMessage<DequeueCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 20);

/**
 * @generated from message hero.conversation.v1.GetQueueStatusRequest
 */
export type GetQueueStatusRequest = Message<"hero.conversation.v1.GetQueueStatusRequest"> & {
};

/**
 * Describes the message hero.conversation.v1.GetQueueStatusRequest.
 * Use `create(GetQueueStatusRequestSchema)` to create a new message.
 */
export const GetQueueStatusRequestSchema: GenMessage<GetQueueStatusRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 21);

/**
 * @generated from message hero.conversation.v1.GetQueueStatusResponse
 */
export type GetQueueStatusResponse = Message<"hero.conversation.v1.GetQueueStatusResponse"> & {
  /**
   * Number of calls waiting in queue
   *
   * @generated from field: int32 queue_size = 1;
   */
  queueSize: number;

  /**
   * Number of calls on hold
   *
   * @generated from field: int32 hold_size = 2;
   */
  holdSize: number;

  /**
   * Info about the next call in queue (if any)
   *
   * @generated from field: hero.conversation.v1.QueuedCall next_call = 3;
   */
  nextCall?: QueuedCall;

  /**
   * All calls in the waiting queue
   *
   * @generated from field: repeated hero.conversation.v1.QueuedCall waiting_calls = 4;
   */
  waitingCalls: QueuedCall[];

  /**
   * All calls on hold
   *
   * @generated from field: repeated hero.conversation.v1.QueuedCall on_hold_calls = 5;
   */
  onHoldCalls: QueuedCall[];
};

/**
 * Describes the message hero.conversation.v1.GetQueueStatusResponse.
 * Use `create(GetQueueStatusResponseSchema)` to create a new message.
 */
export const GetQueueStatusResponseSchema: GenMessage<GetQueueStatusResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 22);

/**
 * @generated from message hero.conversation.v1.HoldCallRequest
 */
export type HoldCallRequest = Message<"hero.conversation.v1.HoldCallRequest"> & {
  /**
   * Call SID to place on hold
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;

  /**
   * Asset ID of the agent/dispatcher placing the call on hold
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * Optional reason for hold
   *
   * @generated from field: string reason = 3;
   */
  reason: string;
};

/**
 * Describes the message hero.conversation.v1.HoldCallRequest.
 * Use `create(HoldCallRequestSchema)` to create a new message.
 */
export const HoldCallRequestSchema: GenMessage<HoldCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 23);

/**
 * @generated from message hero.conversation.v1.HoldCallResponse
 */
export type HoldCallResponse = Message<"hero.conversation.v1.HoldCallResponse"> & {
  /**
   * Whether the call was successfully placed on hold
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.conversation.v1.HoldCallResponse.
 * Use `create(HoldCallResponseSchema)` to create a new message.
 */
export const HoldCallResponseSchema: GenMessage<HoldCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 24);

/**
 * @generated from message hero.conversation.v1.GetAssetHeldCallsRequest
 */
export type GetAssetHeldCallsRequest = Message<"hero.conversation.v1.GetAssetHeldCallsRequest"> & {
  /**
   * Asset ID of the agent/dispatcher
   *
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.conversation.v1.GetAssetHeldCallsRequest.
 * Use `create(GetAssetHeldCallsRequestSchema)` to create a new message.
 */
export const GetAssetHeldCallsRequestSchema: GenMessage<GetAssetHeldCallsRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 25);

/**
 * @generated from message hero.conversation.v1.GetAssetHeldCallsResponse
 */
export type GetAssetHeldCallsResponse = Message<"hero.conversation.v1.GetAssetHeldCallsResponse"> & {
  /**
   * List of calls on hold for this asset
   *
   * @generated from field: repeated hero.conversation.v1.QueuedCall held_calls = 1;
   */
  heldCalls: QueuedCall[];
};

/**
 * Describes the message hero.conversation.v1.GetAssetHeldCallsResponse.
 * Use `create(GetAssetHeldCallsResponseSchema)` to create a new message.
 */
export const GetAssetHeldCallsResponseSchema: GenMessage<GetAssetHeldCallsResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 26);

/**
 * @generated from message hero.conversation.v1.ResumeCallRequest
 */
export type ResumeCallRequest = Message<"hero.conversation.v1.ResumeCallRequest"> & {
  /**
   * Twilio Call SID to resume
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;

  /**
   * Asset ID of the agent/dispatcher resuming the call
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * Current session suffix from the client (for cross-session resume support)
   *
   * @generated from field: string current_session_suffix = 3;
   */
  currentSessionSuffix: string;
};

/**
 * Describes the message hero.conversation.v1.ResumeCallRequest.
 * Use `create(ResumeCallRequestSchema)` to create a new message.
 */
export const ResumeCallRequestSchema: GenMessage<ResumeCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 27);

/**
 * @generated from message hero.conversation.v1.ResumeCallResponse
 */
export type ResumeCallResponse = Message<"hero.conversation.v1.ResumeCallResponse"> & {
  /**
   * Whether the call was successfully resumed
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.conversation.v1.ResumeCallResponse.
 * Use `create(ResumeCallResponseSchema)` to create a new message.
 */
export const ResumeCallResponseSchema: GenMessage<ResumeCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 28);

/**
 * @generated from message hero.conversation.v1.EndCallRequest
 */
export type EndCallRequest = Message<"hero.conversation.v1.EndCallRequest"> & {
  /**
   * Call SID to end
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;

  /**
   * Asset ID of the agent/dispatcher ending the call
   *
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * Optional reason for ending call
   *
   * @generated from field: string reason = 3;
   */
  reason: string;
};

/**
 * Describes the message hero.conversation.v1.EndCallRequest.
 * Use `create(EndCallRequestSchema)` to create a new message.
 */
export const EndCallRequestSchema: GenMessage<EndCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 29);

/**
 * @generated from message hero.conversation.v1.EndCallResponse
 */
export type EndCallResponse = Message<"hero.conversation.v1.EndCallResponse"> & {
  /**
   * Whether the call was successfully ended
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.conversation.v1.EndCallResponse.
 * Use `create(EndCallResponseSchema)` to create a new message.
 */
export const EndCallResponseSchema: GenMessage<EndCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 30);

/**
 * CallEvent represents a single event in a call's history
 *
 * @generated from message hero.conversation.v1.CallEvent
 */
export type CallEvent = Message<"hero.conversation.v1.CallEvent"> & {
  /**
   * When the event occurred
   *
   * @generated from field: google.protobuf.Timestamp timestamp = 1;
   */
  timestamp?: Timestamp;

  /**
   * Type of event
   *
   * @generated from field: hero.conversation.v1.CallEventType event_type = 2;
   */
  eventType: CallEventType;

  /**
   * Asset ID of the agent/dispatcher involved
   *
   * @generated from field: string asset_id = 3;
   */
  assetId: string;

  /**
   * Notes about the event
   *
   * @generated from field: string notes = 4;
   */
  notes: string;
};

/**
 * Describes the message hero.conversation.v1.CallEvent.
 * Use `create(CallEventSchema)` to create a new message.
 */
export const CallEventSchema: GenMessage<CallEvent> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 31);

/**
 * @generated from message hero.conversation.v1.GetSituationForCallRequest
 */
export type GetSituationForCallRequest = Message<"hero.conversation.v1.GetSituationForCallRequest"> & {
  /**
   * Call SID to get situation for
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;
};

/**
 * Describes the message hero.conversation.v1.GetSituationForCallRequest.
 * Use `create(GetSituationForCallRequestSchema)` to create a new message.
 */
export const GetSituationForCallRequestSchema: GenMessage<GetSituationForCallRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 32);

/**
 * @generated from message hero.conversation.v1.GetSituationForCallResponse
 */
export type GetSituationForCallResponse = Message<"hero.conversation.v1.GetSituationForCallResponse"> & {
  /**
   * Whether situation was found
   *
   * @generated from field: bool found = 1;
   */
  found: boolean;

  /**
   * Situation ID if found
   *
   * @generated from field: string situation_id = 2;
   */
  situationId: string;

  /**
   * Full situation object
   *
   * @generated from field: hero.situations.v2.Situation situation = 3;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.conversation.v1.GetSituationForCallResponse.
 * Use `create(GetSituationForCallResponseSchema)` to create a new message.
 */
export const GetSituationForCallResponseSchema: GenMessage<GetSituationForCallResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 33);

/**
 * @generated from message hero.conversation.v1.RevertSelectiveClaimRequest
 */
export type RevertSelectiveClaimRequest = Message<"hero.conversation.v1.RevertSelectiveClaimRequest"> & {
  /**
   * The call SID to revert
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;
};

/**
 * Describes the message hero.conversation.v1.RevertSelectiveClaimRequest.
 * Use `create(RevertSelectiveClaimRequestSchema)` to create a new message.
 */
export const RevertSelectiveClaimRequestSchema: GenMessage<RevertSelectiveClaimRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 34);

/**
 * @generated from message hero.conversation.v1.RevertSelectiveClaimResponse
 */
export type RevertSelectiveClaimResponse = Message<"hero.conversation.v1.RevertSelectiveClaimResponse"> & {
  /**
   * Whether the operation was successful
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Whether the call was actually reverted (false if already in waiting state)
   *
   * @generated from field: bool reverted = 2;
   */
  reverted: boolean;
};

/**
 * Describes the message hero.conversation.v1.RevertSelectiveClaimResponse.
 * Use `create(RevertSelectiveClaimResponseSchema)` to create a new message.
 */
export const RevertSelectiveClaimResponseSchema: GenMessage<RevertSelectiveClaimResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 35);

/**
 * @generated from message hero.conversation.v1.ListCallsRequest
 */
export type ListCallsRequest = Message<"hero.conversation.v1.ListCallsRequest"> & {
  /**
   * Optional start date filter (RFC3339 format)
   *
   * @generated from field: string start_date = 1;
   */
  startDate: string;

  /**
   * Optional end date filter (RFC3339 format)
   *
   * @generated from field: string end_date = 2;
   */
  endDate: string;

  /**
   * Page number (1-based)
   *
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * Number of results per page (default: 50, max: 100)
   *
   * @generated from field: int32 page_size = 4;
   */
  pageSize: number;

  /**
   * Sort order: "asc" or "desc" (default: "desc" - newest first)
   *
   * @generated from field: string sort_order = 5;
   */
  sortOrder: string;

  /**
   * Optional filter by call state
   *
   * @generated from field: string state = 6;
   */
  state: string;

  /**
   * Optional filter by call direction
   *
   * @generated from field: string direction = 7;
   */
  direction: string;
};

/**
 * Describes the message hero.conversation.v1.ListCallsRequest.
 * Use `create(ListCallsRequestSchema)` to create a new message.
 */
export const ListCallsRequestSchema: GenMessage<ListCallsRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 36);

/**
 * @generated from message hero.conversation.v1.ListCallsResponse
 */
export type ListCallsResponse = Message<"hero.conversation.v1.ListCallsResponse"> & {
  /**
   * List of calls
   *
   * @generated from field: repeated hero.conversation.v1.QueuedCall calls = 1;
   */
  calls: QueuedCall[];

  /**
   * Total number of calls matching the filter
   *
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * Next page number if there are more results, null if this is the last page
   *
   * @generated from field: optional int32 next_page = 3;
   */
  nextPage?: number;
};

/**
 * Describes the message hero.conversation.v1.ListCallsResponse.
 * Use `create(ListCallsResponseSchema)` to create a new message.
 */
export const ListCallsResponseSchema: GenMessage<ListCallsResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 37);

/**
 * GetHistoryMetadataRequest represents the request parameters for retrieving Zello history metadata
 *
 * @generated from message hero.conversation.v1.GetHistoryMetadataRequest
 */
export type GetHistoryMetadataRequest = Message<"hero.conversation.v1.GetHistoryMetadataRequest"> & {
  /**
   * @generated from field: string sender = 1;
   */
  sender: string;

  /**
   * @generated from field: string recipient = 2;
   */
  recipient: string;

  /**
   * @generated from field: string via_channel = 3;
   */
  viaChannel: string;

  /**
   * @generated from field: optional bool is_channel = 4;
   */
  isChannel?: boolean;

  /**
   * @generated from field: string type = 5;
   */
  type: string;

  /**
   * @generated from field: string text = 6;
   */
  text: string;

  /**
   * @generated from field: string eid = 7;
   */
  eid: string;

  /**
   * @generated from field: int32 call_id = 8;
   */
  callId: number;

  /**
   * @generated from field: int64 start_ts = 9;
   */
  startTs: bigint;

  /**
   * @generated from field: int64 end_ts = 10;
   */
  endTs: bigint;

  /**
   * @generated from field: int32 start_id = 11;
   */
  startId: number;

  /**
   * @generated from field: int32 end_id = 12;
   */
  endId: number;

  /**
   * @generated from field: int32 max = 13;
   */
  max: number;

  /**
   * @generated from field: int32 start = 14;
   */
  start: number;

  /**
   * @generated from field: string sort = 15;
   */
  sort: string;

  /**
   * @generated from field: string sort_order = 16;
   */
  sortOrder: string;
};

/**
 * Describes the message hero.conversation.v1.GetHistoryMetadataRequest.
 * Use `create(GetHistoryMetadataRequestSchema)` to create a new message.
 */
export const GetHistoryMetadataRequestSchema: GenMessage<GetHistoryMetadataRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 38);

/**
 * HistoryMessage represents a message in the Zello history
 *
 * @generated from message hero.conversation.v1.HistoryMessage
 */
export type HistoryMessage = Message<"hero.conversation.v1.HistoryMessage"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string type = 2;
   */
  type: string;

  /**
   * @generated from field: int64 ts = 3;
   */
  ts: bigint;

  /**
   * @generated from field: int64 call_id = 4;
   */
  callId: bigint;

  /**
   * @generated from field: int64 call_id_short = 5;
   */
  callIdShort: bigint;

  /**
   * @generated from field: string eid = 6;
   */
  eid: string;

  /**
   * @generated from field: hero.assets.v2.Asset sender = 7;
   */
  sender?: Asset;

  /**
   * @generated from field: string recipient = 8;
   */
  recipient: string;

  /**
   * @generated from field: string dispatch_call_recipient = 9;
   */
  dispatchCallRecipient: string;

  /**
   * @generated from field: string recipient_type = 10;
   */
  recipientType: string;

  /**
   * @generated from field: string media_key = 11;
   */
  mediaKey: string;

  /**
   * @generated from field: int64 duration = 12;
   */
  duration: bigint;

  /**
   * @generated from field: repeated string channel_users = 13;
   */
  channelUsers: string[];

  /**
   * @generated from field: int64 image_ts = 14;
   */
  imageTs: bigint;

  /**
   * @generated from field: string image_source = 15;
   */
  imageSource: string;

  /**
   * @generated from field: string text = 16;
   */
  text: string;

  /**
   * @generated from field: string transcription = 17;
   */
  transcription: string;

  /**
   * @generated from field: bool transcription_inaccurate = 18;
   */
  transcriptionInaccurate: boolean;
};

/**
 * Describes the message hero.conversation.v1.HistoryMessage.
 * Use `create(HistoryMessageSchema)` to create a new message.
 */
export const HistoryMessageSchema: GenMessage<HistoryMessage> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 39);

/**
 * GetHistoryMetadataResponse represents the response from retrieving Zello history metadata
 *
 * @generated from message hero.conversation.v1.GetHistoryMetadataResponse
 */
export type GetHistoryMetadataResponse = Message<"hero.conversation.v1.GetHistoryMetadataResponse"> & {
  /**
   * @generated from field: string status = 1;
   */
  status: string;

  /**
   * @generated from field: string code = 2;
   */
  code: string;

  /**
   * @generated from field: int64 total = 3;
   */
  total: bigint;

  /**
   * @generated from field: int64 returned = 4;
   */
  returned: bigint;

  /**
   * @generated from field: repeated hero.conversation.v1.HistoryMessage messages = 5;
   */
  messages: HistoryMessage[];
};

/**
 * Describes the message hero.conversation.v1.GetHistoryMetadataResponse.
 * Use `create(GetHistoryMetadataResponseSchema)` to create a new message.
 */
export const GetHistoryMetadataResponseSchema: GenMessage<GetHistoryMetadataResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 40);

/**
 * UpdateCallerNameRequest represents a request to update the caller name for a call
 *
 * @generated from message hero.conversation.v1.UpdateCallerNameRequest
 */
export type UpdateCallerNameRequest = Message<"hero.conversation.v1.UpdateCallerNameRequest"> & {
  /**
   * The Twilio Call SID to update
   *
   * @generated from field: string call_sid = 1;
   */
  callSid: string;

  /**
   * The caller name to set (can be empty to clear)
   *
   * @generated from field: string caller_name = 2;
   */
  callerName: string;
};

/**
 * Describes the message hero.conversation.v1.UpdateCallerNameRequest.
 * Use `create(UpdateCallerNameRequestSchema)` to create a new message.
 */
export const UpdateCallerNameRequestSchema: GenMessage<UpdateCallerNameRequest> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 41);

/**
 * UpdateCallerNameResponse represents the response from updating a caller name
 *
 * @generated from message hero.conversation.v1.UpdateCallerNameResponse
 */
export type UpdateCallerNameResponse = Message<"hero.conversation.v1.UpdateCallerNameResponse"> & {
  /**
   * Whether the update was successful
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Descriptive message about the result
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hero.conversation.v1.UpdateCallerNameResponse.
 * Use `create(UpdateCallerNameResponseSchema)` to create a new message.
 */
export const UpdateCallerNameResponseSchema: GenMessage<UpdateCallerNameResponse> = /*@__PURE__*/
  messageDesc(file_hero_communications_v1_conversation, 42);

/**
 * CallEventType defines the types of events in a call's lifecycle
 *
 * @generated from enum hero.conversation.v1.CallEventType
 */
export enum CallEventType {
  /**
   * @generated from enum value: CALL_EVENT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CALL_EVENT_TYPE_QUEUED = 1;
   */
  QUEUED = 1,

  /**
   * @generated from enum value: CALL_EVENT_TYPE_DEQUEUED = 2;
   */
  DEQUEUED = 2,

  /**
   * @generated from enum value: CALL_EVENT_TYPE_HELD = 3;
   */
  HELD = 3,

  /**
   * @generated from enum value: CALL_EVENT_TYPE_RESUMED = 4;
   */
  RESUMED = 4,

  /**
   * @generated from enum value: CALL_EVENT_TYPE_ENDED = 5;
   */
  ENDED = 5,
}

/**
 * Describes the enum hero.conversation.v1.CallEventType.
 */
export const CallEventTypeSchema: GenEnum<CallEventType> = /*@__PURE__*/
  enumDesc(file_hero_communications_v1_conversation, 0);

/**
 * -----------------------------------------------------
 * VideoCallService
 * -----------------------------------------------------
 *
 * @generated from service hero.conversation.v1.VideoCallService
 */
export const VideoCallService: GenService<{
  /**
   * Generate an Agora Video Access Token by providing a user ID and channel
   *
   * @generated from rpc hero.conversation.v1.VideoCallService.GetVideoCallAccessToken
   */
  getVideoCallAccessToken: {
    methodKind: "unary";
    input: typeof GetVideoCallAccessTokenRequestSchema;
    output: typeof GetVideoCallAccessTokenResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_communications_v1_conversation, 0);

/**
 * ChatService defines the RPC methods for generating tokens and managing groups
 *
 * @generated from service hero.conversation.v1.ChatService
 */
export const ChatService: GenService<{
  /**
   * GetChatUserToken generates a token for a specific user
   *
   * @generated from rpc hero.conversation.v1.ChatService.GetChatUserToken
   */
  getChatUserToken: {
    methodKind: "unary";
    input: typeof GetChatUserTokenRequestSchema;
    output: typeof GetChatUserTokenResponseSchema;
  },
  /**
   * GetChatAppToken generates a token for the entire application
   *
   * @generated from rpc hero.conversation.v1.ChatService.GetChatAppToken
   */
  getChatAppToken: {
    methodKind: "unary";
    input: typeof GetChatAppTokenRequestSchema;
    output: typeof GetChatAppTokenResponseSchema;
  },
  /**
   * CreateGroupChat creates a new group chat and returns the group ID if successful
   *
   * @generated from rpc hero.conversation.v1.ChatService.CreateGroupChat
   */
  createGroupChat: {
    methodKind: "unary";
    input: typeof CreateGroupChatRequestSchema;
    output: typeof CreateGroupChatResponseSchema;
  },
  /**
   * GetGroupChatId retrieves the group chat ID from the DB by providing a group chat name
   *
   * @generated from rpc hero.conversation.v1.ChatService.GetGroupChatId
   */
  getGroupChatId: {
    methodKind: "unary";
    input: typeof GetGroupChatIdRequestSchema;
    output: typeof GetGroupChatIdResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_communications_v1_conversation, 1);

/**
 * -----------------------------------------------------
 * CellularCallService (Twilio)
 * -----------------------------------------------------
 *
 * @generated from service hero.conversation.v1.CellularCallService
 */
export const CellularCallService: GenService<{
  /**
   * Generate a Twilio Cellular Access Token for voice calls
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.GetCellularCallAccessToken
   */
  getCellularCallAccessToken: {
    methodKind: "unary";
    input: typeof GetCellularCallAccessTokenRequestSchema;
    output: typeof GetCellularCallAccessTokenResponseSchema;
  },
  /**
   * Handle incoming or outgoing cellular calls and return a TwiML response
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.HandleCall
   */
  handleCall: {
    methodKind: "unary";
    input: typeof HandleCallRequestSchema;
    output: typeof HandleCallResponseSchema;
  },
  /**
   * Place a call in the queue with TwiML instructions for waiting
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.QueueCall
   */
  queueCall: {
    methodKind: "unary";
    input: typeof QueueCallRequestSchema;
    output: typeof QueueCallResponseSchema;
  },
  /**
   * Connect an asset (agent) to the next call in queue
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.DequeueCall
   */
  dequeueCall: {
    methodKind: "unary";
    input: typeof DequeueCallRequestSchema;
    output: typeof DequeueCallResponseSchema;
  },
  /**
   * Connect an asset (agent) to a specific call by SID
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.DequeueCallBySid
   */
  dequeueCallBySid: {
    methodKind: "unary";
    input: typeof DequeueCallBySidRequestSchema;
    output: typeof DequeueCallBySidResponseSchema;
  },
  /**
   * Get current status of the call queue (size, next caller)
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.GetQueueStatus
   */
  getQueueStatus: {
    methodKind: "unary";
    input: typeof GetQueueStatusRequestSchema;
    output: typeof GetQueueStatusResponseSchema;
  },
  /**
   * Place an active call on hold
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.HoldCall
   */
  holdCall: {
    methodKind: "unary";
    input: typeof HoldCallRequestSchema;
    output: typeof HoldCallResponseSchema;
  },
  /**
   * Get calls on hold for a specific asset (agent)
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.GetAssetHeldCalls
   */
  getAssetHeldCalls: {
    methodKind: "unary";
    input: typeof GetAssetHeldCallsRequestSchema;
    output: typeof GetAssetHeldCallsResponseSchema;
  },
  /**
   * Resume a call from hold
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.ResumeCall
   */
  resumeCall: {
    methodKind: "unary";
    input: typeof ResumeCallRequestSchema;
    output: typeof ResumeCallResponseSchema;
  },
  /**
   * End an active call
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.EndCall
   */
  endCall: {
    methodKind: "unary";
    input: typeof EndCallRequestSchema;
    output: typeof EndCallResponseSchema;
  },
  /**
   * Get the situation associated with a call
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.GetSituationForCall
   */
  getSituationForCall: {
    methodKind: "unary";
    input: typeof GetSituationForCallRequestSchema;
    output: typeof GetSituationForCallResponseSchema;
  },
  /**
   * Revert a call from pending_selective_assignment back to waiting state
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.RevertSelectiveClaim
   */
  revertSelectiveClaim: {
    methodKind: "unary";
    input: typeof RevertSelectiveClaimRequestSchema;
    output: typeof RevertSelectiveClaimResponseSchema;
  },
  /**
   * List calls with filtering and pagination
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.ListCalls
   */
  listCalls: {
    methodKind: "unary";
    input: typeof ListCallsRequestSchema;
    output: typeof ListCallsResponseSchema;
  },
  /**
   * Update the caller name for a specific call
   *
   * @generated from rpc hero.conversation.v1.CellularCallService.UpdateCallerName
   */
  updateCallerName: {
    methodKind: "unary";
    input: typeof UpdateCallerNameRequestSchema;
    output: typeof UpdateCallerNameResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_communications_v1_conversation, 2);

/**
 * @generated from service hero.conversation.v1.PTTService
 */
export const PTTService: GenService<{
  /**
   * Get Zello channel history metadata
   *
   * @generated from rpc hero.conversation.v1.PTTService.GetHistoryMetadata
   */
  getHistoryMetadata: {
    methodKind: "unary";
    input: typeof GetHistoryMetadataRequestSchema;
    output: typeof GetHistoryMetadataResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_communications_v1_conversation, 3);

