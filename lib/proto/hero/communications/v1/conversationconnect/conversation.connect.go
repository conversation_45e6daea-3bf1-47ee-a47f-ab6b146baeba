// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/communications/v1/conversation.proto

package conversationconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v1 "proto/hero/communications/v1"
	strings "strings"

	connect "connectrpc.com/connect"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VideoCallServiceName is the fully-qualified name of the VideoCallService service.
	VideoCallServiceName = "hero.conversation.v1.VideoCallService"
	// ChatServiceName is the fully-qualified name of the ChatService service.
	ChatServiceName = "hero.conversation.v1.ChatService"
	// CellularCallServiceName is the fully-qualified name of the CellularCallService service.
	CellularCallServiceName = "hero.conversation.v1.CellularCallService"
	// PTTServiceName is the fully-qualified name of the PTTService service.
	PTTServiceName = "hero.conversation.v1.PTTService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VideoCallServiceGetVideoCallAccessTokenProcedure is the fully-qualified name of the
	// VideoCallService's GetVideoCallAccessToken RPC.
	VideoCallServiceGetVideoCallAccessTokenProcedure = "/hero.conversation.v1.VideoCallService/GetVideoCallAccessToken"
	// ChatServiceGetChatUserTokenProcedure is the fully-qualified name of the ChatService's
	// GetChatUserToken RPC.
	ChatServiceGetChatUserTokenProcedure = "/hero.conversation.v1.ChatService/GetChatUserToken"
	// ChatServiceGetChatAppTokenProcedure is the fully-qualified name of the ChatService's
	// GetChatAppToken RPC.
	ChatServiceGetChatAppTokenProcedure = "/hero.conversation.v1.ChatService/GetChatAppToken"
	// ChatServiceCreateGroupChatProcedure is the fully-qualified name of the ChatService's
	// CreateGroupChat RPC.
	ChatServiceCreateGroupChatProcedure = "/hero.conversation.v1.ChatService/CreateGroupChat"
	// ChatServiceGetGroupChatIdProcedure is the fully-qualified name of the ChatService's
	// GetGroupChatId RPC.
	ChatServiceGetGroupChatIdProcedure = "/hero.conversation.v1.ChatService/GetGroupChatId"
	// CellularCallServiceGetCellularCallAccessTokenProcedure is the fully-qualified name of the
	// CellularCallService's GetCellularCallAccessToken RPC.
	CellularCallServiceGetCellularCallAccessTokenProcedure = "/hero.conversation.v1.CellularCallService/GetCellularCallAccessToken"
	// CellularCallServiceHandleCallProcedure is the fully-qualified name of the CellularCallService's
	// HandleCall RPC.
	CellularCallServiceHandleCallProcedure = "/hero.conversation.v1.CellularCallService/HandleCall"
	// CellularCallServiceQueueCallProcedure is the fully-qualified name of the CellularCallService's
	// QueueCall RPC.
	CellularCallServiceQueueCallProcedure = "/hero.conversation.v1.CellularCallService/QueueCall"
	// CellularCallServiceDequeueCallProcedure is the fully-qualified name of the CellularCallService's
	// DequeueCall RPC.
	CellularCallServiceDequeueCallProcedure = "/hero.conversation.v1.CellularCallService/DequeueCall"
	// CellularCallServiceDequeueCallBySidProcedure is the fully-qualified name of the
	// CellularCallService's DequeueCallBySid RPC.
	CellularCallServiceDequeueCallBySidProcedure = "/hero.conversation.v1.CellularCallService/DequeueCallBySid"
	// CellularCallServiceGetQueueStatusProcedure is the fully-qualified name of the
	// CellularCallService's GetQueueStatus RPC.
	CellularCallServiceGetQueueStatusProcedure = "/hero.conversation.v1.CellularCallService/GetQueueStatus"
	// CellularCallServiceHoldCallProcedure is the fully-qualified name of the CellularCallService's
	// HoldCall RPC.
	CellularCallServiceHoldCallProcedure = "/hero.conversation.v1.CellularCallService/HoldCall"
	// CellularCallServiceGetAssetHeldCallsProcedure is the fully-qualified name of the
	// CellularCallService's GetAssetHeldCalls RPC.
	CellularCallServiceGetAssetHeldCallsProcedure = "/hero.conversation.v1.CellularCallService/GetAssetHeldCalls"
	// CellularCallServiceResumeCallProcedure is the fully-qualified name of the CellularCallService's
	// ResumeCall RPC.
	CellularCallServiceResumeCallProcedure = "/hero.conversation.v1.CellularCallService/ResumeCall"
	// CellularCallServiceEndCallProcedure is the fully-qualified name of the CellularCallService's
	// EndCall RPC.
	CellularCallServiceEndCallProcedure = "/hero.conversation.v1.CellularCallService/EndCall"
	// CellularCallServiceGetSituationForCallProcedure is the fully-qualified name of the
	// CellularCallService's GetSituationForCall RPC.
	CellularCallServiceGetSituationForCallProcedure = "/hero.conversation.v1.CellularCallService/GetSituationForCall"
	// CellularCallServiceRevertSelectiveClaimProcedure is the fully-qualified name of the
	// CellularCallService's RevertSelectiveClaim RPC.
	CellularCallServiceRevertSelectiveClaimProcedure = "/hero.conversation.v1.CellularCallService/RevertSelectiveClaim"
	// CellularCallServiceListCallsProcedure is the fully-qualified name of the CellularCallService's
	// ListCalls RPC.
	CellularCallServiceListCallsProcedure = "/hero.conversation.v1.CellularCallService/ListCalls"
	// CellularCallServiceUpdateCallerNameProcedure is the fully-qualified name of the
	// CellularCallService's UpdateCallerName RPC.
	CellularCallServiceUpdateCallerNameProcedure = "/hero.conversation.v1.CellularCallService/UpdateCallerName"
	// PTTServiceGetHistoryMetadataProcedure is the fully-qualified name of the PTTService's
	// GetHistoryMetadata RPC.
	PTTServiceGetHistoryMetadataProcedure = "/hero.conversation.v1.PTTService/GetHistoryMetadata"
)

// VideoCallServiceClient is a client for the hero.conversation.v1.VideoCallService service.
type VideoCallServiceClient interface {
	// Generate an Agora Video Access Token by providing a user ID and channel
	GetVideoCallAccessToken(context.Context, *connect.Request[v1.GetVideoCallAccessTokenRequest]) (*connect.Response[v1.GetVideoCallAccessTokenResponse], error)
}

// NewVideoCallServiceClient constructs a client for the hero.conversation.v1.VideoCallService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVideoCallServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VideoCallServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	videoCallServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("VideoCallService").Methods()
	return &videoCallServiceClient{
		getVideoCallAccessToken: connect.NewClient[v1.GetVideoCallAccessTokenRequest, v1.GetVideoCallAccessTokenResponse](
			httpClient,
			baseURL+VideoCallServiceGetVideoCallAccessTokenProcedure,
			connect.WithSchema(videoCallServiceMethods.ByName("GetVideoCallAccessToken")),
			connect.WithClientOptions(opts...),
		),
	}
}

// videoCallServiceClient implements VideoCallServiceClient.
type videoCallServiceClient struct {
	getVideoCallAccessToken *connect.Client[v1.GetVideoCallAccessTokenRequest, v1.GetVideoCallAccessTokenResponse]
}

// GetVideoCallAccessToken calls hero.conversation.v1.VideoCallService.GetVideoCallAccessToken.
func (c *videoCallServiceClient) GetVideoCallAccessToken(ctx context.Context, req *connect.Request[v1.GetVideoCallAccessTokenRequest]) (*connect.Response[v1.GetVideoCallAccessTokenResponse], error) {
	return c.getVideoCallAccessToken.CallUnary(ctx, req)
}

// VideoCallServiceHandler is an implementation of the hero.conversation.v1.VideoCallService
// service.
type VideoCallServiceHandler interface {
	// Generate an Agora Video Access Token by providing a user ID and channel
	GetVideoCallAccessToken(context.Context, *connect.Request[v1.GetVideoCallAccessTokenRequest]) (*connect.Response[v1.GetVideoCallAccessTokenResponse], error)
}

// NewVideoCallServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVideoCallServiceHandler(svc VideoCallServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	videoCallServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("VideoCallService").Methods()
	videoCallServiceGetVideoCallAccessTokenHandler := connect.NewUnaryHandler(
		VideoCallServiceGetVideoCallAccessTokenProcedure,
		svc.GetVideoCallAccessToken,
		connect.WithSchema(videoCallServiceMethods.ByName("GetVideoCallAccessToken")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.conversation.v1.VideoCallService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VideoCallServiceGetVideoCallAccessTokenProcedure:
			videoCallServiceGetVideoCallAccessTokenHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVideoCallServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVideoCallServiceHandler struct{}

func (UnimplementedVideoCallServiceHandler) GetVideoCallAccessToken(context.Context, *connect.Request[v1.GetVideoCallAccessTokenRequest]) (*connect.Response[v1.GetVideoCallAccessTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.VideoCallService.GetVideoCallAccessToken is not implemented"))
}

// ChatServiceClient is a client for the hero.conversation.v1.ChatService service.
type ChatServiceClient interface {
	// GetChatUserToken generates a token for a specific user
	GetChatUserToken(context.Context, *connect.Request[v1.GetChatUserTokenRequest]) (*connect.Response[v1.GetChatUserTokenResponse], error)
	// GetChatAppToken generates a token for the entire application
	GetChatAppToken(context.Context, *connect.Request[v1.GetChatAppTokenRequest]) (*connect.Response[v1.GetChatAppTokenResponse], error)
	// CreateGroupChat creates a new group chat and returns the group ID if successful
	CreateGroupChat(context.Context, *connect.Request[v1.CreateGroupChatRequest]) (*connect.Response[v1.CreateGroupChatResponse], error)
	// GetGroupChatId retrieves the group chat ID from the DB by providing a group chat name
	GetGroupChatId(context.Context, *connect.Request[v1.GetGroupChatIdRequest]) (*connect.Response[v1.GetGroupChatIdResponse], error)
}

// NewChatServiceClient constructs a client for the hero.conversation.v1.ChatService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewChatServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ChatServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	chatServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("ChatService").Methods()
	return &chatServiceClient{
		getChatUserToken: connect.NewClient[v1.GetChatUserTokenRequest, v1.GetChatUserTokenResponse](
			httpClient,
			baseURL+ChatServiceGetChatUserTokenProcedure,
			connect.WithSchema(chatServiceMethods.ByName("GetChatUserToken")),
			connect.WithClientOptions(opts...),
		),
		getChatAppToken: connect.NewClient[v1.GetChatAppTokenRequest, v1.GetChatAppTokenResponse](
			httpClient,
			baseURL+ChatServiceGetChatAppTokenProcedure,
			connect.WithSchema(chatServiceMethods.ByName("GetChatAppToken")),
			connect.WithClientOptions(opts...),
		),
		createGroupChat: connect.NewClient[v1.CreateGroupChatRequest, v1.CreateGroupChatResponse](
			httpClient,
			baseURL+ChatServiceCreateGroupChatProcedure,
			connect.WithSchema(chatServiceMethods.ByName("CreateGroupChat")),
			connect.WithClientOptions(opts...),
		),
		getGroupChatId: connect.NewClient[v1.GetGroupChatIdRequest, v1.GetGroupChatIdResponse](
			httpClient,
			baseURL+ChatServiceGetGroupChatIdProcedure,
			connect.WithSchema(chatServiceMethods.ByName("GetGroupChatId")),
			connect.WithClientOptions(opts...),
		),
	}
}

// chatServiceClient implements ChatServiceClient.
type chatServiceClient struct {
	getChatUserToken *connect.Client[v1.GetChatUserTokenRequest, v1.GetChatUserTokenResponse]
	getChatAppToken  *connect.Client[v1.GetChatAppTokenRequest, v1.GetChatAppTokenResponse]
	createGroupChat  *connect.Client[v1.CreateGroupChatRequest, v1.CreateGroupChatResponse]
	getGroupChatId   *connect.Client[v1.GetGroupChatIdRequest, v1.GetGroupChatIdResponse]
}

// GetChatUserToken calls hero.conversation.v1.ChatService.GetChatUserToken.
func (c *chatServiceClient) GetChatUserToken(ctx context.Context, req *connect.Request[v1.GetChatUserTokenRequest]) (*connect.Response[v1.GetChatUserTokenResponse], error) {
	return c.getChatUserToken.CallUnary(ctx, req)
}

// GetChatAppToken calls hero.conversation.v1.ChatService.GetChatAppToken.
func (c *chatServiceClient) GetChatAppToken(ctx context.Context, req *connect.Request[v1.GetChatAppTokenRequest]) (*connect.Response[v1.GetChatAppTokenResponse], error) {
	return c.getChatAppToken.CallUnary(ctx, req)
}

// CreateGroupChat calls hero.conversation.v1.ChatService.CreateGroupChat.
func (c *chatServiceClient) CreateGroupChat(ctx context.Context, req *connect.Request[v1.CreateGroupChatRequest]) (*connect.Response[v1.CreateGroupChatResponse], error) {
	return c.createGroupChat.CallUnary(ctx, req)
}

// GetGroupChatId calls hero.conversation.v1.ChatService.GetGroupChatId.
func (c *chatServiceClient) GetGroupChatId(ctx context.Context, req *connect.Request[v1.GetGroupChatIdRequest]) (*connect.Response[v1.GetGroupChatIdResponse], error) {
	return c.getGroupChatId.CallUnary(ctx, req)
}

// ChatServiceHandler is an implementation of the hero.conversation.v1.ChatService service.
type ChatServiceHandler interface {
	// GetChatUserToken generates a token for a specific user
	GetChatUserToken(context.Context, *connect.Request[v1.GetChatUserTokenRequest]) (*connect.Response[v1.GetChatUserTokenResponse], error)
	// GetChatAppToken generates a token for the entire application
	GetChatAppToken(context.Context, *connect.Request[v1.GetChatAppTokenRequest]) (*connect.Response[v1.GetChatAppTokenResponse], error)
	// CreateGroupChat creates a new group chat and returns the group ID if successful
	CreateGroupChat(context.Context, *connect.Request[v1.CreateGroupChatRequest]) (*connect.Response[v1.CreateGroupChatResponse], error)
	// GetGroupChatId retrieves the group chat ID from the DB by providing a group chat name
	GetGroupChatId(context.Context, *connect.Request[v1.GetGroupChatIdRequest]) (*connect.Response[v1.GetGroupChatIdResponse], error)
}

// NewChatServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewChatServiceHandler(svc ChatServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	chatServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("ChatService").Methods()
	chatServiceGetChatUserTokenHandler := connect.NewUnaryHandler(
		ChatServiceGetChatUserTokenProcedure,
		svc.GetChatUserToken,
		connect.WithSchema(chatServiceMethods.ByName("GetChatUserToken")),
		connect.WithHandlerOptions(opts...),
	)
	chatServiceGetChatAppTokenHandler := connect.NewUnaryHandler(
		ChatServiceGetChatAppTokenProcedure,
		svc.GetChatAppToken,
		connect.WithSchema(chatServiceMethods.ByName("GetChatAppToken")),
		connect.WithHandlerOptions(opts...),
	)
	chatServiceCreateGroupChatHandler := connect.NewUnaryHandler(
		ChatServiceCreateGroupChatProcedure,
		svc.CreateGroupChat,
		connect.WithSchema(chatServiceMethods.ByName("CreateGroupChat")),
		connect.WithHandlerOptions(opts...),
	)
	chatServiceGetGroupChatIdHandler := connect.NewUnaryHandler(
		ChatServiceGetGroupChatIdProcedure,
		svc.GetGroupChatId,
		connect.WithSchema(chatServiceMethods.ByName("GetGroupChatId")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.conversation.v1.ChatService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ChatServiceGetChatUserTokenProcedure:
			chatServiceGetChatUserTokenHandler.ServeHTTP(w, r)
		case ChatServiceGetChatAppTokenProcedure:
			chatServiceGetChatAppTokenHandler.ServeHTTP(w, r)
		case ChatServiceCreateGroupChatProcedure:
			chatServiceCreateGroupChatHandler.ServeHTTP(w, r)
		case ChatServiceGetGroupChatIdProcedure:
			chatServiceGetGroupChatIdHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedChatServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedChatServiceHandler struct{}

func (UnimplementedChatServiceHandler) GetChatUserToken(context.Context, *connect.Request[v1.GetChatUserTokenRequest]) (*connect.Response[v1.GetChatUserTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.ChatService.GetChatUserToken is not implemented"))
}

func (UnimplementedChatServiceHandler) GetChatAppToken(context.Context, *connect.Request[v1.GetChatAppTokenRequest]) (*connect.Response[v1.GetChatAppTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.ChatService.GetChatAppToken is not implemented"))
}

func (UnimplementedChatServiceHandler) CreateGroupChat(context.Context, *connect.Request[v1.CreateGroupChatRequest]) (*connect.Response[v1.CreateGroupChatResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.ChatService.CreateGroupChat is not implemented"))
}

func (UnimplementedChatServiceHandler) GetGroupChatId(context.Context, *connect.Request[v1.GetGroupChatIdRequest]) (*connect.Response[v1.GetGroupChatIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.ChatService.GetGroupChatId is not implemented"))
}

// CellularCallServiceClient is a client for the hero.conversation.v1.CellularCallService service.
type CellularCallServiceClient interface {
	// Generate a Twilio Cellular Access Token for voice calls
	GetCellularCallAccessToken(context.Context, *connect.Request[v1.GetCellularCallAccessTokenRequest]) (*connect.Response[v1.GetCellularCallAccessTokenResponse], error)
	// Handle incoming or outgoing cellular calls and return a TwiML response
	HandleCall(context.Context, *connect.Request[v1.HandleCallRequest]) (*connect.Response[v1.HandleCallResponse], error)
	// Place a call in the queue with TwiML instructions for waiting
	QueueCall(context.Context, *connect.Request[v1.QueueCallRequest]) (*connect.Response[v1.QueueCallResponse], error)
	// Connect an asset (agent) to the next call in queue
	DequeueCall(context.Context, *connect.Request[v1.DequeueCallRequest]) (*connect.Response[v1.DequeueCallResponse], error)
	// Connect an asset (agent) to a specific call by SID
	DequeueCallBySid(context.Context, *connect.Request[v1.DequeueCallBySidRequest]) (*connect.Response[v1.DequeueCallBySidResponse], error)
	// Get current status of the call queue (size, next caller)
	GetQueueStatus(context.Context, *connect.Request[v1.GetQueueStatusRequest]) (*connect.Response[v1.GetQueueStatusResponse], error)
	// Place an active call on hold
	HoldCall(context.Context, *connect.Request[v1.HoldCallRequest]) (*connect.Response[v1.HoldCallResponse], error)
	// Get calls on hold for a specific asset (agent)
	GetAssetHeldCalls(context.Context, *connect.Request[v1.GetAssetHeldCallsRequest]) (*connect.Response[v1.GetAssetHeldCallsResponse], error)
	// Resume a call from hold
	ResumeCall(context.Context, *connect.Request[v1.ResumeCallRequest]) (*connect.Response[v1.ResumeCallResponse], error)
	// End an active call
	EndCall(context.Context, *connect.Request[v1.EndCallRequest]) (*connect.Response[v1.EndCallResponse], error)
	// Get the situation associated with a call
	GetSituationForCall(context.Context, *connect.Request[v1.GetSituationForCallRequest]) (*connect.Response[v1.GetSituationForCallResponse], error)
	// Revert a call from pending_selective_assignment back to waiting state
	RevertSelectiveClaim(context.Context, *connect.Request[v1.RevertSelectiveClaimRequest]) (*connect.Response[v1.RevertSelectiveClaimResponse], error)
	// List calls with filtering and pagination
	ListCalls(context.Context, *connect.Request[v1.ListCallsRequest]) (*connect.Response[v1.ListCallsResponse], error)
	// Update the caller name for a specific call
	UpdateCallerName(context.Context, *connect.Request[v1.UpdateCallerNameRequest]) (*connect.Response[v1.UpdateCallerNameResponse], error)
}

// NewCellularCallServiceClient constructs a client for the hero.conversation.v1.CellularCallService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCellularCallServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CellularCallServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	cellularCallServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("CellularCallService").Methods()
	return &cellularCallServiceClient{
		getCellularCallAccessToken: connect.NewClient[v1.GetCellularCallAccessTokenRequest, v1.GetCellularCallAccessTokenResponse](
			httpClient,
			baseURL+CellularCallServiceGetCellularCallAccessTokenProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("GetCellularCallAccessToken")),
			connect.WithClientOptions(opts...),
		),
		handleCall: connect.NewClient[v1.HandleCallRequest, v1.HandleCallResponse](
			httpClient,
			baseURL+CellularCallServiceHandleCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("HandleCall")),
			connect.WithClientOptions(opts...),
		),
		queueCall: connect.NewClient[v1.QueueCallRequest, v1.QueueCallResponse](
			httpClient,
			baseURL+CellularCallServiceQueueCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("QueueCall")),
			connect.WithClientOptions(opts...),
		),
		dequeueCall: connect.NewClient[v1.DequeueCallRequest, v1.DequeueCallResponse](
			httpClient,
			baseURL+CellularCallServiceDequeueCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("DequeueCall")),
			connect.WithClientOptions(opts...),
		),
		dequeueCallBySid: connect.NewClient[v1.DequeueCallBySidRequest, v1.DequeueCallBySidResponse](
			httpClient,
			baseURL+CellularCallServiceDequeueCallBySidProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("DequeueCallBySid")),
			connect.WithClientOptions(opts...),
		),
		getQueueStatus: connect.NewClient[v1.GetQueueStatusRequest, v1.GetQueueStatusResponse](
			httpClient,
			baseURL+CellularCallServiceGetQueueStatusProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("GetQueueStatus")),
			connect.WithClientOptions(opts...),
		),
		holdCall: connect.NewClient[v1.HoldCallRequest, v1.HoldCallResponse](
			httpClient,
			baseURL+CellularCallServiceHoldCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("HoldCall")),
			connect.WithClientOptions(opts...),
		),
		getAssetHeldCalls: connect.NewClient[v1.GetAssetHeldCallsRequest, v1.GetAssetHeldCallsResponse](
			httpClient,
			baseURL+CellularCallServiceGetAssetHeldCallsProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("GetAssetHeldCalls")),
			connect.WithClientOptions(opts...),
		),
		resumeCall: connect.NewClient[v1.ResumeCallRequest, v1.ResumeCallResponse](
			httpClient,
			baseURL+CellularCallServiceResumeCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("ResumeCall")),
			connect.WithClientOptions(opts...),
		),
		endCall: connect.NewClient[v1.EndCallRequest, v1.EndCallResponse](
			httpClient,
			baseURL+CellularCallServiceEndCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("EndCall")),
			connect.WithClientOptions(opts...),
		),
		getSituationForCall: connect.NewClient[v1.GetSituationForCallRequest, v1.GetSituationForCallResponse](
			httpClient,
			baseURL+CellularCallServiceGetSituationForCallProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("GetSituationForCall")),
			connect.WithClientOptions(opts...),
		),
		revertSelectiveClaim: connect.NewClient[v1.RevertSelectiveClaimRequest, v1.RevertSelectiveClaimResponse](
			httpClient,
			baseURL+CellularCallServiceRevertSelectiveClaimProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("RevertSelectiveClaim")),
			connect.WithClientOptions(opts...),
		),
		listCalls: connect.NewClient[v1.ListCallsRequest, v1.ListCallsResponse](
			httpClient,
			baseURL+CellularCallServiceListCallsProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("ListCalls")),
			connect.WithClientOptions(opts...),
		),
		updateCallerName: connect.NewClient[v1.UpdateCallerNameRequest, v1.UpdateCallerNameResponse](
			httpClient,
			baseURL+CellularCallServiceUpdateCallerNameProcedure,
			connect.WithSchema(cellularCallServiceMethods.ByName("UpdateCallerName")),
			connect.WithClientOptions(opts...),
		),
	}
}

// cellularCallServiceClient implements CellularCallServiceClient.
type cellularCallServiceClient struct {
	getCellularCallAccessToken *connect.Client[v1.GetCellularCallAccessTokenRequest, v1.GetCellularCallAccessTokenResponse]
	handleCall                 *connect.Client[v1.HandleCallRequest, v1.HandleCallResponse]
	queueCall                  *connect.Client[v1.QueueCallRequest, v1.QueueCallResponse]
	dequeueCall                *connect.Client[v1.DequeueCallRequest, v1.DequeueCallResponse]
	dequeueCallBySid           *connect.Client[v1.DequeueCallBySidRequest, v1.DequeueCallBySidResponse]
	getQueueStatus             *connect.Client[v1.GetQueueStatusRequest, v1.GetQueueStatusResponse]
	holdCall                   *connect.Client[v1.HoldCallRequest, v1.HoldCallResponse]
	getAssetHeldCalls          *connect.Client[v1.GetAssetHeldCallsRequest, v1.GetAssetHeldCallsResponse]
	resumeCall                 *connect.Client[v1.ResumeCallRequest, v1.ResumeCallResponse]
	endCall                    *connect.Client[v1.EndCallRequest, v1.EndCallResponse]
	getSituationForCall        *connect.Client[v1.GetSituationForCallRequest, v1.GetSituationForCallResponse]
	revertSelectiveClaim       *connect.Client[v1.RevertSelectiveClaimRequest, v1.RevertSelectiveClaimResponse]
	listCalls                  *connect.Client[v1.ListCallsRequest, v1.ListCallsResponse]
	updateCallerName           *connect.Client[v1.UpdateCallerNameRequest, v1.UpdateCallerNameResponse]
}

// GetCellularCallAccessToken calls
// hero.conversation.v1.CellularCallService.GetCellularCallAccessToken.
func (c *cellularCallServiceClient) GetCellularCallAccessToken(ctx context.Context, req *connect.Request[v1.GetCellularCallAccessTokenRequest]) (*connect.Response[v1.GetCellularCallAccessTokenResponse], error) {
	return c.getCellularCallAccessToken.CallUnary(ctx, req)
}

// HandleCall calls hero.conversation.v1.CellularCallService.HandleCall.
func (c *cellularCallServiceClient) HandleCall(ctx context.Context, req *connect.Request[v1.HandleCallRequest]) (*connect.Response[v1.HandleCallResponse], error) {
	return c.handleCall.CallUnary(ctx, req)
}

// QueueCall calls hero.conversation.v1.CellularCallService.QueueCall.
func (c *cellularCallServiceClient) QueueCall(ctx context.Context, req *connect.Request[v1.QueueCallRequest]) (*connect.Response[v1.QueueCallResponse], error) {
	return c.queueCall.CallUnary(ctx, req)
}

// DequeueCall calls hero.conversation.v1.CellularCallService.DequeueCall.
func (c *cellularCallServiceClient) DequeueCall(ctx context.Context, req *connect.Request[v1.DequeueCallRequest]) (*connect.Response[v1.DequeueCallResponse], error) {
	return c.dequeueCall.CallUnary(ctx, req)
}

// DequeueCallBySid calls hero.conversation.v1.CellularCallService.DequeueCallBySid.
func (c *cellularCallServiceClient) DequeueCallBySid(ctx context.Context, req *connect.Request[v1.DequeueCallBySidRequest]) (*connect.Response[v1.DequeueCallBySidResponse], error) {
	return c.dequeueCallBySid.CallUnary(ctx, req)
}

// GetQueueStatus calls hero.conversation.v1.CellularCallService.GetQueueStatus.
func (c *cellularCallServiceClient) GetQueueStatus(ctx context.Context, req *connect.Request[v1.GetQueueStatusRequest]) (*connect.Response[v1.GetQueueStatusResponse], error) {
	return c.getQueueStatus.CallUnary(ctx, req)
}

// HoldCall calls hero.conversation.v1.CellularCallService.HoldCall.
func (c *cellularCallServiceClient) HoldCall(ctx context.Context, req *connect.Request[v1.HoldCallRequest]) (*connect.Response[v1.HoldCallResponse], error) {
	return c.holdCall.CallUnary(ctx, req)
}

// GetAssetHeldCalls calls hero.conversation.v1.CellularCallService.GetAssetHeldCalls.
func (c *cellularCallServiceClient) GetAssetHeldCalls(ctx context.Context, req *connect.Request[v1.GetAssetHeldCallsRequest]) (*connect.Response[v1.GetAssetHeldCallsResponse], error) {
	return c.getAssetHeldCalls.CallUnary(ctx, req)
}

// ResumeCall calls hero.conversation.v1.CellularCallService.ResumeCall.
func (c *cellularCallServiceClient) ResumeCall(ctx context.Context, req *connect.Request[v1.ResumeCallRequest]) (*connect.Response[v1.ResumeCallResponse], error) {
	return c.resumeCall.CallUnary(ctx, req)
}

// EndCall calls hero.conversation.v1.CellularCallService.EndCall.
func (c *cellularCallServiceClient) EndCall(ctx context.Context, req *connect.Request[v1.EndCallRequest]) (*connect.Response[v1.EndCallResponse], error) {
	return c.endCall.CallUnary(ctx, req)
}

// GetSituationForCall calls hero.conversation.v1.CellularCallService.GetSituationForCall.
func (c *cellularCallServiceClient) GetSituationForCall(ctx context.Context, req *connect.Request[v1.GetSituationForCallRequest]) (*connect.Response[v1.GetSituationForCallResponse], error) {
	return c.getSituationForCall.CallUnary(ctx, req)
}

// RevertSelectiveClaim calls hero.conversation.v1.CellularCallService.RevertSelectiveClaim.
func (c *cellularCallServiceClient) RevertSelectiveClaim(ctx context.Context, req *connect.Request[v1.RevertSelectiveClaimRequest]) (*connect.Response[v1.RevertSelectiveClaimResponse], error) {
	return c.revertSelectiveClaim.CallUnary(ctx, req)
}

// ListCalls calls hero.conversation.v1.CellularCallService.ListCalls.
func (c *cellularCallServiceClient) ListCalls(ctx context.Context, req *connect.Request[v1.ListCallsRequest]) (*connect.Response[v1.ListCallsResponse], error) {
	return c.listCalls.CallUnary(ctx, req)
}

// UpdateCallerName calls hero.conversation.v1.CellularCallService.UpdateCallerName.
func (c *cellularCallServiceClient) UpdateCallerName(ctx context.Context, req *connect.Request[v1.UpdateCallerNameRequest]) (*connect.Response[v1.UpdateCallerNameResponse], error) {
	return c.updateCallerName.CallUnary(ctx, req)
}

// CellularCallServiceHandler is an implementation of the hero.conversation.v1.CellularCallService
// service.
type CellularCallServiceHandler interface {
	// Generate a Twilio Cellular Access Token for voice calls
	GetCellularCallAccessToken(context.Context, *connect.Request[v1.GetCellularCallAccessTokenRequest]) (*connect.Response[v1.GetCellularCallAccessTokenResponse], error)
	// Handle incoming or outgoing cellular calls and return a TwiML response
	HandleCall(context.Context, *connect.Request[v1.HandleCallRequest]) (*connect.Response[v1.HandleCallResponse], error)
	// Place a call in the queue with TwiML instructions for waiting
	QueueCall(context.Context, *connect.Request[v1.QueueCallRequest]) (*connect.Response[v1.QueueCallResponse], error)
	// Connect an asset (agent) to the next call in queue
	DequeueCall(context.Context, *connect.Request[v1.DequeueCallRequest]) (*connect.Response[v1.DequeueCallResponse], error)
	// Connect an asset (agent) to a specific call by SID
	DequeueCallBySid(context.Context, *connect.Request[v1.DequeueCallBySidRequest]) (*connect.Response[v1.DequeueCallBySidResponse], error)
	// Get current status of the call queue (size, next caller)
	GetQueueStatus(context.Context, *connect.Request[v1.GetQueueStatusRequest]) (*connect.Response[v1.GetQueueStatusResponse], error)
	// Place an active call on hold
	HoldCall(context.Context, *connect.Request[v1.HoldCallRequest]) (*connect.Response[v1.HoldCallResponse], error)
	// Get calls on hold for a specific asset (agent)
	GetAssetHeldCalls(context.Context, *connect.Request[v1.GetAssetHeldCallsRequest]) (*connect.Response[v1.GetAssetHeldCallsResponse], error)
	// Resume a call from hold
	ResumeCall(context.Context, *connect.Request[v1.ResumeCallRequest]) (*connect.Response[v1.ResumeCallResponse], error)
	// End an active call
	EndCall(context.Context, *connect.Request[v1.EndCallRequest]) (*connect.Response[v1.EndCallResponse], error)
	// Get the situation associated with a call
	GetSituationForCall(context.Context, *connect.Request[v1.GetSituationForCallRequest]) (*connect.Response[v1.GetSituationForCallResponse], error)
	// Revert a call from pending_selective_assignment back to waiting state
	RevertSelectiveClaim(context.Context, *connect.Request[v1.RevertSelectiveClaimRequest]) (*connect.Response[v1.RevertSelectiveClaimResponse], error)
	// List calls with filtering and pagination
	ListCalls(context.Context, *connect.Request[v1.ListCallsRequest]) (*connect.Response[v1.ListCallsResponse], error)
	// Update the caller name for a specific call
	UpdateCallerName(context.Context, *connect.Request[v1.UpdateCallerNameRequest]) (*connect.Response[v1.UpdateCallerNameResponse], error)
}

// NewCellularCallServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCellularCallServiceHandler(svc CellularCallServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	cellularCallServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("CellularCallService").Methods()
	cellularCallServiceGetCellularCallAccessTokenHandler := connect.NewUnaryHandler(
		CellularCallServiceGetCellularCallAccessTokenProcedure,
		svc.GetCellularCallAccessToken,
		connect.WithSchema(cellularCallServiceMethods.ByName("GetCellularCallAccessToken")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceHandleCallHandler := connect.NewUnaryHandler(
		CellularCallServiceHandleCallProcedure,
		svc.HandleCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("HandleCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceQueueCallHandler := connect.NewUnaryHandler(
		CellularCallServiceQueueCallProcedure,
		svc.QueueCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("QueueCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceDequeueCallHandler := connect.NewUnaryHandler(
		CellularCallServiceDequeueCallProcedure,
		svc.DequeueCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("DequeueCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceDequeueCallBySidHandler := connect.NewUnaryHandler(
		CellularCallServiceDequeueCallBySidProcedure,
		svc.DequeueCallBySid,
		connect.WithSchema(cellularCallServiceMethods.ByName("DequeueCallBySid")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceGetQueueStatusHandler := connect.NewUnaryHandler(
		CellularCallServiceGetQueueStatusProcedure,
		svc.GetQueueStatus,
		connect.WithSchema(cellularCallServiceMethods.ByName("GetQueueStatus")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceHoldCallHandler := connect.NewUnaryHandler(
		CellularCallServiceHoldCallProcedure,
		svc.HoldCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("HoldCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceGetAssetHeldCallsHandler := connect.NewUnaryHandler(
		CellularCallServiceGetAssetHeldCallsProcedure,
		svc.GetAssetHeldCalls,
		connect.WithSchema(cellularCallServiceMethods.ByName("GetAssetHeldCalls")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceResumeCallHandler := connect.NewUnaryHandler(
		CellularCallServiceResumeCallProcedure,
		svc.ResumeCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("ResumeCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceEndCallHandler := connect.NewUnaryHandler(
		CellularCallServiceEndCallProcedure,
		svc.EndCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("EndCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceGetSituationForCallHandler := connect.NewUnaryHandler(
		CellularCallServiceGetSituationForCallProcedure,
		svc.GetSituationForCall,
		connect.WithSchema(cellularCallServiceMethods.ByName("GetSituationForCall")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceRevertSelectiveClaimHandler := connect.NewUnaryHandler(
		CellularCallServiceRevertSelectiveClaimProcedure,
		svc.RevertSelectiveClaim,
		connect.WithSchema(cellularCallServiceMethods.ByName("RevertSelectiveClaim")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceListCallsHandler := connect.NewUnaryHandler(
		CellularCallServiceListCallsProcedure,
		svc.ListCalls,
		connect.WithSchema(cellularCallServiceMethods.ByName("ListCalls")),
		connect.WithHandlerOptions(opts...),
	)
	cellularCallServiceUpdateCallerNameHandler := connect.NewUnaryHandler(
		CellularCallServiceUpdateCallerNameProcedure,
		svc.UpdateCallerName,
		connect.WithSchema(cellularCallServiceMethods.ByName("UpdateCallerName")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.conversation.v1.CellularCallService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CellularCallServiceGetCellularCallAccessTokenProcedure:
			cellularCallServiceGetCellularCallAccessTokenHandler.ServeHTTP(w, r)
		case CellularCallServiceHandleCallProcedure:
			cellularCallServiceHandleCallHandler.ServeHTTP(w, r)
		case CellularCallServiceQueueCallProcedure:
			cellularCallServiceQueueCallHandler.ServeHTTP(w, r)
		case CellularCallServiceDequeueCallProcedure:
			cellularCallServiceDequeueCallHandler.ServeHTTP(w, r)
		case CellularCallServiceDequeueCallBySidProcedure:
			cellularCallServiceDequeueCallBySidHandler.ServeHTTP(w, r)
		case CellularCallServiceGetQueueStatusProcedure:
			cellularCallServiceGetQueueStatusHandler.ServeHTTP(w, r)
		case CellularCallServiceHoldCallProcedure:
			cellularCallServiceHoldCallHandler.ServeHTTP(w, r)
		case CellularCallServiceGetAssetHeldCallsProcedure:
			cellularCallServiceGetAssetHeldCallsHandler.ServeHTTP(w, r)
		case CellularCallServiceResumeCallProcedure:
			cellularCallServiceResumeCallHandler.ServeHTTP(w, r)
		case CellularCallServiceEndCallProcedure:
			cellularCallServiceEndCallHandler.ServeHTTP(w, r)
		case CellularCallServiceGetSituationForCallProcedure:
			cellularCallServiceGetSituationForCallHandler.ServeHTTP(w, r)
		case CellularCallServiceRevertSelectiveClaimProcedure:
			cellularCallServiceRevertSelectiveClaimHandler.ServeHTTP(w, r)
		case CellularCallServiceListCallsProcedure:
			cellularCallServiceListCallsHandler.ServeHTTP(w, r)
		case CellularCallServiceUpdateCallerNameProcedure:
			cellularCallServiceUpdateCallerNameHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCellularCallServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCellularCallServiceHandler struct{}

func (UnimplementedCellularCallServiceHandler) GetCellularCallAccessToken(context.Context, *connect.Request[v1.GetCellularCallAccessTokenRequest]) (*connect.Response[v1.GetCellularCallAccessTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.GetCellularCallAccessToken is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) HandleCall(context.Context, *connect.Request[v1.HandleCallRequest]) (*connect.Response[v1.HandleCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.HandleCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) QueueCall(context.Context, *connect.Request[v1.QueueCallRequest]) (*connect.Response[v1.QueueCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.QueueCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) DequeueCall(context.Context, *connect.Request[v1.DequeueCallRequest]) (*connect.Response[v1.DequeueCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.DequeueCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) DequeueCallBySid(context.Context, *connect.Request[v1.DequeueCallBySidRequest]) (*connect.Response[v1.DequeueCallBySidResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.DequeueCallBySid is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) GetQueueStatus(context.Context, *connect.Request[v1.GetQueueStatusRequest]) (*connect.Response[v1.GetQueueStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.GetQueueStatus is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) HoldCall(context.Context, *connect.Request[v1.HoldCallRequest]) (*connect.Response[v1.HoldCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.HoldCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) GetAssetHeldCalls(context.Context, *connect.Request[v1.GetAssetHeldCallsRequest]) (*connect.Response[v1.GetAssetHeldCallsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.GetAssetHeldCalls is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) ResumeCall(context.Context, *connect.Request[v1.ResumeCallRequest]) (*connect.Response[v1.ResumeCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.ResumeCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) EndCall(context.Context, *connect.Request[v1.EndCallRequest]) (*connect.Response[v1.EndCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.EndCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) GetSituationForCall(context.Context, *connect.Request[v1.GetSituationForCallRequest]) (*connect.Response[v1.GetSituationForCallResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.GetSituationForCall is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) RevertSelectiveClaim(context.Context, *connect.Request[v1.RevertSelectiveClaimRequest]) (*connect.Response[v1.RevertSelectiveClaimResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.RevertSelectiveClaim is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) ListCalls(context.Context, *connect.Request[v1.ListCallsRequest]) (*connect.Response[v1.ListCallsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.ListCalls is not implemented"))
}

func (UnimplementedCellularCallServiceHandler) UpdateCallerName(context.Context, *connect.Request[v1.UpdateCallerNameRequest]) (*connect.Response[v1.UpdateCallerNameResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.CellularCallService.UpdateCallerName is not implemented"))
}

// PTTServiceClient is a client for the hero.conversation.v1.PTTService service.
type PTTServiceClient interface {
	// Get Zello channel history metadata
	GetHistoryMetadata(context.Context, *connect.Request[v1.GetHistoryMetadataRequest]) (*connect.Response[v1.GetHistoryMetadataResponse], error)
}

// NewPTTServiceClient constructs a client for the hero.conversation.v1.PTTService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPTTServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PTTServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	pTTServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("PTTService").Methods()
	return &pTTServiceClient{
		getHistoryMetadata: connect.NewClient[v1.GetHistoryMetadataRequest, v1.GetHistoryMetadataResponse](
			httpClient,
			baseURL+PTTServiceGetHistoryMetadataProcedure,
			connect.WithSchema(pTTServiceMethods.ByName("GetHistoryMetadata")),
			connect.WithClientOptions(opts...),
		),
	}
}

// pTTServiceClient implements PTTServiceClient.
type pTTServiceClient struct {
	getHistoryMetadata *connect.Client[v1.GetHistoryMetadataRequest, v1.GetHistoryMetadataResponse]
}

// GetHistoryMetadata calls hero.conversation.v1.PTTService.GetHistoryMetadata.
func (c *pTTServiceClient) GetHistoryMetadata(ctx context.Context, req *connect.Request[v1.GetHistoryMetadataRequest]) (*connect.Response[v1.GetHistoryMetadataResponse], error) {
	return c.getHistoryMetadata.CallUnary(ctx, req)
}

// PTTServiceHandler is an implementation of the hero.conversation.v1.PTTService service.
type PTTServiceHandler interface {
	// Get Zello channel history metadata
	GetHistoryMetadata(context.Context, *connect.Request[v1.GetHistoryMetadataRequest]) (*connect.Response[v1.GetHistoryMetadataResponse], error)
}

// NewPTTServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPTTServiceHandler(svc PTTServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	pTTServiceMethods := v1.File_hero_communications_v1_conversation_proto.Services().ByName("PTTService").Methods()
	pTTServiceGetHistoryMetadataHandler := connect.NewUnaryHandler(
		PTTServiceGetHistoryMetadataProcedure,
		svc.GetHistoryMetadata,
		connect.WithSchema(pTTServiceMethods.ByName("GetHistoryMetadata")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.conversation.v1.PTTService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PTTServiceGetHistoryMetadataProcedure:
			pTTServiceGetHistoryMetadataHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPTTServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPTTServiceHandler struct{}

func (UnimplementedPTTServiceHandler) GetHistoryMetadata(context.Context, *connect.Request[v1.GetHistoryMetadataRequest]) (*connect.Response[v1.GetHistoryMetadataResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.conversation.v1.PTTService.GetHistoryMetadata is not implemented"))
}
