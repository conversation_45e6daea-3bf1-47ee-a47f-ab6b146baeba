// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/entity/v1/entity.proto (package hero.entity.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty, file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/entity/v1/entity.proto.
 */
export const file_hero_entity_v1_entity: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_struct, file_google_protobuf_empty]);

/**
 * Reference captures a relationship between an entity (or schema) and an external object.
 *
 * @generated from message hero.entity.v1.Reference
 */
export type Reference = Message<"hero.entity.v1.Reference"> & {
  /**
   * Unique identifier for the referenced object.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Type of the referenced object (e.g., "entity", "report", etc.).
   *
   * @generated from field: string type = 2;
   */
  type: string;

  /**
   * Version of the referenced object.
   *
   * @generated from field: int32 version = 3;
   */
  version: number;

  /**
   * Optional display name for the reference.
   *
   * @generated from field: string display_name = 4;
   */
  displayName: string;

  /**
   * Type of relation between the referencing and referenced objects.
   *
   * @generated from field: string relation_type = 5;
   */
  relationType: string;
};

/**
 * Describes the message hero.entity.v1.Reference.
 * Use `create(ReferenceSchema)` to create a new message.
 */
export const ReferenceSchema: GenMessage<Reference> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 0);

/**
 * EntitySchema defines a JSON schema used to validate dynamic entity data.
 * It includes audit information and meta-data to support versioning.
 *
 * @generated from message hero.entity.v1.EntitySchema
 */
export type EntitySchema = Message<"hero.entity.v1.EntitySchema"> & {
  /**
   * Unique identifier for the schema.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Organization identifier that owns this schema.
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Friendly name for the schema.
   *
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * Description of the schema.
   *
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * Detailed JSON schema for validating entity data.
   *
   * @generated from field: google.protobuf.Struct schema_definition = 5;
   */
  schemaDefinition?: JsonObject;

  /**
   * ISO8601 creation timestamp.
   *
   * @generated from field: string create_time = 6;
   */
  createTime: string;

  /**
   * ISO8601 last update timestamp.
   *
   * @generated from field: string update_time = 7;
   */
  updateTime: string;

  /**
   * User ID who created the schema.
   *
   * @generated from field: string created_by = 8;
   */
  createdBy: string;

  /**
   * User ID who last updated the schema.
   *
   * @generated from field: string updated_by = 9;
   */
  updatedBy: string;

  /**
   * Version number of the schema; incremented upon changes.
   *
   * @generated from field: int32 version = 10;
   */
  version: number;

  /**
   * The type of entity defined by this schema.
   *
   * @generated from field: hero.entity.v1.EntityType entity_type = 11;
   */
  entityType: EntityType;

  /**
   * Current status of the schema.
   *
   * @generated from field: hero.entity.v1.RecordStatus status = 12;
   */
  status: RecordStatus;

  /**
   * Optional tags for categorization.
   *
   * @generated from field: repeated string tags = 13;
   */
  tags: string[];

  /**
   * For EntitySchema it will be fixed value "ENTITY_SCHEMA".
   *
   * @generated from field: string resource_type = 14;
   */
  resourceType: string;
};

/**
 * Describes the message hero.entity.v1.EntitySchema.
 * Use `create(EntitySchemaSchema)` to create a new message.
 */
export const EntitySchemaSchema: GenMessage<EntitySchema> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 1);

/**
 * EntitySchemaVersion stores a historical snapshot of an EntitySchema.
 * This supports audit trails and rollback capabilities.
 *
 * @generated from message hero.entity.v1.EntitySchemaVersion
 */
export type EntitySchemaVersion = Message<"hero.entity.v1.EntitySchemaVersion"> & {
  /**
   * Associated schema identifier.
   *
   * @generated from field: string schema_id = 1;
   */
  schemaId: string;

  /**
   * Version number of this snapshot.
   *
   * @generated from field: int32 version = 2;
   */
  version: number;

  /**
   * The state of the schema at this version.
   *
   * @generated from field: hero.entity.v1.EntitySchema schema_snapshot = 3;
   */
  schemaSnapshot?: EntitySchema;

  /**
   * User ID who made the change.
   *
   * @generated from field: string modified_by = 4;
   */
  modifiedBy: string;

  /**
   * ISO8601 formatted timestamp when the change occurred.
   *
   * @generated from field: string modified_time = 5;
   */
  modifiedTime: string;

  /**
   * Optional comment describing the change.
   *
   * @generated from field: string change_comment = 6;
   */
  changeComment: string;
};

/**
 * Describes the message hero.entity.v1.EntitySchemaVersion.
 * Use `create(EntitySchemaVersionSchema)` to create a new message.
 */
export const EntitySchemaVersionSchema: GenMessage<EntitySchemaVersion> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 2);

/**
 * CreateEntitySchemaRequest is used to add a new EntitySchema.
 *
 * @generated from message hero.entity.v1.CreateEntitySchemaRequest
 */
export type CreateEntitySchemaRequest = Message<"hero.entity.v1.CreateEntitySchemaRequest"> & {
  /**
   * @generated from field: hero.entity.v1.EntitySchema schema = 1;
   */
  schema?: EntitySchema;
};

/**
 * Describes the message hero.entity.v1.CreateEntitySchemaRequest.
 * Use `create(CreateEntitySchemaRequestSchema)` to create a new message.
 */
export const CreateEntitySchemaRequestSchema: GenMessage<CreateEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 3);

/**
 * @generated from message hero.entity.v1.CreateEntitySchemaResponse
 */
export type CreateEntitySchemaResponse = Message<"hero.entity.v1.CreateEntitySchemaResponse"> & {
  /**
   * @generated from field: hero.entity.v1.EntitySchema schema = 1;
   */
  schema?: EntitySchema;
};

/**
 * Describes the message hero.entity.v1.CreateEntitySchemaResponse.
 * Use `create(CreateEntitySchemaResponseSchema)` to create a new message.
 */
export const CreateEntitySchemaResponseSchema: GenMessage<CreateEntitySchemaResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 4);

/**
 * GetLatestEntitySchemaRequest retrieves the latest version of an EntitySchema using its ID.
 *
 * @generated from message hero.entity.v1.GetLatestEntitySchemaRequest
 */
export type GetLatestEntitySchemaRequest = Message<"hero.entity.v1.GetLatestEntitySchemaRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.entity.v1.GetLatestEntitySchemaRequest.
 * Use `create(GetLatestEntitySchemaRequestSchema)` to create a new message.
 */
export const GetLatestEntitySchemaRequestSchema: GenMessage<GetLatestEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 5);

/**
 * GetEntitySchemaByVersionRequest retrieves a specific version of an EntitySchema.
 *
 * @generated from message hero.entity.v1.GetEntitySchemaByVersionRequest
 */
export type GetEntitySchemaByVersionRequest = Message<"hero.entity.v1.GetEntitySchemaByVersionRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.entity.v1.GetEntitySchemaByVersionRequest.
 * Use `create(GetEntitySchemaByVersionRequestSchema)` to create a new message.
 */
export const GetEntitySchemaByVersionRequestSchema: GenMessage<GetEntitySchemaByVersionRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 6);

/**
 * GetLatestActiveEntitySchema retrieves the latest active version of an EntitySchema.
 *
 * @generated from message hero.entity.v1.GetLatestActiveEntitySchemaRequest
 */
export type GetLatestActiveEntitySchemaRequest = Message<"hero.entity.v1.GetLatestActiveEntitySchemaRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.entity.v1.GetLatestActiveEntitySchemaRequest.
 * Use `create(GetLatestActiveEntitySchemaRequestSchema)` to create a new message.
 */
export const GetLatestActiveEntitySchemaRequestSchema: GenMessage<GetLatestActiveEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 7);

/**
 * UpdateEntitySchemaRequest updates an existing EntitySchema.
 *
 * @generated from message hero.entity.v1.UpdateEntitySchemaRequest
 */
export type UpdateEntitySchemaRequest = Message<"hero.entity.v1.UpdateEntitySchemaRequest"> & {
  /**
   * @generated from field: hero.entity.v1.EntitySchema schema = 1;
   */
  schema?: EntitySchema;
};

/**
 * Describes the message hero.entity.v1.UpdateEntitySchemaRequest.
 * Use `create(UpdateEntitySchemaRequestSchema)` to create a new message.
 */
export const UpdateEntitySchemaRequestSchema: GenMessage<UpdateEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 8);

/**
 * DeleteAllVersionsOfEntitySchemaRequest deletes every version of a schema.
 *
 * @generated from message hero.entity.v1.DeleteAllVersionsOfEntitySchemaRequest
 */
export type DeleteAllVersionsOfEntitySchemaRequest = Message<"hero.entity.v1.DeleteAllVersionsOfEntitySchemaRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.entity.v1.DeleteAllVersionsOfEntitySchemaRequest.
 * Use `create(DeleteAllVersionsOfEntitySchemaRequestSchema)` to create a new message.
 */
export const DeleteAllVersionsOfEntitySchemaRequestSchema: GenMessage<DeleteAllVersionsOfEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 9);

/**
 * DeleteSpecificVersionOfEntitySchemaRequest deletes a particular version of a schema.
 *
 * @generated from message hero.entity.v1.DeleteSpecificVersionOfEntitySchemaRequest
 */
export type DeleteSpecificVersionOfEntitySchemaRequest = Message<"hero.entity.v1.DeleteSpecificVersionOfEntitySchemaRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.entity.v1.DeleteSpecificVersionOfEntitySchemaRequest.
 * Use `create(DeleteSpecificVersionOfEntitySchemaRequestSchema)` to create a new message.
 */
export const DeleteSpecificVersionOfEntitySchemaRequestSchema: GenMessage<DeleteSpecificVersionOfEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 10);

/**
 * ListLatestEntitySchemasRequest lists the current versions of schemas with optional filtering by EntityType.
 *
 * @generated from message hero.entity.v1.ListLatestEntitySchemasRequest
 */
export type ListLatestEntitySchemasRequest = Message<"hero.entity.v1.ListLatestEntitySchemasRequest"> & {
  /**
   * Maximum number of schemas per page.
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * Token to fetch a specific page.
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * If not set or set to ENTITY_TYPE_UNSPECIFIED, all schemas are returned.
   *
   * @generated from field: hero.entity.v1.EntityType entity_type = 3;
   */
  entityType: EntityType;
};

/**
 * Describes the message hero.entity.v1.ListLatestEntitySchemasRequest.
 * Use `create(ListLatestEntitySchemasRequestSchema)` to create a new message.
 */
export const ListLatestEntitySchemasRequestSchema: GenMessage<ListLatestEntitySchemasRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 11);

/**
 * @generated from message hero.entity.v1.ListLatestEntitySchemasResponse
 */
export type ListLatestEntitySchemasResponse = Message<"hero.entity.v1.ListLatestEntitySchemasResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntitySchema schemas = 1;
   */
  schemas: EntitySchema[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.entity.v1.ListLatestEntitySchemasResponse.
 * Use `create(ListLatestEntitySchemasResponseSchema)` to create a new message.
 */
export const ListLatestEntitySchemasResponseSchema: GenMessage<ListLatestEntitySchemasResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 12);

/**
 * @generated from message hero.entity.v1.ListAllVersionsOfEntitySchemaRequest
 */
export type ListAllVersionsOfEntitySchemaRequest = Message<"hero.entity.v1.ListAllVersionsOfEntitySchemaRequest"> & {
  /**
   * Unique identifier for the schema.
   *
   * @generated from field: string schema_id = 1;
   */
  schemaId: string;
};

/**
 * Describes the message hero.entity.v1.ListAllVersionsOfEntitySchemaRequest.
 * Use `create(ListAllVersionsOfEntitySchemaRequestSchema)` to create a new message.
 */
export const ListAllVersionsOfEntitySchemaRequestSchema: GenMessage<ListAllVersionsOfEntitySchemaRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 13);

/**
 * @generated from message hero.entity.v1.ListAllVersionsOfEntitySchemaResponse
 */
export type ListAllVersionsOfEntitySchemaResponse = Message<"hero.entity.v1.ListAllVersionsOfEntitySchemaResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntitySchemaVersion schema_versions = 1;
   */
  schemaVersions: EntitySchemaVersion[];
};

/**
 * Describes the message hero.entity.v1.ListAllVersionsOfEntitySchemaResponse.
 * Use `create(ListAllVersionsOfEntitySchemaResponseSchema)` to create a new message.
 */
export const ListAllVersionsOfEntitySchemaResponseSchema: GenMessage<ListAllVersionsOfEntitySchemaResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 14);

/**
 * BulkCreateEntitySchemas allows creation of multiple schemas in one request.
 *
 * @generated from message hero.entity.v1.BulkCreateEntitySchemasRequest
 */
export type BulkCreateEntitySchemasRequest = Message<"hero.entity.v1.BulkCreateEntitySchemasRequest"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntitySchema schemas = 1;
   */
  schemas: EntitySchema[];
};

/**
 * Describes the message hero.entity.v1.BulkCreateEntitySchemasRequest.
 * Use `create(BulkCreateEntitySchemasRequestSchema)` to create a new message.
 */
export const BulkCreateEntitySchemasRequestSchema: GenMessage<BulkCreateEntitySchemasRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 15);

/**
 * @generated from message hero.entity.v1.BulkCreateEntitySchemasResponse
 */
export type BulkCreateEntitySchemasResponse = Message<"hero.entity.v1.BulkCreateEntitySchemasResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntitySchema schemas = 1;
   */
  schemas: EntitySchema[];
};

/**
 * Describes the message hero.entity.v1.BulkCreateEntitySchemasResponse.
 * Use `create(BulkCreateEntitySchemasResponseSchema)` to create a new message.
 */
export const BulkCreateEntitySchemasResponseSchema: GenMessage<BulkCreateEntitySchemasResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 16);

/**
 * BulkUpdateEntitySchemas allows updating multiple schemas in one request.
 *
 * @generated from message hero.entity.v1.BulkUpdateEntitySchemasRequest
 */
export type BulkUpdateEntitySchemasRequest = Message<"hero.entity.v1.BulkUpdateEntitySchemasRequest"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntitySchema schemas = 1;
   */
  schemas: EntitySchema[];
};

/**
 * Describes the message hero.entity.v1.BulkUpdateEntitySchemasRequest.
 * Use `create(BulkUpdateEntitySchemasRequestSchema)` to create a new message.
 */
export const BulkUpdateEntitySchemasRequestSchema: GenMessage<BulkUpdateEntitySchemasRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 17);

/**
 * @generated from message hero.entity.v1.BulkUpdateEntitySchemasResponse
 */
export type BulkUpdateEntitySchemasResponse = Message<"hero.entity.v1.BulkUpdateEntitySchemasResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntitySchema schemas = 1;
   */
  schemas: EntitySchema[];
};

/**
 * Describes the message hero.entity.v1.BulkUpdateEntitySchemasResponse.
 * Use `create(BulkUpdateEntitySchemasResponseSchema)` to create a new message.
 */
export const BulkUpdateEntitySchemasResponseSchema: GenMessage<BulkUpdateEntitySchemasResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 18);

/**
 * BulkDeleteEntitySchemas enables deletion of multiple schemas at once.
 *
 * @generated from message hero.entity.v1.BulkDeleteEntitySchemasRequest
 */
export type BulkDeleteEntitySchemasRequest = Message<"hero.entity.v1.BulkDeleteEntitySchemasRequest"> & {
  /**
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];
};

/**
 * Describes the message hero.entity.v1.BulkDeleteEntitySchemasRequest.
 * Use `create(BulkDeleteEntitySchemasRequestSchema)` to create a new message.
 */
export const BulkDeleteEntitySchemasRequestSchema: GenMessage<BulkDeleteEntitySchemasRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 19);

/**
 * RestoreEntitySchemaVersion allows reverting an EntitySchema to a previous version.
 *
 * @generated from message hero.entity.v1.RestoreEntitySchemaVersionRequest
 */
export type RestoreEntitySchemaVersionRequest = Message<"hero.entity.v1.RestoreEntitySchemaVersionRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.entity.v1.RestoreEntitySchemaVersionRequest.
 * Use `create(RestoreEntitySchemaVersionRequestSchema)` to create a new message.
 */
export const RestoreEntitySchemaVersionRequestSchema: GenMessage<RestoreEntitySchemaVersionRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 20);

/**
 * @generated from message hero.entity.v1.RestoreEntitySchemaVersionResponse
 */
export type RestoreEntitySchemaVersionResponse = Message<"hero.entity.v1.RestoreEntitySchemaVersionResponse"> & {
  /**
   * @generated from field: hero.entity.v1.EntitySchema schema = 1;
   */
  schema?: EntitySchema;
};

/**
 * Describes the message hero.entity.v1.RestoreEntitySchemaVersionResponse.
 * Use `create(RestoreEntitySchemaVersionResponseSchema)` to create a new message.
 */
export const RestoreEntitySchemaVersionResponseSchema: GenMessage<RestoreEntitySchemaVersionResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 21);

/**
 * DiffEntitySchemaVersions computes and returns differences between two versions of a schema.
 *
 * @generated from message hero.entity.v1.DiffEntitySchemaVersionsRequest
 */
export type DiffEntitySchemaVersionsRequest = Message<"hero.entity.v1.DiffEntitySchemaVersionsRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version1 = 2;
   */
  version1: number;

  /**
   * @generated from field: int32 version2 = 3;
   */
  version2: number;
};

/**
 * Describes the message hero.entity.v1.DiffEntitySchemaVersionsRequest.
 * Use `create(DiffEntitySchemaVersionsRequestSchema)` to create a new message.
 */
export const DiffEntitySchemaVersionsRequestSchema: GenMessage<DiffEntitySchemaVersionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 22);

/**
 * @generated from message hero.entity.v1.DiffEntitySchemaVersionsResponse
 */
export type DiffEntitySchemaVersionsResponse = Message<"hero.entity.v1.DiffEntitySchemaVersionsResponse"> & {
  /**
   * The computed diff output.
   *
   * @generated from field: string diff_result = 1;
   */
  diffResult: string;
};

/**
 * Describes the message hero.entity.v1.DiffEntitySchemaVersionsResponse.
 * Use `create(DiffEntitySchemaVersionsResponseSchema)` to create a new message.
 */
export const DiffEntitySchemaVersionsResponseSchema: GenMessage<DiffEntitySchemaVersionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 23);

/**
 * CheckEntitySchemaPermissions verifies whether a user can perform a specific action on a schema.
 *
 * @generated from message hero.entity.v1.CheckEntitySchemaPermissionsRequest
 */
export type CheckEntitySchemaPermissionsRequest = Message<"hero.entity.v1.CheckEntitySchemaPermissionsRequest"> & {
  /**
   * @generated from field: string schema_id = 1;
   */
  schemaId: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * e.g., "read", "write", "delete"
   *
   * @generated from field: string action = 3;
   */
  action: string;
};

/**
 * Describes the message hero.entity.v1.CheckEntitySchemaPermissionsRequest.
 * Use `create(CheckEntitySchemaPermissionsRequestSchema)` to create a new message.
 */
export const CheckEntitySchemaPermissionsRequestSchema: GenMessage<CheckEntitySchemaPermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 24);

/**
 * @generated from message hero.entity.v1.CheckEntitySchemaPermissionsResponse
 */
export type CheckEntitySchemaPermissionsResponse = Message<"hero.entity.v1.CheckEntitySchemaPermissionsResponse"> & {
  /**
   * @generated from field: bool allowed = 1;
   */
  allowed: boolean;
};

/**
 * Describes the message hero.entity.v1.CheckEntitySchemaPermissionsResponse.
 * Use `create(CheckEntitySchemaPermissionsResponseSchema)` to create a new message.
 */
export const CheckEntitySchemaPermissionsResponseSchema: GenMessage<CheckEntitySchemaPermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 25);

/**
 * Entity represents a record whose structure is defined by an EntitySchema.
 * It includes audit metadata to support versioning and historical tracking.
 *
 * @generated from message hero.entity.v1.Entity
 */
export type Entity = Message<"hero.entity.v1.Entity"> & {
  /**
   * Unique identifier for the entity.
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Organization that owns the entity.
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Identifier of the schema for this entity.
   *
   * @generated from field: string schema_id = 3;
   */
  schemaId: string;

  /**
   * The version of the schema used.
   *
   * @generated from field: int32 schema_version = 4;
   */
  schemaVersion: number;

  /**
   * The dynamic JSON data.
   *
   * @generated from field: google.protobuf.Struct data = 5;
   */
  data?: JsonObject;

  /**
   * Links to other entities or external objects.
   *
   * @generated from field: repeated hero.entity.v1.Reference references = 6;
   */
  references: Reference[];

  /**
   * ISO8601 creation timestamp.
   *
   * @generated from field: string create_time = 7;
   */
  createTime: string;

  /**
   * ISO8601 last update timestamp.
   *
   * @generated from field: string update_time = 8;
   */
  updateTime: string;

  /**
   * Type classification of the entity.
   *
   * @generated from field: hero.entity.v1.EntityType entity_type = 9;
   */
  entityType: EntityType;

  /**
   * User ID of the creator.
   *
   * @generated from field: string created_by = 10;
   */
  createdBy: string;

  /**
   * User ID of the last updater.
   *
   * @generated from field: string updated_by = 11;
   */
  updatedBy: string;

  /**
   * Version number (for audit/history purposes).
   *
   * @generated from field: int32 version = 12;
   */
  version: number;

  /**
   * Current operational status.
   *
   * @generated from field: hero.entity.v1.RecordStatus status = 13;
   */
  status: RecordStatus;

  /**
   * Optional tags.
   *
   * @generated from field: repeated string tags = 14;
   */
  tags: string[];

  /**
   * For Entity it will be fixed value "ENTITY".
   *
   * @generated from field: string resource_type = 15;
   */
  resourceType: string;
};

/**
 * Describes the message hero.entity.v1.Entity.
 * Use `create(EntitySchema$)` to create a new message.
 */
export const EntitySchema$: GenMessage<Entity> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 26);

/**
 * EntityVersion records a snapshot of an Entity at a specific version.
 * This facilitates audit trails and potential rollback operations.
 *
 * @generated from message hero.entity.v1.EntityVersion
 */
export type EntityVersion = Message<"hero.entity.v1.EntityVersion"> & {
  /**
   * Identifier for the entity.
   *
   * @generated from field: string entity_id = 1;
   */
  entityId: string;

  /**
   * Version number of the snapshot.
   *
   * @generated from field: int32 version = 2;
   */
  version: number;

  /**
   * The recorded state of the entity.
   *
   * @generated from field: hero.entity.v1.Entity entity_snapshot = 3;
   */
  entitySnapshot?: Entity;

  /**
   * User ID who made the change.
   *
   * @generated from field: string modified_by = 4;
   */
  modifiedBy: string;

  /**
   * ISO8601 formatted timestamp of the change.
   *
   * @generated from field: string modified_time = 5;
   */
  modifiedTime: string;

  /**
   * Optional comment explaining the change.
   *
   * @generated from field: string change_comment = 6;
   */
  changeComment: string;
};

/**
 * Describes the message hero.entity.v1.EntityVersion.
 * Use `create(EntityVersionSchema)` to create a new message.
 */
export const EntityVersionSchema: GenMessage<EntityVersion> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 27);

/**
 * CreateEntity creates a new dynamic entity.
 *
 * @generated from message hero.entity.v1.CreateEntityRequest
 */
export type CreateEntityRequest = Message<"hero.entity.v1.CreateEntityRequest"> & {
  /**
   * @generated from field: hero.entity.v1.Entity entity = 1;
   */
  entity?: Entity;
};

/**
 * Describes the message hero.entity.v1.CreateEntityRequest.
 * Use `create(CreateEntityRequestSchema)` to create a new message.
 */
export const CreateEntityRequestSchema: GenMessage<CreateEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 28);

/**
 * @generated from message hero.entity.v1.CreateEntityResponse
 */
export type CreateEntityResponse = Message<"hero.entity.v1.CreateEntityResponse"> & {
  /**
   * @generated from field: hero.entity.v1.Entity entity = 1;
   */
  entity?: Entity;
};

/**
 * Describes the message hero.entity.v1.CreateEntityResponse.
 * Use `create(CreateEntityResponseSchema)` to create a new message.
 */
export const CreateEntityResponseSchema: GenMessage<CreateEntityResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 29);

/**
 * GetLatestEntity retrieves the current version of an entity.
 *
 * @generated from message hero.entity.v1.GetLatestEntityRequest
 */
export type GetLatestEntityRequest = Message<"hero.entity.v1.GetLatestEntityRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.entity.v1.GetLatestEntityRequest.
 * Use `create(GetLatestEntityRequestSchema)` to create a new message.
 */
export const GetLatestEntityRequestSchema: GenMessage<GetLatestEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 30);

/**
 * GetEntityByVersion retrieves a specific historical version of an entity.
 *
 * @generated from message hero.entity.v1.GetEntityByVersionRequest
 */
export type GetEntityByVersionRequest = Message<"hero.entity.v1.GetEntityByVersionRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.entity.v1.GetEntityByVersionRequest.
 * Use `create(GetEntityByVersionRequestSchema)` to create a new message.
 */
export const GetEntityByVersionRequestSchema: GenMessage<GetEntityByVersionRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 31);

/**
 * GetLatestActiveEntity retrieves the latest entity that is marked as active.
 *
 * @generated from message hero.entity.v1.GetLatestActiveEntityRequest
 */
export type GetLatestActiveEntityRequest = Message<"hero.entity.v1.GetLatestActiveEntityRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.entity.v1.GetLatestActiveEntityRequest.
 * Use `create(GetLatestActiveEntityRequestSchema)` to create a new message.
 */
export const GetLatestActiveEntityRequestSchema: GenMessage<GetLatestActiveEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 32);

/**
 * @generated from message hero.entity.v1.BatchGetLatestEntitiesRequest
 */
export type BatchGetLatestEntitiesRequest = Message<"hero.entity.v1.BatchGetLatestEntitiesRequest"> & {
  /**
   * The list of entity IDs to retrieve.
   *
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];
};

/**
 * Describes the message hero.entity.v1.BatchGetLatestEntitiesRequest.
 * Use `create(BatchGetLatestEntitiesRequestSchema)` to create a new message.
 */
export const BatchGetLatestEntitiesRequestSchema: GenMessage<BatchGetLatestEntitiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 33);

/**
 * @generated from message hero.entity.v1.BatchGetLatestEntitiesResponse
 */
export type BatchGetLatestEntitiesResponse = Message<"hero.entity.v1.BatchGetLatestEntitiesResponse"> & {
  /**
   * The set of entities matching the provided IDs.
   *
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];
};

/**
 * Describes the message hero.entity.v1.BatchGetLatestEntitiesResponse.
 * Use `create(BatchGetLatestEntitiesResponseSchema)` to create a new message.
 */
export const BatchGetLatestEntitiesResponseSchema: GenMessage<BatchGetLatestEntitiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 34);

/**
 * UpdateEntity updates an existing entity. This request includes the entire updated entity.
 *
 * @generated from message hero.entity.v1.UpdateEntityRequest
 */
export type UpdateEntityRequest = Message<"hero.entity.v1.UpdateEntityRequest"> & {
  /**
   * @generated from field: hero.entity.v1.Entity entity = 1;
   */
  entity?: Entity;
};

/**
 * Describes the message hero.entity.v1.UpdateEntityRequest.
 * Use `create(UpdateEntityRequestSchema)` to create a new message.
 */
export const UpdateEntityRequestSchema: GenMessage<UpdateEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 35);

/**
 * DeleteAllVersionsOfEntity deletes every version of an entity.
 *
 * @generated from message hero.entity.v1.DeleteAllVersionsOfEntityRequest
 */
export type DeleteAllVersionsOfEntityRequest = Message<"hero.entity.v1.DeleteAllVersionsOfEntityRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.entity.v1.DeleteAllVersionsOfEntityRequest.
 * Use `create(DeleteAllVersionsOfEntityRequestSchema)` to create a new message.
 */
export const DeleteAllVersionsOfEntityRequestSchema: GenMessage<DeleteAllVersionsOfEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 36);

/**
 * DeleteSpecificVersionOfEntity deletes a particular version of an entity.
 *
 * @generated from message hero.entity.v1.DeleteSpecificVersionOfEntityRequest
 */
export type DeleteSpecificVersionOfEntityRequest = Message<"hero.entity.v1.DeleteSpecificVersionOfEntityRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.entity.v1.DeleteSpecificVersionOfEntityRequest.
 * Use `create(DeleteSpecificVersionOfEntityRequestSchema)` to create a new message.
 */
export const DeleteSpecificVersionOfEntityRequestSchema: GenMessage<DeleteSpecificVersionOfEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 37);

/**
 * ListLatestEntities lists the latest versions of entities with pagination and an optional type filter.
 *
 * @generated from message hero.entity.v1.ListLatestEntitiesRequest
 */
export type ListLatestEntitiesRequest = Message<"hero.entity.v1.ListLatestEntitiesRequest"> & {
  /**
   * Maximum number of entities per page.
   *
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * Token for pagination.
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * If not set or set to ENTITY_TYPE_UNSPECIFIED, all entities are returned.
   *
   * @generated from field: hero.entity.v1.EntityType entity_type = 3;
   */
  entityType: EntityType;
};

/**
 * Describes the message hero.entity.v1.ListLatestEntitiesRequest.
 * Use `create(ListLatestEntitiesRequestSchema)` to create a new message.
 */
export const ListLatestEntitiesRequestSchema: GenMessage<ListLatestEntitiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 38);

/**
 * @generated from message hero.entity.v1.ListLatestEntitiesResponse
 */
export type ListLatestEntitiesResponse = Message<"hero.entity.v1.ListLatestEntitiesResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.entity.v1.ListLatestEntitiesResponse.
 * Use `create(ListLatestEntitiesResponseSchema)` to create a new message.
 */
export const ListLatestEntitiesResponseSchema: GenMessage<ListLatestEntitiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 39);

/**
 * ListAllVersionsOfEntity returns all historical snapshots for an entity.
 *
 * @generated from message hero.entity.v1.ListAllVersionsOfEntityRequest
 */
export type ListAllVersionsOfEntityRequest = Message<"hero.entity.v1.ListAllVersionsOfEntityRequest"> & {
  /**
   * @generated from field: string entity_id = 1;
   */
  entityId: string;
};

/**
 * Describes the message hero.entity.v1.ListAllVersionsOfEntityRequest.
 * Use `create(ListAllVersionsOfEntityRequestSchema)` to create a new message.
 */
export const ListAllVersionsOfEntityRequestSchema: GenMessage<ListAllVersionsOfEntityRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 40);

/**
 * @generated from message hero.entity.v1.ListAllVersionsOfEntityResponse
 */
export type ListAllVersionsOfEntityResponse = Message<"hero.entity.v1.ListAllVersionsOfEntityResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.EntityVersion versions = 1;
   */
  versions: EntityVersion[];
};

/**
 * Describes the message hero.entity.v1.ListAllVersionsOfEntityResponse.
 * Use `create(ListAllVersionsOfEntityResponseSchema)` to create a new message.
 */
export const ListAllVersionsOfEntityResponseSchema: GenMessage<ListAllVersionsOfEntityResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 41);

/**
 * BulkCreateEntities allows you to create multiple entities in one request.
 *
 * @generated from message hero.entity.v1.BulkCreateEntitiesRequest
 */
export type BulkCreateEntitiesRequest = Message<"hero.entity.v1.BulkCreateEntitiesRequest"> & {
  /**
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];
};

/**
 * Describes the message hero.entity.v1.BulkCreateEntitiesRequest.
 * Use `create(BulkCreateEntitiesRequestSchema)` to create a new message.
 */
export const BulkCreateEntitiesRequestSchema: GenMessage<BulkCreateEntitiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 42);

/**
 * @generated from message hero.entity.v1.BulkCreateEntitiesResponse
 */
export type BulkCreateEntitiesResponse = Message<"hero.entity.v1.BulkCreateEntitiesResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];
};

/**
 * Describes the message hero.entity.v1.BulkCreateEntitiesResponse.
 * Use `create(BulkCreateEntitiesResponseSchema)` to create a new message.
 */
export const BulkCreateEntitiesResponseSchema: GenMessage<BulkCreateEntitiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 43);

/**
 * BulkUpdateEntities allows you to update several entities at once.
 * (No advanced concurrency handling is implemented for bulk updates.)
 *
 * @generated from message hero.entity.v1.BulkUpdateEntitiesRequest
 */
export type BulkUpdateEntitiesRequest = Message<"hero.entity.v1.BulkUpdateEntitiesRequest"> & {
  /**
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];
};

/**
 * Describes the message hero.entity.v1.BulkUpdateEntitiesRequest.
 * Use `create(BulkUpdateEntitiesRequestSchema)` to create a new message.
 */
export const BulkUpdateEntitiesRequestSchema: GenMessage<BulkUpdateEntitiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 44);

/**
 * @generated from message hero.entity.v1.BulkUpdateEntitiesResponse
 */
export type BulkUpdateEntitiesResponse = Message<"hero.entity.v1.BulkUpdateEntitiesResponse"> & {
  /**
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];
};

/**
 * Describes the message hero.entity.v1.BulkUpdateEntitiesResponse.
 * Use `create(BulkUpdateEntitiesResponseSchema)` to create a new message.
 */
export const BulkUpdateEntitiesResponseSchema: GenMessage<BulkUpdateEntitiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 45);

/**
 * BulkDeleteEntities enables the deletion of multiple entities in one request.
 *
 * @generated from message hero.entity.v1.BulkDeleteEntitiesRequest
 */
export type BulkDeleteEntitiesRequest = Message<"hero.entity.v1.BulkDeleteEntitiesRequest"> & {
  /**
   * @generated from field: repeated string ids = 1;
   */
  ids: string[];
};

/**
 * Describes the message hero.entity.v1.BulkDeleteEntitiesRequest.
 * Use `create(BulkDeleteEntitiesRequestSchema)` to create a new message.
 */
export const BulkDeleteEntitiesRequestSchema: GenMessage<BulkDeleteEntitiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 46);

/**
 * RestoreEntityVersion reverts an entity to a specified previous version.
 *
 * @generated from message hero.entity.v1.RestoreEntityVersionRequest
 */
export type RestoreEntityVersionRequest = Message<"hero.entity.v1.RestoreEntityVersionRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version = 2;
   */
  version: number;
};

/**
 * Describes the message hero.entity.v1.RestoreEntityVersionRequest.
 * Use `create(RestoreEntityVersionRequestSchema)` to create a new message.
 */
export const RestoreEntityVersionRequestSchema: GenMessage<RestoreEntityVersionRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 47);

/**
 * @generated from message hero.entity.v1.RestoreEntityVersionResponse
 */
export type RestoreEntityVersionResponse = Message<"hero.entity.v1.RestoreEntityVersionResponse"> & {
  /**
   * @generated from field: hero.entity.v1.Entity entity = 1;
   */
  entity?: Entity;
};

/**
 * Describes the message hero.entity.v1.RestoreEntityVersionResponse.
 * Use `create(RestoreEntityVersionResponseSchema)` to create a new message.
 */
export const RestoreEntityVersionResponseSchema: GenMessage<RestoreEntityVersionResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 48);

/**
 * DiffEntityVersions computes the differences between two versions of an entity.
 * The diff_result may be a textual report or a structured JSON diff.
 *
 * @generated from message hero.entity.v1.DiffEntityVersionsRequest
 */
export type DiffEntityVersionsRequest = Message<"hero.entity.v1.DiffEntityVersionsRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 version1 = 2;
   */
  version1: number;

  /**
   * @generated from field: int32 version2 = 3;
   */
  version2: number;
};

/**
 * Describes the message hero.entity.v1.DiffEntityVersionsRequest.
 * Use `create(DiffEntityVersionsRequestSchema)` to create a new message.
 */
export const DiffEntityVersionsRequestSchema: GenMessage<DiffEntityVersionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 49);

/**
 * @generated from message hero.entity.v1.DiffEntityVersionsResponse
 */
export type DiffEntityVersionsResponse = Message<"hero.entity.v1.DiffEntityVersionsResponse"> & {
  /**
   * @generated from field: string diff_result = 1;
   */
  diffResult: string;
};

/**
 * Describes the message hero.entity.v1.DiffEntityVersionsResponse.
 * Use `create(DiffEntityVersionsResponseSchema)` to create a new message.
 */
export const DiffEntityVersionsResponseSchema: GenMessage<DiffEntityVersionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 50);

/**
 * CheckEntityPermissions verifies if a user is allowed to perform a specific action (e.g., read, write, delete)
 * on an entity. This is used to enforce security policies.
 *
 * @generated from message hero.entity.v1.CheckEntityPermissionsRequest
 */
export type CheckEntityPermissionsRequest = Message<"hero.entity.v1.CheckEntityPermissionsRequest"> & {
  /**
   * @generated from field: string entity_id = 1;
   */
  entityId: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * e.g., "read", "write", "delete"
   *
   * @generated from field: string action = 3;
   */
  action: string;
};

/**
 * Describes the message hero.entity.v1.CheckEntityPermissionsRequest.
 * Use `create(CheckEntityPermissionsRequestSchema)` to create a new message.
 */
export const CheckEntityPermissionsRequestSchema: GenMessage<CheckEntityPermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 51);

/**
 * @generated from message hero.entity.v1.CheckEntityPermissionsResponse
 */
export type CheckEntityPermissionsResponse = Message<"hero.entity.v1.CheckEntityPermissionsResponse"> & {
  /**
   * @generated from field: bool allowed = 1;
   */
  allowed: boolean;
};

/**
 * Describes the message hero.entity.v1.CheckEntityPermissionsResponse.
 * Use `create(CheckEntityPermissionsResponseSchema)` to create a new message.
 */
export const CheckEntityPermissionsResponseSchema: GenMessage<CheckEntityPermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 52);

/**
 * DateRange represents a half-open timestamp range (inclusive).
 *
 * @generated from message hero.entity.v1.DateRange
 */
export type DateRange = Message<"hero.entity.v1.DateRange"> & {
  /**
   * RFC3339 timestamp, inclusive
   *
   * @generated from field: string from = 1;
   */
  from: string;

  /**
   * RFC3339 timestamp, inclusive
   *
   * @generated from field: string to = 2;
   */
  to: string;
};

/**
 * Describes the message hero.entity.v1.DateRange.
 * Use `create(DateRangeSchema)` to create a new message.
 */
export const DateRangeSchema: GenMessage<DateRange> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 53);

/**
 * FieldQuery represents a field-specific query (limits a search term to one field)
 *
 * @generated from message hero.entity.v1.FieldQuery
 */
export type FieldQuery = Message<"hero.entity.v1.FieldQuery"> & {
  /**
   * e.g. "id", "data", "tags", "reference_display_name", "reference_relation_type"
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * the term to match in that field
   *
   * @generated from field: string query = 2;
   */
  query: string;
};

/**
 * Describes the message hero.entity.v1.FieldQuery.
 * Use `create(FieldQuerySchema)` to create a new message.
 */
export const FieldQuerySchema: GenMessage<FieldQuery> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 54);

/**
 * HighlightResult contains highlighted fragments for a given field in each entity
 *
 * @generated from message hero.entity.v1.HighlightResult
 */
export type HighlightResult = Message<"hero.entity.v1.HighlightResult"> & {
  /**
   * the field name where matches occurred
   *
   * @generated from field: string field = 1;
   */
  field: string;

  /**
   * snippets with matches, e.g. ["…urgent…", "…critical…"]
   *
   * @generated from field: repeated string fragments = 2;
   */
  fragments: string[];
};

/**
 * Describes the message hero.entity.v1.HighlightResult.
 * Use `create(HighlightResultSchema)` to create a new message.
 */
export const HighlightResultSchema: GenMessage<HighlightResult> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 55);

/**
 * SearchEntitiesRequest is used to search entities with various filters and options.
 *
 * All partial/fuzzy matching (ILIKE, substring, full-text) must be done via the `query`, `search_fields`, or `field_queries` parameters.
 *
 * Supported field names for `search_fields` and `field_queries.field`:
 *   - "id"                      (entity id)
 *   - "data"                    (content inside the JSON data field)
 *   - "tags"                    (entity tags)
 *   - "reference_display_name"  (reference display name)
 *   - "reference_relation_type" (reference relation type)
 *
 * For exact-value and range filters, use the dedicated fields below.
 *
 * @generated from message hero.entity.v1.SearchEntitiesRequest
 */
export type SearchEntitiesRequest = Message<"hero.entity.v1.SearchEntitiesRequest"> & {
  /**
   * ────── Free-text & scoped field queries ──────
   *
   * full-text / fuzzy across all indexed fields
   *
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * limit `query` to these fields; empty = all
   *
   * @generated from field: repeated string search_fields = 2;
   */
  searchFields: string[];

  /**
   * individual term→field queries
   *
   * @generated from field: repeated hero.entity.v1.FieldQuery field_queries = 3;
   */
  fieldQueries: FieldQuery[];

  /**
   * ────── Entity filters (exact match) ──────
   *
   * entities.status IN (...)
   *
   * @generated from field: repeated hero.entity.v1.RecordStatus status = 4;
   */
  status: RecordStatus[];

  /**
   * entities.created_by IN (...)
   *
   * @generated from field: repeated string created_by = 5;
   */
  createdBy: string[];

  /**
   * entities.updated_by IN (...)
   *
   * @generated from field: repeated string updated_by = 6;
   */
  updatedBy: string[];

  /**
   * entities.schema_id IN (...)
   *
   * @generated from field: repeated string schema_ids = 7;
   */
  schemaIds: string[];

  /**
   * entities.entity_type IN (...)
   *
   * @generated from field: repeated hero.entity.v1.EntityType entity_types = 8;
   */
  entityTypes: EntityType[];

  /**
   * entities.org_id IN (...)
   *
   * @generated from field: repeated int32 org_ids = 9;
   */
  orgIds: number[];

  /**
   * entities.tags @> ARRAY[...]
   *
   * @generated from field: repeated string tags = 10;
   */
  tags: string[];

  /**
   * ────── Reference filters (exact match) ──────
   *
   * references[].id IN (...)
   *
   * @generated from field: repeated string reference_ids = 11;
   */
  referenceIds: string[];

  /**
   * references[].type IN (...)
   *
   * @generated from field: repeated string reference_types = 12;
   */
  referenceTypes: string[];

  /**
   * ────── Date range filters ──────
   *
   * entities.create_time BETWEEN ...
   *
   * @generated from field: hero.entity.v1.DateRange create_time = 13;
   */
  createTime?: DateRange;

  /**
   * entities.update_time BETWEEN ...
   *
   * @generated from field: hero.entity.v1.DateRange update_time = 14;
   */
  updateTime?: DateRange;

  /**
   * ────── Pagination & sorting ──────
   *
   * @generated from field: int32 page_size = 15;
   */
  pageSize: number;

  /**
   * cursor
   *
   * @generated from field: string page_token = 16;
   */
  pageToken: string;

  /**
   * default = RELEVANCE
   *
   * @generated from field: hero.entity.v1.SearchOrderBy order_by = 17;
   */
  orderBy: SearchOrderBy;

  /**
   * default = false (DESC)
   *
   * @generated from field: bool ascending = 18;
   */
  ascending: boolean;

  /**
   * ────── Archive behavior control ──────
   *
   * include archived entities in search results (default = false)
   *
   * @generated from field: bool include_archived_entities = 19;
   */
  includeArchivedEntities: boolean;
};

/**
 * Describes the message hero.entity.v1.SearchEntitiesRequest.
 * Use `create(SearchEntitiesRequestSchema)` to create a new message.
 */
export const SearchEntitiesRequestSchema: GenMessage<SearchEntitiesRequest> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 56);

/**
 * @generated from message hero.entity.v1.SearchEntitiesResponse
 */
export type SearchEntitiesResponse = Message<"hero.entity.v1.SearchEntitiesResponse"> & {
  /**
   * The page of entities that matched the query (already ordered & trimmed).
   *
   * @generated from field: repeated hero.entity.v1.Entity entities = 1;
   */
  entities: Entity[];

  /**
   * Cursor for fetching the next page; empty when you're on the last page.
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * Per-entity highlight information keyed by entity ID.
   * Each HighlightResult lists the field name and one-or-more matched fragments
   * (e.g.  "…suspect vehicle…", "…dark-blue sedan…").
   *
   * @generated from field: map<string, hero.entity.v1.HighlightResult> highlights = 3;
   */
  highlights: { [key: string]: HighlightResult };

  /**
   * Total number of hits *before* pagination—useful for UI counters.
   *
   * @generated from field: int32 total_results = 4;
   */
  totalResults: number;
};

/**
 * Describes the message hero.entity.v1.SearchEntitiesResponse.
 * Use `create(SearchEntitiesResponseSchema)` to create a new message.
 */
export const SearchEntitiesResponseSchema: GenMessage<SearchEntitiesResponse> = /*@__PURE__*/
  messageDesc(file_hero_entity_v1_entity, 57);

/**
 * EntityType enumerates the types of entities supported by the system.
 * This classification allows you to apply type-specific processing if needed.
 *
 * @generated from enum hero.entity.v1.EntityType
 */
export enum EntityType {
  /**
   * Default unspecified entity type.
   *
   * @generated from enum value: ENTITY_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Represents a person.
   *
   * @generated from enum value: ENTITY_TYPE_PERSON = 1;
   */
  PERSON = 1,

  /**
   * Represents a vehicle.
   *
   * @generated from enum value: ENTITY_TYPE_VEHICLE = 2;
   */
  VEHICLE = 2,

  /**
   * Represents a property.
   *
   * @generated from enum value: ENTITY_TYPE_PROPERTY = 3;
   */
  PROPERTY = 3,

  /**
   * Represents any other or undefined type.
   *
   * @generated from enum value: ENTITY_TYPE_OTHER = 4;
   */
  OTHER = 4,

  /**
   * Represents an organization.
   *
   * @generated from enum value: ENTITY_TYPE_ORGANIZATION = 5;
   */
  ORGANIZATION = 5,
}

/**
 * Describes the enum hero.entity.v1.EntityType.
 */
export const EntityTypeSchema: GenEnum<EntityType> = /*@__PURE__*/
  enumDesc(file_hero_entity_v1_entity, 0);

/**
 * RecordStatus defines the state of a record (entity or schema).
 * This indicates whether the record is actively used, archived, or in another status.
 *
 * @generated from enum hero.entity.v1.RecordStatus
 */
export enum RecordStatus {
  /**
   * Default unspecified status.
   *
   * @generated from enum value: RECORD_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * The record is active and in use.
   *
   * @generated from enum value: RECORD_STATUS_ACTIVE = 1;
   */
  ACTIVE = 1,

  /**
   * The record is archived for historical purposes.
   *
   * @generated from enum value: RECORD_STATUS_ARCHIVED = 2;
   */
  ARCHIVED = 2,

  /**
   * The record is outdated or superseded.
   *
   * @generated from enum value: RECORD_STATUS_DEPRECATED = 3;
   */
  DEPRECATED = 3,

  /**
   * The record is marked as deleted (soft deletion).
   *
   * @generated from enum value: RECORD_STATUS_DELETED = 4;
   */
  DELETED = 4,

  /**
   * The record is in a draft stage.
   *
   * @generated from enum value: RECORD_STATUS_DRAFT = 5;
   */
  DRAFT = 5,
}

/**
 * Describes the enum hero.entity.v1.RecordStatus.
 */
export const RecordStatusSchema: GenEnum<RecordStatus> = /*@__PURE__*/
  enumDesc(file_hero_entity_v1_entity, 1);

/**
 * SearchOrderBy enumerates how to sort entity search results
 *
 * @generated from enum hero.entity.v1.SearchOrderBy
 */
export enum SearchOrderBy {
  /**
   * @generated from enum value: SEARCH_ORDER_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_RELEVANCE = 1;
   */
  RELEVANCE = 1,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_CREATED_AT = 2;
   */
  CREATED_AT = 2,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_UPDATED_AT = 3;
   */
  UPDATED_AT = 3,

  /**
   * @generated from enum value: SEARCH_ORDER_BY_STATUS = 4;
   */
  STATUS = 4,
}

/**
 * Describes the enum hero.entity.v1.SearchOrderBy.
 */
export const SearchOrderBySchema: GenEnum<SearchOrderBy> = /*@__PURE__*/
  enumDesc(file_hero_entity_v1_entity, 2);

/**
 * EntityService defines all operations for managing dynamic entities and their schemas.
 * It includes CRUD, versioning, bulk operations, restore/diff, and access control endpoints.
 *
 * @generated from service hero.entity.v1.EntityService
 */
export const EntityService: GenService<{
  /**
   * --------------------
   * Entity CRUD Operations
   * --------------------
   *
   * @generated from rpc hero.entity.v1.EntityService.CreateEntity
   */
  createEntity: {
    methodKind: "unary";
    input: typeof CreateEntityRequestSchema;
    output: typeof CreateEntityResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.GetLatestEntity
   */
  getLatestEntity: {
    methodKind: "unary";
    input: typeof GetLatestEntityRequestSchema;
    output: typeof EntitySchema$;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.GetEntityByVersion
   */
  getEntityByVersion: {
    methodKind: "unary";
    input: typeof GetEntityByVersionRequestSchema;
    output: typeof EntitySchema$;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.GetLatestActiveEntity
   */
  getLatestActiveEntity: {
    methodKind: "unary";
    input: typeof GetLatestActiveEntityRequestSchema;
    output: typeof EntitySchema$;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.UpdateEntity
   */
  updateEntity: {
    methodKind: "unary";
    input: typeof UpdateEntityRequestSchema;
    output: typeof EntitySchema$;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.ListLatestEntities
   */
  listLatestEntities: {
    methodKind: "unary";
    input: typeof ListLatestEntitiesRequestSchema;
    output: typeof ListLatestEntitiesResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.DeleteAllVersionsOfEntity
   */
  deleteAllVersionsOfEntity: {
    methodKind: "unary";
    input: typeof DeleteAllVersionsOfEntityRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.DeleteSpecificVersionOfEntity
   */
  deleteSpecificVersionOfEntity: {
    methodKind: "unary";
    input: typeof DeleteSpecificVersionOfEntityRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.ListAllVersionsOfEntity
   */
  listAllVersionsOfEntity: {
    methodKind: "unary";
    input: typeof ListAllVersionsOfEntityRequestSchema;
    output: typeof ListAllVersionsOfEntityResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.BatchGetLatestEntities
   */
  batchGetLatestEntities: {
    methodKind: "unary";
    input: typeof BatchGetLatestEntitiesRequestSchema;
    output: typeof BatchGetLatestEntitiesResponseSchema;
  },
  /**
   * Bulk operations for entities.
   *
   * @generated from rpc hero.entity.v1.EntityService.BulkCreateEntities
   */
  bulkCreateEntities: {
    methodKind: "unary";
    input: typeof BulkCreateEntitiesRequestSchema;
    output: typeof BulkCreateEntitiesResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.BulkUpdateEntities
   */
  bulkUpdateEntities: {
    methodKind: "unary";
    input: typeof BulkUpdateEntitiesRequestSchema;
    output: typeof BulkUpdateEntitiesResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.BulkDeleteEntities
   */
  bulkDeleteEntities: {
    methodKind: "unary";
    input: typeof BulkDeleteEntitiesRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * Restore and diff operations for entities.
   *
   * @generated from rpc hero.entity.v1.EntityService.RestoreEntityVersion
   */
  restoreEntityVersion: {
    methodKind: "unary";
    input: typeof RestoreEntityVersionRequestSchema;
    output: typeof RestoreEntityVersionResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.DiffEntityVersions
   */
  diffEntityVersions: {
    methodKind: "unary";
    input: typeof DiffEntityVersionsRequestSchema;
    output: typeof DiffEntityVersionsResponseSchema;
  },
  /**
   * Access control for entities.
   *
   * @generated from rpc hero.entity.v1.EntityService.CheckEntityPermissions
   */
  checkEntityPermissions: {
    methodKind: "unary";
    input: typeof CheckEntityPermissionsRequestSchema;
    output: typeof CheckEntityPermissionsResponseSchema;
  },
  /**
   * Search entities.
   *
   * @generated from rpc hero.entity.v1.EntityService.SearchEntities
   */
  searchEntities: {
    methodKind: "unary";
    input: typeof SearchEntitiesRequestSchema;
    output: typeof SearchEntitiesResponseSchema;
  },
  /**
   * --------------------
   * Entity Schema CRUD Operations
   * --------------------
   *
   * @generated from rpc hero.entity.v1.EntityService.CreateEntitySchema
   */
  createEntitySchema: {
    methodKind: "unary";
    input: typeof CreateEntitySchemaRequestSchema;
    output: typeof CreateEntitySchemaResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.GetLatestEntitySchema
   */
  getLatestEntitySchema: {
    methodKind: "unary";
    input: typeof GetLatestEntitySchemaRequestSchema;
    output: typeof EntitySchemaSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.GetLatestActiveEntitySchema
   */
  getLatestActiveEntitySchema: {
    methodKind: "unary";
    input: typeof GetLatestActiveEntitySchemaRequestSchema;
    output: typeof EntitySchemaSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.GetEntitySchemaByVersion
   */
  getEntitySchemaByVersion: {
    methodKind: "unary";
    input: typeof GetEntitySchemaByVersionRequestSchema;
    output: typeof EntitySchemaSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.UpdateEntitySchema
   */
  updateEntitySchema: {
    methodKind: "unary";
    input: typeof UpdateEntitySchemaRequestSchema;
    output: typeof EntitySchemaSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.DeleteAllVersionsOfEntitySchema
   */
  deleteAllVersionsOfEntitySchema: {
    methodKind: "unary";
    input: typeof DeleteAllVersionsOfEntitySchemaRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.DeleteSpecificVersionOfEntitySchema
   */
  deleteSpecificVersionOfEntitySchema: {
    methodKind: "unary";
    input: typeof DeleteSpecificVersionOfEntitySchemaRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.ListLatestEntitySchemas
   */
  listLatestEntitySchemas: {
    methodKind: "unary";
    input: typeof ListLatestEntitySchemasRequestSchema;
    output: typeof ListLatestEntitySchemasResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.ListAllVersionsOfEntitySchema
   */
  listAllVersionsOfEntitySchema: {
    methodKind: "unary";
    input: typeof ListAllVersionsOfEntitySchemaRequestSchema;
    output: typeof ListAllVersionsOfEntitySchemaResponseSchema;
  },
  /**
   * Bulk operations for entity schemas.
   *
   * @generated from rpc hero.entity.v1.EntityService.BulkCreateEntitySchemas
   */
  bulkCreateEntitySchemas: {
    methodKind: "unary";
    input: typeof BulkCreateEntitySchemasRequestSchema;
    output: typeof BulkCreateEntitySchemasResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.BulkUpdateEntitySchemas
   */
  bulkUpdateEntitySchemas: {
    methodKind: "unary";
    input: typeof BulkUpdateEntitySchemasRequestSchema;
    output: typeof BulkUpdateEntitySchemasResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.BulkDeleteEntitySchemas
   */
  bulkDeleteEntitySchemas: {
    methodKind: "unary";
    input: typeof BulkDeleteEntitySchemasRequestSchema;
    output: typeof EmptySchema;
  },
  /**
   * Restore and diff operations for entity schemas.
   *
   * @generated from rpc hero.entity.v1.EntityService.RestoreEntitySchemaVersion
   */
  restoreEntitySchemaVersion: {
    methodKind: "unary";
    input: typeof RestoreEntitySchemaVersionRequestSchema;
    output: typeof RestoreEntitySchemaVersionResponseSchema;
  },
  /**
   * @generated from rpc hero.entity.v1.EntityService.DiffEntitySchemaVersions
   */
  diffEntitySchemaVersions: {
    methodKind: "unary";
    input: typeof DiffEntitySchemaVersionsRequestSchema;
    output: typeof DiffEntitySchemaVersionsResponseSchema;
  },
  /**
   * Access control for entity schemas.
   *
   * @generated from rpc hero.entity.v1.EntityService.CheckEntitySchemaPermissions
   */
  checkEntitySchemaPermissions: {
    methodKind: "unary";
    input: typeof CheckEntitySchemaPermissionsRequestSchema;
    output: typeof CheckEntitySchemaPermissionsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_entity_v1_entity, 0);

