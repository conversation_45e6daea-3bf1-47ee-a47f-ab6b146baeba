// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/orders/v2/orders.proto

package orders

import (
	v2 "proto/hero/assets/v2"
	v21 "proto/hero/situations/v2"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ----------------------------------------------
// Enums
// ----------------------------------------------
type OrderStatus int32

const (
	OrderStatus_ORDER_STATUS_UNSPECIFIED  OrderStatus = 0 // Default unspecified order status.
	OrderStatus_ORDER_STATUS_CREATED      OrderStatus = 1 // Order has been created.
	OrderStatus_ORDER_STATUS_ACKNOWLEDGED OrderStatus = 2 // Asset has acknowledged the order.
	OrderStatus_ORDER_STATUS_REJECTED     OrderStatus = 3 // Order was rejected by the asset.
	OrderStatus_ORDER_STATUS_SNOOZED      OrderStatus = 4 // Order has been snoozed (delayed).
	OrderStatus_ORDER_STATUS_IN_PROGRESS  OrderStatus = 5 // Order is actively being worked on.
	OrderStatus_ORDER_STATUS_COMPLETED    OrderStatus = 6 // Order has been completed.
	OrderStatus_ORDER_STATUS_CANCELLED    OrderStatus = 7 // Order has been cancelled.
)

// Enum value maps for OrderStatus.
var (
	OrderStatus_name = map[int32]string{
		0: "ORDER_STATUS_UNSPECIFIED",
		1: "ORDER_STATUS_CREATED",
		2: "ORDER_STATUS_ACKNOWLEDGED",
		3: "ORDER_STATUS_REJECTED",
		4: "ORDER_STATUS_SNOOZED",
		5: "ORDER_STATUS_IN_PROGRESS",
		6: "ORDER_STATUS_COMPLETED",
		7: "ORDER_STATUS_CANCELLED",
	}
	OrderStatus_value = map[string]int32{
		"ORDER_STATUS_UNSPECIFIED":  0,
		"ORDER_STATUS_CREATED":      1,
		"ORDER_STATUS_ACKNOWLEDGED": 2,
		"ORDER_STATUS_REJECTED":     3,
		"ORDER_STATUS_SNOOZED":      4,
		"ORDER_STATUS_IN_PROGRESS":  5,
		"ORDER_STATUS_COMPLETED":    6,
		"ORDER_STATUS_CANCELLED":    7,
	}
)

func (x OrderStatus) Enum() *OrderStatus {
	p := new(OrderStatus)
	*p = x
	return p
}

func (x OrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_orders_v2_orders_proto_enumTypes[0].Descriptor()
}

func (OrderStatus) Type() protoreflect.EnumType {
	return &file_hero_orders_v2_orders_proto_enumTypes[0]
}

func (x OrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderStatus.Descriptor instead.
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{0}
}

type OrderType int32

const (
	OrderType_ORDER_TYPE_UNSPECIFIED            OrderType = 0  // Default unspecified order type.
	OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT   OrderType = 1  // Order for triaging a situation reported by a member.
	OrderType_ORDER_TYPE_ASSIST_MEMBER          OrderType = 2  // Order for assisting a member.
	OrderType_ORDER_TYPE_SUBMIT_FINAL_REPORT    OrderType = 3  // Order to submit a final report.
	OrderType_ORDER_TYPE_ASSIGN_AGENT           OrderType = 4  // Order to assign an agent to a task.
	OrderType_ORDER_TYPE_TRIAGE_AGENT_REPORT    OrderType = 5  // Order for triaging a situation reported by an agent.
	OrderType_ORDER_TYPE_TRIAGE_CAMERA_INCIDENT OrderType = 6  // Order for triaging an incident captured by a camera.
	OrderType_ORDER_TYPE_WRITE_REPORT           OrderType = 7  // Order for writing a report.
	OrderType_ORDER_TYPE_REVIEW_REPORT          OrderType = 8  // Order for reviewing a report.
	OrderType_ORDER_TYPE_REVISE_REPORT          OrderType = 9  // Order for revising a report.
	OrderType_ORDER_TYPE_MANAGE_CASE            OrderType = 10 // Order for managing an assigned case as primary investigator.
)

// Enum value maps for OrderType.
var (
	OrderType_name = map[int32]string{
		0:  "ORDER_TYPE_UNSPECIFIED",
		1:  "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
		2:  "ORDER_TYPE_ASSIST_MEMBER",
		3:  "ORDER_TYPE_SUBMIT_FINAL_REPORT",
		4:  "ORDER_TYPE_ASSIGN_AGENT",
		5:  "ORDER_TYPE_TRIAGE_AGENT_REPORT",
		6:  "ORDER_TYPE_TRIAGE_CAMERA_INCIDENT",
		7:  "ORDER_TYPE_WRITE_REPORT",
		8:  "ORDER_TYPE_REVIEW_REPORT",
		9:  "ORDER_TYPE_REVISE_REPORT",
		10: "ORDER_TYPE_MANAGE_CASE",
	}
	OrderType_value = map[string]int32{
		"ORDER_TYPE_UNSPECIFIED":            0,
		"ORDER_TYPE_TRIAGE_MEMBER_REPORT":   1,
		"ORDER_TYPE_ASSIST_MEMBER":          2,
		"ORDER_TYPE_SUBMIT_FINAL_REPORT":    3,
		"ORDER_TYPE_ASSIGN_AGENT":           4,
		"ORDER_TYPE_TRIAGE_AGENT_REPORT":    5,
		"ORDER_TYPE_TRIAGE_CAMERA_INCIDENT": 6,
		"ORDER_TYPE_WRITE_REPORT":           7,
		"ORDER_TYPE_REVIEW_REPORT":          8,
		"ORDER_TYPE_REVISE_REPORT":          9,
		"ORDER_TYPE_MANAGE_CASE":            10,
	}
)

func (x OrderType) Enum() *OrderType {
	p := new(OrderType)
	*p = x
	return p
}

func (x OrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_orders_v2_orders_proto_enumTypes[1].Descriptor()
}

func (OrderType) Type() protoreflect.EnumType {
	return &file_hero_orders_v2_orders_proto_enumTypes[1]
}

func (x OrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderType.Descriptor instead.
func (OrderType) EnumDescriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{1}
}

// ----------------------------------------------
// Permissions sub-message
// ----------------------------------------------
type OrderPermissions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Which AssetTypes are allowed to change this order's status.
	CanChangeStatus []v2.AssetType `protobuf:"varint,1,rep,packed,name=can_change_status,json=canChangeStatus,proto3,enum=hero.assets.v2.AssetType" json:"can_change_status,omitempty"`
	// Which AssetTypes are allowed to add/change the assigned asset_id.
	CanAssignAsset []v2.AssetType `protobuf:"varint,2,rep,packed,name=can_assign_asset,json=canAssignAsset,proto3,enum=hero.assets.v2.AssetType" json:"can_assign_asset,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *OrderPermissions) Reset() {
	*x = OrderPermissions{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderPermissions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPermissions) ProtoMessage() {}

func (x *OrderPermissions) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPermissions.ProtoReflect.Descriptor instead.
func (*OrderPermissions) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{0}
}

func (x *OrderPermissions) GetCanChangeStatus() []v2.AssetType {
	if x != nil {
		return x.CanChangeStatus
	}
	return nil
}

func (x *OrderPermissions) GetCanAssignAsset() []v2.AssetType {
	if x != nil {
		return x.CanAssignAsset
	}
	return nil
}

// ----------------------------------------------
// Messages
// ----------------------------------------------
type OrderUpdateEntry struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Message   string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp string                 `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // ISO8601 timestamp
	// Re-use the shared UpdateSource enum from situation.proto.
	UpdateSource  v21.UpdateSource `protobuf:"varint,3,opt,name=update_source,json=updateSource,proto3,enum=hero.situations.v2.UpdateSource" json:"update_source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderUpdateEntry) Reset() {
	*x = OrderUpdateEntry{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderUpdateEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderUpdateEntry) ProtoMessage() {}

func (x *OrderUpdateEntry) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderUpdateEntry.ProtoReflect.Descriptor instead.
func (*OrderUpdateEntry) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{1}
}

func (x *OrderUpdateEntry) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OrderUpdateEntry) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *OrderUpdateEntry) GetUpdateSource() v21.UpdateSource {
	if x != nil {
		return x.UpdateSource
	}
	return v21.UpdateSource(0)
}

type OrderStatusUpdateEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The time at which the status update occurred.
	EntryTimestamp string `protobuf:"bytes,1,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"` // ISO8601 timestamp
	// The new status of the order.
	NewStatus OrderStatus `protobuf:"varint,2,opt,name=new_status,json=newStatus,proto3,enum=hero.orders.v2.OrderStatus" json:"new_status,omitempty"`
	// The previous status before this update.
	PreviousStatus OrderStatus `protobuf:"varint,3,opt,name=previous_status,json=previousStatus,proto3,enum=hero.orders.v2.OrderStatus" json:"previous_status,omitempty"`
	// The new type-specific status of the order.
	NewTypeSpecificStatus string `protobuf:"bytes,4,opt,name=new_type_specific_status,json=newTypeSpecificStatus,proto3" json:"new_type_specific_status,omitempty"`
	// The previous type-specific status before this update.
	PreviousTypeSpecificStatus string `protobuf:"bytes,5,opt,name=previous_type_specific_status,json=previousTypeSpecificStatus,proto3" json:"previous_type_specific_status,omitempty"`
	// (Optional) A note explaining the status change.
	Note string `protobuf:"bytes,6,opt,name=note,proto3" json:"note,omitempty"`
	// (Optional) ID of the updater.
	UpdaterId string `protobuf:"bytes,7,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	// (Optional) Source of the update.
	UpdateSource v21.UpdateSource `protobuf:"varint,8,opt,name=update_source,json=updateSource,proto3,enum=hero.situations.v2.UpdateSource" json:"update_source,omitempty"`
	// The time at which the status update was set to - defaults to the timestamp field unless manually overridden.
	StatusUpdateTimestamp string `protobuf:"bytes,9,opt,name=status_update_timestamp,json=statusUpdateTimestamp,proto3" json:"status_update_timestamp,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *OrderStatusUpdateEntry) Reset() {
	*x = OrderStatusUpdateEntry{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderStatusUpdateEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderStatusUpdateEntry) ProtoMessage() {}

func (x *OrderStatusUpdateEntry) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderStatusUpdateEntry.ProtoReflect.Descriptor instead.
func (*OrderStatusUpdateEntry) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{2}
}

func (x *OrderStatusUpdateEntry) GetEntryTimestamp() string {
	if x != nil {
		return x.EntryTimestamp
	}
	return ""
}

func (x *OrderStatusUpdateEntry) GetNewStatus() OrderStatus {
	if x != nil {
		return x.NewStatus
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *OrderStatusUpdateEntry) GetPreviousStatus() OrderStatus {
	if x != nil {
		return x.PreviousStatus
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *OrderStatusUpdateEntry) GetNewTypeSpecificStatus() string {
	if x != nil {
		return x.NewTypeSpecificStatus
	}
	return ""
}

func (x *OrderStatusUpdateEntry) GetPreviousTypeSpecificStatus() string {
	if x != nil {
		return x.PreviousTypeSpecificStatus
	}
	return ""
}

func (x *OrderStatusUpdateEntry) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *OrderStatusUpdateEntry) GetUpdaterId() string {
	if x != nil {
		return x.UpdaterId
	}
	return ""
}

func (x *OrderStatusUpdateEntry) GetUpdateSource() v21.UpdateSource {
	if x != nil {
		return x.UpdateSource
	}
	return v21.UpdateSource(0)
}

func (x *OrderStatusUpdateEntry) GetStatusUpdateTimestamp() string {
	if x != nil {
		return x.StatusUpdateTimestamp
	}
	return ""
}

type Order struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier for the order.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Reference to the associated situation.
	SituationId string `protobuf:"bytes,2,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	// Reference to the asset assigned to execute this order.
	AssetId string `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Specifies what type of order this is.
	Type OrderType `protobuf:"varint,4,opt,name=type,proto3,enum=hero.orders.v2.OrderType" json:"type,omitempty"`
	// Indicates the current execution status of the order.
	Status OrderStatus `protobuf:"varint,5,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	// Detailed instructions for what the asset needs to do.
	Instructions string `protobuf:"bytes,6,opt,name=instructions,proto3" json:"instructions,omitempty"`
	// Priority level of the order.
	Priority int32 `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`
	// JSON string (or any relevant format) containing order-specific parameters.
	AdditionalInfoJson string `protobuf:"bytes,8,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	// Field to hold additional status information specific to the order type.
	TypeSpecificStatus string `protobuf:"bytes,9,opt,name=type_specific_status,json=typeSpecificStatus,proto3" json:"type_specific_status,omitempty"`
	// Free-form notes or comments regarding the order.
	Notes string `protobuf:"bytes,10,opt,name=notes,proto3" json:"notes,omitempty"`
	// Timestamp when the order was created.
	CreateTime string `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"` // ISO8601 timestamp
	// Timestamp when the order was last updated.
	UpdateTime string `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"` // ISO8601 timestamp
	// Timestamp when the order was completed (if applicable).
	CompletionTime string `protobuf:"bytes,13,opt,name=completion_time,json=completionTime,proto3" json:"completion_time,omitempty"` // ISO8601 timestamp
	// A list of update entries to track changes and status transitions.
	Updates []*OrderUpdateEntry `protobuf:"bytes,14,rep,name=updates,proto3" json:"updates,omitempty"`
	// Timestamp when the order was actually assigned to an asset.
	AssignedTime string `protobuf:"bytes,15,opt,name=assigned_time,json=assignedTime,proto3" json:"assigned_time,omitempty"` // ISO8601 timestamp
	// Timestamp when the asset acknowledged the order.
	AcknowledgedTime string `protobuf:"bytes,16,opt,name=acknowledged_time,json=acknowledgedTime,proto3" json:"acknowledged_time,omitempty"` // ISO8601 timestamp
	// Expected or estimated timestamp for order completion.
	EstimatedCompletionTime string `protobuf:"bytes,17,opt,name=estimated_completion_time,json=estimatedCompletionTime,proto3" json:"estimated_completion_time,omitempty"` // ISO8601 timestamp
	// Reason for cancellation or rejection of the order, if applicable.
	CancellationOrRejectionReason string `protobuf:"bytes,18,opt,name=cancellation_or_rejection_reason,json=cancellationOrRejectionReason,proto3" json:"cancellation_or_rejection_reason,omitempty"`
	// Number of times the order has been retried.
	RetryCount int32 `protobuf:"varint,19,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	// Indicates the source of order creation using the shared UpdateSource enum.
	CreatedBy v21.UpdateSource `protobuf:"varint,20,opt,name=created_by,json=createdBy,proto3,enum=hero.situations.v2.UpdateSource" json:"created_by,omitempty"`
	// A short descriptive title for the order.
	Title string `protobuf:"bytes,21,opt,name=title,proto3" json:"title,omitempty"`
	// Indicates the type(s) of assets that can perform this order.
	AllowedAssetTypes []v2.AssetType `protobuf:"varint,23,rep,packed,name=allowed_asset_types,json=allowedAssetTypes,proto3,enum=hero.assets.v2.AssetType" json:"allowed_asset_types,omitempty"`
	// Reason for snoozing the order.
	SnoozeReason string `protobuf:"bytes,24,opt,name=snooze_reason,json=snoozeReason,proto3" json:"snooze_reason,omitempty"`
	// Timestamp until which the order is snoozed.
	SnoozeUntil string `protobuf:"bytes,25,opt,name=snooze_until,json=snoozeUntil,proto3" json:"snooze_until,omitempty"` // ISO8601 timestamp
	// Number of times the order has been snoozed.
	SnoozeCount int32 `protobuf:"varint,26,opt,name=snooze_count,json=snoozeCount,proto3" json:"snooze_count,omitempty"`
	// List of asset IDs that should NOT be assigned to this order.
	BlacklistedAssetIds []string `protobuf:"bytes,27,rep,name=blacklisted_asset_ids,json=blacklistedAssetIds,proto3" json:"blacklisted_asset_ids,omitempty"`
	// For orders it will be fixed value "ORDER".
	ResourceType string `protobuf:"bytes,28,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// Permissions specifying which asset types can change status or reassign assets.
	Permissions *OrderPermissions `protobuf:"bytes,29,opt,name=permissions,proto3" json:"permissions,omitempty"`
	// Timeseries status updates for the order.
	StatusUpdates []*OrderStatusUpdateEntry `protobuf:"bytes,30,rep,name=status_updates,json=statusUpdates,proto3" json:"status_updates,omitempty"`
	// Reference this order to a report
	ReportId string `protobuf:"bytes,31,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// If this is a review order, point back at the ReviewRound
	ReviewRoundId string `protobuf:"bytes,32,opt,name=review_round_id,json=reviewRoundId,proto3" json:"review_round_id,omitempty"`
	// Reference this order to a case (for MANAGE_CASE orders)
	CaseId        string `protobuf:"bytes,33,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Order) Reset() {
	*x = Order{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{3}
}

func (x *Order) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Order) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *Order) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *Order) GetType() OrderType {
	if x != nil {
		return x.Type
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *Order) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *Order) GetInstructions() string {
	if x != nil {
		return x.Instructions
	}
	return ""
}

func (x *Order) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Order) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

func (x *Order) GetTypeSpecificStatus() string {
	if x != nil {
		return x.TypeSpecificStatus
	}
	return ""
}

func (x *Order) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *Order) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Order) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Order) GetCompletionTime() string {
	if x != nil {
		return x.CompletionTime
	}
	return ""
}

func (x *Order) GetUpdates() []*OrderUpdateEntry {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *Order) GetAssignedTime() string {
	if x != nil {
		return x.AssignedTime
	}
	return ""
}

func (x *Order) GetAcknowledgedTime() string {
	if x != nil {
		return x.AcknowledgedTime
	}
	return ""
}

func (x *Order) GetEstimatedCompletionTime() string {
	if x != nil {
		return x.EstimatedCompletionTime
	}
	return ""
}

func (x *Order) GetCancellationOrRejectionReason() string {
	if x != nil {
		return x.CancellationOrRejectionReason
	}
	return ""
}

func (x *Order) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *Order) GetCreatedBy() v21.UpdateSource {
	if x != nil {
		return x.CreatedBy
	}
	return v21.UpdateSource(0)
}

func (x *Order) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Order) GetAllowedAssetTypes() []v2.AssetType {
	if x != nil {
		return x.AllowedAssetTypes
	}
	return nil
}

func (x *Order) GetSnoozeReason() string {
	if x != nil {
		return x.SnoozeReason
	}
	return ""
}

func (x *Order) GetSnoozeUntil() string {
	if x != nil {
		return x.SnoozeUntil
	}
	return ""
}

func (x *Order) GetSnoozeCount() int32 {
	if x != nil {
		return x.SnoozeCount
	}
	return 0
}

func (x *Order) GetBlacklistedAssetIds() []string {
	if x != nil {
		return x.BlacklistedAssetIds
	}
	return nil
}

func (x *Order) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Order) GetPermissions() *OrderPermissions {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *Order) GetStatusUpdates() []*OrderStatusUpdateEntry {
	if x != nil {
		return x.StatusUpdates
	}
	return nil
}

func (x *Order) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *Order) GetReviewRoundId() string {
	if x != nil {
		return x.ReviewRoundId
	}
	return ""
}

func (x *Order) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

// ----------------------------------------------
// Request/Response Messages
// ----------------------------------------------
type CreateOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Order to be created (id may be omitted and auto-generated by the system).
	Order         *Order `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{4}
}

func (x *CreateOrderRequest) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

type CreateOrderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Order         *Order                 `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderResponse) Reset() {
	*x = CreateOrderResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResponse) ProtoMessage() {}

func (x *CreateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateOrderResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrderResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

type GetOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID of the order to retrieve.
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderRequest) Reset() {
	*x = GetOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderRequest) ProtoMessage() {}

func (x *GetOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderRequest.ProtoReflect.Descriptor instead.
func (*GetOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{6}
}

func (x *GetOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UpdateOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The updated order object. Must contain the ID of the order to update.
	Order         *Order `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrderRequest) Reset() {
	*x = UpdateOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderRequest) ProtoMessage() {}

func (x *UpdateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateOrderRequest) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

// Request message to list orders with pagination, filtering, and ordering options.
type ListOrdersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Maximum number of orders to return in the response.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// A token identifying a specific page of results to retrieve.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter: Returns only orders matching the specified status.
	// A value of ORDER_STATUS_UNSPECIFIED indicates no filtering by status.
	Status OrderStatus `protobuf:"varint,3,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	// Optional filter: Returns only orders of the specified type.
	Type OrderType `protobuf:"varint,4,opt,name=type,proto3,enum=hero.orders.v2.OrderType" json:"type,omitempty"`
	// Optional: Specifies the ordering of returned orders (e.g., "create_time desc").
	OrderBy       string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersRequest) Reset() {
	*x = ListOrdersRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersRequest) ProtoMessage() {}

func (x *ListOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{8}
}

func (x *ListOrdersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOrdersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListOrdersRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *ListOrdersRequest) GetType() OrderType {
	if x != nil {
		return x.Type
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *ListOrdersRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type ListOrdersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orders        []*Order               `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersResponse) Reset() {
	*x = ListOrdersResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersResponse) ProtoMessage() {}

func (x *ListOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{9}
}

func (x *ListOrdersResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type DeleteOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteOrderRequest) Reset() {
	*x = DeleteOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteOrderRequest) ProtoMessage() {}

func (x *DeleteOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteOrderRequest.ProtoReflect.Descriptor instead.
func (*DeleteOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AddOrderUpdateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the order to which we add an update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The new update entry.
	Update        *OrderUpdateEntry `protobuf:"bytes,2,opt,name=update,proto3" json:"update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddOrderUpdateRequest) Reset() {
	*x = AddOrderUpdateRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOrderUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrderUpdateRequest) ProtoMessage() {}

func (x *AddOrderUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrderUpdateRequest.ProtoReflect.Descriptor instead.
func (*AddOrderUpdateRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{11}
}

func (x *AddOrderUpdateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddOrderUpdateRequest) GetUpdate() *OrderUpdateEntry {
	if x != nil {
		return x.Update
	}
	return nil
}

type RemoveOrderUpdateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the order from which an update is removed.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The update to remove; matching by fields such as message/timestamp.
	Update        *OrderUpdateEntry `protobuf:"bytes,2,opt,name=update,proto3" json:"update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveOrderUpdateRequest) Reset() {
	*x = RemoveOrderUpdateRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveOrderUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveOrderUpdateRequest) ProtoMessage() {}

func (x *RemoveOrderUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveOrderUpdateRequest.ProtoReflect.Descriptor instead.
func (*RemoveOrderUpdateRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{12}
}

func (x *RemoveOrderUpdateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RemoveOrderUpdateRequest) GetUpdate() *OrderUpdateEntry {
	if x != nil {
		return x.Update
	}
	return nil
}

// New messages for managing allowed asset types.
type AddAllowedAssetTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID of the order to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Allowed asset type to add.
	AllowedAssetType v2.AssetType `protobuf:"varint,2,opt,name=allowed_asset_type,json=allowedAssetType,proto3,enum=hero.assets.v2.AssetType" json:"allowed_asset_type,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AddAllowedAssetTypeRequest) Reset() {
	*x = AddAllowedAssetTypeRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAllowedAssetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAllowedAssetTypeRequest) ProtoMessage() {}

func (x *AddAllowedAssetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAllowedAssetTypeRequest.ProtoReflect.Descriptor instead.
func (*AddAllowedAssetTypeRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{13}
}

func (x *AddAllowedAssetTypeRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddAllowedAssetTypeRequest) GetAllowedAssetType() v2.AssetType {
	if x != nil {
		return x.AllowedAssetType
	}
	return v2.AssetType(0)
}

type RemoveAllowedAssetTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID of the order to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Allowed asset type to remove.
	AllowedAssetType v2.AssetType `protobuf:"varint,2,opt,name=allowed_asset_type,json=allowedAssetType,proto3,enum=hero.assets.v2.AssetType" json:"allowed_asset_type,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RemoveAllowedAssetTypeRequest) Reset() {
	*x = RemoveAllowedAssetTypeRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveAllowedAssetTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveAllowedAssetTypeRequest) ProtoMessage() {}

func (x *RemoveAllowedAssetTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveAllowedAssetTypeRequest.ProtoReflect.Descriptor instead.
func (*RemoveAllowedAssetTypeRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{14}
}

func (x *RemoveAllowedAssetTypeRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RemoveAllowedAssetTypeRequest) GetAllowedAssetType() v2.AssetType {
	if x != nil {
		return x.AllowedAssetType
	}
	return v2.AssetType(0)
}

// New messages for managing blacklisted asset IDs.
type AddBlacklistedAssetIdRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID of the order to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Asset ID to add to the blacklist.
	AssetId       string `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddBlacklistedAssetIdRequest) Reset() {
	*x = AddBlacklistedAssetIdRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBlacklistedAssetIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBlacklistedAssetIdRequest) ProtoMessage() {}

func (x *AddBlacklistedAssetIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBlacklistedAssetIdRequest.ProtoReflect.Descriptor instead.
func (*AddBlacklistedAssetIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{15}
}

func (x *AddBlacklistedAssetIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddBlacklistedAssetIdRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type RemoveBlacklistedAssetIdRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID of the order to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Asset ID to remove from the blacklist.
	AssetId       string `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveBlacklistedAssetIdRequest) Reset() {
	*x = RemoveBlacklistedAssetIdRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveBlacklistedAssetIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveBlacklistedAssetIdRequest) ProtoMessage() {}

func (x *RemoveBlacklistedAssetIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveBlacklistedAssetIdRequest.ProtoReflect.Descriptor instead.
func (*RemoveBlacklistedAssetIdRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{16}
}

func (x *RemoveBlacklistedAssetIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RemoveBlacklistedAssetIdRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

// Status transition requests
type AcknowledgeOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AcknowledgeOrderRequest) Reset() {
	*x = AcknowledgeOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcknowledgeOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcknowledgeOrderRequest) ProtoMessage() {}

func (x *AcknowledgeOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcknowledgeOrderRequest.ProtoReflect.Descriptor instead.
func (*AcknowledgeOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{17}
}

func (x *AcknowledgeOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type RejectOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Optionally include a reason for rejection.
	Reason        string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectOrderRequest) Reset() {
	*x = RejectOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectOrderRequest) ProtoMessage() {}

func (x *RejectOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectOrderRequest.ProtoReflect.Descriptor instead.
func (*RejectOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{18}
}

func (x *RejectOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RejectOrderRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type SnoozeOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SnoozeReason  string                 `protobuf:"bytes,2,opt,name=snooze_reason,json=snoozeReason,proto3" json:"snooze_reason,omitempty"`
	SnoozeUntil   string                 `protobuf:"bytes,3,opt,name=snooze_until,json=snoozeUntil,proto3" json:"snooze_until,omitempty"` // ISO8601 timestamp
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SnoozeOrderRequest) Reset() {
	*x = SnoozeOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SnoozeOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnoozeOrderRequest) ProtoMessage() {}

func (x *SnoozeOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnoozeOrderRequest.ProtoReflect.Descriptor instead.
func (*SnoozeOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{19}
}

func (x *SnoozeOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SnoozeOrderRequest) GetSnoozeReason() string {
	if x != nil {
		return x.SnoozeReason
	}
	return ""
}

func (x *SnoozeOrderRequest) GetSnoozeUntil() string {
	if x != nil {
		return x.SnoozeUntil
	}
	return ""
}

type CancelOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelOrderRequest) Reset() {
	*x = CancelOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelOrderRequest) ProtoMessage() {}

func (x *CancelOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelOrderRequest.ProtoReflect.Descriptor instead.
func (*CancelOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{20}
}

func (x *CancelOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CancelOrderRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type CompleteOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompleteOrderRequest) Reset() {
	*x = CompleteOrderRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompleteOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteOrderRequest) ProtoMessage() {}

func (x *CompleteOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteOrderRequest.ProtoReflect.Descriptor instead.
func (*CompleteOrderRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{21}
}

func (x *CompleteOrderRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// *
//  1. List "Active Assigned" orders for a particular asset.
//     This includes only orders with status in [CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS].
type ListActiveAssignedOrdersForAssetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Asset ID for which to retrieve active assigned orders.
	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Optional pagination.
	PageSize      int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActiveAssignedOrdersForAssetRequest) Reset() {
	*x = ListActiveAssignedOrdersForAssetRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActiveAssignedOrdersForAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActiveAssignedOrdersForAssetRequest) ProtoMessage() {}

func (x *ListActiveAssignedOrdersForAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActiveAssignedOrdersForAssetRequest.ProtoReflect.Descriptor instead.
func (*ListActiveAssignedOrdersForAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{22}
}

func (x *ListActiveAssignedOrdersForAssetRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ListActiveAssignedOrdersForAssetRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListActiveAssignedOrdersForAssetRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListActiveAssignedOrdersForAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orders        []*Order               `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActiveAssignedOrdersForAssetResponse) Reset() {
	*x = ListActiveAssignedOrdersForAssetResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActiveAssignedOrdersForAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActiveAssignedOrdersForAssetResponse) ProtoMessage() {}

func (x *ListActiveAssignedOrdersForAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActiveAssignedOrdersForAssetResponse.ProtoReflect.Descriptor instead.
func (*ListActiveAssignedOrdersForAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{23}
}

func (x *ListActiveAssignedOrdersForAssetResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListActiveAssignedOrdersForAssetResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// *
//  2. List orders that are in the CREATED status for a particular asset.
//     Typically, this implies the asset_id is assigned to the order,
//     but you can also choose how your service filters the data (e.g., created for no assigned asset yet).
type ListNewOrdersForAssetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Asset ID for which to retrieve created orders.
	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Optional pagination.
	PageSize      int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNewOrdersForAssetRequest) Reset() {
	*x = ListNewOrdersForAssetRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNewOrdersForAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNewOrdersForAssetRequest) ProtoMessage() {}

func (x *ListNewOrdersForAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNewOrdersForAssetRequest.ProtoReflect.Descriptor instead.
func (*ListNewOrdersForAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{24}
}

func (x *ListNewOrdersForAssetRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ListNewOrdersForAssetRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListNewOrdersForAssetRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListNewOrdersForAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orders        []*Order               `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNewOrdersForAssetResponse) Reset() {
	*x = ListNewOrdersForAssetResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNewOrdersForAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNewOrdersForAssetResponse) ProtoMessage() {}

func (x *ListNewOrdersForAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNewOrdersForAssetResponse.ProtoReflect.Descriptor instead.
func (*ListNewOrdersForAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{25}
}

func (x *ListNewOrdersForAssetResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListNewOrdersForAssetResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// *
// 3. List orders for an asset with an optional filter by OrderStatus.
type ListOrdersForAssetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Asset ID for which to retrieve orders.
	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Optional pagination: number of orders to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional pagination token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter by order status.
	Status        OrderStatus `protobuf:"varint,4,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForAssetRequest) Reset() {
	*x = ListOrdersForAssetRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForAssetRequest) ProtoMessage() {}

func (x *ListOrdersForAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForAssetRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersForAssetRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{26}
}

func (x *ListOrdersForAssetRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ListOrdersForAssetRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOrdersForAssetRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListOrdersForAssetRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

type ListOrdersForAssetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orders        []*Order               `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForAssetResponse) Reset() {
	*x = ListOrdersForAssetResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForAssetResponse) ProtoMessage() {}

func (x *ListOrdersForAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForAssetResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersForAssetResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{27}
}

func (x *ListOrdersForAssetResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersForAssetResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Modified listing request for orders in a situation.
type ListOrdersForSituationRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the situation for which to list orders.
	SituationId string `protobuf:"bytes,1,opt,name=situation_id,json=situationId,proto3" json:"situation_id,omitempty"`
	// Optional pagination: number of orders to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional pagination token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter by order status.
	Status        OrderStatus `protobuf:"varint,4,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForSituationRequest) Reset() {
	*x = ListOrdersForSituationRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForSituationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForSituationRequest) ProtoMessage() {}

func (x *ListOrdersForSituationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForSituationRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersForSituationRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{28}
}

func (x *ListOrdersForSituationRequest) GetSituationId() string {
	if x != nil {
		return x.SituationId
	}
	return ""
}

func (x *ListOrdersForSituationRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOrdersForSituationRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListOrdersForSituationRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

type ListOrdersForSituationResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of orders associated with the given situation.
	Orders []*Order `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Token to retrieve the next page of results, if any.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForSituationResponse) Reset() {
	*x = ListOrdersForSituationResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForSituationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForSituationResponse) ProtoMessage() {}

func (x *ListOrdersForSituationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForSituationResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersForSituationResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{29}
}

func (x *ListOrdersForSituationResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersForSituationResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Listing orders by report ID
type ListOrdersForReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the report for which to list orders.
	ReportId string `protobuf:"bytes,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	// Optional pagination: number of orders to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional pagination token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter by order status.
	Status        OrderStatus `protobuf:"varint,4,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForReportRequest) Reset() {
	*x = ListOrdersForReportRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForReportRequest) ProtoMessage() {}

func (x *ListOrdersForReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForReportRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersForReportRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{30}
}

func (x *ListOrdersForReportRequest) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ListOrdersForReportRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOrdersForReportRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListOrdersForReportRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

type ListOrdersForReportResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of orders associated with the given report.
	Orders []*Order `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Token to retrieve the next page of results, if any.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForReportResponse) Reset() {
	*x = ListOrdersForReportResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForReportResponse) ProtoMessage() {}

func (x *ListOrdersForReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForReportResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersForReportResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{31}
}

func (x *ListOrdersForReportResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersForReportResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Listing orders by review round ID
type ListOrdersForReviewRoundRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the review round for which to list orders.
	ReviewRoundId string `protobuf:"bytes,1,opt,name=review_round_id,json=reviewRoundId,proto3" json:"review_round_id,omitempty"`
	// Optional pagination: number of orders to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional pagination token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter by order status.
	Status        OrderStatus `protobuf:"varint,4,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForReviewRoundRequest) Reset() {
	*x = ListOrdersForReviewRoundRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForReviewRoundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForReviewRoundRequest) ProtoMessage() {}

func (x *ListOrdersForReviewRoundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForReviewRoundRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersForReviewRoundRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{32}
}

func (x *ListOrdersForReviewRoundRequest) GetReviewRoundId() string {
	if x != nil {
		return x.ReviewRoundId
	}
	return ""
}

func (x *ListOrdersForReviewRoundRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOrdersForReviewRoundRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListOrdersForReviewRoundRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

type ListOrdersForReviewRoundResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of orders associated with the given review round.
	Orders []*Order `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Token to retrieve the next page of results, if any.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForReviewRoundResponse) Reset() {
	*x = ListOrdersForReviewRoundResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForReviewRoundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForReviewRoundResponse) ProtoMessage() {}

func (x *ListOrdersForReviewRoundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForReviewRoundResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersForReviewRoundResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{33}
}

func (x *ListOrdersForReviewRoundResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersForReviewRoundResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Listing orders by case ID
type ListOrdersForCaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the case for which to list orders.
	CaseId string `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// Optional pagination: number of orders to return.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional pagination token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional filter by order status.
	Status        OrderStatus `protobuf:"varint,4,opt,name=status,proto3,enum=hero.orders.v2.OrderStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForCaseRequest) Reset() {
	*x = ListOrdersForCaseRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCaseRequest) ProtoMessage() {}

func (x *ListOrdersForCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCaseRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersForCaseRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{34}
}

func (x *ListOrdersForCaseRequest) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ListOrdersForCaseRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListOrdersForCaseRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListOrdersForCaseRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

type ListOrdersForCaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of orders associated with the given case.
	Orders []*Order `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Token to retrieve the next page of results, if any.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersForCaseResponse) Reset() {
	*x = ListOrdersForCaseResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersForCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersForCaseResponse) ProtoMessage() {}

func (x *ListOrdersForCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersForCaseResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersForCaseResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{35}
}

func (x *ListOrdersForCaseResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersForCaseResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type UpdateOrderPermissionsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the order whose permissions are being updated.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The new permissions object to overwrite existing permissions.
	// (Alternatively, you can define logic to merge, but typically you replace the entire field).
	Permissions   *OrderPermissions `protobuf:"bytes,2,opt,name=permissions,proto3" json:"permissions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrderPermissionsRequest) Reset() {
	*x = UpdateOrderPermissionsRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrderPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderPermissionsRequest) ProtoMessage() {}

func (x *UpdateOrderPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderPermissionsRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateOrderPermissionsRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateOrderPermissionsRequest) GetPermissions() *OrderPermissions {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type AddAdditionalInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the order to update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// A JSON string containing the additional info to be merged.
	AdditionalInfoJson string `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddAdditionalInfoRequest) Reset() {
	*x = AddAdditionalInfoRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdditionalInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdditionalInfoRequest) ProtoMessage() {}

func (x *AddAdditionalInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdditionalInfoRequest.ProtoReflect.Descriptor instead.
func (*AddAdditionalInfoRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{37}
}

func (x *AddAdditionalInfoRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddAdditionalInfoRequest) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

type AddAdditionalInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The id of the updated order
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The updated additional_info_json object.
	AdditionalInfoJson string `protobuf:"bytes,2,opt,name=additional_info_json,json=additionalInfoJson,proto3" json:"additional_info_json,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddAdditionalInfoResponse) Reset() {
	*x = AddAdditionalInfoResponse{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAdditionalInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAdditionalInfoResponse) ProtoMessage() {}

func (x *AddAdditionalInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAdditionalInfoResponse.ProtoReflect.Descriptor instead.
func (*AddAdditionalInfoResponse) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{38}
}

func (x *AddAdditionalInfoResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddAdditionalInfoResponse) GetAdditionalInfoJson() string {
	if x != nil {
		return x.AdditionalInfoJson
	}
	return ""
}

// Request message to manually add a status update entry to an order.
type AddOrderStatusUpdateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the order to which we add a status update.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The new status update entry.
	StatusUpdate  *OrderStatusUpdateEntry `protobuf:"bytes,2,opt,name=status_update,json=statusUpdate,proto3" json:"status_update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddOrderStatusUpdateRequest) Reset() {
	*x = AddOrderStatusUpdateRequest{}
	mi := &file_hero_orders_v2_orders_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOrderStatusUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrderStatusUpdateRequest) ProtoMessage() {}

func (x *AddOrderStatusUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_orders_v2_orders_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrderStatusUpdateRequest.ProtoReflect.Descriptor instead.
func (*AddOrderStatusUpdateRequest) Descriptor() ([]byte, []int) {
	return file_hero_orders_v2_orders_proto_rawDescGZIP(), []int{39}
}

func (x *AddOrderStatusUpdateRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddOrderStatusUpdateRequest) GetStatusUpdate() *OrderStatusUpdateEntry {
	if x != nil {
		return x.StatusUpdate
	}
	return nil
}

var File_hero_orders_v2_orders_proto protoreflect.FileDescriptor

var file_hero_orders_v2_orders_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x65, 0x72, 0x6f,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x73, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x73, 0x69, 0x74, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x01, 0x0a,
	0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x45, 0x0a, 0x11, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x63, 0x61, 0x6e, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63,
	0x61, 0x6e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x22, 0x91, 0x01,
	0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x45, 0x0a, 0x0d, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0xf1, 0x03, 0x0a, 0x16, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x0f,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3a, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x44, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x6e, 0x65, 0x77, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6e, 0x65, 0x77, 0x54, 0x79,
	0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x41, 0x0a, 0x1d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x36, 0x0a,
	0x17, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xe9, 0x0a, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a,
	0x73, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x74, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x63, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x61, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x47, 0x0a, 0x20, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x49, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x75, 0x6e, 0x74, 0x69,
	0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x55,
	0x6e, 0x74, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x6e, 0x6f, 0x6f,
	0x7a, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x62, 0x6c, 0x61, 0x63, 0x6b,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x1b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73,
	0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x42, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x22, 0x41, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x22, 0x42, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x41, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xce,
	0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x22,
	0x6b, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x24, 0x0a, 0x12,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x61, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x06, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x64, 0x0a, 0x18, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x38, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x75, 0x0a, 0x1a, 0x41,
	0x64, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x12, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x78, 0x0a, 0x1d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x47, 0x0a, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x49, 0x0a, 0x1c,
	0x41, 0x64, 0x64, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x1f, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x29, 0x0a, 0x17, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x3c, 0x0a, 0x12, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x6c,
	0x0a, 0x12, 0x53, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6e, 0x6f,
	0x6f, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6e, 0x6f,
	0x6f, 0x7a, 0x65, 0x5f, 0x75, 0x6e, 0x74, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x55, 0x6e, 0x74, 0x69, 0x6c, 0x22, 0x3c, 0x0a, 0x12,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x26, 0x0a, 0x14, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x80, 0x01, 0x0a, 0x27, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46,
	0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x75, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x76, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa7, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x73, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xb3, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x74,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x77, 0x0a,
	0x1e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x69,
	0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x33,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x74, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xba, 0x01, 0x0a, 0x1f, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f,
	0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x79, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0xa4, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x72, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x73, 0x0a, 0x1d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x42, 0x0a,
	0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x5c, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a,
	0x14, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22,
	0x5d, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f,
	0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x7a,
	0x0a, 0x1b, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4b, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2a, 0xef, 0x01, 0x0a, 0x0b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x41, 0x43, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4e, 0x4f,
	0x4f, 0x5a, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x06,
	0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x2a, 0xeb, 0x02, 0x0a,
	0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x42,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54,
	0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x46,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x03, 0x12, 0x1b, 0x0a,
	0x17, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x49,
	0x47, 0x4e, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x41, 0x47, 0x45, 0x5f,
	0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x05, 0x12, 0x25,
	0x0a, 0x21, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x49,
	0x41, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x4d, 0x45, 0x52, 0x41, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x08,
	0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x09, 0x12, 0x1a,
	0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x4e,
	0x41, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x0a, 0x32, 0xa9, 0x13, 0x0a, 0x0c, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x1f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x53, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x21, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x4e, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x25, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x54, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x41, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x41, 0x64, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x5e, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x5c, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73,
	0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x42,
	0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x62, 0x0a, 0x18, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69,
	0x73, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x64, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x10, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0b, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x48, 0x0a, 0x0b, 0x53, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x22, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x53, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0b, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x24, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68,
	0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x95, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x37, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x12, 0x2c, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5e, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2d, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x77, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46,
	0x6f, 0x72, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x69, 0x74, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x2a,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f,
	0x75, 0x6e, 0x64, 0x12, 0x2f, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46,
	0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x12, 0x28, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x46, 0x6f, 0x72, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x68, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x41, 0x64,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x2b, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x15, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x1d, 0x5a, 0x1b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x68, 0x65, 0x72, 0x6f, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x76, 0x32, 0x3b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hero_orders_v2_orders_proto_rawDescOnce sync.Once
	file_hero_orders_v2_orders_proto_rawDescData = file_hero_orders_v2_orders_proto_rawDesc
)

func file_hero_orders_v2_orders_proto_rawDescGZIP() []byte {
	file_hero_orders_v2_orders_proto_rawDescOnce.Do(func() {
		file_hero_orders_v2_orders_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_orders_v2_orders_proto_rawDescData)
	})
	return file_hero_orders_v2_orders_proto_rawDescData
}

var file_hero_orders_v2_orders_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_hero_orders_v2_orders_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_hero_orders_v2_orders_proto_goTypes = []any{
	(OrderStatus)(0),                                 // 0: hero.orders.v2.OrderStatus
	(OrderType)(0),                                   // 1: hero.orders.v2.OrderType
	(*OrderPermissions)(nil),                         // 2: hero.orders.v2.OrderPermissions
	(*OrderUpdateEntry)(nil),                         // 3: hero.orders.v2.OrderUpdateEntry
	(*OrderStatusUpdateEntry)(nil),                   // 4: hero.orders.v2.OrderStatusUpdateEntry
	(*Order)(nil),                                    // 5: hero.orders.v2.Order
	(*CreateOrderRequest)(nil),                       // 6: hero.orders.v2.CreateOrderRequest
	(*CreateOrderResponse)(nil),                      // 7: hero.orders.v2.CreateOrderResponse
	(*GetOrderRequest)(nil),                          // 8: hero.orders.v2.GetOrderRequest
	(*UpdateOrderRequest)(nil),                       // 9: hero.orders.v2.UpdateOrderRequest
	(*ListOrdersRequest)(nil),                        // 10: hero.orders.v2.ListOrdersRequest
	(*ListOrdersResponse)(nil),                       // 11: hero.orders.v2.ListOrdersResponse
	(*DeleteOrderRequest)(nil),                       // 12: hero.orders.v2.DeleteOrderRequest
	(*AddOrderUpdateRequest)(nil),                    // 13: hero.orders.v2.AddOrderUpdateRequest
	(*RemoveOrderUpdateRequest)(nil),                 // 14: hero.orders.v2.RemoveOrderUpdateRequest
	(*AddAllowedAssetTypeRequest)(nil),               // 15: hero.orders.v2.AddAllowedAssetTypeRequest
	(*RemoveAllowedAssetTypeRequest)(nil),            // 16: hero.orders.v2.RemoveAllowedAssetTypeRequest
	(*AddBlacklistedAssetIdRequest)(nil),             // 17: hero.orders.v2.AddBlacklistedAssetIdRequest
	(*RemoveBlacklistedAssetIdRequest)(nil),          // 18: hero.orders.v2.RemoveBlacklistedAssetIdRequest
	(*AcknowledgeOrderRequest)(nil),                  // 19: hero.orders.v2.AcknowledgeOrderRequest
	(*RejectOrderRequest)(nil),                       // 20: hero.orders.v2.RejectOrderRequest
	(*SnoozeOrderRequest)(nil),                       // 21: hero.orders.v2.SnoozeOrderRequest
	(*CancelOrderRequest)(nil),                       // 22: hero.orders.v2.CancelOrderRequest
	(*CompleteOrderRequest)(nil),                     // 23: hero.orders.v2.CompleteOrderRequest
	(*ListActiveAssignedOrdersForAssetRequest)(nil),  // 24: hero.orders.v2.ListActiveAssignedOrdersForAssetRequest
	(*ListActiveAssignedOrdersForAssetResponse)(nil), // 25: hero.orders.v2.ListActiveAssignedOrdersForAssetResponse
	(*ListNewOrdersForAssetRequest)(nil),             // 26: hero.orders.v2.ListNewOrdersForAssetRequest
	(*ListNewOrdersForAssetResponse)(nil),            // 27: hero.orders.v2.ListNewOrdersForAssetResponse
	(*ListOrdersForAssetRequest)(nil),                // 28: hero.orders.v2.ListOrdersForAssetRequest
	(*ListOrdersForAssetResponse)(nil),               // 29: hero.orders.v2.ListOrdersForAssetResponse
	(*ListOrdersForSituationRequest)(nil),            // 30: hero.orders.v2.ListOrdersForSituationRequest
	(*ListOrdersForSituationResponse)(nil),           // 31: hero.orders.v2.ListOrdersForSituationResponse
	(*ListOrdersForReportRequest)(nil),               // 32: hero.orders.v2.ListOrdersForReportRequest
	(*ListOrdersForReportResponse)(nil),              // 33: hero.orders.v2.ListOrdersForReportResponse
	(*ListOrdersForReviewRoundRequest)(nil),          // 34: hero.orders.v2.ListOrdersForReviewRoundRequest
	(*ListOrdersForReviewRoundResponse)(nil),         // 35: hero.orders.v2.ListOrdersForReviewRoundResponse
	(*ListOrdersForCaseRequest)(nil),                 // 36: hero.orders.v2.ListOrdersForCaseRequest
	(*ListOrdersForCaseResponse)(nil),                // 37: hero.orders.v2.ListOrdersForCaseResponse
	(*UpdateOrderPermissionsRequest)(nil),            // 38: hero.orders.v2.UpdateOrderPermissionsRequest
	(*AddAdditionalInfoRequest)(nil),                 // 39: hero.orders.v2.AddAdditionalInfoRequest
	(*AddAdditionalInfoResponse)(nil),                // 40: hero.orders.v2.AddAdditionalInfoResponse
	(*AddOrderStatusUpdateRequest)(nil),              // 41: hero.orders.v2.AddOrderStatusUpdateRequest
	(v2.AssetType)(0),                                // 42: hero.assets.v2.AssetType
	(v21.UpdateSource)(0),                            // 43: hero.situations.v2.UpdateSource
	(*emptypb.Empty)(nil),                            // 44: google.protobuf.Empty
}
var file_hero_orders_v2_orders_proto_depIdxs = []int32{
	42, // 0: hero.orders.v2.OrderPermissions.can_change_status:type_name -> hero.assets.v2.AssetType
	42, // 1: hero.orders.v2.OrderPermissions.can_assign_asset:type_name -> hero.assets.v2.AssetType
	43, // 2: hero.orders.v2.OrderUpdateEntry.update_source:type_name -> hero.situations.v2.UpdateSource
	0,  // 3: hero.orders.v2.OrderStatusUpdateEntry.new_status:type_name -> hero.orders.v2.OrderStatus
	0,  // 4: hero.orders.v2.OrderStatusUpdateEntry.previous_status:type_name -> hero.orders.v2.OrderStatus
	43, // 5: hero.orders.v2.OrderStatusUpdateEntry.update_source:type_name -> hero.situations.v2.UpdateSource
	1,  // 6: hero.orders.v2.Order.type:type_name -> hero.orders.v2.OrderType
	0,  // 7: hero.orders.v2.Order.status:type_name -> hero.orders.v2.OrderStatus
	3,  // 8: hero.orders.v2.Order.updates:type_name -> hero.orders.v2.OrderUpdateEntry
	43, // 9: hero.orders.v2.Order.created_by:type_name -> hero.situations.v2.UpdateSource
	42, // 10: hero.orders.v2.Order.allowed_asset_types:type_name -> hero.assets.v2.AssetType
	2,  // 11: hero.orders.v2.Order.permissions:type_name -> hero.orders.v2.OrderPermissions
	4,  // 12: hero.orders.v2.Order.status_updates:type_name -> hero.orders.v2.OrderStatusUpdateEntry
	5,  // 13: hero.orders.v2.CreateOrderRequest.order:type_name -> hero.orders.v2.Order
	5,  // 14: hero.orders.v2.CreateOrderResponse.order:type_name -> hero.orders.v2.Order
	5,  // 15: hero.orders.v2.UpdateOrderRequest.order:type_name -> hero.orders.v2.Order
	0,  // 16: hero.orders.v2.ListOrdersRequest.status:type_name -> hero.orders.v2.OrderStatus
	1,  // 17: hero.orders.v2.ListOrdersRequest.type:type_name -> hero.orders.v2.OrderType
	5,  // 18: hero.orders.v2.ListOrdersResponse.orders:type_name -> hero.orders.v2.Order
	3,  // 19: hero.orders.v2.AddOrderUpdateRequest.update:type_name -> hero.orders.v2.OrderUpdateEntry
	3,  // 20: hero.orders.v2.RemoveOrderUpdateRequest.update:type_name -> hero.orders.v2.OrderUpdateEntry
	42, // 21: hero.orders.v2.AddAllowedAssetTypeRequest.allowed_asset_type:type_name -> hero.assets.v2.AssetType
	42, // 22: hero.orders.v2.RemoveAllowedAssetTypeRequest.allowed_asset_type:type_name -> hero.assets.v2.AssetType
	5,  // 23: hero.orders.v2.ListActiveAssignedOrdersForAssetResponse.orders:type_name -> hero.orders.v2.Order
	5,  // 24: hero.orders.v2.ListNewOrdersForAssetResponse.orders:type_name -> hero.orders.v2.Order
	0,  // 25: hero.orders.v2.ListOrdersForAssetRequest.status:type_name -> hero.orders.v2.OrderStatus
	5,  // 26: hero.orders.v2.ListOrdersForAssetResponse.orders:type_name -> hero.orders.v2.Order
	0,  // 27: hero.orders.v2.ListOrdersForSituationRequest.status:type_name -> hero.orders.v2.OrderStatus
	5,  // 28: hero.orders.v2.ListOrdersForSituationResponse.orders:type_name -> hero.orders.v2.Order
	0,  // 29: hero.orders.v2.ListOrdersForReportRequest.status:type_name -> hero.orders.v2.OrderStatus
	5,  // 30: hero.orders.v2.ListOrdersForReportResponse.orders:type_name -> hero.orders.v2.Order
	0,  // 31: hero.orders.v2.ListOrdersForReviewRoundRequest.status:type_name -> hero.orders.v2.OrderStatus
	5,  // 32: hero.orders.v2.ListOrdersForReviewRoundResponse.orders:type_name -> hero.orders.v2.Order
	0,  // 33: hero.orders.v2.ListOrdersForCaseRequest.status:type_name -> hero.orders.v2.OrderStatus
	5,  // 34: hero.orders.v2.ListOrdersForCaseResponse.orders:type_name -> hero.orders.v2.Order
	2,  // 35: hero.orders.v2.UpdateOrderPermissionsRequest.permissions:type_name -> hero.orders.v2.OrderPermissions
	4,  // 36: hero.orders.v2.AddOrderStatusUpdateRequest.status_update:type_name -> hero.orders.v2.OrderStatusUpdateEntry
	6,  // 37: hero.orders.v2.OrderService.CreateOrder:input_type -> hero.orders.v2.CreateOrderRequest
	8,  // 38: hero.orders.v2.OrderService.GetOrder:input_type -> hero.orders.v2.GetOrderRequest
	9,  // 39: hero.orders.v2.OrderService.UpdateOrder:input_type -> hero.orders.v2.UpdateOrderRequest
	10, // 40: hero.orders.v2.OrderService.ListOrders:input_type -> hero.orders.v2.ListOrdersRequest
	12, // 41: hero.orders.v2.OrderService.DeleteOrder:input_type -> hero.orders.v2.DeleteOrderRequest
	13, // 42: hero.orders.v2.OrderService.AddOrderUpdate:input_type -> hero.orders.v2.AddOrderUpdateRequest
	14, // 43: hero.orders.v2.OrderService.RemoveOrderUpdate:input_type -> hero.orders.v2.RemoveOrderUpdateRequest
	15, // 44: hero.orders.v2.OrderService.AddAllowedAssetType:input_type -> hero.orders.v2.AddAllowedAssetTypeRequest
	16, // 45: hero.orders.v2.OrderService.RemoveAllowedAssetType:input_type -> hero.orders.v2.RemoveAllowedAssetTypeRequest
	17, // 46: hero.orders.v2.OrderService.AddBlacklistedAssetId:input_type -> hero.orders.v2.AddBlacklistedAssetIdRequest
	18, // 47: hero.orders.v2.OrderService.RemoveBlacklistedAssetId:input_type -> hero.orders.v2.RemoveBlacklistedAssetIdRequest
	19, // 48: hero.orders.v2.OrderService.AcknowledgeOrder:input_type -> hero.orders.v2.AcknowledgeOrderRequest
	20, // 49: hero.orders.v2.OrderService.RejectOrder:input_type -> hero.orders.v2.RejectOrderRequest
	21, // 50: hero.orders.v2.OrderService.SnoozeOrder:input_type -> hero.orders.v2.SnoozeOrderRequest
	22, // 51: hero.orders.v2.OrderService.CancelOrder:input_type -> hero.orders.v2.CancelOrderRequest
	23, // 52: hero.orders.v2.OrderService.CompleteOrder:input_type -> hero.orders.v2.CompleteOrderRequest
	24, // 53: hero.orders.v2.OrderService.ListActiveAssignedOrdersForAsset:input_type -> hero.orders.v2.ListActiveAssignedOrdersForAssetRequest
	26, // 54: hero.orders.v2.OrderService.ListNewOrdersForAsset:input_type -> hero.orders.v2.ListNewOrdersForAssetRequest
	38, // 55: hero.orders.v2.OrderService.UpdateOrderPermissions:input_type -> hero.orders.v2.UpdateOrderPermissionsRequest
	30, // 56: hero.orders.v2.OrderService.ListOrdersForSituation:input_type -> hero.orders.v2.ListOrdersForSituationRequest
	28, // 57: hero.orders.v2.OrderService.ListOrdersForAsset:input_type -> hero.orders.v2.ListOrdersForAssetRequest
	32, // 58: hero.orders.v2.OrderService.ListOrdersForReport:input_type -> hero.orders.v2.ListOrdersForReportRequest
	34, // 59: hero.orders.v2.OrderService.ListOrdersForReviewRound:input_type -> hero.orders.v2.ListOrdersForReviewRoundRequest
	36, // 60: hero.orders.v2.OrderService.ListOrdersForCase:input_type -> hero.orders.v2.ListOrdersForCaseRequest
	39, // 61: hero.orders.v2.OrderService.AddAdditionalInfo:input_type -> hero.orders.v2.AddAdditionalInfoRequest
	41, // 62: hero.orders.v2.OrderService.AddOrderStatusUpdate:input_type -> hero.orders.v2.AddOrderStatusUpdateRequest
	7,  // 63: hero.orders.v2.OrderService.CreateOrder:output_type -> hero.orders.v2.CreateOrderResponse
	5,  // 64: hero.orders.v2.OrderService.GetOrder:output_type -> hero.orders.v2.Order
	5,  // 65: hero.orders.v2.OrderService.UpdateOrder:output_type -> hero.orders.v2.Order
	11, // 66: hero.orders.v2.OrderService.ListOrders:output_type -> hero.orders.v2.ListOrdersResponse
	44, // 67: hero.orders.v2.OrderService.DeleteOrder:output_type -> google.protobuf.Empty
	5,  // 68: hero.orders.v2.OrderService.AddOrderUpdate:output_type -> hero.orders.v2.Order
	5,  // 69: hero.orders.v2.OrderService.RemoveOrderUpdate:output_type -> hero.orders.v2.Order
	5,  // 70: hero.orders.v2.OrderService.AddAllowedAssetType:output_type -> hero.orders.v2.Order
	5,  // 71: hero.orders.v2.OrderService.RemoveAllowedAssetType:output_type -> hero.orders.v2.Order
	5,  // 72: hero.orders.v2.OrderService.AddBlacklistedAssetId:output_type -> hero.orders.v2.Order
	5,  // 73: hero.orders.v2.OrderService.RemoveBlacklistedAssetId:output_type -> hero.orders.v2.Order
	5,  // 74: hero.orders.v2.OrderService.AcknowledgeOrder:output_type -> hero.orders.v2.Order
	5,  // 75: hero.orders.v2.OrderService.RejectOrder:output_type -> hero.orders.v2.Order
	5,  // 76: hero.orders.v2.OrderService.SnoozeOrder:output_type -> hero.orders.v2.Order
	5,  // 77: hero.orders.v2.OrderService.CancelOrder:output_type -> hero.orders.v2.Order
	5,  // 78: hero.orders.v2.OrderService.CompleteOrder:output_type -> hero.orders.v2.Order
	25, // 79: hero.orders.v2.OrderService.ListActiveAssignedOrdersForAsset:output_type -> hero.orders.v2.ListActiveAssignedOrdersForAssetResponse
	27, // 80: hero.orders.v2.OrderService.ListNewOrdersForAsset:output_type -> hero.orders.v2.ListNewOrdersForAssetResponse
	5,  // 81: hero.orders.v2.OrderService.UpdateOrderPermissions:output_type -> hero.orders.v2.Order
	31, // 82: hero.orders.v2.OrderService.ListOrdersForSituation:output_type -> hero.orders.v2.ListOrdersForSituationResponse
	29, // 83: hero.orders.v2.OrderService.ListOrdersForAsset:output_type -> hero.orders.v2.ListOrdersForAssetResponse
	33, // 84: hero.orders.v2.OrderService.ListOrdersForReport:output_type -> hero.orders.v2.ListOrdersForReportResponse
	35, // 85: hero.orders.v2.OrderService.ListOrdersForReviewRound:output_type -> hero.orders.v2.ListOrdersForReviewRoundResponse
	37, // 86: hero.orders.v2.OrderService.ListOrdersForCase:output_type -> hero.orders.v2.ListOrdersForCaseResponse
	40, // 87: hero.orders.v2.OrderService.AddAdditionalInfo:output_type -> hero.orders.v2.AddAdditionalInfoResponse
	5,  // 88: hero.orders.v2.OrderService.AddOrderStatusUpdate:output_type -> hero.orders.v2.Order
	63, // [63:89] is the sub-list for method output_type
	37, // [37:63] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_hero_orders_v2_orders_proto_init() }
func file_hero_orders_v2_orders_proto_init() {
	if File_hero_orders_v2_orders_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_orders_v2_orders_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_orders_v2_orders_proto_goTypes,
		DependencyIndexes: file_hero_orders_v2_orders_proto_depIdxs,
		EnumInfos:         file_hero_orders_v2_orders_proto_enumTypes,
		MessageInfos:      file_hero_orders_v2_orders_proto_msgTypes,
	}.Build()
	File_hero_orders_v2_orders_proto = out.File
	file_hero_orders_v2_orders_proto_rawDesc = nil
	file_hero_orders_v2_orders_proto_goTypes = nil
	file_hero_orders_v2_orders_proto_depIdxs = nil
}
