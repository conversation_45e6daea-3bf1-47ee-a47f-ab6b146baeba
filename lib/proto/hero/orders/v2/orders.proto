syntax = "proto3";

package hero.orders.v2;

option go_package = "proto/hero/orders/v2;orders";

import "google/protobuf/empty.proto";
import "hero/assets/v2/assets.proto";         
import "hero/situations/v2/situations.proto";  

// ----------------------------------------------
// Enums
// ----------------------------------------------
enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0; // Default unspecified order status.
  ORDER_STATUS_CREATED = 1;            // Order has been created.
  ORDER_STATUS_ACKNOWLEDGED = 2;       // As<PERSON> has acknowledged the order.
  ORDER_STATUS_REJECTED = 3;           // Order was rejected by the asset.
  ORDER_STATUS_SNOOZED = 4;            // Order has been snoozed (delayed).
  ORDER_STATUS_IN_PROGRESS = 5;        // Order is actively being worked on.
  ORDER_STATUS_COMPLETED = 6;          // Order has been completed.
  ORDER_STATUS_CANCELLED = 7;          // Order has been cancelled.
}

enum OrderType {
  ORDER_TYPE_UNSPECIFIED = 0;            // Default unspecified order type.
  ORDER_TYPE_TRIAGE_MEMBER_REPORT = 1;   // Order for triaging a situation reported by a member.
  ORDER_TYPE_ASSIST_MEMBER = 2;          // Order for assisting a member.
  ORDER_TYPE_SUBMIT_FINAL_REPORT = 3;    // Order to submit a final report.
  ORDER_TYPE_ASSIGN_AGENT = 4;           // Order to assign an agent to a task.
  ORDER_TYPE_TRIAGE_AGENT_REPORT = 5;    // Order for triaging a situation reported by an agent.
  ORDER_TYPE_TRIAGE_CAMERA_INCIDENT = 6; // Order for triaging an incident captured by a camera.
  ORDER_TYPE_WRITE_REPORT = 7;           // Order for writing a report.
  ORDER_TYPE_REVIEW_REPORT = 8;          // Order for reviewing a report.
  ORDER_TYPE_REVISE_REPORT = 9;          // Order for revising a report.
  ORDER_TYPE_MANAGE_CASE = 10;           // Order for managing an assigned case as primary investigator.
}

// ----------------------------------------------
// Permissions sub-message
// ----------------------------------------------
message OrderPermissions {
  // Which AssetTypes are allowed to change this order's status.
  repeated hero.assets.v2.AssetType can_change_status = 1;

  // Which AssetTypes are allowed to add/change the assigned asset_id.
  repeated hero.assets.v2.AssetType can_assign_asset = 2;
}

// ----------------------------------------------
// Messages
// ----------------------------------------------
message OrderUpdateEntry {
  string message = 1;
  string timestamp = 2;  // ISO8601 timestamp
  // Re-use the shared UpdateSource enum from situation.proto.
  hero.situations.v2.UpdateSource update_source = 3;
}

message OrderStatusUpdateEntry {
  // The time at which the status update occurred.
  string entry_timestamp = 1;  // ISO8601 timestamp
  
  // The new status of the order.
  OrderStatus new_status = 2;
  
  // The previous status before this update.
  OrderStatus previous_status = 3;
  
  // The new type-specific status of the order.
  string new_type_specific_status = 4;
  
  // The previous type-specific status before this update.
  string previous_type_specific_status = 5;
  
  // (Optional) A note explaining the status change.
  string note = 6;
  
  // (Optional) ID of the updater.
  string updater_id = 7;
  
  // (Optional) Source of the update.
  hero.situations.v2.UpdateSource update_source = 8;

  // The time at which the status update was set to - defaults to the timestamp field unless manually overridden.
  string status_update_timestamp = 9;
}

message Order {
  // Unique identifier for the order.
  string id = 1;

  // Reference to the associated situation.
  string situation_id = 2;

  // Reference to the asset assigned to execute this order.
  string asset_id = 3;

  // Specifies what type of order this is.
  OrderType type = 4;

  // Indicates the current execution status of the order.
  OrderStatus status = 5;

  // Detailed instructions for what the asset needs to do.
  string instructions = 6;

  // Priority level of the order.
  int32 priority = 7;

  // JSON string (or any relevant format) containing order-specific parameters.
  string additional_info_json = 8;

  // Field to hold additional status information specific to the order type.
  string type_specific_status = 9;

  // Free-form notes or comments regarding the order.
  string notes = 10;

  // Timestamp when the order was created.
  string create_time = 11;  // ISO8601 timestamp

  // Timestamp when the order was last updated.
  string update_time = 12;  // ISO8601 timestamp

  // Timestamp when the order was completed (if applicable).
  string completion_time = 13;  // ISO8601 timestamp

  // A list of update entries to track changes and status transitions.
  repeated OrderUpdateEntry updates = 14;

  // Timestamp when the order was actually assigned to an asset.
  string assigned_time = 15;  // ISO8601 timestamp

  // Timestamp when the asset acknowledged the order.
  string acknowledged_time = 16;  // ISO8601 timestamp

  // Expected or estimated timestamp for order completion.
  string estimated_completion_time = 17;  // ISO8601 timestamp

  // Reason for cancellation or rejection of the order, if applicable.
  string cancellation_or_rejection_reason = 18;

  // Number of times the order has been retried.
  int32 retry_count = 19;

  // Indicates the source of order creation using the shared UpdateSource enum.
  hero.situations.v2.UpdateSource created_by = 20;

  // A short descriptive title for the order.
  string title = 21;

  // Indicates the type(s) of assets that can perform this order.
  repeated hero.assets.v2.AssetType allowed_asset_types = 23;

  // Reason for snoozing the order.
  string snooze_reason = 24;

  // Timestamp until which the order is snoozed.
  string snooze_until = 25;  // ISO8601 timestamp

  // Number of times the order has been snoozed.
  int32 snooze_count = 26;

  // List of asset IDs that should NOT be assigned to this order.
  repeated string blacklisted_asset_ids = 27;

  // For orders it will be fixed value "ORDER".
  string resource_type = 28;

  // Permissions specifying which asset types can change status or reassign assets.
  OrderPermissions permissions = 29;

  // Timeseries status updates for the order.
  repeated OrderStatusUpdateEntry status_updates = 30;

  // Reference this order to a report
  string report_id = 31;

  // If this is a review order, point back at the ReviewRound
  string review_round_id = 32;

  // Reference this order to a case (for MANAGE_CASE orders)
  string case_id = 33;
}

// ----------------------------------------------
// Request/Response Messages
// ----------------------------------------------
message CreateOrderRequest {
  // Order to be created (id may be omitted and auto-generated by the system).
  Order order = 1;
}

message CreateOrderResponse {
  Order order = 1;
}

message GetOrderRequest {
  // ID of the order to retrieve.
  string id = 1;
}

message UpdateOrderRequest {
  // The updated order object. Must contain the ID of the order to update.
  Order order = 1;
}

// Request message to list orders with pagination, filtering, and ordering options.
message ListOrdersRequest {
  // Maximum number of orders to return in the response.
  int32 page_size = 1;

  // A token identifying a specific page of results to retrieve.
  string page_token = 2;

  // Optional filter: Returns only orders matching the specified status.
  // A value of ORDER_STATUS_UNSPECIFIED indicates no filtering by status.
  OrderStatus status = 3;

  // Optional filter: Returns only orders of the specified type.
  OrderType type = 4;

  // Optional: Specifies the ordering of returned orders (e.g., "create_time desc").
  string order_by = 5;
}

message ListOrdersResponse {
  repeated Order orders = 1;
  string next_page_token = 2;
}

message DeleteOrderRequest {
  string id = 1;
}

message AddOrderUpdateRequest {
  // The ID of the order to which we add an update.
  string id = 1;
  // The new update entry.
  OrderUpdateEntry update = 2;
}

message RemoveOrderUpdateRequest {
  // The ID of the order from which an update is removed.
  string id = 1;
  // The update to remove; matching by fields such as message/timestamp.
  OrderUpdateEntry update = 2;
}

// New messages for managing allowed asset types.
message AddAllowedAssetTypeRequest {
  // ID of the order to update.
  string id = 1;
  // Allowed asset type to add.
  hero.assets.v2.AssetType allowed_asset_type = 2;
}

message RemoveAllowedAssetTypeRequest {
  // ID of the order to update.
  string id = 1;
  // Allowed asset type to remove.
  hero.assets.v2.AssetType allowed_asset_type = 2;
}

// New messages for managing blacklisted asset IDs.
message AddBlacklistedAssetIdRequest {
  // ID of the order to update.
  string id = 1;
  // Asset ID to add to the blacklist.
  string asset_id = 2;
}

message RemoveBlacklistedAssetIdRequest {
  // ID of the order to update.
  string id = 1;
  // Asset ID to remove from the blacklist.
  string asset_id = 2;
}

// Status transition requests
message AcknowledgeOrderRequest {
  string id = 1;
}

message RejectOrderRequest {
  string id = 1;
  // Optionally include a reason for rejection.
  string reason = 2;
}

message SnoozeOrderRequest {
  string id = 1;
  string snooze_reason = 2;
  string snooze_until = 3;  // ISO8601 timestamp
}

message CancelOrderRequest {
  string id = 1;
  string reason = 2;
}

message CompleteOrderRequest {
  string id = 1;
}

// Specialized listing requests

/**
 * 1. List "Active Assigned" orders for a particular asset.
 *    This includes only orders with status in [CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS].
 */
message ListActiveAssignedOrdersForAssetRequest {
  // Asset ID for which to retrieve active assigned orders.
  string asset_id = 1;
  // Optional pagination.
  int32 page_size = 2;
  string page_token = 3;
}

message ListActiveAssignedOrdersForAssetResponse {
  repeated Order orders = 1;
  string next_page_token = 2;
}

/**
 * 2. List orders that are in the CREATED status for a particular asset.
 *    Typically, this implies the asset_id is assigned to the order,
 *    but you can also choose how your service filters the data (e.g., created for no assigned asset yet).
 */
message ListNewOrdersForAssetRequest {
  // Asset ID for which to retrieve created orders.
  string asset_id = 1;
  // Optional pagination.
  int32 page_size = 2;
  string page_token = 3;
}

message ListNewOrdersForAssetResponse {
  repeated Order orders = 1;
  string next_page_token = 2;
}

/**
 * 3. List orders for an asset with an optional filter by OrderStatus.
 */
message ListOrdersForAssetRequest {
  // Asset ID for which to retrieve orders.
  string asset_id = 1;
  // Optional pagination: number of orders to return.
  int32 page_size = 2;
  // Optional pagination token.
  string page_token = 3;
  // Optional filter by order status.
  OrderStatus status = 4;
}

message ListOrdersForAssetResponse {
  repeated Order orders = 1;
  string next_page_token = 2;
}

// Modified listing request for orders in a situation.
message ListOrdersForSituationRequest {
  // The ID of the situation for which to list orders.
  string situation_id = 1;
  // Optional pagination: number of orders to return.
  int32 page_size = 2;
  // Optional pagination token.
  string page_token = 3;
  // Optional filter by order status.
  OrderStatus status = 4;
}

message ListOrdersForSituationResponse {
  // The list of orders associated with the given situation.
  repeated Order orders = 1;
  // Token to retrieve the next page of results, if any.
  string next_page_token = 2;
}

// Listing orders by report ID
message ListOrdersForReportRequest {
  // The ID of the report for which to list orders.
  string report_id = 1;
  // Optional pagination: number of orders to return.
  int32 page_size = 2;
  // Optional pagination token.
  string page_token = 3;
  // Optional filter by order status.
  OrderStatus status = 4;
}

message ListOrdersForReportResponse {
  // The list of orders associated with the given report.
  repeated Order orders = 1;
  // Token to retrieve the next page of results, if any.
  string next_page_token = 2;
}

// Listing orders by review round ID
message ListOrdersForReviewRoundRequest {
  // The ID of the review round for which to list orders.
  string review_round_id = 1;
  // Optional pagination: number of orders to return.
  int32 page_size = 2;
  // Optional pagination token.
  string page_token = 3;
  // Optional filter by order status.
  OrderStatus status = 4;
}

message ListOrdersForReviewRoundResponse {
  // The list of orders associated with the given review round.
  repeated Order orders = 1;
  // Token to retrieve the next page of results, if any.
  string next_page_token = 2;
}

// Listing orders by case ID
message ListOrdersForCaseRequest {
  // The ID of the case for which to list orders.
  string case_id = 1;
  // Optional pagination: number of orders to return.
  int32 page_size = 2;
  // Optional pagination token.
  string page_token = 3;
  // Optional filter by order status.
  OrderStatus status = 4;
}

message ListOrdersForCaseResponse {
  // The list of orders associated with the given case.
  repeated Order orders = 1;
  // Token to retrieve the next page of results, if any.
  string next_page_token = 2;
}

message UpdateOrderPermissionsRequest {
  // The ID of the order whose permissions are being updated.
  string id = 1;

  // The new permissions object to overwrite existing permissions.
  // (Alternatively, you can define logic to merge, but typically you replace the entire field).
  OrderPermissions permissions = 2;
}

message AddAdditionalInfoRequest {
  // The ID of the order to update.
  string id = 1;
  // A JSON string containing the additional info to be merged.
  string additional_info_json = 2;
}

message AddAdditionalInfoResponse {
  // The id of the updated order 
  string id = 1;
  // The updated additional_info_json object.
  string additional_info_json = 2;
}

// Request message to manually add a status update entry to an order.
message AddOrderStatusUpdateRequest {
  // The ID of the order to which we add a status update.
  string id = 1;
  // The new status update entry.
  OrderStatusUpdateEntry status_update = 2;
}

// ----------------------------------------------
// Service Definition
// ----------------------------------------------
service OrderService {
  // Create a new order.
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);

  // Retrieve an order by its ID.
  rpc GetOrder(GetOrderRequest) returns (Order);

  // Update an existing order.
  rpc UpdateOrder(UpdateOrderRequest) returns (Order);

  // List orders with optional filtering, ordering, and pagination.
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);

  // Delete an order by its ID.
  rpc DeleteOrder(DeleteOrderRequest) returns (google.protobuf.Empty);

  // Add an update entry to an existing order.
  rpc AddOrderUpdate(AddOrderUpdateRequest) returns (Order);

  // Remove an update entry from an existing order.
  rpc RemoveOrderUpdate(RemoveOrderUpdateRequest) returns (Order);

  // Add an allowed asset type to an order.
  rpc AddAllowedAssetType(AddAllowedAssetTypeRequest) returns (Order);

  // Remove an allowed asset type from an order.
  rpc RemoveAllowedAssetType(RemoveAllowedAssetTypeRequest) returns (Order);

  // Add a blacklisted asset ID to an order.
  rpc AddBlacklistedAssetId(AddBlacklistedAssetIdRequest) returns (Order);

  // Remove a blacklisted asset ID from an order.
  rpc RemoveBlacklistedAssetId(RemoveBlacklistedAssetIdRequest) returns (Order);

  // Acknowledge an order.
  rpc AcknowledgeOrder(AcknowledgeOrderRequest) returns (Order);

  // Reject an order.
  rpc RejectOrder(RejectOrderRequest) returns (Order);

  // Snooze an order.
  rpc SnoozeOrder(SnoozeOrderRequest) returns (Order);

  // Cancel an order.
  rpc CancelOrder(CancelOrderRequest) returns (Order);

  // Complete an order.
  rpc CompleteOrder(CompleteOrderRequest) returns (Order);

  // ListActiveAssignedOrdersForAsset returns orders for the given asset that are in statuses: CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS.
  rpc ListActiveAssignedOrdersForAsset(ListActiveAssignedOrdersForAssetRequest)
      returns (ListActiveAssignedOrdersForAssetResponse);

  // ListNewOrdersForAsset returns orders in CREATED status for the given asset.
  rpc ListNewOrdersForAsset(ListNewOrdersForAssetRequest)
      returns (ListNewOrdersForAssetResponse);

  // UpdateOrderPermissions updates the permissions for an existing order.
  rpc UpdateOrderPermissions(UpdateOrderPermissionsRequest) returns (Order);

  // ListOrdersForSituation returns a paginated list of orders for the given situation with optional status filter.
  rpc ListOrdersForSituation(ListOrdersForSituationRequest)
      returns (ListOrdersForSituationResponse);
  
  // ListOrdersForAsset returns a paginated list of orders for the given asset with optional status filter.
  rpc ListOrdersForAsset(ListOrdersForAssetRequest)
      returns (ListOrdersForAssetResponse);

  // ListOrdersForReport returns orders for the given report with optional status filter.
  rpc ListOrdersForReport(ListOrdersForReportRequest) returns (ListOrdersForReportResponse);

  // ListOrdersForReviewRound returns orders for the given review round with optional status filter.
  rpc ListOrdersForReviewRound(ListOrdersForReviewRoundRequest) returns (ListOrdersForReviewRoundResponse);

  // ListOrdersForCase returns orders for the given case with optional status filter.
  rpc ListOrdersForCase(ListOrdersForCaseRequest) returns (ListOrdersForCaseResponse);

  // Add additional info to an order by merging provided JSON into the existing additional_info_json.
  rpc AddAdditionalInfo(AddAdditionalInfoRequest) returns (AddAdditionalInfoResponse);

  // Add a status update to an order.
  rpc AddOrderStatusUpdate(AddOrderStatusUpdateRequest) returns (Order);
}
