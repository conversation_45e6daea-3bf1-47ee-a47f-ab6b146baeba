// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/orders/v2/orders.proto

package ordersconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v2 "proto/hero/orders/v2"
	strings "strings"

	connect "connectrpc.com/connect"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// OrderServiceName is the fully-qualified name of the OrderService service.
	OrderServiceName = "hero.orders.v2.OrderService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// OrderServiceCreateOrderProcedure is the fully-qualified name of the OrderService's CreateOrder
	// RPC.
	OrderServiceCreateOrderProcedure = "/hero.orders.v2.OrderService/CreateOrder"
	// OrderServiceGetOrderProcedure is the fully-qualified name of the OrderService's GetOrder RPC.
	OrderServiceGetOrderProcedure = "/hero.orders.v2.OrderService/GetOrder"
	// OrderServiceUpdateOrderProcedure is the fully-qualified name of the OrderService's UpdateOrder
	// RPC.
	OrderServiceUpdateOrderProcedure = "/hero.orders.v2.OrderService/UpdateOrder"
	// OrderServiceListOrdersProcedure is the fully-qualified name of the OrderService's ListOrders RPC.
	OrderServiceListOrdersProcedure = "/hero.orders.v2.OrderService/ListOrders"
	// OrderServiceDeleteOrderProcedure is the fully-qualified name of the OrderService's DeleteOrder
	// RPC.
	OrderServiceDeleteOrderProcedure = "/hero.orders.v2.OrderService/DeleteOrder"
	// OrderServiceAddOrderUpdateProcedure is the fully-qualified name of the OrderService's
	// AddOrderUpdate RPC.
	OrderServiceAddOrderUpdateProcedure = "/hero.orders.v2.OrderService/AddOrderUpdate"
	// OrderServiceRemoveOrderUpdateProcedure is the fully-qualified name of the OrderService's
	// RemoveOrderUpdate RPC.
	OrderServiceRemoveOrderUpdateProcedure = "/hero.orders.v2.OrderService/RemoveOrderUpdate"
	// OrderServiceAddAllowedAssetTypeProcedure is the fully-qualified name of the OrderService's
	// AddAllowedAssetType RPC.
	OrderServiceAddAllowedAssetTypeProcedure = "/hero.orders.v2.OrderService/AddAllowedAssetType"
	// OrderServiceRemoveAllowedAssetTypeProcedure is the fully-qualified name of the OrderService's
	// RemoveAllowedAssetType RPC.
	OrderServiceRemoveAllowedAssetTypeProcedure = "/hero.orders.v2.OrderService/RemoveAllowedAssetType"
	// OrderServiceAddBlacklistedAssetIdProcedure is the fully-qualified name of the OrderService's
	// AddBlacklistedAssetId RPC.
	OrderServiceAddBlacklistedAssetIdProcedure = "/hero.orders.v2.OrderService/AddBlacklistedAssetId"
	// OrderServiceRemoveBlacklistedAssetIdProcedure is the fully-qualified name of the OrderService's
	// RemoveBlacklistedAssetId RPC.
	OrderServiceRemoveBlacklistedAssetIdProcedure = "/hero.orders.v2.OrderService/RemoveBlacklistedAssetId"
	// OrderServiceAcknowledgeOrderProcedure is the fully-qualified name of the OrderService's
	// AcknowledgeOrder RPC.
	OrderServiceAcknowledgeOrderProcedure = "/hero.orders.v2.OrderService/AcknowledgeOrder"
	// OrderServiceRejectOrderProcedure is the fully-qualified name of the OrderService's RejectOrder
	// RPC.
	OrderServiceRejectOrderProcedure = "/hero.orders.v2.OrderService/RejectOrder"
	// OrderServiceSnoozeOrderProcedure is the fully-qualified name of the OrderService's SnoozeOrder
	// RPC.
	OrderServiceSnoozeOrderProcedure = "/hero.orders.v2.OrderService/SnoozeOrder"
	// OrderServiceCancelOrderProcedure is the fully-qualified name of the OrderService's CancelOrder
	// RPC.
	OrderServiceCancelOrderProcedure = "/hero.orders.v2.OrderService/CancelOrder"
	// OrderServiceCompleteOrderProcedure is the fully-qualified name of the OrderService's
	// CompleteOrder RPC.
	OrderServiceCompleteOrderProcedure = "/hero.orders.v2.OrderService/CompleteOrder"
	// OrderServiceListActiveAssignedOrdersForAssetProcedure is the fully-qualified name of the
	// OrderService's ListActiveAssignedOrdersForAsset RPC.
	OrderServiceListActiveAssignedOrdersForAssetProcedure = "/hero.orders.v2.OrderService/ListActiveAssignedOrdersForAsset"
	// OrderServiceListNewOrdersForAssetProcedure is the fully-qualified name of the OrderService's
	// ListNewOrdersForAsset RPC.
	OrderServiceListNewOrdersForAssetProcedure = "/hero.orders.v2.OrderService/ListNewOrdersForAsset"
	// OrderServiceUpdateOrderPermissionsProcedure is the fully-qualified name of the OrderService's
	// UpdateOrderPermissions RPC.
	OrderServiceUpdateOrderPermissionsProcedure = "/hero.orders.v2.OrderService/UpdateOrderPermissions"
	// OrderServiceListOrdersForSituationProcedure is the fully-qualified name of the OrderService's
	// ListOrdersForSituation RPC.
	OrderServiceListOrdersForSituationProcedure = "/hero.orders.v2.OrderService/ListOrdersForSituation"
	// OrderServiceListOrdersForAssetProcedure is the fully-qualified name of the OrderService's
	// ListOrdersForAsset RPC.
	OrderServiceListOrdersForAssetProcedure = "/hero.orders.v2.OrderService/ListOrdersForAsset"
	// OrderServiceListOrdersForReportProcedure is the fully-qualified name of the OrderService's
	// ListOrdersForReport RPC.
	OrderServiceListOrdersForReportProcedure = "/hero.orders.v2.OrderService/ListOrdersForReport"
	// OrderServiceListOrdersForReviewRoundProcedure is the fully-qualified name of the OrderService's
	// ListOrdersForReviewRound RPC.
	OrderServiceListOrdersForReviewRoundProcedure = "/hero.orders.v2.OrderService/ListOrdersForReviewRound"
	// OrderServiceListOrdersForCaseProcedure is the fully-qualified name of the OrderService's
	// ListOrdersForCase RPC.
	OrderServiceListOrdersForCaseProcedure = "/hero.orders.v2.OrderService/ListOrdersForCase"
	// OrderServiceAddAdditionalInfoProcedure is the fully-qualified name of the OrderService's
	// AddAdditionalInfo RPC.
	OrderServiceAddAdditionalInfoProcedure = "/hero.orders.v2.OrderService/AddAdditionalInfo"
	// OrderServiceAddOrderStatusUpdateProcedure is the fully-qualified name of the OrderService's
	// AddOrderStatusUpdate RPC.
	OrderServiceAddOrderStatusUpdateProcedure = "/hero.orders.v2.OrderService/AddOrderStatusUpdate"
)

// OrderServiceClient is a client for the hero.orders.v2.OrderService service.
type OrderServiceClient interface {
	// Create a new order.
	CreateOrder(context.Context, *connect.Request[v2.CreateOrderRequest]) (*connect.Response[v2.CreateOrderResponse], error)
	// Retrieve an order by its ID.
	GetOrder(context.Context, *connect.Request[v2.GetOrderRequest]) (*connect.Response[v2.Order], error)
	// Update an existing order.
	UpdateOrder(context.Context, *connect.Request[v2.UpdateOrderRequest]) (*connect.Response[v2.Order], error)
	// List orders with optional filtering, ordering, and pagination.
	ListOrders(context.Context, *connect.Request[v2.ListOrdersRequest]) (*connect.Response[v2.ListOrdersResponse], error)
	// Delete an order by its ID.
	DeleteOrder(context.Context, *connect.Request[v2.DeleteOrderRequest]) (*connect.Response[emptypb.Empty], error)
	// Add an update entry to an existing order.
	AddOrderUpdate(context.Context, *connect.Request[v2.AddOrderUpdateRequest]) (*connect.Response[v2.Order], error)
	// Remove an update entry from an existing order.
	RemoveOrderUpdate(context.Context, *connect.Request[v2.RemoveOrderUpdateRequest]) (*connect.Response[v2.Order], error)
	// Add an allowed asset type to an order.
	AddAllowedAssetType(context.Context, *connect.Request[v2.AddAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error)
	// Remove an allowed asset type from an order.
	RemoveAllowedAssetType(context.Context, *connect.Request[v2.RemoveAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error)
	// Add a blacklisted asset ID to an order.
	AddBlacklistedAssetId(context.Context, *connect.Request[v2.AddBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error)
	// Remove a blacklisted asset ID from an order.
	RemoveBlacklistedAssetId(context.Context, *connect.Request[v2.RemoveBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error)
	// Acknowledge an order.
	AcknowledgeOrder(context.Context, *connect.Request[v2.AcknowledgeOrderRequest]) (*connect.Response[v2.Order], error)
	// Reject an order.
	RejectOrder(context.Context, *connect.Request[v2.RejectOrderRequest]) (*connect.Response[v2.Order], error)
	// Snooze an order.
	SnoozeOrder(context.Context, *connect.Request[v2.SnoozeOrderRequest]) (*connect.Response[v2.Order], error)
	// Cancel an order.
	CancelOrder(context.Context, *connect.Request[v2.CancelOrderRequest]) (*connect.Response[v2.Order], error)
	// Complete an order.
	CompleteOrder(context.Context, *connect.Request[v2.CompleteOrderRequest]) (*connect.Response[v2.Order], error)
	// ListActiveAssignedOrdersForAsset returns orders for the given asset that are in statuses: CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS.
	ListActiveAssignedOrdersForAsset(context.Context, *connect.Request[v2.ListActiveAssignedOrdersForAssetRequest]) (*connect.Response[v2.ListActiveAssignedOrdersForAssetResponse], error)
	// ListNewOrdersForAsset returns orders in CREATED status for the given asset.
	ListNewOrdersForAsset(context.Context, *connect.Request[v2.ListNewOrdersForAssetRequest]) (*connect.Response[v2.ListNewOrdersForAssetResponse], error)
	// UpdateOrderPermissions updates the permissions for an existing order.
	UpdateOrderPermissions(context.Context, *connect.Request[v2.UpdateOrderPermissionsRequest]) (*connect.Response[v2.Order], error)
	// ListOrdersForSituation returns a paginated list of orders for the given situation with optional status filter.
	ListOrdersForSituation(context.Context, *connect.Request[v2.ListOrdersForSituationRequest]) (*connect.Response[v2.ListOrdersForSituationResponse], error)
	// ListOrdersForAsset returns a paginated list of orders for the given asset with optional status filter.
	ListOrdersForAsset(context.Context, *connect.Request[v2.ListOrdersForAssetRequest]) (*connect.Response[v2.ListOrdersForAssetResponse], error)
	// ListOrdersForReport returns orders for the given report with optional status filter.
	ListOrdersForReport(context.Context, *connect.Request[v2.ListOrdersForReportRequest]) (*connect.Response[v2.ListOrdersForReportResponse], error)
	// ListOrdersForReviewRound returns orders for the given review round with optional status filter.
	ListOrdersForReviewRound(context.Context, *connect.Request[v2.ListOrdersForReviewRoundRequest]) (*connect.Response[v2.ListOrdersForReviewRoundResponse], error)
	// ListOrdersForCase returns orders for the given case with optional status filter.
	ListOrdersForCase(context.Context, *connect.Request[v2.ListOrdersForCaseRequest]) (*connect.Response[v2.ListOrdersForCaseResponse], error)
	// Add additional info to an order by merging provided JSON into the existing additional_info_json.
	AddAdditionalInfo(context.Context, *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error)
	// Add a status update to an order.
	AddOrderStatusUpdate(context.Context, *connect.Request[v2.AddOrderStatusUpdateRequest]) (*connect.Response[v2.Order], error)
}

// NewOrderServiceClient constructs a client for the hero.orders.v2.OrderService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewOrderServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) OrderServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	orderServiceMethods := v2.File_hero_orders_v2_orders_proto.Services().ByName("OrderService").Methods()
	return &orderServiceClient{
		createOrder: connect.NewClient[v2.CreateOrderRequest, v2.CreateOrderResponse](
			httpClient,
			baseURL+OrderServiceCreateOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("CreateOrder")),
			connect.WithClientOptions(opts...),
		),
		getOrder: connect.NewClient[v2.GetOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceGetOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("GetOrder")),
			connect.WithClientOptions(opts...),
		),
		updateOrder: connect.NewClient[v2.UpdateOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceUpdateOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("UpdateOrder")),
			connect.WithClientOptions(opts...),
		),
		listOrders: connect.NewClient[v2.ListOrdersRequest, v2.ListOrdersResponse](
			httpClient,
			baseURL+OrderServiceListOrdersProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListOrders")),
			connect.WithClientOptions(opts...),
		),
		deleteOrder: connect.NewClient[v2.DeleteOrderRequest, emptypb.Empty](
			httpClient,
			baseURL+OrderServiceDeleteOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("DeleteOrder")),
			connect.WithClientOptions(opts...),
		),
		addOrderUpdate: connect.NewClient[v2.AddOrderUpdateRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceAddOrderUpdateProcedure,
			connect.WithSchema(orderServiceMethods.ByName("AddOrderUpdate")),
			connect.WithClientOptions(opts...),
		),
		removeOrderUpdate: connect.NewClient[v2.RemoveOrderUpdateRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceRemoveOrderUpdateProcedure,
			connect.WithSchema(orderServiceMethods.ByName("RemoveOrderUpdate")),
			connect.WithClientOptions(opts...),
		),
		addAllowedAssetType: connect.NewClient[v2.AddAllowedAssetTypeRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceAddAllowedAssetTypeProcedure,
			connect.WithSchema(orderServiceMethods.ByName("AddAllowedAssetType")),
			connect.WithClientOptions(opts...),
		),
		removeAllowedAssetType: connect.NewClient[v2.RemoveAllowedAssetTypeRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceRemoveAllowedAssetTypeProcedure,
			connect.WithSchema(orderServiceMethods.ByName("RemoveAllowedAssetType")),
			connect.WithClientOptions(opts...),
		),
		addBlacklistedAssetId: connect.NewClient[v2.AddBlacklistedAssetIdRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceAddBlacklistedAssetIdProcedure,
			connect.WithSchema(orderServiceMethods.ByName("AddBlacklistedAssetId")),
			connect.WithClientOptions(opts...),
		),
		removeBlacklistedAssetId: connect.NewClient[v2.RemoveBlacklistedAssetIdRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceRemoveBlacklistedAssetIdProcedure,
			connect.WithSchema(orderServiceMethods.ByName("RemoveBlacklistedAssetId")),
			connect.WithClientOptions(opts...),
		),
		acknowledgeOrder: connect.NewClient[v2.AcknowledgeOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceAcknowledgeOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("AcknowledgeOrder")),
			connect.WithClientOptions(opts...),
		),
		rejectOrder: connect.NewClient[v2.RejectOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceRejectOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("RejectOrder")),
			connect.WithClientOptions(opts...),
		),
		snoozeOrder: connect.NewClient[v2.SnoozeOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceSnoozeOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("SnoozeOrder")),
			connect.WithClientOptions(opts...),
		),
		cancelOrder: connect.NewClient[v2.CancelOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceCancelOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("CancelOrder")),
			connect.WithClientOptions(opts...),
		),
		completeOrder: connect.NewClient[v2.CompleteOrderRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceCompleteOrderProcedure,
			connect.WithSchema(orderServiceMethods.ByName("CompleteOrder")),
			connect.WithClientOptions(opts...),
		),
		listActiveAssignedOrdersForAsset: connect.NewClient[v2.ListActiveAssignedOrdersForAssetRequest, v2.ListActiveAssignedOrdersForAssetResponse](
			httpClient,
			baseURL+OrderServiceListActiveAssignedOrdersForAssetProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListActiveAssignedOrdersForAsset")),
			connect.WithClientOptions(opts...),
		),
		listNewOrdersForAsset: connect.NewClient[v2.ListNewOrdersForAssetRequest, v2.ListNewOrdersForAssetResponse](
			httpClient,
			baseURL+OrderServiceListNewOrdersForAssetProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListNewOrdersForAsset")),
			connect.WithClientOptions(opts...),
		),
		updateOrderPermissions: connect.NewClient[v2.UpdateOrderPermissionsRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceUpdateOrderPermissionsProcedure,
			connect.WithSchema(orderServiceMethods.ByName("UpdateOrderPermissions")),
			connect.WithClientOptions(opts...),
		),
		listOrdersForSituation: connect.NewClient[v2.ListOrdersForSituationRequest, v2.ListOrdersForSituationResponse](
			httpClient,
			baseURL+OrderServiceListOrdersForSituationProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListOrdersForSituation")),
			connect.WithClientOptions(opts...),
		),
		listOrdersForAsset: connect.NewClient[v2.ListOrdersForAssetRequest, v2.ListOrdersForAssetResponse](
			httpClient,
			baseURL+OrderServiceListOrdersForAssetProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListOrdersForAsset")),
			connect.WithClientOptions(opts...),
		),
		listOrdersForReport: connect.NewClient[v2.ListOrdersForReportRequest, v2.ListOrdersForReportResponse](
			httpClient,
			baseURL+OrderServiceListOrdersForReportProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListOrdersForReport")),
			connect.WithClientOptions(opts...),
		),
		listOrdersForReviewRound: connect.NewClient[v2.ListOrdersForReviewRoundRequest, v2.ListOrdersForReviewRoundResponse](
			httpClient,
			baseURL+OrderServiceListOrdersForReviewRoundProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListOrdersForReviewRound")),
			connect.WithClientOptions(opts...),
		),
		listOrdersForCase: connect.NewClient[v2.ListOrdersForCaseRequest, v2.ListOrdersForCaseResponse](
			httpClient,
			baseURL+OrderServiceListOrdersForCaseProcedure,
			connect.WithSchema(orderServiceMethods.ByName("ListOrdersForCase")),
			connect.WithClientOptions(opts...),
		),
		addAdditionalInfo: connect.NewClient[v2.AddAdditionalInfoRequest, v2.AddAdditionalInfoResponse](
			httpClient,
			baseURL+OrderServiceAddAdditionalInfoProcedure,
			connect.WithSchema(orderServiceMethods.ByName("AddAdditionalInfo")),
			connect.WithClientOptions(opts...),
		),
		addOrderStatusUpdate: connect.NewClient[v2.AddOrderStatusUpdateRequest, v2.Order](
			httpClient,
			baseURL+OrderServiceAddOrderStatusUpdateProcedure,
			connect.WithSchema(orderServiceMethods.ByName("AddOrderStatusUpdate")),
			connect.WithClientOptions(opts...),
		),
	}
}

// orderServiceClient implements OrderServiceClient.
type orderServiceClient struct {
	createOrder                      *connect.Client[v2.CreateOrderRequest, v2.CreateOrderResponse]
	getOrder                         *connect.Client[v2.GetOrderRequest, v2.Order]
	updateOrder                      *connect.Client[v2.UpdateOrderRequest, v2.Order]
	listOrders                       *connect.Client[v2.ListOrdersRequest, v2.ListOrdersResponse]
	deleteOrder                      *connect.Client[v2.DeleteOrderRequest, emptypb.Empty]
	addOrderUpdate                   *connect.Client[v2.AddOrderUpdateRequest, v2.Order]
	removeOrderUpdate                *connect.Client[v2.RemoveOrderUpdateRequest, v2.Order]
	addAllowedAssetType              *connect.Client[v2.AddAllowedAssetTypeRequest, v2.Order]
	removeAllowedAssetType           *connect.Client[v2.RemoveAllowedAssetTypeRequest, v2.Order]
	addBlacklistedAssetId            *connect.Client[v2.AddBlacklistedAssetIdRequest, v2.Order]
	removeBlacklistedAssetId         *connect.Client[v2.RemoveBlacklistedAssetIdRequest, v2.Order]
	acknowledgeOrder                 *connect.Client[v2.AcknowledgeOrderRequest, v2.Order]
	rejectOrder                      *connect.Client[v2.RejectOrderRequest, v2.Order]
	snoozeOrder                      *connect.Client[v2.SnoozeOrderRequest, v2.Order]
	cancelOrder                      *connect.Client[v2.CancelOrderRequest, v2.Order]
	completeOrder                    *connect.Client[v2.CompleteOrderRequest, v2.Order]
	listActiveAssignedOrdersForAsset *connect.Client[v2.ListActiveAssignedOrdersForAssetRequest, v2.ListActiveAssignedOrdersForAssetResponse]
	listNewOrdersForAsset            *connect.Client[v2.ListNewOrdersForAssetRequest, v2.ListNewOrdersForAssetResponse]
	updateOrderPermissions           *connect.Client[v2.UpdateOrderPermissionsRequest, v2.Order]
	listOrdersForSituation           *connect.Client[v2.ListOrdersForSituationRequest, v2.ListOrdersForSituationResponse]
	listOrdersForAsset               *connect.Client[v2.ListOrdersForAssetRequest, v2.ListOrdersForAssetResponse]
	listOrdersForReport              *connect.Client[v2.ListOrdersForReportRequest, v2.ListOrdersForReportResponse]
	listOrdersForReviewRound         *connect.Client[v2.ListOrdersForReviewRoundRequest, v2.ListOrdersForReviewRoundResponse]
	listOrdersForCase                *connect.Client[v2.ListOrdersForCaseRequest, v2.ListOrdersForCaseResponse]
	addAdditionalInfo                *connect.Client[v2.AddAdditionalInfoRequest, v2.AddAdditionalInfoResponse]
	addOrderStatusUpdate             *connect.Client[v2.AddOrderStatusUpdateRequest, v2.Order]
}

// CreateOrder calls hero.orders.v2.OrderService.CreateOrder.
func (c *orderServiceClient) CreateOrder(ctx context.Context, req *connect.Request[v2.CreateOrderRequest]) (*connect.Response[v2.CreateOrderResponse], error) {
	return c.createOrder.CallUnary(ctx, req)
}

// GetOrder calls hero.orders.v2.OrderService.GetOrder.
func (c *orderServiceClient) GetOrder(ctx context.Context, req *connect.Request[v2.GetOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.getOrder.CallUnary(ctx, req)
}

// UpdateOrder calls hero.orders.v2.OrderService.UpdateOrder.
func (c *orderServiceClient) UpdateOrder(ctx context.Context, req *connect.Request[v2.UpdateOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.updateOrder.CallUnary(ctx, req)
}

// ListOrders calls hero.orders.v2.OrderService.ListOrders.
func (c *orderServiceClient) ListOrders(ctx context.Context, req *connect.Request[v2.ListOrdersRequest]) (*connect.Response[v2.ListOrdersResponse], error) {
	return c.listOrders.CallUnary(ctx, req)
}

// DeleteOrder calls hero.orders.v2.OrderService.DeleteOrder.
func (c *orderServiceClient) DeleteOrder(ctx context.Context, req *connect.Request[v2.DeleteOrderRequest]) (*connect.Response[emptypb.Empty], error) {
	return c.deleteOrder.CallUnary(ctx, req)
}

// AddOrderUpdate calls hero.orders.v2.OrderService.AddOrderUpdate.
func (c *orderServiceClient) AddOrderUpdate(ctx context.Context, req *connect.Request[v2.AddOrderUpdateRequest]) (*connect.Response[v2.Order], error) {
	return c.addOrderUpdate.CallUnary(ctx, req)
}

// RemoveOrderUpdate calls hero.orders.v2.OrderService.RemoveOrderUpdate.
func (c *orderServiceClient) RemoveOrderUpdate(ctx context.Context, req *connect.Request[v2.RemoveOrderUpdateRequest]) (*connect.Response[v2.Order], error) {
	return c.removeOrderUpdate.CallUnary(ctx, req)
}

// AddAllowedAssetType calls hero.orders.v2.OrderService.AddAllowedAssetType.
func (c *orderServiceClient) AddAllowedAssetType(ctx context.Context, req *connect.Request[v2.AddAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error) {
	return c.addAllowedAssetType.CallUnary(ctx, req)
}

// RemoveAllowedAssetType calls hero.orders.v2.OrderService.RemoveAllowedAssetType.
func (c *orderServiceClient) RemoveAllowedAssetType(ctx context.Context, req *connect.Request[v2.RemoveAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error) {
	return c.removeAllowedAssetType.CallUnary(ctx, req)
}

// AddBlacklistedAssetId calls hero.orders.v2.OrderService.AddBlacklistedAssetId.
func (c *orderServiceClient) AddBlacklistedAssetId(ctx context.Context, req *connect.Request[v2.AddBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error) {
	return c.addBlacklistedAssetId.CallUnary(ctx, req)
}

// RemoveBlacklistedAssetId calls hero.orders.v2.OrderService.RemoveBlacklistedAssetId.
func (c *orderServiceClient) RemoveBlacklistedAssetId(ctx context.Context, req *connect.Request[v2.RemoveBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error) {
	return c.removeBlacklistedAssetId.CallUnary(ctx, req)
}

// AcknowledgeOrder calls hero.orders.v2.OrderService.AcknowledgeOrder.
func (c *orderServiceClient) AcknowledgeOrder(ctx context.Context, req *connect.Request[v2.AcknowledgeOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.acknowledgeOrder.CallUnary(ctx, req)
}

// RejectOrder calls hero.orders.v2.OrderService.RejectOrder.
func (c *orderServiceClient) RejectOrder(ctx context.Context, req *connect.Request[v2.RejectOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.rejectOrder.CallUnary(ctx, req)
}

// SnoozeOrder calls hero.orders.v2.OrderService.SnoozeOrder.
func (c *orderServiceClient) SnoozeOrder(ctx context.Context, req *connect.Request[v2.SnoozeOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.snoozeOrder.CallUnary(ctx, req)
}

// CancelOrder calls hero.orders.v2.OrderService.CancelOrder.
func (c *orderServiceClient) CancelOrder(ctx context.Context, req *connect.Request[v2.CancelOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.cancelOrder.CallUnary(ctx, req)
}

// CompleteOrder calls hero.orders.v2.OrderService.CompleteOrder.
func (c *orderServiceClient) CompleteOrder(ctx context.Context, req *connect.Request[v2.CompleteOrderRequest]) (*connect.Response[v2.Order], error) {
	return c.completeOrder.CallUnary(ctx, req)
}

// ListActiveAssignedOrdersForAsset calls
// hero.orders.v2.OrderService.ListActiveAssignedOrdersForAsset.
func (c *orderServiceClient) ListActiveAssignedOrdersForAsset(ctx context.Context, req *connect.Request[v2.ListActiveAssignedOrdersForAssetRequest]) (*connect.Response[v2.ListActiveAssignedOrdersForAssetResponse], error) {
	return c.listActiveAssignedOrdersForAsset.CallUnary(ctx, req)
}

// ListNewOrdersForAsset calls hero.orders.v2.OrderService.ListNewOrdersForAsset.
func (c *orderServiceClient) ListNewOrdersForAsset(ctx context.Context, req *connect.Request[v2.ListNewOrdersForAssetRequest]) (*connect.Response[v2.ListNewOrdersForAssetResponse], error) {
	return c.listNewOrdersForAsset.CallUnary(ctx, req)
}

// UpdateOrderPermissions calls hero.orders.v2.OrderService.UpdateOrderPermissions.
func (c *orderServiceClient) UpdateOrderPermissions(ctx context.Context, req *connect.Request[v2.UpdateOrderPermissionsRequest]) (*connect.Response[v2.Order], error) {
	return c.updateOrderPermissions.CallUnary(ctx, req)
}

// ListOrdersForSituation calls hero.orders.v2.OrderService.ListOrdersForSituation.
func (c *orderServiceClient) ListOrdersForSituation(ctx context.Context, req *connect.Request[v2.ListOrdersForSituationRequest]) (*connect.Response[v2.ListOrdersForSituationResponse], error) {
	return c.listOrdersForSituation.CallUnary(ctx, req)
}

// ListOrdersForAsset calls hero.orders.v2.OrderService.ListOrdersForAsset.
func (c *orderServiceClient) ListOrdersForAsset(ctx context.Context, req *connect.Request[v2.ListOrdersForAssetRequest]) (*connect.Response[v2.ListOrdersForAssetResponse], error) {
	return c.listOrdersForAsset.CallUnary(ctx, req)
}

// ListOrdersForReport calls hero.orders.v2.OrderService.ListOrdersForReport.
func (c *orderServiceClient) ListOrdersForReport(ctx context.Context, req *connect.Request[v2.ListOrdersForReportRequest]) (*connect.Response[v2.ListOrdersForReportResponse], error) {
	return c.listOrdersForReport.CallUnary(ctx, req)
}

// ListOrdersForReviewRound calls hero.orders.v2.OrderService.ListOrdersForReviewRound.
func (c *orderServiceClient) ListOrdersForReviewRound(ctx context.Context, req *connect.Request[v2.ListOrdersForReviewRoundRequest]) (*connect.Response[v2.ListOrdersForReviewRoundResponse], error) {
	return c.listOrdersForReviewRound.CallUnary(ctx, req)
}

// ListOrdersForCase calls hero.orders.v2.OrderService.ListOrdersForCase.
func (c *orderServiceClient) ListOrdersForCase(ctx context.Context, req *connect.Request[v2.ListOrdersForCaseRequest]) (*connect.Response[v2.ListOrdersForCaseResponse], error) {
	return c.listOrdersForCase.CallUnary(ctx, req)
}

// AddAdditionalInfo calls hero.orders.v2.OrderService.AddAdditionalInfo.
func (c *orderServiceClient) AddAdditionalInfo(ctx context.Context, req *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error) {
	return c.addAdditionalInfo.CallUnary(ctx, req)
}

// AddOrderStatusUpdate calls hero.orders.v2.OrderService.AddOrderStatusUpdate.
func (c *orderServiceClient) AddOrderStatusUpdate(ctx context.Context, req *connect.Request[v2.AddOrderStatusUpdateRequest]) (*connect.Response[v2.Order], error) {
	return c.addOrderStatusUpdate.CallUnary(ctx, req)
}

// OrderServiceHandler is an implementation of the hero.orders.v2.OrderService service.
type OrderServiceHandler interface {
	// Create a new order.
	CreateOrder(context.Context, *connect.Request[v2.CreateOrderRequest]) (*connect.Response[v2.CreateOrderResponse], error)
	// Retrieve an order by its ID.
	GetOrder(context.Context, *connect.Request[v2.GetOrderRequest]) (*connect.Response[v2.Order], error)
	// Update an existing order.
	UpdateOrder(context.Context, *connect.Request[v2.UpdateOrderRequest]) (*connect.Response[v2.Order], error)
	// List orders with optional filtering, ordering, and pagination.
	ListOrders(context.Context, *connect.Request[v2.ListOrdersRequest]) (*connect.Response[v2.ListOrdersResponse], error)
	// Delete an order by its ID.
	DeleteOrder(context.Context, *connect.Request[v2.DeleteOrderRequest]) (*connect.Response[emptypb.Empty], error)
	// Add an update entry to an existing order.
	AddOrderUpdate(context.Context, *connect.Request[v2.AddOrderUpdateRequest]) (*connect.Response[v2.Order], error)
	// Remove an update entry from an existing order.
	RemoveOrderUpdate(context.Context, *connect.Request[v2.RemoveOrderUpdateRequest]) (*connect.Response[v2.Order], error)
	// Add an allowed asset type to an order.
	AddAllowedAssetType(context.Context, *connect.Request[v2.AddAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error)
	// Remove an allowed asset type from an order.
	RemoveAllowedAssetType(context.Context, *connect.Request[v2.RemoveAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error)
	// Add a blacklisted asset ID to an order.
	AddBlacklistedAssetId(context.Context, *connect.Request[v2.AddBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error)
	// Remove a blacklisted asset ID from an order.
	RemoveBlacklistedAssetId(context.Context, *connect.Request[v2.RemoveBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error)
	// Acknowledge an order.
	AcknowledgeOrder(context.Context, *connect.Request[v2.AcknowledgeOrderRequest]) (*connect.Response[v2.Order], error)
	// Reject an order.
	RejectOrder(context.Context, *connect.Request[v2.RejectOrderRequest]) (*connect.Response[v2.Order], error)
	// Snooze an order.
	SnoozeOrder(context.Context, *connect.Request[v2.SnoozeOrderRequest]) (*connect.Response[v2.Order], error)
	// Cancel an order.
	CancelOrder(context.Context, *connect.Request[v2.CancelOrderRequest]) (*connect.Response[v2.Order], error)
	// Complete an order.
	CompleteOrder(context.Context, *connect.Request[v2.CompleteOrderRequest]) (*connect.Response[v2.Order], error)
	// ListActiveAssignedOrdersForAsset returns orders for the given asset that are in statuses: CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS.
	ListActiveAssignedOrdersForAsset(context.Context, *connect.Request[v2.ListActiveAssignedOrdersForAssetRequest]) (*connect.Response[v2.ListActiveAssignedOrdersForAssetResponse], error)
	// ListNewOrdersForAsset returns orders in CREATED status for the given asset.
	ListNewOrdersForAsset(context.Context, *connect.Request[v2.ListNewOrdersForAssetRequest]) (*connect.Response[v2.ListNewOrdersForAssetResponse], error)
	// UpdateOrderPermissions updates the permissions for an existing order.
	UpdateOrderPermissions(context.Context, *connect.Request[v2.UpdateOrderPermissionsRequest]) (*connect.Response[v2.Order], error)
	// ListOrdersForSituation returns a paginated list of orders for the given situation with optional status filter.
	ListOrdersForSituation(context.Context, *connect.Request[v2.ListOrdersForSituationRequest]) (*connect.Response[v2.ListOrdersForSituationResponse], error)
	// ListOrdersForAsset returns a paginated list of orders for the given asset with optional status filter.
	ListOrdersForAsset(context.Context, *connect.Request[v2.ListOrdersForAssetRequest]) (*connect.Response[v2.ListOrdersForAssetResponse], error)
	// ListOrdersForReport returns orders for the given report with optional status filter.
	ListOrdersForReport(context.Context, *connect.Request[v2.ListOrdersForReportRequest]) (*connect.Response[v2.ListOrdersForReportResponse], error)
	// ListOrdersForReviewRound returns orders for the given review round with optional status filter.
	ListOrdersForReviewRound(context.Context, *connect.Request[v2.ListOrdersForReviewRoundRequest]) (*connect.Response[v2.ListOrdersForReviewRoundResponse], error)
	// ListOrdersForCase returns orders for the given case with optional status filter.
	ListOrdersForCase(context.Context, *connect.Request[v2.ListOrdersForCaseRequest]) (*connect.Response[v2.ListOrdersForCaseResponse], error)
	// Add additional info to an order by merging provided JSON into the existing additional_info_json.
	AddAdditionalInfo(context.Context, *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error)
	// Add a status update to an order.
	AddOrderStatusUpdate(context.Context, *connect.Request[v2.AddOrderStatusUpdateRequest]) (*connect.Response[v2.Order], error)
}

// NewOrderServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewOrderServiceHandler(svc OrderServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	orderServiceMethods := v2.File_hero_orders_v2_orders_proto.Services().ByName("OrderService").Methods()
	orderServiceCreateOrderHandler := connect.NewUnaryHandler(
		OrderServiceCreateOrderProcedure,
		svc.CreateOrder,
		connect.WithSchema(orderServiceMethods.ByName("CreateOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceGetOrderHandler := connect.NewUnaryHandler(
		OrderServiceGetOrderProcedure,
		svc.GetOrder,
		connect.WithSchema(orderServiceMethods.ByName("GetOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceUpdateOrderHandler := connect.NewUnaryHandler(
		OrderServiceUpdateOrderProcedure,
		svc.UpdateOrder,
		connect.WithSchema(orderServiceMethods.ByName("UpdateOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListOrdersHandler := connect.NewUnaryHandler(
		OrderServiceListOrdersProcedure,
		svc.ListOrders,
		connect.WithSchema(orderServiceMethods.ByName("ListOrders")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceDeleteOrderHandler := connect.NewUnaryHandler(
		OrderServiceDeleteOrderProcedure,
		svc.DeleteOrder,
		connect.WithSchema(orderServiceMethods.ByName("DeleteOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceAddOrderUpdateHandler := connect.NewUnaryHandler(
		OrderServiceAddOrderUpdateProcedure,
		svc.AddOrderUpdate,
		connect.WithSchema(orderServiceMethods.ByName("AddOrderUpdate")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceRemoveOrderUpdateHandler := connect.NewUnaryHandler(
		OrderServiceRemoveOrderUpdateProcedure,
		svc.RemoveOrderUpdate,
		connect.WithSchema(orderServiceMethods.ByName("RemoveOrderUpdate")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceAddAllowedAssetTypeHandler := connect.NewUnaryHandler(
		OrderServiceAddAllowedAssetTypeProcedure,
		svc.AddAllowedAssetType,
		connect.WithSchema(orderServiceMethods.ByName("AddAllowedAssetType")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceRemoveAllowedAssetTypeHandler := connect.NewUnaryHandler(
		OrderServiceRemoveAllowedAssetTypeProcedure,
		svc.RemoveAllowedAssetType,
		connect.WithSchema(orderServiceMethods.ByName("RemoveAllowedAssetType")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceAddBlacklistedAssetIdHandler := connect.NewUnaryHandler(
		OrderServiceAddBlacklistedAssetIdProcedure,
		svc.AddBlacklistedAssetId,
		connect.WithSchema(orderServiceMethods.ByName("AddBlacklistedAssetId")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceRemoveBlacklistedAssetIdHandler := connect.NewUnaryHandler(
		OrderServiceRemoveBlacklistedAssetIdProcedure,
		svc.RemoveBlacklistedAssetId,
		connect.WithSchema(orderServiceMethods.ByName("RemoveBlacklistedAssetId")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceAcknowledgeOrderHandler := connect.NewUnaryHandler(
		OrderServiceAcknowledgeOrderProcedure,
		svc.AcknowledgeOrder,
		connect.WithSchema(orderServiceMethods.ByName("AcknowledgeOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceRejectOrderHandler := connect.NewUnaryHandler(
		OrderServiceRejectOrderProcedure,
		svc.RejectOrder,
		connect.WithSchema(orderServiceMethods.ByName("RejectOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceSnoozeOrderHandler := connect.NewUnaryHandler(
		OrderServiceSnoozeOrderProcedure,
		svc.SnoozeOrder,
		connect.WithSchema(orderServiceMethods.ByName("SnoozeOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceCancelOrderHandler := connect.NewUnaryHandler(
		OrderServiceCancelOrderProcedure,
		svc.CancelOrder,
		connect.WithSchema(orderServiceMethods.ByName("CancelOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceCompleteOrderHandler := connect.NewUnaryHandler(
		OrderServiceCompleteOrderProcedure,
		svc.CompleteOrder,
		connect.WithSchema(orderServiceMethods.ByName("CompleteOrder")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListActiveAssignedOrdersForAssetHandler := connect.NewUnaryHandler(
		OrderServiceListActiveAssignedOrdersForAssetProcedure,
		svc.ListActiveAssignedOrdersForAsset,
		connect.WithSchema(orderServiceMethods.ByName("ListActiveAssignedOrdersForAsset")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListNewOrdersForAssetHandler := connect.NewUnaryHandler(
		OrderServiceListNewOrdersForAssetProcedure,
		svc.ListNewOrdersForAsset,
		connect.WithSchema(orderServiceMethods.ByName("ListNewOrdersForAsset")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceUpdateOrderPermissionsHandler := connect.NewUnaryHandler(
		OrderServiceUpdateOrderPermissionsProcedure,
		svc.UpdateOrderPermissions,
		connect.WithSchema(orderServiceMethods.ByName("UpdateOrderPermissions")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListOrdersForSituationHandler := connect.NewUnaryHandler(
		OrderServiceListOrdersForSituationProcedure,
		svc.ListOrdersForSituation,
		connect.WithSchema(orderServiceMethods.ByName("ListOrdersForSituation")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListOrdersForAssetHandler := connect.NewUnaryHandler(
		OrderServiceListOrdersForAssetProcedure,
		svc.ListOrdersForAsset,
		connect.WithSchema(orderServiceMethods.ByName("ListOrdersForAsset")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListOrdersForReportHandler := connect.NewUnaryHandler(
		OrderServiceListOrdersForReportProcedure,
		svc.ListOrdersForReport,
		connect.WithSchema(orderServiceMethods.ByName("ListOrdersForReport")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListOrdersForReviewRoundHandler := connect.NewUnaryHandler(
		OrderServiceListOrdersForReviewRoundProcedure,
		svc.ListOrdersForReviewRound,
		connect.WithSchema(orderServiceMethods.ByName("ListOrdersForReviewRound")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceListOrdersForCaseHandler := connect.NewUnaryHandler(
		OrderServiceListOrdersForCaseProcedure,
		svc.ListOrdersForCase,
		connect.WithSchema(orderServiceMethods.ByName("ListOrdersForCase")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceAddAdditionalInfoHandler := connect.NewUnaryHandler(
		OrderServiceAddAdditionalInfoProcedure,
		svc.AddAdditionalInfo,
		connect.WithSchema(orderServiceMethods.ByName("AddAdditionalInfo")),
		connect.WithHandlerOptions(opts...),
	)
	orderServiceAddOrderStatusUpdateHandler := connect.NewUnaryHandler(
		OrderServiceAddOrderStatusUpdateProcedure,
		svc.AddOrderStatusUpdate,
		connect.WithSchema(orderServiceMethods.ByName("AddOrderStatusUpdate")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.orders.v2.OrderService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case OrderServiceCreateOrderProcedure:
			orderServiceCreateOrderHandler.ServeHTTP(w, r)
		case OrderServiceGetOrderProcedure:
			orderServiceGetOrderHandler.ServeHTTP(w, r)
		case OrderServiceUpdateOrderProcedure:
			orderServiceUpdateOrderHandler.ServeHTTP(w, r)
		case OrderServiceListOrdersProcedure:
			orderServiceListOrdersHandler.ServeHTTP(w, r)
		case OrderServiceDeleteOrderProcedure:
			orderServiceDeleteOrderHandler.ServeHTTP(w, r)
		case OrderServiceAddOrderUpdateProcedure:
			orderServiceAddOrderUpdateHandler.ServeHTTP(w, r)
		case OrderServiceRemoveOrderUpdateProcedure:
			orderServiceRemoveOrderUpdateHandler.ServeHTTP(w, r)
		case OrderServiceAddAllowedAssetTypeProcedure:
			orderServiceAddAllowedAssetTypeHandler.ServeHTTP(w, r)
		case OrderServiceRemoveAllowedAssetTypeProcedure:
			orderServiceRemoveAllowedAssetTypeHandler.ServeHTTP(w, r)
		case OrderServiceAddBlacklistedAssetIdProcedure:
			orderServiceAddBlacklistedAssetIdHandler.ServeHTTP(w, r)
		case OrderServiceRemoveBlacklistedAssetIdProcedure:
			orderServiceRemoveBlacklistedAssetIdHandler.ServeHTTP(w, r)
		case OrderServiceAcknowledgeOrderProcedure:
			orderServiceAcknowledgeOrderHandler.ServeHTTP(w, r)
		case OrderServiceRejectOrderProcedure:
			orderServiceRejectOrderHandler.ServeHTTP(w, r)
		case OrderServiceSnoozeOrderProcedure:
			orderServiceSnoozeOrderHandler.ServeHTTP(w, r)
		case OrderServiceCancelOrderProcedure:
			orderServiceCancelOrderHandler.ServeHTTP(w, r)
		case OrderServiceCompleteOrderProcedure:
			orderServiceCompleteOrderHandler.ServeHTTP(w, r)
		case OrderServiceListActiveAssignedOrdersForAssetProcedure:
			orderServiceListActiveAssignedOrdersForAssetHandler.ServeHTTP(w, r)
		case OrderServiceListNewOrdersForAssetProcedure:
			orderServiceListNewOrdersForAssetHandler.ServeHTTP(w, r)
		case OrderServiceUpdateOrderPermissionsProcedure:
			orderServiceUpdateOrderPermissionsHandler.ServeHTTP(w, r)
		case OrderServiceListOrdersForSituationProcedure:
			orderServiceListOrdersForSituationHandler.ServeHTTP(w, r)
		case OrderServiceListOrdersForAssetProcedure:
			orderServiceListOrdersForAssetHandler.ServeHTTP(w, r)
		case OrderServiceListOrdersForReportProcedure:
			orderServiceListOrdersForReportHandler.ServeHTTP(w, r)
		case OrderServiceListOrdersForReviewRoundProcedure:
			orderServiceListOrdersForReviewRoundHandler.ServeHTTP(w, r)
		case OrderServiceListOrdersForCaseProcedure:
			orderServiceListOrdersForCaseHandler.ServeHTTP(w, r)
		case OrderServiceAddAdditionalInfoProcedure:
			orderServiceAddAdditionalInfoHandler.ServeHTTP(w, r)
		case OrderServiceAddOrderStatusUpdateProcedure:
			orderServiceAddOrderStatusUpdateHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedOrderServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedOrderServiceHandler struct{}

func (UnimplementedOrderServiceHandler) CreateOrder(context.Context, *connect.Request[v2.CreateOrderRequest]) (*connect.Response[v2.CreateOrderResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.CreateOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) GetOrder(context.Context, *connect.Request[v2.GetOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.GetOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) UpdateOrder(context.Context, *connect.Request[v2.UpdateOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.UpdateOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListOrders(context.Context, *connect.Request[v2.ListOrdersRequest]) (*connect.Response[v2.ListOrdersResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListOrders is not implemented"))
}

func (UnimplementedOrderServiceHandler) DeleteOrder(context.Context, *connect.Request[v2.DeleteOrderRequest]) (*connect.Response[emptypb.Empty], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.DeleteOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) AddOrderUpdate(context.Context, *connect.Request[v2.AddOrderUpdateRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.AddOrderUpdate is not implemented"))
}

func (UnimplementedOrderServiceHandler) RemoveOrderUpdate(context.Context, *connect.Request[v2.RemoveOrderUpdateRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.RemoveOrderUpdate is not implemented"))
}

func (UnimplementedOrderServiceHandler) AddAllowedAssetType(context.Context, *connect.Request[v2.AddAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.AddAllowedAssetType is not implemented"))
}

func (UnimplementedOrderServiceHandler) RemoveAllowedAssetType(context.Context, *connect.Request[v2.RemoveAllowedAssetTypeRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.RemoveAllowedAssetType is not implemented"))
}

func (UnimplementedOrderServiceHandler) AddBlacklistedAssetId(context.Context, *connect.Request[v2.AddBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.AddBlacklistedAssetId is not implemented"))
}

func (UnimplementedOrderServiceHandler) RemoveBlacklistedAssetId(context.Context, *connect.Request[v2.RemoveBlacklistedAssetIdRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.RemoveBlacklistedAssetId is not implemented"))
}

func (UnimplementedOrderServiceHandler) AcknowledgeOrder(context.Context, *connect.Request[v2.AcknowledgeOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.AcknowledgeOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) RejectOrder(context.Context, *connect.Request[v2.RejectOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.RejectOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) SnoozeOrder(context.Context, *connect.Request[v2.SnoozeOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.SnoozeOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) CancelOrder(context.Context, *connect.Request[v2.CancelOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.CancelOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) CompleteOrder(context.Context, *connect.Request[v2.CompleteOrderRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.CompleteOrder is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListActiveAssignedOrdersForAsset(context.Context, *connect.Request[v2.ListActiveAssignedOrdersForAssetRequest]) (*connect.Response[v2.ListActiveAssignedOrdersForAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListActiveAssignedOrdersForAsset is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListNewOrdersForAsset(context.Context, *connect.Request[v2.ListNewOrdersForAssetRequest]) (*connect.Response[v2.ListNewOrdersForAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListNewOrdersForAsset is not implemented"))
}

func (UnimplementedOrderServiceHandler) UpdateOrderPermissions(context.Context, *connect.Request[v2.UpdateOrderPermissionsRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.UpdateOrderPermissions is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListOrdersForSituation(context.Context, *connect.Request[v2.ListOrdersForSituationRequest]) (*connect.Response[v2.ListOrdersForSituationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListOrdersForSituation is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListOrdersForAsset(context.Context, *connect.Request[v2.ListOrdersForAssetRequest]) (*connect.Response[v2.ListOrdersForAssetResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListOrdersForAsset is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListOrdersForReport(context.Context, *connect.Request[v2.ListOrdersForReportRequest]) (*connect.Response[v2.ListOrdersForReportResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListOrdersForReport is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListOrdersForReviewRound(context.Context, *connect.Request[v2.ListOrdersForReviewRoundRequest]) (*connect.Response[v2.ListOrdersForReviewRoundResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListOrdersForReviewRound is not implemented"))
}

func (UnimplementedOrderServiceHandler) ListOrdersForCase(context.Context, *connect.Request[v2.ListOrdersForCaseRequest]) (*connect.Response[v2.ListOrdersForCaseResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.ListOrdersForCase is not implemented"))
}

func (UnimplementedOrderServiceHandler) AddAdditionalInfo(context.Context, *connect.Request[v2.AddAdditionalInfoRequest]) (*connect.Response[v2.AddAdditionalInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.AddAdditionalInfo is not implemented"))
}

func (UnimplementedOrderServiceHandler) AddOrderStatusUpdate(context.Context, *connect.Request[v2.AddOrderStatusUpdateRequest]) (*connect.Response[v2.Order], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orders.v2.OrderService.AddOrderStatusUpdate is not implemented"))
}
