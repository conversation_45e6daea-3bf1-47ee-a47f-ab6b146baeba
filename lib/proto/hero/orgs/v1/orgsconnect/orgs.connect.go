// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/orgs/v1/orgs.proto

package orgsconnect

import (
	context "context"
	errors "errors"
	http "net/http"
	v1 "proto/hero/orgs/v1"
	strings "strings"

	connect "connectrpc.com/connect"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// OrgsServiceName is the fully-qualified name of the OrgsService service.
	OrgsServiceName = "hero.orgs.v1.OrgsService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// OrgsServiceCreateOrgProcedure is the fully-qualified name of the OrgsService's CreateOrg RPC.
	OrgsServiceCreateOrgProcedure = "/hero.orgs.v1.OrgsService/CreateOrg"
	// OrgsServiceUpdateOrgProcedure is the fully-qualified name of the OrgsService's UpdateOrg RPC.
	OrgsServiceUpdateOrgProcedure = "/hero.orgs.v1.OrgsService/UpdateOrg"
	// OrgsServiceUpdateForwardingConfigProcedure is the fully-qualified name of the OrgsService's
	// UpdateForwardingConfig RPC.
	OrgsServiceUpdateForwardingConfigProcedure = "/hero.orgs.v1.OrgsService/UpdateForwardingConfig"
	// OrgsServiceDeleteOrgProcedure is the fully-qualified name of the OrgsService's DeleteOrg RPC.
	OrgsServiceDeleteOrgProcedure = "/hero.orgs.v1.OrgsService/DeleteOrg"
	// OrgsServiceGetOrgProcedure is the fully-qualified name of the OrgsService's GetOrg RPC.
	OrgsServiceGetOrgProcedure = "/hero.orgs.v1.OrgsService/GetOrg"
	// OrgsServiceListOrgsProcedure is the fully-qualified name of the OrgsService's ListOrgs RPC.
	OrgsServiceListOrgsProcedure = "/hero.orgs.v1.OrgsService/ListOrgs"
	// OrgsServiceGetForwardingConfigProcedure is the fully-qualified name of the OrgsService's
	// GetForwardingConfig RPC.
	OrgsServiceGetForwardingConfigProcedure = "/hero.orgs.v1.OrgsService/GetForwardingConfig"
	// OrgsServiceValidateOrgCredsProcedure is the fully-qualified name of the OrgsService's
	// ValidateOrgCreds RPC.
	OrgsServiceValidateOrgCredsProcedure = "/hero.orgs.v1.OrgsService/ValidateOrgCreds"
	// OrgsServiceCreateOrgAPIUserProcedure is the fully-qualified name of the OrgsService's
	// CreateOrgAPIUser RPC.
	OrgsServiceCreateOrgAPIUserProcedure = "/hero.orgs.v1.OrgsService/CreateOrgAPIUser"
	// OrgsServiceGetOrgAPIUserPrivateByIdProcedure is the fully-qualified name of the OrgsService's
	// GetOrgAPIUserPrivateById RPC.
	OrgsServiceGetOrgAPIUserPrivateByIdProcedure = "/hero.orgs.v1.OrgsService/GetOrgAPIUserPrivateById"
	// OrgsServiceGetZelloChannelsProcedure is the fully-qualified name of the OrgsService's
	// GetZelloChannels RPC.
	OrgsServiceGetZelloChannelsProcedure = "/hero.orgs.v1.OrgsService/GetZelloChannels"
	// OrgsServiceInsertOrgQueueProcedure is the fully-qualified name of the OrgsService's
	// InsertOrgQueue RPC.
	OrgsServiceInsertOrgQueueProcedure = "/hero.orgs.v1.OrgsService/InsertOrgQueue"
	// OrgsServiceCreateOrgTwilioQueueProcedure is the fully-qualified name of the OrgsService's
	// CreateOrgTwilioQueue RPC.
	OrgsServiceCreateOrgTwilioQueueProcedure = "/hero.orgs.v1.OrgsService/CreateOrgTwilioQueue"
	// OrgsServiceAddToContactBookProcedure is the fully-qualified name of the OrgsService's
	// AddToContactBook RPC.
	OrgsServiceAddToContactBookProcedure = "/hero.orgs.v1.OrgsService/AddToContactBook"
	// OrgsServiceUpdateContactInContactBookProcedure is the fully-qualified name of the OrgsService's
	// UpdateContactInContactBook RPC.
	OrgsServiceUpdateContactInContactBookProcedure = "/hero.orgs.v1.OrgsService/UpdateContactInContactBook"
	// OrgsServiceDeleteFromContactBookProcedure is the fully-qualified name of the OrgsService's
	// DeleteFromContactBook RPC.
	OrgsServiceDeleteFromContactBookProcedure = "/hero.orgs.v1.OrgsService/DeleteFromContactBook"
	// OrgsServiceGetContactFromContactBookProcedure is the fully-qualified name of the OrgsService's
	// GetContactFromContactBook RPC.
	OrgsServiceGetContactFromContactBookProcedure = "/hero.orgs.v1.OrgsService/GetContactFromContactBook"
	// OrgsServiceListContactsInContactBookProcedure is the fully-qualified name of the OrgsService's
	// ListContactsInContactBook RPC.
	OrgsServiceListContactsInContactBookProcedure = "/hero.orgs.v1.OrgsService/ListContactsInContactBook"
	// OrgsServiceGetContactByPhoneNumberProcedure is the fully-qualified name of the OrgsService's
	// GetContactByPhoneNumber RPC.
	OrgsServiceGetContactByPhoneNumberProcedure = "/hero.orgs.v1.OrgsService/GetContactByPhoneNumber"
	// OrgsServiceTurnOnGuestModeProcedure is the fully-qualified name of the OrgsService's
	// TurnOnGuestMode RPC.
	OrgsServiceTurnOnGuestModeProcedure = "/hero.orgs.v1.OrgsService/TurnOnGuestMode"
	// OrgsServiceTurnOffGuestModeProcedure is the fully-qualified name of the OrgsService's
	// TurnOffGuestMode RPC.
	OrgsServiceTurnOffGuestModeProcedure = "/hero.orgs.v1.OrgsService/TurnOffGuestMode"
	// OrgsServiceCreatePreRegistrationMappingProcedure is the fully-qualified name of the OrgsService's
	// CreatePreRegistrationMapping RPC.
	OrgsServiceCreatePreRegistrationMappingProcedure = "/hero.orgs.v1.OrgsService/CreatePreRegistrationMapping"
	// OrgsServiceCreatePreRegistrationMappingsProcedure is the fully-qualified name of the
	// OrgsService's CreatePreRegistrationMappings RPC.
	OrgsServiceCreatePreRegistrationMappingsProcedure = "/hero.orgs.v1.OrgsService/CreatePreRegistrationMappings"
	// OrgsServiceGetPreRegistrationMappingProcedure is the fully-qualified name of the OrgsService's
	// GetPreRegistrationMapping RPC.
	OrgsServiceGetPreRegistrationMappingProcedure = "/hero.orgs.v1.OrgsService/GetPreRegistrationMapping"
	// OrgsServiceListPreRegistrationMappingsProcedure is the fully-qualified name of the OrgsService's
	// ListPreRegistrationMappings RPC.
	OrgsServiceListPreRegistrationMappingsProcedure = "/hero.orgs.v1.OrgsService/ListPreRegistrationMappings"
	// OrgsServiceUpdatePreRegistrationMappingProcedure is the fully-qualified name of the OrgsService's
	// UpdatePreRegistrationMapping RPC.
	OrgsServiceUpdatePreRegistrationMappingProcedure = "/hero.orgs.v1.OrgsService/UpdatePreRegistrationMapping"
	// OrgsServiceDeletePreRegistrationMappingProcedure is the fully-qualified name of the OrgsService's
	// DeletePreRegistrationMapping RPC.
	OrgsServiceDeletePreRegistrationMappingProcedure = "/hero.orgs.v1.OrgsService/DeletePreRegistrationMapping"
	// OrgsServiceMarkMappingAsUsedProcedure is the fully-qualified name of the OrgsService's
	// MarkMappingAsUsed RPC.
	OrgsServiceMarkMappingAsUsedProcedure = "/hero.orgs.v1.OrgsService/MarkMappingAsUsed"
)

// OrgsServiceClient is a client for the hero.orgs.v1.OrgsService service.
type OrgsServiceClient interface {
	CreateOrg(context.Context, *connect.Request[v1.CreateOrgRequest]) (*connect.Response[v1.CreateOrgResponse], error)
	UpdateOrg(context.Context, *connect.Request[v1.UpdateOrgRequest]) (*connect.Response[v1.UpdateOrgResponse], error)
	UpdateForwardingConfig(context.Context, *connect.Request[v1.UpdateForwardingConfigRequest]) (*connect.Response[v1.UpdateForwardingConfigResponse], error)
	DeleteOrg(context.Context, *connect.Request[v1.DeleteOrgRequest]) (*connect.Response[v1.DeleteOrgResponse], error)
	GetOrg(context.Context, *connect.Request[v1.GetOrgRequest]) (*connect.Response[v1.GetOrgResponse], error)
	ListOrgs(context.Context, *connect.Request[v1.ListOrgsRequest]) (*connect.Response[v1.ListOrgsResponse], error)
	GetForwardingConfig(context.Context, *connect.Request[v1.GetForwardingConfigRequest]) (*connect.Response[v1.GetForwardingConfigResponse], error)
	ValidateOrgCreds(context.Context, *connect.Request[v1.ValidateOrgCredsRequest]) (*connect.Response[v1.ValidateOrgCredsResponse], error)
	CreateOrgAPIUser(context.Context, *connect.Request[v1.CreateOrgAPIUserRequest]) (*connect.Response[v1.CreateOrgAPIUserResponse], error)
	GetOrgAPIUserPrivateById(context.Context, *connect.Request[v1.GetOrgAPIUserPrivateByIdRequest]) (*connect.Response[v1.GetOrgAPIUserPrivateByIdResponse], error)
	GetZelloChannels(context.Context, *connect.Request[v1.GetZelloChannelsRequest]) (*connect.Response[v1.GetZelloChannelsResponse], error)
	InsertOrgQueue(context.Context, *connect.Request[v1.InsertOrgQueueRequest]) (*connect.Response[v1.InsertOrgQueueResponse], error)
	CreateOrgTwilioQueue(context.Context, *connect.Request[v1.CreateOrgTwilioQueueRequest]) (*connect.Response[v1.CreateOrgTwilioQueueResponse], error)
	// Organization Contact Book Management
	AddToContactBook(context.Context, *connect.Request[v1.AddToContactBookRequest]) (*connect.Response[v1.AddToContactBookResponse], error)
	UpdateContactInContactBook(context.Context, *connect.Request[v1.UpdateContactInContactBookRequest]) (*connect.Response[v1.UpdateContactInContactBookResponse], error)
	DeleteFromContactBook(context.Context, *connect.Request[v1.DeleteFromContactBookRequest]) (*connect.Response[v1.DeleteFromContactBookResponse], error)
	GetContactFromContactBook(context.Context, *connect.Request[v1.GetContactFromContactBookRequest]) (*connect.Response[v1.GetContactFromContactBookResponse], error)
	ListContactsInContactBook(context.Context, *connect.Request[v1.ListContactsInContactBookRequest]) (*connect.Response[v1.ListContactsInContactBookResponse], error)
	GetContactByPhoneNumber(context.Context, *connect.Request[v1.GetContactByPhoneNumberRequest]) (*connect.Response[v1.GetContactByPhoneNumberResponse], error)
	TurnOnGuestMode(context.Context, *connect.Request[v1.TurnOnGuestModeRequest]) (*connect.Response[v1.TurnOnGuestModeResponse], error)
	TurnOffGuestMode(context.Context, *connect.Request[v1.TurnOffGuestModeRequest]) (*connect.Response[v1.TurnOffGuestModeResponse], error)
	// Pre-registration user mapping management
	CreatePreRegistrationMapping(context.Context, *connect.Request[v1.CreatePreRegistrationMappingRequest]) (*connect.Response[v1.CreatePreRegistrationMappingResponse], error)
	CreatePreRegistrationMappings(context.Context, *connect.Request[v1.CreatePreRegistrationMappingsRequest]) (*connect.Response[v1.CreatePreRegistrationMappingsResponse], error)
	GetPreRegistrationMapping(context.Context, *connect.Request[v1.GetPreRegistrationMappingRequest]) (*connect.Response[v1.GetPreRegistrationMappingResponse], error)
	ListPreRegistrationMappings(context.Context, *connect.Request[v1.ListPreRegistrationMappingsRequest]) (*connect.Response[v1.ListPreRegistrationMappingsResponse], error)
	UpdatePreRegistrationMapping(context.Context, *connect.Request[v1.UpdatePreRegistrationMappingRequest]) (*connect.Response[v1.UpdatePreRegistrationMappingResponse], error)
	DeletePreRegistrationMapping(context.Context, *connect.Request[v1.DeletePreRegistrationMappingRequest]) (*connect.Response[v1.DeletePreRegistrationMappingResponse], error)
	MarkMappingAsUsed(context.Context, *connect.Request[v1.MarkMappingAsUsedRequest]) (*connect.Response[v1.MarkMappingAsUsedResponse], error)
}

// NewOrgsServiceClient constructs a client for the hero.orgs.v1.OrgsService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewOrgsServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) OrgsServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	orgsServiceMethods := v1.File_hero_orgs_v1_orgs_proto.Services().ByName("OrgsService").Methods()
	return &orgsServiceClient{
		createOrg: connect.NewClient[v1.CreateOrgRequest, v1.CreateOrgResponse](
			httpClient,
			baseURL+OrgsServiceCreateOrgProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("CreateOrg")),
			connect.WithClientOptions(opts...),
		),
		updateOrg: connect.NewClient[v1.UpdateOrgRequest, v1.UpdateOrgResponse](
			httpClient,
			baseURL+OrgsServiceUpdateOrgProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("UpdateOrg")),
			connect.WithClientOptions(opts...),
		),
		updateForwardingConfig: connect.NewClient[v1.UpdateForwardingConfigRequest, v1.UpdateForwardingConfigResponse](
			httpClient,
			baseURL+OrgsServiceUpdateForwardingConfigProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("UpdateForwardingConfig")),
			connect.WithClientOptions(opts...),
		),
		deleteOrg: connect.NewClient[v1.DeleteOrgRequest, v1.DeleteOrgResponse](
			httpClient,
			baseURL+OrgsServiceDeleteOrgProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("DeleteOrg")),
			connect.WithClientOptions(opts...),
		),
		getOrg: connect.NewClient[v1.GetOrgRequest, v1.GetOrgResponse](
			httpClient,
			baseURL+OrgsServiceGetOrgProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetOrg")),
			connect.WithClientOptions(opts...),
		),
		listOrgs: connect.NewClient[v1.ListOrgsRequest, v1.ListOrgsResponse](
			httpClient,
			baseURL+OrgsServiceListOrgsProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("ListOrgs")),
			connect.WithClientOptions(opts...),
		),
		getForwardingConfig: connect.NewClient[v1.GetForwardingConfigRequest, v1.GetForwardingConfigResponse](
			httpClient,
			baseURL+OrgsServiceGetForwardingConfigProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetForwardingConfig")),
			connect.WithClientOptions(opts...),
		),
		validateOrgCreds: connect.NewClient[v1.ValidateOrgCredsRequest, v1.ValidateOrgCredsResponse](
			httpClient,
			baseURL+OrgsServiceValidateOrgCredsProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("ValidateOrgCreds")),
			connect.WithClientOptions(opts...),
		),
		createOrgAPIUser: connect.NewClient[v1.CreateOrgAPIUserRequest, v1.CreateOrgAPIUserResponse](
			httpClient,
			baseURL+OrgsServiceCreateOrgAPIUserProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("CreateOrgAPIUser")),
			connect.WithClientOptions(opts...),
		),
		getOrgAPIUserPrivateById: connect.NewClient[v1.GetOrgAPIUserPrivateByIdRequest, v1.GetOrgAPIUserPrivateByIdResponse](
			httpClient,
			baseURL+OrgsServiceGetOrgAPIUserPrivateByIdProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetOrgAPIUserPrivateById")),
			connect.WithClientOptions(opts...),
		),
		getZelloChannels: connect.NewClient[v1.GetZelloChannelsRequest, v1.GetZelloChannelsResponse](
			httpClient,
			baseURL+OrgsServiceGetZelloChannelsProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetZelloChannels")),
			connect.WithClientOptions(opts...),
		),
		insertOrgQueue: connect.NewClient[v1.InsertOrgQueueRequest, v1.InsertOrgQueueResponse](
			httpClient,
			baseURL+OrgsServiceInsertOrgQueueProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("InsertOrgQueue")),
			connect.WithClientOptions(opts...),
		),
		createOrgTwilioQueue: connect.NewClient[v1.CreateOrgTwilioQueueRequest, v1.CreateOrgTwilioQueueResponse](
			httpClient,
			baseURL+OrgsServiceCreateOrgTwilioQueueProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("CreateOrgTwilioQueue")),
			connect.WithClientOptions(opts...),
		),
		addToContactBook: connect.NewClient[v1.AddToContactBookRequest, v1.AddToContactBookResponse](
			httpClient,
			baseURL+OrgsServiceAddToContactBookProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("AddToContactBook")),
			connect.WithClientOptions(opts...),
		),
		updateContactInContactBook: connect.NewClient[v1.UpdateContactInContactBookRequest, v1.UpdateContactInContactBookResponse](
			httpClient,
			baseURL+OrgsServiceUpdateContactInContactBookProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("UpdateContactInContactBook")),
			connect.WithClientOptions(opts...),
		),
		deleteFromContactBook: connect.NewClient[v1.DeleteFromContactBookRequest, v1.DeleteFromContactBookResponse](
			httpClient,
			baseURL+OrgsServiceDeleteFromContactBookProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("DeleteFromContactBook")),
			connect.WithClientOptions(opts...),
		),
		getContactFromContactBook: connect.NewClient[v1.GetContactFromContactBookRequest, v1.GetContactFromContactBookResponse](
			httpClient,
			baseURL+OrgsServiceGetContactFromContactBookProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetContactFromContactBook")),
			connect.WithClientOptions(opts...),
		),
		listContactsInContactBook: connect.NewClient[v1.ListContactsInContactBookRequest, v1.ListContactsInContactBookResponse](
			httpClient,
			baseURL+OrgsServiceListContactsInContactBookProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("ListContactsInContactBook")),
			connect.WithClientOptions(opts...),
		),
		getContactByPhoneNumber: connect.NewClient[v1.GetContactByPhoneNumberRequest, v1.GetContactByPhoneNumberResponse](
			httpClient,
			baseURL+OrgsServiceGetContactByPhoneNumberProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetContactByPhoneNumber")),
			connect.WithClientOptions(opts...),
		),
		turnOnGuestMode: connect.NewClient[v1.TurnOnGuestModeRequest, v1.TurnOnGuestModeResponse](
			httpClient,
			baseURL+OrgsServiceTurnOnGuestModeProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("TurnOnGuestMode")),
			connect.WithClientOptions(opts...),
		),
		turnOffGuestMode: connect.NewClient[v1.TurnOffGuestModeRequest, v1.TurnOffGuestModeResponse](
			httpClient,
			baseURL+OrgsServiceTurnOffGuestModeProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("TurnOffGuestMode")),
			connect.WithClientOptions(opts...),
		),
		createPreRegistrationMapping: connect.NewClient[v1.CreatePreRegistrationMappingRequest, v1.CreatePreRegistrationMappingResponse](
			httpClient,
			baseURL+OrgsServiceCreatePreRegistrationMappingProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("CreatePreRegistrationMapping")),
			connect.WithClientOptions(opts...),
		),
		createPreRegistrationMappings: connect.NewClient[v1.CreatePreRegistrationMappingsRequest, v1.CreatePreRegistrationMappingsResponse](
			httpClient,
			baseURL+OrgsServiceCreatePreRegistrationMappingsProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("CreatePreRegistrationMappings")),
			connect.WithClientOptions(opts...),
		),
		getPreRegistrationMapping: connect.NewClient[v1.GetPreRegistrationMappingRequest, v1.GetPreRegistrationMappingResponse](
			httpClient,
			baseURL+OrgsServiceGetPreRegistrationMappingProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("GetPreRegistrationMapping")),
			connect.WithClientOptions(opts...),
		),
		listPreRegistrationMappings: connect.NewClient[v1.ListPreRegistrationMappingsRequest, v1.ListPreRegistrationMappingsResponse](
			httpClient,
			baseURL+OrgsServiceListPreRegistrationMappingsProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("ListPreRegistrationMappings")),
			connect.WithClientOptions(opts...),
		),
		updatePreRegistrationMapping: connect.NewClient[v1.UpdatePreRegistrationMappingRequest, v1.UpdatePreRegistrationMappingResponse](
			httpClient,
			baseURL+OrgsServiceUpdatePreRegistrationMappingProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("UpdatePreRegistrationMapping")),
			connect.WithClientOptions(opts...),
		),
		deletePreRegistrationMapping: connect.NewClient[v1.DeletePreRegistrationMappingRequest, v1.DeletePreRegistrationMappingResponse](
			httpClient,
			baseURL+OrgsServiceDeletePreRegistrationMappingProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("DeletePreRegistrationMapping")),
			connect.WithClientOptions(opts...),
		),
		markMappingAsUsed: connect.NewClient[v1.MarkMappingAsUsedRequest, v1.MarkMappingAsUsedResponse](
			httpClient,
			baseURL+OrgsServiceMarkMappingAsUsedProcedure,
			connect.WithSchema(orgsServiceMethods.ByName("MarkMappingAsUsed")),
			connect.WithClientOptions(opts...),
		),
	}
}

// orgsServiceClient implements OrgsServiceClient.
type orgsServiceClient struct {
	createOrg                     *connect.Client[v1.CreateOrgRequest, v1.CreateOrgResponse]
	updateOrg                     *connect.Client[v1.UpdateOrgRequest, v1.UpdateOrgResponse]
	updateForwardingConfig        *connect.Client[v1.UpdateForwardingConfigRequest, v1.UpdateForwardingConfigResponse]
	deleteOrg                     *connect.Client[v1.DeleteOrgRequest, v1.DeleteOrgResponse]
	getOrg                        *connect.Client[v1.GetOrgRequest, v1.GetOrgResponse]
	listOrgs                      *connect.Client[v1.ListOrgsRequest, v1.ListOrgsResponse]
	getForwardingConfig           *connect.Client[v1.GetForwardingConfigRequest, v1.GetForwardingConfigResponse]
	validateOrgCreds              *connect.Client[v1.ValidateOrgCredsRequest, v1.ValidateOrgCredsResponse]
	createOrgAPIUser              *connect.Client[v1.CreateOrgAPIUserRequest, v1.CreateOrgAPIUserResponse]
	getOrgAPIUserPrivateById      *connect.Client[v1.GetOrgAPIUserPrivateByIdRequest, v1.GetOrgAPIUserPrivateByIdResponse]
	getZelloChannels              *connect.Client[v1.GetZelloChannelsRequest, v1.GetZelloChannelsResponse]
	insertOrgQueue                *connect.Client[v1.InsertOrgQueueRequest, v1.InsertOrgQueueResponse]
	createOrgTwilioQueue          *connect.Client[v1.CreateOrgTwilioQueueRequest, v1.CreateOrgTwilioQueueResponse]
	addToContactBook              *connect.Client[v1.AddToContactBookRequest, v1.AddToContactBookResponse]
	updateContactInContactBook    *connect.Client[v1.UpdateContactInContactBookRequest, v1.UpdateContactInContactBookResponse]
	deleteFromContactBook         *connect.Client[v1.DeleteFromContactBookRequest, v1.DeleteFromContactBookResponse]
	getContactFromContactBook     *connect.Client[v1.GetContactFromContactBookRequest, v1.GetContactFromContactBookResponse]
	listContactsInContactBook     *connect.Client[v1.ListContactsInContactBookRequest, v1.ListContactsInContactBookResponse]
	getContactByPhoneNumber       *connect.Client[v1.GetContactByPhoneNumberRequest, v1.GetContactByPhoneNumberResponse]
	turnOnGuestMode               *connect.Client[v1.TurnOnGuestModeRequest, v1.TurnOnGuestModeResponse]
	turnOffGuestMode              *connect.Client[v1.TurnOffGuestModeRequest, v1.TurnOffGuestModeResponse]
	createPreRegistrationMapping  *connect.Client[v1.CreatePreRegistrationMappingRequest, v1.CreatePreRegistrationMappingResponse]
	createPreRegistrationMappings *connect.Client[v1.CreatePreRegistrationMappingsRequest, v1.CreatePreRegistrationMappingsResponse]
	getPreRegistrationMapping     *connect.Client[v1.GetPreRegistrationMappingRequest, v1.GetPreRegistrationMappingResponse]
	listPreRegistrationMappings   *connect.Client[v1.ListPreRegistrationMappingsRequest, v1.ListPreRegistrationMappingsResponse]
	updatePreRegistrationMapping  *connect.Client[v1.UpdatePreRegistrationMappingRequest, v1.UpdatePreRegistrationMappingResponse]
	deletePreRegistrationMapping  *connect.Client[v1.DeletePreRegistrationMappingRequest, v1.DeletePreRegistrationMappingResponse]
	markMappingAsUsed             *connect.Client[v1.MarkMappingAsUsedRequest, v1.MarkMappingAsUsedResponse]
}

// CreateOrg calls hero.orgs.v1.OrgsService.CreateOrg.
func (c *orgsServiceClient) CreateOrg(ctx context.Context, req *connect.Request[v1.CreateOrgRequest]) (*connect.Response[v1.CreateOrgResponse], error) {
	return c.createOrg.CallUnary(ctx, req)
}

// UpdateOrg calls hero.orgs.v1.OrgsService.UpdateOrg.
func (c *orgsServiceClient) UpdateOrg(ctx context.Context, req *connect.Request[v1.UpdateOrgRequest]) (*connect.Response[v1.UpdateOrgResponse], error) {
	return c.updateOrg.CallUnary(ctx, req)
}

// UpdateForwardingConfig calls hero.orgs.v1.OrgsService.UpdateForwardingConfig.
func (c *orgsServiceClient) UpdateForwardingConfig(ctx context.Context, req *connect.Request[v1.UpdateForwardingConfigRequest]) (*connect.Response[v1.UpdateForwardingConfigResponse], error) {
	return c.updateForwardingConfig.CallUnary(ctx, req)
}

// DeleteOrg calls hero.orgs.v1.OrgsService.DeleteOrg.
func (c *orgsServiceClient) DeleteOrg(ctx context.Context, req *connect.Request[v1.DeleteOrgRequest]) (*connect.Response[v1.DeleteOrgResponse], error) {
	return c.deleteOrg.CallUnary(ctx, req)
}

// GetOrg calls hero.orgs.v1.OrgsService.GetOrg.
func (c *orgsServiceClient) GetOrg(ctx context.Context, req *connect.Request[v1.GetOrgRequest]) (*connect.Response[v1.GetOrgResponse], error) {
	return c.getOrg.CallUnary(ctx, req)
}

// ListOrgs calls hero.orgs.v1.OrgsService.ListOrgs.
func (c *orgsServiceClient) ListOrgs(ctx context.Context, req *connect.Request[v1.ListOrgsRequest]) (*connect.Response[v1.ListOrgsResponse], error) {
	return c.listOrgs.CallUnary(ctx, req)
}

// GetForwardingConfig calls hero.orgs.v1.OrgsService.GetForwardingConfig.
func (c *orgsServiceClient) GetForwardingConfig(ctx context.Context, req *connect.Request[v1.GetForwardingConfigRequest]) (*connect.Response[v1.GetForwardingConfigResponse], error) {
	return c.getForwardingConfig.CallUnary(ctx, req)
}

// ValidateOrgCreds calls hero.orgs.v1.OrgsService.ValidateOrgCreds.
func (c *orgsServiceClient) ValidateOrgCreds(ctx context.Context, req *connect.Request[v1.ValidateOrgCredsRequest]) (*connect.Response[v1.ValidateOrgCredsResponse], error) {
	return c.validateOrgCreds.CallUnary(ctx, req)
}

// CreateOrgAPIUser calls hero.orgs.v1.OrgsService.CreateOrgAPIUser.
func (c *orgsServiceClient) CreateOrgAPIUser(ctx context.Context, req *connect.Request[v1.CreateOrgAPIUserRequest]) (*connect.Response[v1.CreateOrgAPIUserResponse], error) {
	return c.createOrgAPIUser.CallUnary(ctx, req)
}

// GetOrgAPIUserPrivateById calls hero.orgs.v1.OrgsService.GetOrgAPIUserPrivateById.
func (c *orgsServiceClient) GetOrgAPIUserPrivateById(ctx context.Context, req *connect.Request[v1.GetOrgAPIUserPrivateByIdRequest]) (*connect.Response[v1.GetOrgAPIUserPrivateByIdResponse], error) {
	return c.getOrgAPIUserPrivateById.CallUnary(ctx, req)
}

// GetZelloChannels calls hero.orgs.v1.OrgsService.GetZelloChannels.
func (c *orgsServiceClient) GetZelloChannels(ctx context.Context, req *connect.Request[v1.GetZelloChannelsRequest]) (*connect.Response[v1.GetZelloChannelsResponse], error) {
	return c.getZelloChannels.CallUnary(ctx, req)
}

// InsertOrgQueue calls hero.orgs.v1.OrgsService.InsertOrgQueue.
func (c *orgsServiceClient) InsertOrgQueue(ctx context.Context, req *connect.Request[v1.InsertOrgQueueRequest]) (*connect.Response[v1.InsertOrgQueueResponse], error) {
	return c.insertOrgQueue.CallUnary(ctx, req)
}

// CreateOrgTwilioQueue calls hero.orgs.v1.OrgsService.CreateOrgTwilioQueue.
func (c *orgsServiceClient) CreateOrgTwilioQueue(ctx context.Context, req *connect.Request[v1.CreateOrgTwilioQueueRequest]) (*connect.Response[v1.CreateOrgTwilioQueueResponse], error) {
	return c.createOrgTwilioQueue.CallUnary(ctx, req)
}

// AddToContactBook calls hero.orgs.v1.OrgsService.AddToContactBook.
func (c *orgsServiceClient) AddToContactBook(ctx context.Context, req *connect.Request[v1.AddToContactBookRequest]) (*connect.Response[v1.AddToContactBookResponse], error) {
	return c.addToContactBook.CallUnary(ctx, req)
}

// UpdateContactInContactBook calls hero.orgs.v1.OrgsService.UpdateContactInContactBook.
func (c *orgsServiceClient) UpdateContactInContactBook(ctx context.Context, req *connect.Request[v1.UpdateContactInContactBookRequest]) (*connect.Response[v1.UpdateContactInContactBookResponse], error) {
	return c.updateContactInContactBook.CallUnary(ctx, req)
}

// DeleteFromContactBook calls hero.orgs.v1.OrgsService.DeleteFromContactBook.
func (c *orgsServiceClient) DeleteFromContactBook(ctx context.Context, req *connect.Request[v1.DeleteFromContactBookRequest]) (*connect.Response[v1.DeleteFromContactBookResponse], error) {
	return c.deleteFromContactBook.CallUnary(ctx, req)
}

// GetContactFromContactBook calls hero.orgs.v1.OrgsService.GetContactFromContactBook.
func (c *orgsServiceClient) GetContactFromContactBook(ctx context.Context, req *connect.Request[v1.GetContactFromContactBookRequest]) (*connect.Response[v1.GetContactFromContactBookResponse], error) {
	return c.getContactFromContactBook.CallUnary(ctx, req)
}

// ListContactsInContactBook calls hero.orgs.v1.OrgsService.ListContactsInContactBook.
func (c *orgsServiceClient) ListContactsInContactBook(ctx context.Context, req *connect.Request[v1.ListContactsInContactBookRequest]) (*connect.Response[v1.ListContactsInContactBookResponse], error) {
	return c.listContactsInContactBook.CallUnary(ctx, req)
}

// GetContactByPhoneNumber calls hero.orgs.v1.OrgsService.GetContactByPhoneNumber.
func (c *orgsServiceClient) GetContactByPhoneNumber(ctx context.Context, req *connect.Request[v1.GetContactByPhoneNumberRequest]) (*connect.Response[v1.GetContactByPhoneNumberResponse], error) {
	return c.getContactByPhoneNumber.CallUnary(ctx, req)
}

// TurnOnGuestMode calls hero.orgs.v1.OrgsService.TurnOnGuestMode.
func (c *orgsServiceClient) TurnOnGuestMode(ctx context.Context, req *connect.Request[v1.TurnOnGuestModeRequest]) (*connect.Response[v1.TurnOnGuestModeResponse], error) {
	return c.turnOnGuestMode.CallUnary(ctx, req)
}

// TurnOffGuestMode calls hero.orgs.v1.OrgsService.TurnOffGuestMode.
func (c *orgsServiceClient) TurnOffGuestMode(ctx context.Context, req *connect.Request[v1.TurnOffGuestModeRequest]) (*connect.Response[v1.TurnOffGuestModeResponse], error) {
	return c.turnOffGuestMode.CallUnary(ctx, req)
}

// CreatePreRegistrationMapping calls hero.orgs.v1.OrgsService.CreatePreRegistrationMapping.
func (c *orgsServiceClient) CreatePreRegistrationMapping(ctx context.Context, req *connect.Request[v1.CreatePreRegistrationMappingRequest]) (*connect.Response[v1.CreatePreRegistrationMappingResponse], error) {
	return c.createPreRegistrationMapping.CallUnary(ctx, req)
}

// CreatePreRegistrationMappings calls hero.orgs.v1.OrgsService.CreatePreRegistrationMappings.
func (c *orgsServiceClient) CreatePreRegistrationMappings(ctx context.Context, req *connect.Request[v1.CreatePreRegistrationMappingsRequest]) (*connect.Response[v1.CreatePreRegistrationMappingsResponse], error) {
	return c.createPreRegistrationMappings.CallUnary(ctx, req)
}

// GetPreRegistrationMapping calls hero.orgs.v1.OrgsService.GetPreRegistrationMapping.
func (c *orgsServiceClient) GetPreRegistrationMapping(ctx context.Context, req *connect.Request[v1.GetPreRegistrationMappingRequest]) (*connect.Response[v1.GetPreRegistrationMappingResponse], error) {
	return c.getPreRegistrationMapping.CallUnary(ctx, req)
}

// ListPreRegistrationMappings calls hero.orgs.v1.OrgsService.ListPreRegistrationMappings.
func (c *orgsServiceClient) ListPreRegistrationMappings(ctx context.Context, req *connect.Request[v1.ListPreRegistrationMappingsRequest]) (*connect.Response[v1.ListPreRegistrationMappingsResponse], error) {
	return c.listPreRegistrationMappings.CallUnary(ctx, req)
}

// UpdatePreRegistrationMapping calls hero.orgs.v1.OrgsService.UpdatePreRegistrationMapping.
func (c *orgsServiceClient) UpdatePreRegistrationMapping(ctx context.Context, req *connect.Request[v1.UpdatePreRegistrationMappingRequest]) (*connect.Response[v1.UpdatePreRegistrationMappingResponse], error) {
	return c.updatePreRegistrationMapping.CallUnary(ctx, req)
}

// DeletePreRegistrationMapping calls hero.orgs.v1.OrgsService.DeletePreRegistrationMapping.
func (c *orgsServiceClient) DeletePreRegistrationMapping(ctx context.Context, req *connect.Request[v1.DeletePreRegistrationMappingRequest]) (*connect.Response[v1.DeletePreRegistrationMappingResponse], error) {
	return c.deletePreRegistrationMapping.CallUnary(ctx, req)
}

// MarkMappingAsUsed calls hero.orgs.v1.OrgsService.MarkMappingAsUsed.
func (c *orgsServiceClient) MarkMappingAsUsed(ctx context.Context, req *connect.Request[v1.MarkMappingAsUsedRequest]) (*connect.Response[v1.MarkMappingAsUsedResponse], error) {
	return c.markMappingAsUsed.CallUnary(ctx, req)
}

// OrgsServiceHandler is an implementation of the hero.orgs.v1.OrgsService service.
type OrgsServiceHandler interface {
	CreateOrg(context.Context, *connect.Request[v1.CreateOrgRequest]) (*connect.Response[v1.CreateOrgResponse], error)
	UpdateOrg(context.Context, *connect.Request[v1.UpdateOrgRequest]) (*connect.Response[v1.UpdateOrgResponse], error)
	UpdateForwardingConfig(context.Context, *connect.Request[v1.UpdateForwardingConfigRequest]) (*connect.Response[v1.UpdateForwardingConfigResponse], error)
	DeleteOrg(context.Context, *connect.Request[v1.DeleteOrgRequest]) (*connect.Response[v1.DeleteOrgResponse], error)
	GetOrg(context.Context, *connect.Request[v1.GetOrgRequest]) (*connect.Response[v1.GetOrgResponse], error)
	ListOrgs(context.Context, *connect.Request[v1.ListOrgsRequest]) (*connect.Response[v1.ListOrgsResponse], error)
	GetForwardingConfig(context.Context, *connect.Request[v1.GetForwardingConfigRequest]) (*connect.Response[v1.GetForwardingConfigResponse], error)
	ValidateOrgCreds(context.Context, *connect.Request[v1.ValidateOrgCredsRequest]) (*connect.Response[v1.ValidateOrgCredsResponse], error)
	CreateOrgAPIUser(context.Context, *connect.Request[v1.CreateOrgAPIUserRequest]) (*connect.Response[v1.CreateOrgAPIUserResponse], error)
	GetOrgAPIUserPrivateById(context.Context, *connect.Request[v1.GetOrgAPIUserPrivateByIdRequest]) (*connect.Response[v1.GetOrgAPIUserPrivateByIdResponse], error)
	GetZelloChannels(context.Context, *connect.Request[v1.GetZelloChannelsRequest]) (*connect.Response[v1.GetZelloChannelsResponse], error)
	InsertOrgQueue(context.Context, *connect.Request[v1.InsertOrgQueueRequest]) (*connect.Response[v1.InsertOrgQueueResponse], error)
	CreateOrgTwilioQueue(context.Context, *connect.Request[v1.CreateOrgTwilioQueueRequest]) (*connect.Response[v1.CreateOrgTwilioQueueResponse], error)
	// Organization Contact Book Management
	AddToContactBook(context.Context, *connect.Request[v1.AddToContactBookRequest]) (*connect.Response[v1.AddToContactBookResponse], error)
	UpdateContactInContactBook(context.Context, *connect.Request[v1.UpdateContactInContactBookRequest]) (*connect.Response[v1.UpdateContactInContactBookResponse], error)
	DeleteFromContactBook(context.Context, *connect.Request[v1.DeleteFromContactBookRequest]) (*connect.Response[v1.DeleteFromContactBookResponse], error)
	GetContactFromContactBook(context.Context, *connect.Request[v1.GetContactFromContactBookRequest]) (*connect.Response[v1.GetContactFromContactBookResponse], error)
	ListContactsInContactBook(context.Context, *connect.Request[v1.ListContactsInContactBookRequest]) (*connect.Response[v1.ListContactsInContactBookResponse], error)
	GetContactByPhoneNumber(context.Context, *connect.Request[v1.GetContactByPhoneNumberRequest]) (*connect.Response[v1.GetContactByPhoneNumberResponse], error)
	TurnOnGuestMode(context.Context, *connect.Request[v1.TurnOnGuestModeRequest]) (*connect.Response[v1.TurnOnGuestModeResponse], error)
	TurnOffGuestMode(context.Context, *connect.Request[v1.TurnOffGuestModeRequest]) (*connect.Response[v1.TurnOffGuestModeResponse], error)
	// Pre-registration user mapping management
	CreatePreRegistrationMapping(context.Context, *connect.Request[v1.CreatePreRegistrationMappingRequest]) (*connect.Response[v1.CreatePreRegistrationMappingResponse], error)
	CreatePreRegistrationMappings(context.Context, *connect.Request[v1.CreatePreRegistrationMappingsRequest]) (*connect.Response[v1.CreatePreRegistrationMappingsResponse], error)
	GetPreRegistrationMapping(context.Context, *connect.Request[v1.GetPreRegistrationMappingRequest]) (*connect.Response[v1.GetPreRegistrationMappingResponse], error)
	ListPreRegistrationMappings(context.Context, *connect.Request[v1.ListPreRegistrationMappingsRequest]) (*connect.Response[v1.ListPreRegistrationMappingsResponse], error)
	UpdatePreRegistrationMapping(context.Context, *connect.Request[v1.UpdatePreRegistrationMappingRequest]) (*connect.Response[v1.UpdatePreRegistrationMappingResponse], error)
	DeletePreRegistrationMapping(context.Context, *connect.Request[v1.DeletePreRegistrationMappingRequest]) (*connect.Response[v1.DeletePreRegistrationMappingResponse], error)
	MarkMappingAsUsed(context.Context, *connect.Request[v1.MarkMappingAsUsedRequest]) (*connect.Response[v1.MarkMappingAsUsedResponse], error)
}

// NewOrgsServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewOrgsServiceHandler(svc OrgsServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	orgsServiceMethods := v1.File_hero_orgs_v1_orgs_proto.Services().ByName("OrgsService").Methods()
	orgsServiceCreateOrgHandler := connect.NewUnaryHandler(
		OrgsServiceCreateOrgProcedure,
		svc.CreateOrg,
		connect.WithSchema(orgsServiceMethods.ByName("CreateOrg")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceUpdateOrgHandler := connect.NewUnaryHandler(
		OrgsServiceUpdateOrgProcedure,
		svc.UpdateOrg,
		connect.WithSchema(orgsServiceMethods.ByName("UpdateOrg")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceUpdateForwardingConfigHandler := connect.NewUnaryHandler(
		OrgsServiceUpdateForwardingConfigProcedure,
		svc.UpdateForwardingConfig,
		connect.WithSchema(orgsServiceMethods.ByName("UpdateForwardingConfig")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceDeleteOrgHandler := connect.NewUnaryHandler(
		OrgsServiceDeleteOrgProcedure,
		svc.DeleteOrg,
		connect.WithSchema(orgsServiceMethods.ByName("DeleteOrg")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetOrgHandler := connect.NewUnaryHandler(
		OrgsServiceGetOrgProcedure,
		svc.GetOrg,
		connect.WithSchema(orgsServiceMethods.ByName("GetOrg")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceListOrgsHandler := connect.NewUnaryHandler(
		OrgsServiceListOrgsProcedure,
		svc.ListOrgs,
		connect.WithSchema(orgsServiceMethods.ByName("ListOrgs")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetForwardingConfigHandler := connect.NewUnaryHandler(
		OrgsServiceGetForwardingConfigProcedure,
		svc.GetForwardingConfig,
		connect.WithSchema(orgsServiceMethods.ByName("GetForwardingConfig")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceValidateOrgCredsHandler := connect.NewUnaryHandler(
		OrgsServiceValidateOrgCredsProcedure,
		svc.ValidateOrgCreds,
		connect.WithSchema(orgsServiceMethods.ByName("ValidateOrgCreds")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceCreateOrgAPIUserHandler := connect.NewUnaryHandler(
		OrgsServiceCreateOrgAPIUserProcedure,
		svc.CreateOrgAPIUser,
		connect.WithSchema(orgsServiceMethods.ByName("CreateOrgAPIUser")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetOrgAPIUserPrivateByIdHandler := connect.NewUnaryHandler(
		OrgsServiceGetOrgAPIUserPrivateByIdProcedure,
		svc.GetOrgAPIUserPrivateById,
		connect.WithSchema(orgsServiceMethods.ByName("GetOrgAPIUserPrivateById")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetZelloChannelsHandler := connect.NewUnaryHandler(
		OrgsServiceGetZelloChannelsProcedure,
		svc.GetZelloChannels,
		connect.WithSchema(orgsServiceMethods.ByName("GetZelloChannels")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceInsertOrgQueueHandler := connect.NewUnaryHandler(
		OrgsServiceInsertOrgQueueProcedure,
		svc.InsertOrgQueue,
		connect.WithSchema(orgsServiceMethods.ByName("InsertOrgQueue")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceCreateOrgTwilioQueueHandler := connect.NewUnaryHandler(
		OrgsServiceCreateOrgTwilioQueueProcedure,
		svc.CreateOrgTwilioQueue,
		connect.WithSchema(orgsServiceMethods.ByName("CreateOrgTwilioQueue")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceAddToContactBookHandler := connect.NewUnaryHandler(
		OrgsServiceAddToContactBookProcedure,
		svc.AddToContactBook,
		connect.WithSchema(orgsServiceMethods.ByName("AddToContactBook")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceUpdateContactInContactBookHandler := connect.NewUnaryHandler(
		OrgsServiceUpdateContactInContactBookProcedure,
		svc.UpdateContactInContactBook,
		connect.WithSchema(orgsServiceMethods.ByName("UpdateContactInContactBook")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceDeleteFromContactBookHandler := connect.NewUnaryHandler(
		OrgsServiceDeleteFromContactBookProcedure,
		svc.DeleteFromContactBook,
		connect.WithSchema(orgsServiceMethods.ByName("DeleteFromContactBook")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetContactFromContactBookHandler := connect.NewUnaryHandler(
		OrgsServiceGetContactFromContactBookProcedure,
		svc.GetContactFromContactBook,
		connect.WithSchema(orgsServiceMethods.ByName("GetContactFromContactBook")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceListContactsInContactBookHandler := connect.NewUnaryHandler(
		OrgsServiceListContactsInContactBookProcedure,
		svc.ListContactsInContactBook,
		connect.WithSchema(orgsServiceMethods.ByName("ListContactsInContactBook")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetContactByPhoneNumberHandler := connect.NewUnaryHandler(
		OrgsServiceGetContactByPhoneNumberProcedure,
		svc.GetContactByPhoneNumber,
		connect.WithSchema(orgsServiceMethods.ByName("GetContactByPhoneNumber")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceTurnOnGuestModeHandler := connect.NewUnaryHandler(
		OrgsServiceTurnOnGuestModeProcedure,
		svc.TurnOnGuestMode,
		connect.WithSchema(orgsServiceMethods.ByName("TurnOnGuestMode")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceTurnOffGuestModeHandler := connect.NewUnaryHandler(
		OrgsServiceTurnOffGuestModeProcedure,
		svc.TurnOffGuestMode,
		connect.WithSchema(orgsServiceMethods.ByName("TurnOffGuestMode")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceCreatePreRegistrationMappingHandler := connect.NewUnaryHandler(
		OrgsServiceCreatePreRegistrationMappingProcedure,
		svc.CreatePreRegistrationMapping,
		connect.WithSchema(orgsServiceMethods.ByName("CreatePreRegistrationMapping")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceCreatePreRegistrationMappingsHandler := connect.NewUnaryHandler(
		OrgsServiceCreatePreRegistrationMappingsProcedure,
		svc.CreatePreRegistrationMappings,
		connect.WithSchema(orgsServiceMethods.ByName("CreatePreRegistrationMappings")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceGetPreRegistrationMappingHandler := connect.NewUnaryHandler(
		OrgsServiceGetPreRegistrationMappingProcedure,
		svc.GetPreRegistrationMapping,
		connect.WithSchema(orgsServiceMethods.ByName("GetPreRegistrationMapping")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceListPreRegistrationMappingsHandler := connect.NewUnaryHandler(
		OrgsServiceListPreRegistrationMappingsProcedure,
		svc.ListPreRegistrationMappings,
		connect.WithSchema(orgsServiceMethods.ByName("ListPreRegistrationMappings")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceUpdatePreRegistrationMappingHandler := connect.NewUnaryHandler(
		OrgsServiceUpdatePreRegistrationMappingProcedure,
		svc.UpdatePreRegistrationMapping,
		connect.WithSchema(orgsServiceMethods.ByName("UpdatePreRegistrationMapping")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceDeletePreRegistrationMappingHandler := connect.NewUnaryHandler(
		OrgsServiceDeletePreRegistrationMappingProcedure,
		svc.DeletePreRegistrationMapping,
		connect.WithSchema(orgsServiceMethods.ByName("DeletePreRegistrationMapping")),
		connect.WithHandlerOptions(opts...),
	)
	orgsServiceMarkMappingAsUsedHandler := connect.NewUnaryHandler(
		OrgsServiceMarkMappingAsUsedProcedure,
		svc.MarkMappingAsUsed,
		connect.WithSchema(orgsServiceMethods.ByName("MarkMappingAsUsed")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.orgs.v1.OrgsService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case OrgsServiceCreateOrgProcedure:
			orgsServiceCreateOrgHandler.ServeHTTP(w, r)
		case OrgsServiceUpdateOrgProcedure:
			orgsServiceUpdateOrgHandler.ServeHTTP(w, r)
		case OrgsServiceUpdateForwardingConfigProcedure:
			orgsServiceUpdateForwardingConfigHandler.ServeHTTP(w, r)
		case OrgsServiceDeleteOrgProcedure:
			orgsServiceDeleteOrgHandler.ServeHTTP(w, r)
		case OrgsServiceGetOrgProcedure:
			orgsServiceGetOrgHandler.ServeHTTP(w, r)
		case OrgsServiceListOrgsProcedure:
			orgsServiceListOrgsHandler.ServeHTTP(w, r)
		case OrgsServiceGetForwardingConfigProcedure:
			orgsServiceGetForwardingConfigHandler.ServeHTTP(w, r)
		case OrgsServiceValidateOrgCredsProcedure:
			orgsServiceValidateOrgCredsHandler.ServeHTTP(w, r)
		case OrgsServiceCreateOrgAPIUserProcedure:
			orgsServiceCreateOrgAPIUserHandler.ServeHTTP(w, r)
		case OrgsServiceGetOrgAPIUserPrivateByIdProcedure:
			orgsServiceGetOrgAPIUserPrivateByIdHandler.ServeHTTP(w, r)
		case OrgsServiceGetZelloChannelsProcedure:
			orgsServiceGetZelloChannelsHandler.ServeHTTP(w, r)
		case OrgsServiceInsertOrgQueueProcedure:
			orgsServiceInsertOrgQueueHandler.ServeHTTP(w, r)
		case OrgsServiceCreateOrgTwilioQueueProcedure:
			orgsServiceCreateOrgTwilioQueueHandler.ServeHTTP(w, r)
		case OrgsServiceAddToContactBookProcedure:
			orgsServiceAddToContactBookHandler.ServeHTTP(w, r)
		case OrgsServiceUpdateContactInContactBookProcedure:
			orgsServiceUpdateContactInContactBookHandler.ServeHTTP(w, r)
		case OrgsServiceDeleteFromContactBookProcedure:
			orgsServiceDeleteFromContactBookHandler.ServeHTTP(w, r)
		case OrgsServiceGetContactFromContactBookProcedure:
			orgsServiceGetContactFromContactBookHandler.ServeHTTP(w, r)
		case OrgsServiceListContactsInContactBookProcedure:
			orgsServiceListContactsInContactBookHandler.ServeHTTP(w, r)
		case OrgsServiceGetContactByPhoneNumberProcedure:
			orgsServiceGetContactByPhoneNumberHandler.ServeHTTP(w, r)
		case OrgsServiceTurnOnGuestModeProcedure:
			orgsServiceTurnOnGuestModeHandler.ServeHTTP(w, r)
		case OrgsServiceTurnOffGuestModeProcedure:
			orgsServiceTurnOffGuestModeHandler.ServeHTTP(w, r)
		case OrgsServiceCreatePreRegistrationMappingProcedure:
			orgsServiceCreatePreRegistrationMappingHandler.ServeHTTP(w, r)
		case OrgsServiceCreatePreRegistrationMappingsProcedure:
			orgsServiceCreatePreRegistrationMappingsHandler.ServeHTTP(w, r)
		case OrgsServiceGetPreRegistrationMappingProcedure:
			orgsServiceGetPreRegistrationMappingHandler.ServeHTTP(w, r)
		case OrgsServiceListPreRegistrationMappingsProcedure:
			orgsServiceListPreRegistrationMappingsHandler.ServeHTTP(w, r)
		case OrgsServiceUpdatePreRegistrationMappingProcedure:
			orgsServiceUpdatePreRegistrationMappingHandler.ServeHTTP(w, r)
		case OrgsServiceDeletePreRegistrationMappingProcedure:
			orgsServiceDeletePreRegistrationMappingHandler.ServeHTTP(w, r)
		case OrgsServiceMarkMappingAsUsedProcedure:
			orgsServiceMarkMappingAsUsedHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedOrgsServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedOrgsServiceHandler struct{}

func (UnimplementedOrgsServiceHandler) CreateOrg(context.Context, *connect.Request[v1.CreateOrgRequest]) (*connect.Response[v1.CreateOrgResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.CreateOrg is not implemented"))
}

func (UnimplementedOrgsServiceHandler) UpdateOrg(context.Context, *connect.Request[v1.UpdateOrgRequest]) (*connect.Response[v1.UpdateOrgResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.UpdateOrg is not implemented"))
}

func (UnimplementedOrgsServiceHandler) UpdateForwardingConfig(context.Context, *connect.Request[v1.UpdateForwardingConfigRequest]) (*connect.Response[v1.UpdateForwardingConfigResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.UpdateForwardingConfig is not implemented"))
}

func (UnimplementedOrgsServiceHandler) DeleteOrg(context.Context, *connect.Request[v1.DeleteOrgRequest]) (*connect.Response[v1.DeleteOrgResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.DeleteOrg is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetOrg(context.Context, *connect.Request[v1.GetOrgRequest]) (*connect.Response[v1.GetOrgResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetOrg is not implemented"))
}

func (UnimplementedOrgsServiceHandler) ListOrgs(context.Context, *connect.Request[v1.ListOrgsRequest]) (*connect.Response[v1.ListOrgsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.ListOrgs is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetForwardingConfig(context.Context, *connect.Request[v1.GetForwardingConfigRequest]) (*connect.Response[v1.GetForwardingConfigResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetForwardingConfig is not implemented"))
}

func (UnimplementedOrgsServiceHandler) ValidateOrgCreds(context.Context, *connect.Request[v1.ValidateOrgCredsRequest]) (*connect.Response[v1.ValidateOrgCredsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.ValidateOrgCreds is not implemented"))
}

func (UnimplementedOrgsServiceHandler) CreateOrgAPIUser(context.Context, *connect.Request[v1.CreateOrgAPIUserRequest]) (*connect.Response[v1.CreateOrgAPIUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.CreateOrgAPIUser is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetOrgAPIUserPrivateById(context.Context, *connect.Request[v1.GetOrgAPIUserPrivateByIdRequest]) (*connect.Response[v1.GetOrgAPIUserPrivateByIdResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetOrgAPIUserPrivateById is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetZelloChannels(context.Context, *connect.Request[v1.GetZelloChannelsRequest]) (*connect.Response[v1.GetZelloChannelsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetZelloChannels is not implemented"))
}

func (UnimplementedOrgsServiceHandler) InsertOrgQueue(context.Context, *connect.Request[v1.InsertOrgQueueRequest]) (*connect.Response[v1.InsertOrgQueueResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.InsertOrgQueue is not implemented"))
}

func (UnimplementedOrgsServiceHandler) CreateOrgTwilioQueue(context.Context, *connect.Request[v1.CreateOrgTwilioQueueRequest]) (*connect.Response[v1.CreateOrgTwilioQueueResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.CreateOrgTwilioQueue is not implemented"))
}

func (UnimplementedOrgsServiceHandler) AddToContactBook(context.Context, *connect.Request[v1.AddToContactBookRequest]) (*connect.Response[v1.AddToContactBookResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.AddToContactBook is not implemented"))
}

func (UnimplementedOrgsServiceHandler) UpdateContactInContactBook(context.Context, *connect.Request[v1.UpdateContactInContactBookRequest]) (*connect.Response[v1.UpdateContactInContactBookResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.UpdateContactInContactBook is not implemented"))
}

func (UnimplementedOrgsServiceHandler) DeleteFromContactBook(context.Context, *connect.Request[v1.DeleteFromContactBookRequest]) (*connect.Response[v1.DeleteFromContactBookResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.DeleteFromContactBook is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetContactFromContactBook(context.Context, *connect.Request[v1.GetContactFromContactBookRequest]) (*connect.Response[v1.GetContactFromContactBookResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetContactFromContactBook is not implemented"))
}

func (UnimplementedOrgsServiceHandler) ListContactsInContactBook(context.Context, *connect.Request[v1.ListContactsInContactBookRequest]) (*connect.Response[v1.ListContactsInContactBookResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.ListContactsInContactBook is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetContactByPhoneNumber(context.Context, *connect.Request[v1.GetContactByPhoneNumberRequest]) (*connect.Response[v1.GetContactByPhoneNumberResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetContactByPhoneNumber is not implemented"))
}

func (UnimplementedOrgsServiceHandler) TurnOnGuestMode(context.Context, *connect.Request[v1.TurnOnGuestModeRequest]) (*connect.Response[v1.TurnOnGuestModeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.TurnOnGuestMode is not implemented"))
}

func (UnimplementedOrgsServiceHandler) TurnOffGuestMode(context.Context, *connect.Request[v1.TurnOffGuestModeRequest]) (*connect.Response[v1.TurnOffGuestModeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.TurnOffGuestMode is not implemented"))
}

func (UnimplementedOrgsServiceHandler) CreatePreRegistrationMapping(context.Context, *connect.Request[v1.CreatePreRegistrationMappingRequest]) (*connect.Response[v1.CreatePreRegistrationMappingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.CreatePreRegistrationMapping is not implemented"))
}

func (UnimplementedOrgsServiceHandler) CreatePreRegistrationMappings(context.Context, *connect.Request[v1.CreatePreRegistrationMappingsRequest]) (*connect.Response[v1.CreatePreRegistrationMappingsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.CreatePreRegistrationMappings is not implemented"))
}

func (UnimplementedOrgsServiceHandler) GetPreRegistrationMapping(context.Context, *connect.Request[v1.GetPreRegistrationMappingRequest]) (*connect.Response[v1.GetPreRegistrationMappingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.GetPreRegistrationMapping is not implemented"))
}

func (UnimplementedOrgsServiceHandler) ListPreRegistrationMappings(context.Context, *connect.Request[v1.ListPreRegistrationMappingsRequest]) (*connect.Response[v1.ListPreRegistrationMappingsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.ListPreRegistrationMappings is not implemented"))
}

func (UnimplementedOrgsServiceHandler) UpdatePreRegistrationMapping(context.Context, *connect.Request[v1.UpdatePreRegistrationMappingRequest]) (*connect.Response[v1.UpdatePreRegistrationMappingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.UpdatePreRegistrationMapping is not implemented"))
}

func (UnimplementedOrgsServiceHandler) DeletePreRegistrationMapping(context.Context, *connect.Request[v1.DeletePreRegistrationMappingRequest]) (*connect.Response[v1.DeletePreRegistrationMappingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.DeletePreRegistrationMapping is not implemented"))
}

func (UnimplementedOrgsServiceHandler) MarkMappingAsUsed(context.Context, *connect.Request[v1.MarkMappingAsUsedRequest]) (*connect.Response[v1.MarkMappingAsUsedResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.orgs.v1.OrgsService.MarkMappingAsUsed is not implemented"))
}
