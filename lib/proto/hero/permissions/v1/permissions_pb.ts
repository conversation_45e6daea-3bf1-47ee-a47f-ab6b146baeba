// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/permissions/v1/permissions.proto (package hero.permissions.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenExtension, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, extDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Empty, FieldOptions, MethodOptions } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_descriptor, file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Asset } from "../../assets/v2/assets_pb";
import { file_hero_assets_v2_assets } from "../../assets/v2/assets_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/permissions/v1/permissions.proto.
 */
export const file_hero_permissions_v1_permissions: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_descriptor, file_google_protobuf_empty, file_hero_assets_v2_assets]);

/**
 * Define a structured message for access control
 *
 * @generated from message hero.permissions.v1.AccessControl
 */
export type AccessControl = Message<"hero.permissions.v1.AccessControl"> & {
  /**
   * @generated from field: hero.permissions.v1.ProtectionLevel required_protection_level = 1;
   */
  requiredProtectionLevel: ProtectionLevel;
};

/**
 * Describes the message hero.permissions.v1.AccessControl.
 * Use `create(AccessControlSchema)` to create a new message.
 */
export const AccessControlSchema: GenMessage<AccessControl> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 0);

/**
 * Role definition
 *
 * @generated from message hero.permissions.v1.Role
 */
export type Role = Message<"hero.permissions.v1.Role"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: int32 org_id = 3;
   */
  orgId: number;

  /**
   * @generated from field: repeated hero.permissions.v1.PermissionCategory categories = 4;
   */
  categories: PermissionCategory[];

  /**
   * @generated from field: repeated hero.permissions.v1.PermissionSet permission_sets = 5;
   */
  permissionSets: PermissionSet[];

  /**
   * If true, this role is for internal/system use and should be hidden in standard UI lists
   *
   * @generated from field: bool is_internal = 6;
   */
  isInternal: boolean;
};

/**
 * Describes the message hero.permissions.v1.Role.
 * Use `create(RoleSchema)` to create a new message.
 */
export const RoleSchema: GenMessage<Role> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 1);

/**
 * @generated from message hero.permissions.v1.Category
 */
export type Category = Message<"hero.permissions.v1.Category"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: repeated string actions = 2;
   */
  actions: string[];
};

/**
 * Describes the message hero.permissions.v1.Category.
 * Use `create(CategorySchema)` to create a new message.
 */
export const CategorySchema: GenMessage<Category> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 2);

/**
 * @generated from message hero.permissions.v1.PermissionCategory
 */
export type PermissionCategory = Message<"hero.permissions.v1.PermissionCategory"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: bool can_do_all = 2;
   */
  canDoAll: boolean;

  /**
   * @generated from field: repeated hero.permissions.v1.Action actions = 3;
   */
  actions: Action[];
};

/**
 * Describes the message hero.permissions.v1.PermissionCategory.
 * Use `create(PermissionCategorySchema)` to create a new message.
 */
export const PermissionCategorySchema: GenMessage<PermissionCategory> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 3);

/**
 * @generated from message hero.permissions.v1.Action
 */
export type Action = Message<"hero.permissions.v1.Action"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: bool can_do_action = 2;
   */
  canDoAction: boolean;
};

/**
 * Describes the message hero.permissions.v1.Action.
 * Use `create(ActionSchema)` to create a new message.
 */
export const ActionSchema: GenMessage<Action> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 4);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOnRequest
 */
export type DispatchRoleOnRequest = Message<"hero.permissions.v1.DispatchRoleOnRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOnRequest.
 * Use `create(DispatchRoleOnRequestSchema)` to create a new message.
 */
export const DispatchRoleOnRequestSchema: GenMessage<DispatchRoleOnRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 5);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOnResponse
 */
export type DispatchRoleOnResponse = Message<"hero.permissions.v1.DispatchRoleOnResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOnResponse.
 * Use `create(DispatchRoleOnResponseSchema)` to create a new message.
 */
export const DispatchRoleOnResponseSchema: GenMessage<DispatchRoleOnResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 6);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOffRequest
 */
export type DispatchRoleOffRequest = Message<"hero.permissions.v1.DispatchRoleOffRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOffRequest.
 * Use `create(DispatchRoleOffRequestSchema)` to create a new message.
 */
export const DispatchRoleOffRequestSchema: GenMessage<DispatchRoleOffRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 7);

/**
 * @generated from message hero.permissions.v1.DispatchRoleOffResponse
 */
export type DispatchRoleOffResponse = Message<"hero.permissions.v1.DispatchRoleOffResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DispatchRoleOffResponse.
 * Use `create(DispatchRoleOffResponseSchema)` to create a new message.
 */
export const DispatchRoleOffResponseSchema: GenMessage<DispatchRoleOffResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 8);

/**
 * @generated from message hero.permissions.v1.ListSituationRolePermissionsRequest
 */
export type ListSituationRolePermissionsRequest = Message<"hero.permissions.v1.ListSituationRolePermissionsRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * @generated from field: bool exclude_internal = 4;
   */
  excludeInternal: boolean;
};

/**
 * Describes the message hero.permissions.v1.ListSituationRolePermissionsRequest.
 * Use `create(ListSituationRolePermissionsRequestSchema)` to create a new message.
 */
export const ListSituationRolePermissionsRequestSchema: GenMessage<ListSituationRolePermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 9);

/**
 * @generated from message hero.permissions.v1.ListSituationRolePermissionsResponse
 */
export type ListSituationRolePermissionsResponse = Message<"hero.permissions.v1.ListSituationRolePermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectViewer object_viewers = 1;
   */
  objectViewers: ObjectViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListSituationRolePermissionsResponse.
 * Use `create(ListSituationRolePermissionsResponseSchema)` to create a new message.
 */
export const ListSituationRolePermissionsResponseSchema: GenMessage<ListSituationRolePermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 10);

/**
 * @generated from message hero.permissions.v1.GetSituationRolePermissionRequest
 */
export type GetSituationRolePermissionRequest = Message<"hero.permissions.v1.GetSituationRolePermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetSituationRolePermissionRequest.
 * Use `create(GetSituationRolePermissionRequestSchema)` to create a new message.
 */
export const GetSituationRolePermissionRequestSchema: GenMessage<GetSituationRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 11);

/**
 * @generated from message hero.permissions.v1.GetSituationRolePermissionResponse
 */
export type GetSituationRolePermissionResponse = Message<"hero.permissions.v1.GetSituationRolePermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetSituationRolePermissionResponse.
 * Use `create(GetSituationRolePermissionResponseSchema)` to create a new message.
 */
export const GetSituationRolePermissionResponseSchema: GenMessage<GetSituationRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 12);

/**
 * @generated from message hero.permissions.v1.UpdateSituationRolePermissionRequest
 */
export type UpdateSituationRolePermissionRequest = Message<"hero.permissions.v1.UpdateSituationRolePermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateSituationRolePermissionRequest.
 * Use `create(UpdateSituationRolePermissionRequestSchema)` to create a new message.
 */
export const UpdateSituationRolePermissionRequestSchema: GenMessage<UpdateSituationRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 13);

/**
 * @generated from message hero.permissions.v1.UpdateSituationRolePermissionResponse
 */
export type UpdateSituationRolePermissionResponse = Message<"hero.permissions.v1.UpdateSituationRolePermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateSituationRolePermissionResponse.
 * Use `create(UpdateSituationRolePermissionResponseSchema)` to create a new message.
 */
export const UpdateSituationRolePermissionResponseSchema: GenMessage<UpdateSituationRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 14);

/**
 * @generated from message hero.permissions.v1.ListSituationAssetPermissionsRequest
 */
export type ListSituationAssetPermissionsRequest = Message<"hero.permissions.v1.ListSituationAssetPermissionsRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListSituationAssetPermissionsRequest.
 * Use `create(ListSituationAssetPermissionsRequestSchema)` to create a new message.
 */
export const ListSituationAssetPermissionsRequestSchema: GenMessage<ListSituationAssetPermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 15);

/**
 * @generated from message hero.permissions.v1.ListSituationAssetPermissionsResponse
 */
export type ListSituationAssetPermissionsResponse = Message<"hero.permissions.v1.ListSituationAssetPermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectUserViewer object_users = 1;
   */
  objectUsers: ObjectUserViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListSituationAssetPermissionsResponse.
 * Use `create(ListSituationAssetPermissionsResponseSchema)` to create a new message.
 */
export const ListSituationAssetPermissionsResponseSchema: GenMessage<ListSituationAssetPermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 16);

/**
 * @generated from message hero.permissions.v1.GetSituationAssetPermissionRequest
 */
export type GetSituationAssetPermissionRequest = Message<"hero.permissions.v1.GetSituationAssetPermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.GetSituationAssetPermissionRequest.
 * Use `create(GetSituationAssetPermissionRequestSchema)` to create a new message.
 */
export const GetSituationAssetPermissionRequestSchema: GenMessage<GetSituationAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 17);

/**
 * @generated from message hero.permissions.v1.GetSituationAssetPermissionResponse
 */
export type GetSituationAssetPermissionResponse = Message<"hero.permissions.v1.GetSituationAssetPermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetSituationAssetPermissionResponse.
 * Use `create(GetSituationAssetPermissionResponseSchema)` to create a new message.
 */
export const GetSituationAssetPermissionResponseSchema: GenMessage<GetSituationAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 18);

/**
 * @generated from message hero.permissions.v1.UpdateSituationAssetPermissionRequest
 */
export type UpdateSituationAssetPermissionRequest = Message<"hero.permissions.v1.UpdateSituationAssetPermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateSituationAssetPermissionRequest.
 * Use `create(UpdateSituationAssetPermissionRequestSchema)` to create a new message.
 */
export const UpdateSituationAssetPermissionRequestSchema: GenMessage<UpdateSituationAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 19);

/**
 * @generated from message hero.permissions.v1.UpdateSituationAssetPermissionResponse
 */
export type UpdateSituationAssetPermissionResponse = Message<"hero.permissions.v1.UpdateSituationAssetPermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateSituationAssetPermissionResponse.
 * Use `create(UpdateSituationAssetPermissionResponseSchema)` to create a new message.
 */
export const UpdateSituationAssetPermissionResponseSchema: GenMessage<UpdateSituationAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 20);

/**
 * *******************************************************
 * Cases
 * *******************************************************
 *
 * @generated from message hero.permissions.v1.ListCaseRolePermissionsRequest
 */
export type ListCaseRolePermissionsRequest = Message<"hero.permissions.v1.ListCaseRolePermissionsRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * @generated from field: bool exclude_internal = 4;
   */
  excludeInternal: boolean;
};

/**
 * Describes the message hero.permissions.v1.ListCaseRolePermissionsRequest.
 * Use `create(ListCaseRolePermissionsRequestSchema)` to create a new message.
 */
export const ListCaseRolePermissionsRequestSchema: GenMessage<ListCaseRolePermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 21);

/**
 * @generated from message hero.permissions.v1.ListCaseRolePermissionsResponse
 */
export type ListCaseRolePermissionsResponse = Message<"hero.permissions.v1.ListCaseRolePermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectViewer object_viewers = 1;
   */
  objectViewers: ObjectViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListCaseRolePermissionsResponse.
 * Use `create(ListCaseRolePermissionsResponseSchema)` to create a new message.
 */
export const ListCaseRolePermissionsResponseSchema: GenMessage<ListCaseRolePermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 22);

/**
 * @generated from message hero.permissions.v1.GetCaseRolePermissionRequest
 */
export type GetCaseRolePermissionRequest = Message<"hero.permissions.v1.GetCaseRolePermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetCaseRolePermissionRequest.
 * Use `create(GetCaseRolePermissionRequestSchema)` to create a new message.
 */
export const GetCaseRolePermissionRequestSchema: GenMessage<GetCaseRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 23);

/**
 * @generated from message hero.permissions.v1.GetCaseRolePermissionResponse
 */
export type GetCaseRolePermissionResponse = Message<"hero.permissions.v1.GetCaseRolePermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetCaseRolePermissionResponse.
 * Use `create(GetCaseRolePermissionResponseSchema)` to create a new message.
 */
export const GetCaseRolePermissionResponseSchema: GenMessage<GetCaseRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 24);

/**
 * @generated from message hero.permissions.v1.UpdateCaseRolePermissionRequest
 */
export type UpdateCaseRolePermissionRequest = Message<"hero.permissions.v1.UpdateCaseRolePermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateCaseRolePermissionRequest.
 * Use `create(UpdateCaseRolePermissionRequestSchema)` to create a new message.
 */
export const UpdateCaseRolePermissionRequestSchema: GenMessage<UpdateCaseRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 25);

/**
 * @generated from message hero.permissions.v1.UpdateCaseRolePermissionResponse
 */
export type UpdateCaseRolePermissionResponse = Message<"hero.permissions.v1.UpdateCaseRolePermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateCaseRolePermissionResponse.
 * Use `create(UpdateCaseRolePermissionResponseSchema)` to create a new message.
 */
export const UpdateCaseRolePermissionResponseSchema: GenMessage<UpdateCaseRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 26);

/**
 * @generated from message hero.permissions.v1.ListCaseAssetPermissionsRequest
 */
export type ListCaseAssetPermissionsRequest = Message<"hero.permissions.v1.ListCaseAssetPermissionsRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListCaseAssetPermissionsRequest.
 * Use `create(ListCaseAssetPermissionsRequestSchema)` to create a new message.
 */
export const ListCaseAssetPermissionsRequestSchema: GenMessage<ListCaseAssetPermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 27);

/**
 * @generated from message hero.permissions.v1.ListCaseAssetPermissionsResponse
 */
export type ListCaseAssetPermissionsResponse = Message<"hero.permissions.v1.ListCaseAssetPermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectUserViewer object_users = 1;
   */
  objectUsers: ObjectUserViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListCaseAssetPermissionsResponse.
 * Use `create(ListCaseAssetPermissionsResponseSchema)` to create a new message.
 */
export const ListCaseAssetPermissionsResponseSchema: GenMessage<ListCaseAssetPermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 28);

/**
 * @generated from message hero.permissions.v1.GetCaseAssetPermissionRequest
 */
export type GetCaseAssetPermissionRequest = Message<"hero.permissions.v1.GetCaseAssetPermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.GetCaseAssetPermissionRequest.
 * Use `create(GetCaseAssetPermissionRequestSchema)` to create a new message.
 */
export const GetCaseAssetPermissionRequestSchema: GenMessage<GetCaseAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 29);

/**
 * @generated from message hero.permissions.v1.GetCaseAssetPermissionResponse
 */
export type GetCaseAssetPermissionResponse = Message<"hero.permissions.v1.GetCaseAssetPermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetCaseAssetPermissionResponse.
 * Use `create(GetCaseAssetPermissionResponseSchema)` to create a new message.
 */
export const GetCaseAssetPermissionResponseSchema: GenMessage<GetCaseAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 30);

/**
 * @generated from message hero.permissions.v1.UpdateCaseAssetPermissionRequest
 */
export type UpdateCaseAssetPermissionRequest = Message<"hero.permissions.v1.UpdateCaseAssetPermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateCaseAssetPermissionRequest.
 * Use `create(UpdateCaseAssetPermissionRequestSchema)` to create a new message.
 */
export const UpdateCaseAssetPermissionRequestSchema: GenMessage<UpdateCaseAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 31);

/**
 * @generated from message hero.permissions.v1.UpdateCaseAssetPermissionResponse
 */
export type UpdateCaseAssetPermissionResponse = Message<"hero.permissions.v1.UpdateCaseAssetPermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateCaseAssetPermissionResponse.
 * Use `create(UpdateCaseAssetPermissionResponseSchema)` to create a new message.
 */
export const UpdateCaseAssetPermissionResponseSchema: GenMessage<UpdateCaseAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 32);

/**
 * @generated from message hero.permissions.v1.ListReportRolePermissionsRequest
 */
export type ListReportRolePermissionsRequest = Message<"hero.permissions.v1.ListReportRolePermissionsRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  /**
   * @generated from field: bool exclude_internal = 4;
   */
  excludeInternal: boolean;
};

/**
 * Describes the message hero.permissions.v1.ListReportRolePermissionsRequest.
 * Use `create(ListReportRolePermissionsRequestSchema)` to create a new message.
 */
export const ListReportRolePermissionsRequestSchema: GenMessage<ListReportRolePermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 33);

/**
 * @generated from message hero.permissions.v1.ListReportRolePermissionsResponse
 */
export type ListReportRolePermissionsResponse = Message<"hero.permissions.v1.ListReportRolePermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectViewer object_viewers = 1;
   */
  objectViewers: ObjectViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListReportRolePermissionsResponse.
 * Use `create(ListReportRolePermissionsResponseSchema)` to create a new message.
 */
export const ListReportRolePermissionsResponseSchema: GenMessage<ListReportRolePermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 34);

/**
 * @generated from message hero.permissions.v1.GetReportRolePermissionRequest
 */
export type GetReportRolePermissionRequest = Message<"hero.permissions.v1.GetReportRolePermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetReportRolePermissionRequest.
 * Use `create(GetReportRolePermissionRequestSchema)` to create a new message.
 */
export const GetReportRolePermissionRequestSchema: GenMessage<GetReportRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 35);

/**
 * @generated from message hero.permissions.v1.GetReportRolePermissionResponse
 */
export type GetReportRolePermissionResponse = Message<"hero.permissions.v1.GetReportRolePermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetReportRolePermissionResponse.
 * Use `create(GetReportRolePermissionResponseSchema)` to create a new message.
 */
export const GetReportRolePermissionResponseSchema: GenMessage<GetReportRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 36);

/**
 * @generated from message hero.permissions.v1.UpdateReportRolePermissionRequest
 */
export type UpdateReportRolePermissionRequest = Message<"hero.permissions.v1.UpdateReportRolePermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateReportRolePermissionRequest.
 * Use `create(UpdateReportRolePermissionRequestSchema)` to create a new message.
 */
export const UpdateReportRolePermissionRequestSchema: GenMessage<UpdateReportRolePermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 37);

/**
 * @generated from message hero.permissions.v1.UpdateReportRolePermissionResponse
 */
export type UpdateReportRolePermissionResponse = Message<"hero.permissions.v1.UpdateReportRolePermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateReportRolePermissionResponse.
 * Use `create(UpdateReportRolePermissionResponseSchema)` to create a new message.
 */
export const UpdateReportRolePermissionResponseSchema: GenMessage<UpdateReportRolePermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 38);

/**
 * @generated from message hero.permissions.v1.ObjectUserViewer
 */
export type ObjectUserViewer = Message<"hero.permissions.v1.ObjectUserViewer"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string user_name = 2;
   */
  userName: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;

  /**
   * @generated from field: string cognito_sub = 4;
   */
  cognitoSub: string;
};

/**
 * Describes the message hero.permissions.v1.ObjectUserViewer.
 * Use `create(ObjectUserViewerSchema)` to create a new message.
 */
export const ObjectUserViewerSchema: GenMessage<ObjectUserViewer> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 39);

/**
 * @generated from message hero.permissions.v1.ListReportAssetPermissionsRequest
 */
export type ListReportAssetPermissionsRequest = Message<"hero.permissions.v1.ListReportAssetPermissionsRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListReportAssetPermissionsRequest.
 * Use `create(ListReportAssetPermissionsRequestSchema)` to create a new message.
 */
export const ListReportAssetPermissionsRequestSchema: GenMessage<ListReportAssetPermissionsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 40);

/**
 * @generated from message hero.permissions.v1.ListReportAssetPermissionsResponse
 */
export type ListReportAssetPermissionsResponse = Message<"hero.permissions.v1.ListReportAssetPermissionsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.ObjectUserViewer object_users = 1;
   */
  objectUsers: ObjectUserViewer[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListReportAssetPermissionsResponse.
 * Use `create(ListReportAssetPermissionsResponseSchema)` to create a new message.
 */
export const ListReportAssetPermissionsResponseSchema: GenMessage<ListReportAssetPermissionsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 41);

/**
 * @generated from message hero.permissions.v1.GetReportAssetPermissionRequest
 */
export type GetReportAssetPermissionRequest = Message<"hero.permissions.v1.GetReportAssetPermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.GetReportAssetPermissionRequest.
 * Use `create(GetReportAssetPermissionRequestSchema)` to create a new message.
 */
export const GetReportAssetPermissionRequestSchema: GenMessage<GetReportAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 42);

/**
 * @generated from message hero.permissions.v1.GetReportAssetPermissionResponse
 */
export type GetReportAssetPermissionResponse = Message<"hero.permissions.v1.GetReportAssetPermissionResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 1;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.GetReportAssetPermissionResponse.
 * Use `create(GetReportAssetPermissionResponseSchema)` to create a new message.
 */
export const GetReportAssetPermissionResponseSchema: GenMessage<GetReportAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 43);

/**
 * @generated from message hero.permissions.v1.UpdateReportAssetPermissionRequest
 */
export type UpdateReportAssetPermissionRequest = Message<"hero.permissions.v1.UpdateReportAssetPermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.UpdateReportAssetPermissionRequest.
 * Use `create(UpdateReportAssetPermissionRequestSchema)` to create a new message.
 */
export const UpdateReportAssetPermissionRequestSchema: GenMessage<UpdateReportAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 44);

/**
 * @generated from message hero.permissions.v1.UpdateReportAssetPermissionResponse
 */
export type UpdateReportAssetPermissionResponse = Message<"hero.permissions.v1.UpdateReportAssetPermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.UpdateReportAssetPermissionResponse.
 * Use `create(UpdateReportAssetPermissionResponseSchema)` to create a new message.
 */
export const UpdateReportAssetPermissionResponseSchema: GenMessage<UpdateReportAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 45);

/**
 * @generated from message hero.permissions.v1.DeleteReportAssetPermissionRequest
 */
export type DeleteReportAssetPermissionRequest = Message<"hero.permissions.v1.DeleteReportAssetPermissionRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteReportAssetPermissionRequest.
 * Use `create(DeleteReportAssetPermissionRequestSchema)` to create a new message.
 */
export const DeleteReportAssetPermissionRequestSchema: GenMessage<DeleteReportAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 46);

/**
 * @generated from message hero.permissions.v1.DeleteReportAssetPermissionResponse
 */
export type DeleteReportAssetPermissionResponse = Message<"hero.permissions.v1.DeleteReportAssetPermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteReportAssetPermissionResponse.
 * Use `create(DeleteReportAssetPermissionResponseSchema)` to create a new message.
 */
export const DeleteReportAssetPermissionResponseSchema: GenMessage<DeleteReportAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 47);

/**
 * @generated from message hero.permissions.v1.DeleteSituationAssetPermissionRequest
 */
export type DeleteSituationAssetPermissionRequest = Message<"hero.permissions.v1.DeleteSituationAssetPermissionRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteSituationAssetPermissionRequest.
 * Use `create(DeleteSituationAssetPermissionRequestSchema)` to create a new message.
 */
export const DeleteSituationAssetPermissionRequestSchema: GenMessage<DeleteSituationAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 48);

/**
 * @generated from message hero.permissions.v1.DeleteSituationAssetPermissionResponse
 */
export type DeleteSituationAssetPermissionResponse = Message<"hero.permissions.v1.DeleteSituationAssetPermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteSituationAssetPermissionResponse.
 * Use `create(DeleteSituationAssetPermissionResponseSchema)` to create a new message.
 */
export const DeleteSituationAssetPermissionResponseSchema: GenMessage<DeleteSituationAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 49);

/**
 * @generated from message hero.permissions.v1.DeleteCaseAssetPermissionRequest
 */
export type DeleteCaseAssetPermissionRequest = Message<"hero.permissions.v1.DeleteCaseAssetPermissionRequest"> & {
  /**
   * @generated from field: string case_id = 1;
   */
  caseId: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteCaseAssetPermissionRequest.
 * Use `create(DeleteCaseAssetPermissionRequestSchema)` to create a new message.
 */
export const DeleteCaseAssetPermissionRequestSchema: GenMessage<DeleteCaseAssetPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 50);

/**
 * @generated from message hero.permissions.v1.DeleteCaseAssetPermissionResponse
 */
export type DeleteCaseAssetPermissionResponse = Message<"hero.permissions.v1.DeleteCaseAssetPermissionResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteCaseAssetPermissionResponse.
 * Use `create(DeleteCaseAssetPermissionResponseSchema)` to create a new message.
 */
export const DeleteCaseAssetPermissionResponseSchema: GenMessage<DeleteCaseAssetPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 51);

/**
 * @generated from message hero.permissions.v1.ObjectViewer
 */
export type ObjectViewer = Message<"hero.permissions.v1.ObjectViewer"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 2;
   */
  roleName: string;

  /**
   * @generated from field: hero.permissions.v1.ObjectPermission permission = 3;
   */
  permission: ObjectPermission;
};

/**
 * Describes the message hero.permissions.v1.ObjectViewer.
 * Use `create(ObjectViewerSchema)` to create a new message.
 */
export const ObjectViewerSchema: GenMessage<ObjectViewer> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 52);

/**
 * @generated from message hero.permissions.v1.PermissionSet
 */
export type PermissionSet = Message<"hero.permissions.v1.PermissionSet"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: int32 org_id = 3;
   */
  orgId: number;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: repeated hero.permissions.v1.PermissionCategory categories = 5;
   */
  categories: PermissionCategory[];
};

/**
 * Describes the message hero.permissions.v1.PermissionSet.
 * Use `create(PermissionSetSchema)` to create a new message.
 */
export const PermissionSetSchema: GenMessage<PermissionSet> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 53);

/**
 * @generated from message hero.permissions.v1.CreatePermissionSetRequest
 */
export type CreatePermissionSetRequest = Message<"hero.permissions.v1.CreatePermissionSetRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.CreatePermissionSetRequest.
 * Use `create(CreatePermissionSetRequestSchema)` to create a new message.
 */
export const CreatePermissionSetRequestSchema: GenMessage<CreatePermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 54);

/**
 * @generated from message hero.permissions.v1.CreatePermissionSetResponse
 */
export type CreatePermissionSetResponse = Message<"hero.permissions.v1.CreatePermissionSetResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.CreatePermissionSetResponse.
 * Use `create(CreatePermissionSetResponseSchema)` to create a new message.
 */
export const CreatePermissionSetResponseSchema: GenMessage<CreatePermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 55);

/**
 * @generated from message hero.permissions.v1.GetPermissionSetRequest
 */
export type GetPermissionSetRequest = Message<"hero.permissions.v1.GetPermissionSetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.permissions.v1.GetPermissionSetRequest.
 * Use `create(GetPermissionSetRequestSchema)` to create a new message.
 */
export const GetPermissionSetRequestSchema: GenMessage<GetPermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 56);

/**
 * @generated from message hero.permissions.v1.GetPermissionSetResponse
 */
export type GetPermissionSetResponse = Message<"hero.permissions.v1.GetPermissionSetResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.GetPermissionSetResponse.
 * Use `create(GetPermissionSetResponseSchema)` to create a new message.
 */
export const GetPermissionSetResponseSchema: GenMessage<GetPermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 57);

/**
 * @generated from message hero.permissions.v1.ListPermissionSetsRequest
 */
export type ListPermissionSetsRequest = Message<"hero.permissions.v1.ListPermissionSetsRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListPermissionSetsRequest.
 * Use `create(ListPermissionSetsRequestSchema)` to create a new message.
 */
export const ListPermissionSetsRequestSchema: GenMessage<ListPermissionSetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 58);

/**
 * @generated from message hero.permissions.v1.ListPermissionSetsResponse
 */
export type ListPermissionSetsResponse = Message<"hero.permissions.v1.ListPermissionSetsResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.PermissionSet permission_sets = 1;
   */
  permissionSets: PermissionSet[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListPermissionSetsResponse.
 * Use `create(ListPermissionSetsResponseSchema)` to create a new message.
 */
export const ListPermissionSetsResponseSchema: GenMessage<ListPermissionSetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 59);

/**
 * @generated from message hero.permissions.v1.UpdatePermissionSetRequest
 */
export type UpdatePermissionSetRequest = Message<"hero.permissions.v1.UpdatePermissionSetRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.UpdatePermissionSetRequest.
 * Use `create(UpdatePermissionSetRequestSchema)` to create a new message.
 */
export const UpdatePermissionSetRequestSchema: GenMessage<UpdatePermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 60);

/**
 * @generated from message hero.permissions.v1.UpdatePermissionSetResponse
 */
export type UpdatePermissionSetResponse = Message<"hero.permissions.v1.UpdatePermissionSetResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.PermissionSet permission_set = 1;
   */
  permissionSet?: PermissionSet;
};

/**
 * Describes the message hero.permissions.v1.UpdatePermissionSetResponse.
 * Use `create(UpdatePermissionSetResponseSchema)` to create a new message.
 */
export const UpdatePermissionSetResponseSchema: GenMessage<UpdatePermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 61);

/**
 * @generated from message hero.permissions.v1.DeletePermissionSetRequest
 */
export type DeletePermissionSetRequest = Message<"hero.permissions.v1.DeletePermissionSetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.permissions.v1.DeletePermissionSetRequest.
 * Use `create(DeletePermissionSetRequestSchema)` to create a new message.
 */
export const DeletePermissionSetRequestSchema: GenMessage<DeletePermissionSetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 62);

/**
 * @generated from message hero.permissions.v1.DeletePermissionSetResponse
 */
export type DeletePermissionSetResponse = Message<"hero.permissions.v1.DeletePermissionSetResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeletePermissionSetResponse.
 * Use `create(DeletePermissionSetResponseSchema)` to create a new message.
 */
export const DeletePermissionSetResponseSchema: GenMessage<DeletePermissionSetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 63);

/**
 * @generated from message hero.permissions.v1.ListActionsByCategoryRequest
 */
export type ListActionsByCategoryRequest = Message<"hero.permissions.v1.ListActionsByCategoryRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListActionsByCategoryRequest.
 * Use `create(ListActionsByCategoryRequestSchema)` to create a new message.
 */
export const ListActionsByCategoryRequestSchema: GenMessage<ListActionsByCategoryRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 64);

/**
 * @generated from message hero.permissions.v1.ListActionsByCategoryResponse
 */
export type ListActionsByCategoryResponse = Message<"hero.permissions.v1.ListActionsByCategoryResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.Category categories = 1;
   */
  categories: Category[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListActionsByCategoryResponse.
 * Use `create(ListActionsByCategoryResponseSchema)` to create a new message.
 */
export const ListActionsByCategoryResponseSchema: GenMessage<ListActionsByCategoryResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 65);

/**
 * @generated from message hero.permissions.v1.ListRoleAssetsRequest
 */
export type ListRoleAssetsRequest = Message<"hero.permissions.v1.ListRoleAssetsRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRoleAssetsRequest.
 * Use `create(ListRoleAssetsRequestSchema)` to create a new message.
 */
export const ListRoleAssetsRequestSchema: GenMessage<ListRoleAssetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 66);

/**
 * @generated from message hero.permissions.v1.ListRoleAssetsResponse
 */
export type ListRoleAssetsResponse = Message<"hero.permissions.v1.ListRoleAssetsResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v2.Asset assets = 1;
   */
  assets: Asset[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRoleAssetsResponse.
 * Use `create(ListRoleAssetsResponseSchema)` to create a new message.
 */
export const ListRoleAssetsResponseSchema: GenMessage<ListRoleAssetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 67);

/**
 * @generated from message hero.permissions.v1.GetAssetRolesRequest
 */
export type GetAssetRolesRequest = Message<"hero.permissions.v1.GetAssetRolesRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;
};

/**
 * Describes the message hero.permissions.v1.GetAssetRolesRequest.
 * Use `create(GetAssetRolesRequestSchema)` to create a new message.
 */
export const GetAssetRolesRequestSchema: GenMessage<GetAssetRolesRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 68);

/**
 * @generated from message hero.permissions.v1.GetAssetRolesResponse
 */
export type GetAssetRolesResponse = Message<"hero.permissions.v1.GetAssetRolesResponse"> & {
  /**
   * @generated from field: repeated string roles = 1;
   */
  roles: string[];
};

/**
 * Describes the message hero.permissions.v1.GetAssetRolesResponse.
 * Use `create(GetAssetRolesResponseSchema)` to create a new message.
 */
export const GetAssetRolesResponseSchema: GenMessage<GetAssetRolesResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 69);

/**
 * @generated from message hero.permissions.v1.ListRolesRequest
 */
export type ListRolesRequest = Message<"hero.permissions.v1.ListRolesRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * When true, exclude roles marked as internal from the results
   *
   * @generated from field: bool exclude_internal = 3;
   */
  excludeInternal: boolean;
};

/**
 * Describes the message hero.permissions.v1.ListRolesRequest.
 * Use `create(ListRolesRequestSchema)` to create a new message.
 */
export const ListRolesRequestSchema: GenMessage<ListRolesRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 70);

/**
 * @generated from message hero.permissions.v1.ListRolesResponse
 */
export type ListRolesResponse = Message<"hero.permissions.v1.ListRolesResponse"> & {
  /**
   * @generated from field: repeated hero.permissions.v1.Role roles = 1;
   */
  roles: Role[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.permissions.v1.ListRolesResponse.
 * Use `create(ListRolesResponseSchema)` to create a new message.
 */
export const ListRolesResponseSchema: GenMessage<ListRolesResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 71);

/**
 * @generated from message hero.permissions.v1.CreateRoleRequest
 */
export type CreateRoleRequest = Message<"hero.permissions.v1.CreateRoleRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.CreateRoleRequest.
 * Use `create(CreateRoleRequestSchema)` to create a new message.
 */
export const CreateRoleRequestSchema: GenMessage<CreateRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 72);

/**
 * @generated from message hero.permissions.v1.CreateRoleResponse
 */
export type CreateRoleResponse = Message<"hero.permissions.v1.CreateRoleResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.CreateRoleResponse.
 * Use `create(CreateRoleResponseSchema)` to create a new message.
 */
export const CreateRoleResponseSchema: GenMessage<CreateRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 73);

/**
 * @generated from message hero.permissions.v1.GetRoleRequest
 */
export type GetRoleRequest = Message<"hero.permissions.v1.GetRoleRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.GetRoleRequest.
 * Use `create(GetRoleRequestSchema)` to create a new message.
 */
export const GetRoleRequestSchema: GenMessage<GetRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 74);

/**
 * @generated from message hero.permissions.v1.GetRoleResponse
 */
export type GetRoleResponse = Message<"hero.permissions.v1.GetRoleResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.GetRoleResponse.
 * Use `create(GetRoleResponseSchema)` to create a new message.
 */
export const GetRoleResponseSchema: GenMessage<GetRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 75);

/**
 * @generated from message hero.permissions.v1.UpdateRoleRequest
 */
export type UpdateRoleRequest = Message<"hero.permissions.v1.UpdateRoleRequest"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.UpdateRoleRequest.
 * Use `create(UpdateRoleRequestSchema)` to create a new message.
 */
export const UpdateRoleRequestSchema: GenMessage<UpdateRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 76);

/**
 * @generated from message hero.permissions.v1.UpdateRoleResponse
 */
export type UpdateRoleResponse = Message<"hero.permissions.v1.UpdateRoleResponse"> & {
  /**
   * @generated from field: hero.permissions.v1.Role role = 1;
   */
  role?: Role;
};

/**
 * Describes the message hero.permissions.v1.UpdateRoleResponse.
 * Use `create(UpdateRoleResponseSchema)` to create a new message.
 */
export const UpdateRoleResponseSchema: GenMessage<UpdateRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 77);

/**
 * @generated from message hero.permissions.v1.DeleteRoleRequest
 */
export type DeleteRoleRequest = Message<"hero.permissions.v1.DeleteRoleRequest"> & {
  /**
   * @generated from field: string role_id = 1;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteRoleRequest.
 * Use `create(DeleteRoleRequestSchema)` to create a new message.
 */
export const DeleteRoleRequestSchema: GenMessage<DeleteRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 78);

/**
 * @generated from message hero.permissions.v1.DeleteRoleResponse
 */
export type DeleteRoleResponse = Message<"hero.permissions.v1.DeleteRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteRoleResponse.
 * Use `create(DeleteRoleResponseSchema)` to create a new message.
 */
export const DeleteRoleResponseSchema: GenMessage<DeleteRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 79);

/**
 * @generated from message hero.permissions.v1.GetUserRolesRequest
 */
export type GetUserRolesRequest = Message<"hero.permissions.v1.GetUserRolesRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;
};

/**
 * Describes the message hero.permissions.v1.GetUserRolesRequest.
 * Use `create(GetUserRolesRequestSchema)` to create a new message.
 */
export const GetUserRolesRequestSchema: GenMessage<GetUserRolesRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 80);

/**
 * @generated from message hero.permissions.v1.GetUserRolesResponse
 */
export type GetUserRolesResponse = Message<"hero.permissions.v1.GetUserRolesResponse"> & {
  /**
   * @generated from field: repeated string roles = 1;
   */
  roles: string[];
};

/**
 * Describes the message hero.permissions.v1.GetUserRolesResponse.
 * Use `create(GetUserRolesResponseSchema)` to create a new message.
 */
export const GetUserRolesResponseSchema: GenMessage<GetUserRolesResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 81);

/**
 * @generated from message hero.permissions.v1.AddAssetToRoleRequest
 */
export type AddAssetToRoleRequest = Message<"hero.permissions.v1.AddAssetToRoleRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;

  /**
   * @generated from field: int32 org_id_override = 4;
   */
  orgIdOverride: number;
};

/**
 * Describes the message hero.permissions.v1.AddAssetToRoleRequest.
 * Use `create(AddAssetToRoleRequestSchema)` to create a new message.
 */
export const AddAssetToRoleRequestSchema: GenMessage<AddAssetToRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 82);

/**
 * @generated from message hero.permissions.v1.AddAssetToRoleResponse
 */
export type AddAssetToRoleResponse = Message<"hero.permissions.v1.AddAssetToRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.AddAssetToRoleResponse.
 * Use `create(AddAssetToRoleResponseSchema)` to create a new message.
 */
export const AddAssetToRoleResponseSchema: GenMessage<AddAssetToRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 83);

/**
 * @generated from message hero.permissions.v1.AddCognitoUserToRoleRequest
 */
export type AddCognitoUserToRoleRequest = Message<"hero.permissions.v1.AddCognitoUserToRoleRequest"> & {
  /**
   * @generated from field: string cognito_sub_id = 1;
   */
  cognitoSubId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;

  /**
   * @generated from field: int32 org_id_override = 4;
   */
  orgIdOverride: number;
};

/**
 * Describes the message hero.permissions.v1.AddCognitoUserToRoleRequest.
 * Use `create(AddCognitoUserToRoleRequestSchema)` to create a new message.
 */
export const AddCognitoUserToRoleRequestSchema: GenMessage<AddCognitoUserToRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 84);

/**
 * @generated from message hero.permissions.v1.AddCognitoUserToRoleResponse
 */
export type AddCognitoUserToRoleResponse = Message<"hero.permissions.v1.AddCognitoUserToRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.AddCognitoUserToRoleResponse.
 * Use `create(AddCognitoUserToRoleResponseSchema)` to create a new message.
 */
export const AddCognitoUserToRoleResponseSchema: GenMessage<AddCognitoUserToRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 85);

/**
 * @generated from message hero.permissions.v1.AddUserToRoleRequest
 */
export type AddUserToRoleRequest = Message<"hero.permissions.v1.AddUserToRoleRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;

  /**
   * @generated from field: int32 org_id_override = 4;
   */
  orgIdOverride: number;
};

/**
 * Describes the message hero.permissions.v1.AddUserToRoleRequest.
 * Use `create(AddUserToRoleRequestSchema)` to create a new message.
 */
export const AddUserToRoleRequestSchema: GenMessage<AddUserToRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 86);

/**
 * @generated from message hero.permissions.v1.RemoveUserFromRoleRequest
 */
export type RemoveUserFromRoleRequest = Message<"hero.permissions.v1.RemoveUserFromRoleRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;

  /**
   * @generated from field: string role_name = 3;
   */
  roleName: string;
};

/**
 * Describes the message hero.permissions.v1.RemoveUserFromRoleRequest.
 * Use `create(RemoveUserFromRoleRequestSchema)` to create a new message.
 */
export const RemoveUserFromRoleRequestSchema: GenMessage<RemoveUserFromRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 87);

/**
 * @generated from message hero.permissions.v1.AddUserToRoleResponse
 */
export type AddUserToRoleResponse = Message<"hero.permissions.v1.AddUserToRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.AddUserToRoleResponse.
 * Use `create(AddUserToRoleResponseSchema)` to create a new message.
 */
export const AddUserToRoleResponseSchema: GenMessage<AddUserToRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 88);

/**
 * @generated from message hero.permissions.v1.RemoveUserFromRoleResponse
 */
export type RemoveUserFromRoleResponse = Message<"hero.permissions.v1.RemoveUserFromRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.RemoveUserFromRoleResponse.
 * Use `create(RemoveUserFromRoleResponseSchema)` to create a new message.
 */
export const RemoveUserFromRoleResponseSchema: GenMessage<RemoveUserFromRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 89);

/**
 * @generated from message hero.permissions.v1.RemoveAssetFromRoleRequest
 */
export type RemoveAssetFromRoleRequest = Message<"hero.permissions.v1.RemoveAssetFromRoleRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * @generated from field: string role_id = 2;
   */
  roleId: string;
};

/**
 * Describes the message hero.permissions.v1.RemoveAssetFromRoleRequest.
 * Use `create(RemoveAssetFromRoleRequestSchema)` to create a new message.
 */
export const RemoveAssetFromRoleRequestSchema: GenMessage<RemoveAssetFromRoleRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 90);

/**
 * @generated from message hero.permissions.v1.RemoveAssetFromRoleResponse
 */
export type RemoveAssetFromRoleResponse = Message<"hero.permissions.v1.RemoveAssetFromRoleResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.RemoveAssetFromRoleResponse.
 * Use `create(RemoveAssetFromRoleResponseSchema)` to create a new message.
 */
export const RemoveAssetFromRoleResponseSchema: GenMessage<RemoveAssetFromRoleResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 91);

/**
 * @generated from message hero.permissions.v1.DeleteAssetRequest
 */
export type DeleteAssetRequest = Message<"hero.permissions.v1.DeleteAssetRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.permissions.v1.DeleteAssetRequest.
 * Use `create(DeleteAssetRequestSchema)` to create a new message.
 */
export const DeleteAssetRequestSchema: GenMessage<DeleteAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 92);

/**
 * @generated from message hero.permissions.v1.DeleteAssetResponse
 */
export type DeleteAssetResponse = Message<"hero.permissions.v1.DeleteAssetResponse"> & {
  /**
   * @generated from field: google.protobuf.Empty response = 1;
   */
  response?: Empty;
};

/**
 * Describes the message hero.permissions.v1.DeleteAssetResponse.
 * Use `create(DeleteAssetResponseSchema)` to create a new message.
 */
export const DeleteAssetResponseSchema: GenMessage<DeleteAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 93);

/**
 * @generated from message hero.permissions.v1.CheckPermissionRequest
 */
export type CheckPermissionRequest = Message<"hero.permissions.v1.CheckPermissionRequest"> & {
  /**
   * @generated from field: string category = 1;
   */
  category: string;

  /**
   * @generated from field: string action = 2;
   */
  action: string;

  /**
   * @generated from field: string object_id = 3;
   */
  objectId: string;
};

/**
 * Describes the message hero.permissions.v1.CheckPermissionRequest.
 * Use `create(CheckPermissionRequestSchema)` to create a new message.
 */
export const CheckPermissionRequestSchema: GenMessage<CheckPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 94);

/**
 * @generated from message hero.permissions.v1.CheckPermissionResponse
 */
export type CheckPermissionResponse = Message<"hero.permissions.v1.CheckPermissionResponse"> & {
  /**
   * @generated from field: bool allowed = 1;
   */
  allowed: boolean;
};

/**
 * Describes the message hero.permissions.v1.CheckPermissionResponse.
 * Use `create(CheckPermissionResponseSchema)` to create a new message.
 */
export const CheckPermissionResponseSchema: GenMessage<CheckPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 95);

/**
 * @generated from message hero.permissions.v1.BatchCheckPermissionRequest
 */
export type BatchCheckPermissionRequest = Message<"hero.permissions.v1.BatchCheckPermissionRequest"> & {
  /**
   * @generated from field: string category = 1;
   */
  category: string;

  /**
   * @generated from field: string action = 2;
   */
  action: string;

  /**
   * @generated from field: repeated string object_ids = 3;
   */
  objectIds: string[];
};

/**
 * Describes the message hero.permissions.v1.BatchCheckPermissionRequest.
 * Use `create(BatchCheckPermissionRequestSchema)` to create a new message.
 */
export const BatchCheckPermissionRequestSchema: GenMessage<BatchCheckPermissionRequest> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 96);

/**
 * @generated from message hero.permissions.v1.BatchCheckPermissionResponse
 */
export type BatchCheckPermissionResponse = Message<"hero.permissions.v1.BatchCheckPermissionResponse"> & {
  /**
   * @generated from field: map<string, bool> results = 1;
   */
  results: { [key: string]: boolean };
};

/**
 * Describes the message hero.permissions.v1.BatchCheckPermissionResponse.
 * Use `create(BatchCheckPermissionResponseSchema)` to create a new message.
 */
export const BatchCheckPermissionResponseSchema: GenMessage<BatchCheckPermissionResponse> = /*@__PURE__*/
  messageDesc(file_hero_permissions_v1_permissions, 97);

/**
 * Define an enum for roles
 *
 * @generated from enum hero.permissions.v1.ProtectionLevel
 */
export enum ProtectionLevel {
  /**
   * @generated from enum value: PROTECTION_LEVEL_UNSPECIFIED = 0;
   */
  PROTECTION_LEVEL_UNSPECIFIED = 0,

  /**
   * Open to all authenticated users. Currently only used for meta permission endpoints (e.g. CheckPermission)
   *
   * @generated from enum value: OPEN = 1;
   */
  OPEN = 1,

  /**
   * Only authenticated users with the necessary permissions can access (e.g. PermissionService decides)
   *
   * @generated from enum value: PERMISSIONED = 2;
   */
  PERMISSIONED = 2,
}

/**
 * Describes the enum hero.permissions.v1.ProtectionLevel.
 */
export const ProtectionLevelSchema: GenEnum<ProtectionLevel> = /*@__PURE__*/
  enumDesc(file_hero_permissions_v1_permissions, 0);

/**
 * @generated from enum hero.permissions.v1.ObjectPermission
 */
export enum ObjectPermission {
  /**
   * @generated from enum value: OBJECT_PERMISSION_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_MANAGE = 1;
   */
  CAN_MANAGE = 1,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_EDIT = 2;
   */
  CAN_EDIT = 2,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_VIEW = 3;
   */
  CAN_VIEW = 3,

  /**
   * @generated from enum value: OBJECT_PERMISSION_CAN_FIND = 4;
   */
  CAN_FIND = 4,

  /**
   * @generated from enum value: OBJECT_PERMISSION_BLOCKED = 5;
   */
  BLOCKED = 5,
}

/**
 * Describes the enum hero.permissions.v1.ObjectPermission.
 */
export const ObjectPermissionSchema: GenEnum<ObjectPermission> = /*@__PURE__*/
  enumDesc(file_hero_permissions_v1_permissions, 1);

/**
 * @generated from enum hero.permissions.v1.ObjectType
 */
export enum ObjectType {
  /**
   * @generated from enum value: OBJECT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: OBJECT_TYPE_REPORT = 1;
   */
  REPORT = 1,

  /**
   * @generated from enum value: OBJECT_TYPE_SITUATION = 2;
   */
  SITUATION = 2,

  /**
   * @generated from enum value: OBJECT_TYPE_CASE = 3;
   */
  CASE = 3,

  /**
   * @generated from enum value: OBJECT_TYPE_FILE = 4;
   */
  FILE = 4,
}

/**
 * Describes the enum hero.permissions.v1.ObjectType.
 */
export const ObjectTypeSchema: GenEnum<ObjectType> = /*@__PURE__*/
  enumDesc(file_hero_permissions_v1_permissions, 2);

/**
 * === Service for managing user roles and checking permissions ===
 *
 * @generated from service hero.permissions.v1.PermissionService
 */
export const PermissionService: GenService<{
  /**
   * # Permission Checking
   *
   * @generated from rpc hero.permissions.v1.PermissionService.CheckPermission
   */
  checkPermission: {
    methodKind: "unary";
    input: typeof CheckPermissionRequestSchema;
    output: typeof CheckPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.BatchCheckPermission
   */
  batchCheckPermission: {
    methodKind: "unary";
    input: typeof BatchCheckPermissionRequestSchema;
    output: typeof BatchCheckPermissionResponseSchema;
  },
  /**
   * List out available actions, organized by category
   * Used for role creation and permission set creation
   *
   * @generated from rpc hero.permissions.v1.PermissionService.ListActionsByCategory
   */
  listActionsByCategory: {
    methodKind: "unary";
    input: typeof ListActionsByCategoryRequestSchema;
    output: typeof ListActionsByCategoryResponseSchema;
  },
  /**
   * # Role Management
   *
   * @generated from rpc hero.permissions.v1.PermissionService.CreateRole
   */
  createRole: {
    methodKind: "unary";
    input: typeof CreateRoleRequestSchema;
    output: typeof CreateRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetRole
   */
  getRole: {
    methodKind: "unary";
    input: typeof GetRoleRequestSchema;
    output: typeof GetRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListRoles
   */
  listRoles: {
    methodKind: "unary";
    input: typeof ListRolesRequestSchema;
    output: typeof ListRolesResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateRole
   */
  updateRole: {
    methodKind: "unary";
    input: typeof UpdateRoleRequestSchema;
    output: typeof UpdateRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteRole
   */
  deleteRole: {
    methodKind: "unary";
    input: typeof DeleteRoleRequestSchema;
    output: typeof DeleteRoleResponseSchema;
  },
  /**
   * # Permission Set Management
   *
   * @generated from rpc hero.permissions.v1.PermissionService.CreatePermissionSet
   */
  createPermissionSet: {
    methodKind: "unary";
    input: typeof CreatePermissionSetRequestSchema;
    output: typeof CreatePermissionSetResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetPermissionSet
   */
  getPermissionSet: {
    methodKind: "unary";
    input: typeof GetPermissionSetRequestSchema;
    output: typeof GetPermissionSetResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListPermissionSets
   */
  listPermissionSets: {
    methodKind: "unary";
    input: typeof ListPermissionSetsRequestSchema;
    output: typeof ListPermissionSetsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdatePermissionSet
   */
  updatePermissionSet: {
    methodKind: "unary";
    input: typeof UpdatePermissionSetRequestSchema;
    output: typeof UpdatePermissionSetResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeletePermissionSet
   */
  deletePermissionSet: {
    methodKind: "unary";
    input: typeof DeletePermissionSetRequestSchema;
    output: typeof DeletePermissionSetResponseSchema;
  },
  /**
   * # User Role Management
   * Get all assets for a role
   *
   * @generated from rpc hero.permissions.v1.PermissionService.ListRoleAssets
   */
  listRoleAssets: {
    methodKind: "unary";
    input: typeof ListRoleAssetsRequestSchema;
    output: typeof ListRoleAssetsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetAssetRoles
   */
  getAssetRoles: {
    methodKind: "unary";
    input: typeof GetAssetRolesRequestSchema;
    output: typeof GetAssetRolesResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.AddAssetToRole
   */
  addAssetToRole: {
    methodKind: "unary";
    input: typeof AddAssetToRoleRequestSchema;
    output: typeof AddAssetToRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.RemoveAssetFromRole
   */
  removeAssetFromRole: {
    methodKind: "unary";
    input: typeof RemoveAssetFromRoleRequestSchema;
    output: typeof RemoveAssetFromRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteAsset
   */
  deleteAsset: {
    methodKind: "unary";
    input: typeof DeleteAssetRequestSchema;
    output: typeof DeleteAssetResponseSchema;
  },
  /**
   * // slightly lower level than AddAssetToRole, currently used for twilio webhook and other bots
   *
   * @generated from rpc hero.permissions.v1.PermissionService.AddUserToRole
   */
  addUserToRole: {
    methodKind: "unary";
    input: typeof AddUserToRoleRequestSchema;
    output: typeof AddUserToRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.RemoveUserFromRole
   */
  removeUserFromRole: {
    methodKind: "unary";
    input: typeof RemoveUserFromRoleRequestSchema;
    output: typeof RemoveUserFromRoleResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.GetUserRoles
   */
  getUserRoles: {
    methodKind: "unary";
    input: typeof GetUserRolesRequestSchema;
    output: typeof GetUserRolesResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.AddCognitoUserToRole
   */
  addCognitoUserToRole: {
    methodKind: "unary";
    input: typeof AddCognitoUserToRoleRequestSchema;
    output: typeof AddCognitoUserToRoleResponseSchema;
  },
  /**
   * # Object Override Management
   * REPORTS
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetReportRolePermission
   */
  getReportRolePermission: {
    methodKind: "unary";
    input: typeof GetReportRolePermissionRequestSchema;
    output: typeof GetReportRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateReportRolePermission
   */
  updateReportRolePermission: {
    methodKind: "unary";
    input: typeof UpdateReportRolePermissionRequestSchema;
    output: typeof UpdateReportRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListReportRolePermissions
   */
  listReportRolePermissions: {
    methodKind: "unary";
    input: typeof ListReportRolePermissionsRequestSchema;
    output: typeof ListReportRolePermissionsResponseSchema;
  },
  /**
   * SITUATIONS
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetSituationRolePermission
   */
  getSituationRolePermission: {
    methodKind: "unary";
    input: typeof GetSituationRolePermissionRequestSchema;
    output: typeof GetSituationRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateSituationRolePermission
   */
  updateSituationRolePermission: {
    methodKind: "unary";
    input: typeof UpdateSituationRolePermissionRequestSchema;
    output: typeof UpdateSituationRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListSituationRolePermissions
   */
  listSituationRolePermissions: {
    methodKind: "unary";
    input: typeof ListSituationRolePermissionsRequestSchema;
    output: typeof ListSituationRolePermissionsResponseSchema;
  },
  /**
   * CASES
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetCaseRolePermission
   */
  getCaseRolePermission: {
    methodKind: "unary";
    input: typeof GetCaseRolePermissionRequestSchema;
    output: typeof GetCaseRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateCaseRolePermission
   */
  updateCaseRolePermission: {
    methodKind: "unary";
    input: typeof UpdateCaseRolePermissionRequestSchema;
    output: typeof UpdateCaseRolePermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListCaseRolePermissions
   */
  listCaseRolePermissions: {
    methodKind: "unary";
    input: typeof ListCaseRolePermissionsRequestSchema;
    output: typeof ListCaseRolePermissionsResponseSchema;
  },
  /**
   * # User Object Override Management
   * REPORTS
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetReportAssetPermission
   */
  getReportAssetPermission: {
    methodKind: "unary";
    input: typeof GetReportAssetPermissionRequestSchema;
    output: typeof GetReportAssetPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateReportAssetPermission
   */
  updateReportAssetPermission: {
    methodKind: "unary";
    input: typeof UpdateReportAssetPermissionRequestSchema;
    output: typeof UpdateReportAssetPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListReportAssetPermissions
   */
  listReportAssetPermissions: {
    methodKind: "unary";
    input: typeof ListReportAssetPermissionsRequestSchema;
    output: typeof ListReportAssetPermissionsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteReportAssetPermission
   */
  deleteReportAssetPermission: {
    methodKind: "unary";
    input: typeof DeleteReportAssetPermissionRequestSchema;
    output: typeof DeleteReportAssetPermissionResponseSchema;
  },
  /**
   * SITUATIONS
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetSituationAssetPermission
   */
  getSituationAssetPermission: {
    methodKind: "unary";
    input: typeof GetSituationAssetPermissionRequestSchema;
    output: typeof GetSituationAssetPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateSituationAssetPermission
   */
  updateSituationAssetPermission: {
    methodKind: "unary";
    input: typeof UpdateSituationAssetPermissionRequestSchema;
    output: typeof UpdateSituationAssetPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListSituationAssetPermissions
   */
  listSituationAssetPermissions: {
    methodKind: "unary";
    input: typeof ListSituationAssetPermissionsRequestSchema;
    output: typeof ListSituationAssetPermissionsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteSituationAssetPermission
   */
  deleteSituationAssetPermission: {
    methodKind: "unary";
    input: typeof DeleteSituationAssetPermissionRequestSchema;
    output: typeof DeleteSituationAssetPermissionResponseSchema;
  },
  /**
   * CASES
   *
   * @generated from rpc hero.permissions.v1.PermissionService.GetCaseAssetPermission
   */
  getCaseAssetPermission: {
    methodKind: "unary";
    input: typeof GetCaseAssetPermissionRequestSchema;
    output: typeof GetCaseAssetPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.UpdateCaseAssetPermission
   */
  updateCaseAssetPermission: {
    methodKind: "unary";
    input: typeof UpdateCaseAssetPermissionRequestSchema;
    output: typeof UpdateCaseAssetPermissionResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.ListCaseAssetPermissions
   */
  listCaseAssetPermissions: {
    methodKind: "unary";
    input: typeof ListCaseAssetPermissionsRequestSchema;
    output: typeof ListCaseAssetPermissionsResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DeleteCaseAssetPermission
   */
  deleteCaseAssetPermission: {
    methodKind: "unary";
    input: typeof DeleteCaseAssetPermissionRequestSchema;
    output: typeof DeleteCaseAssetPermissionResponseSchema;
  },
  /**
   * # Dispatch Role Management
   *
   * @generated from rpc hero.permissions.v1.PermissionService.DispatchRoleOn
   */
  dispatchRoleOn: {
    methodKind: "unary";
    input: typeof DispatchRoleOnRequestSchema;
    output: typeof DispatchRoleOnResponseSchema;
  },
  /**
   * @generated from rpc hero.permissions.v1.PermissionService.DispatchRoleOff
   */
  dispatchRoleOff: {
    methodKind: "unary";
    input: typeof DispatchRoleOffRequestSchema;
    output: typeof DispatchRoleOffResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_permissions_v1_permissions, 0);

/**
 * @generated from extension: bool permission_id = 50000;
 */
export const permission_id: GenExtension<FieldOptions, boolean> = /*@__PURE__*/
  extDesc(file_hero_permissions_v1_permissions, 0);

/**
 * @generated from extension: hero.permissions.v1.AccessControl access_control = 50003;
 */
export const access_control: GenExtension<MethodOptions, AccessControl> = /*@__PURE__*/
  extDesc(file_hero_permissions_v1_permissions, 1);

