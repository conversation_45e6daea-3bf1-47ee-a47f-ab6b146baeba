// @generated by protoc-gen-es v2.7.0 with parameter "target=ts"
// @generated from file hero/situations/v1/situations.proto (package hero.situations.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/situations/v1/situations.proto.
 */
export const file_hero_situations_v1_situations: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * @generated from message hero.situations.v1.CreateSituationRequest
 */
export type CreateSituationRequest = Message<"hero.situations.v1.CreateSituationRequest"> & {
  /**
   * @generated from field: hero.situations.v1.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v1.CreateSituationRequest.
 * Use `create(CreateSituationRequestSchema)` to create a new message.
 */
export const CreateSituationRequestSchema: GenMessage<CreateSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 0);

/**
 * @generated from message hero.situations.v1.CreateSituationResponse
 */
export type CreateSituationResponse = Message<"hero.situations.v1.CreateSituationResponse"> & {
  /**
   * @generated from field: hero.situations.v1.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v1.CreateSituationResponse.
 * Use `create(CreateSituationResponseSchema)` to create a new message.
 */
export const CreateSituationResponseSchema: GenMessage<CreateSituationResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 1);

/**
 * @generated from message hero.situations.v1.GetSituationForReportRequest
 */
export type GetSituationForReportRequest = Message<"hero.situations.v1.GetSituationForReportRequest"> & {
  /**
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.situations.v1.GetSituationForReportRequest.
 * Use `create(GetSituationForReportRequestSchema)` to create a new message.
 */
export const GetSituationForReportRequestSchema: GenMessage<GetSituationForReportRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 2);

/**
 * @generated from message hero.situations.v1.GetSituationForReportResponse
 */
export type GetSituationForReportResponse = Message<"hero.situations.v1.GetSituationForReportResponse"> & {
  /**
   * @generated from field: hero.situations.v1.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v1.GetSituationForReportResponse.
 * Use `create(GetSituationForReportResponseSchema)` to create a new message.
 */
export const GetSituationForReportResponseSchema: GenMessage<GetSituationForReportResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 3);

/**
 * @generated from message hero.situations.v1.GetSituationRequest
 */
export type GetSituationRequest = Message<"hero.situations.v1.GetSituationRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;
};

/**
 * Describes the message hero.situations.v1.GetSituationRequest.
 * Use `create(GetSituationRequestSchema)` to create a new message.
 */
export const GetSituationRequestSchema: GenMessage<GetSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 4);

/**
 * @generated from message hero.situations.v1.GetSituationResponse
 */
export type GetSituationResponse = Message<"hero.situations.v1.GetSituationResponse"> & {
  /**
   * @generated from field: hero.situations.v1.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v1.GetSituationResponse.
 * Use `create(GetSituationResponseSchema)` to create a new message.
 */
export const GetSituationResponseSchema: GenMessage<GetSituationResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 5);

/**
 * @generated from message hero.situations.v1.Situation
 */
export type Situation = Message<"hero.situations.v1.Situation"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string report_id = 2;
   */
  reportId: string;

  /**
   * @generated from field: int32 priority = 3;
   */
  priority: number;

  /**
   * @generated from field: hero.situations.v1.SituationType type = 4;
   */
  type: SituationType;

  /**
   * Indicates if you can use reporter information to handle the situation or not 
   *
   * @generated from field: bool ignore_reporter = 7;
   */
  ignoreReporter: boolean;

  /**
   * This can arbitarily capture any information in json structure 
   * It is hard to have structured defined data for that   
   *
   * @generated from field: string incident_details_json = 8;
   */
  incidentDetailsJson: string;

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 5;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp update_time = 6;
   */
  updateTime?: Timestamp;
};

/**
 * Describes the message hero.situations.v1.Situation.
 * Use `create(SituationSchema)` to create a new message.
 */
export const SituationSchema: GenMessage<Situation> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 6);

/**
 * @generated from message hero.situations.v1.ListSituationsRequest
 */
export type ListSituationsRequest = Message<"hero.situations.v1.ListSituationsRequest"> & {
};

/**
 * Describes the message hero.situations.v1.ListSituationsRequest.
 * Use `create(ListSituationsRequestSchema)` to create a new message.
 */
export const ListSituationsRequestSchema: GenMessage<ListSituationsRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 7);

/**
 * @generated from message hero.situations.v1.ListSituationsResponse
 */
export type ListSituationsResponse = Message<"hero.situations.v1.ListSituationsResponse"> & {
  /**
   * @generated from field: repeated hero.situations.v1.Situation situations = 1;
   */
  situations: Situation[];
};

/**
 * Describes the message hero.situations.v1.ListSituationsResponse.
 * Use `create(ListSituationsResponseSchema)` to create a new message.
 */
export const ListSituationsResponseSchema: GenMessage<ListSituationsResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 8);

/**
 * @generated from message hero.situations.v1.UpdateSituationRequest
 */
export type UpdateSituationRequest = Message<"hero.situations.v1.UpdateSituationRequest"> & {
  /**
   * @generated from field: hero.situations.v1.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v1.UpdateSituationRequest.
 * Use `create(UpdateSituationRequestSchema)` to create a new message.
 */
export const UpdateSituationRequestSchema: GenMessage<UpdateSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 9);

/**
 * @generated from message hero.situations.v1.UpdateSituationResponse
 */
export type UpdateSituationResponse = Message<"hero.situations.v1.UpdateSituationResponse"> & {
  /**
   * @generated from field: hero.situations.v1.Situation situation = 1;
   */
  situation?: Situation;
};

/**
 * Describes the message hero.situations.v1.UpdateSituationResponse.
 * Use `create(UpdateSituationResponseSchema)` to create a new message.
 */
export const UpdateSituationResponseSchema: GenMessage<UpdateSituationResponse> = /*@__PURE__*/
  messageDesc(file_hero_situations_v1_situations, 10);

/**
 * @generated from enum hero.situations.v1.SituationType
 */
export enum SituationType {
  /**
   * @generated from enum value: SITUATION_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SITUATION_TYPE_NEED_HELP = 1;
   */
  NEED_HELP = 1,
}

/**
 * Describes the enum hero.situations.v1.SituationType.
 */
export const SituationTypeSchema: GenEnum<SituationType> = /*@__PURE__*/
  enumDesc(file_hero_situations_v1_situations, 0);

/**
 * @generated from service hero.situations.v1.SituationService
 */
export const SituationService: GenService<{
  /**
   * 3 - Called by Command
   *
   * @generated from rpc hero.situations.v1.SituationService.CreateSituation
   */
  createSituation: {
    methodKind: "unary";
    input: typeof CreateSituationRequestSchema;
    output: typeof CreateSituationResponseSchema;
  },
  /**
   * 4 - called by [user|agent|sensor]
   *
   * @generated from rpc hero.situations.v1.SituationService.GetSituation
   */
  getSituation: {
    methodKind: "unary";
    input: typeof GetSituationRequestSchema;
    output: typeof GetSituationResponseSchema;
  },
  /**
   * @generated from rpc hero.situations.v1.SituationService.GetSituationForReport
   */
  getSituationForReport: {
    methodKind: "unary";
    input: typeof GetSituationForReportRequestSchema;
    output: typeof GetSituationForReportResponseSchema;
  },
  /**
   * 5 - called by Central Dashboard and Command
   *
   * @generated from rpc hero.situations.v1.SituationService.ListSituations
   */
  listSituations: {
    methodKind: "unary";
    input: typeof ListSituationsRequestSchema;
    output: typeof ListSituationsResponseSchema;
  },
  /**
   * 6 - called by Command to prioritize
   *
   * @generated from rpc hero.situations.v1.SituationService.UpdateSituation
   */
  updateSituation: {
    methodKind: "unary";
    input: typeof UpdateSituationRequestSchema;
    output: typeof UpdateSituationResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_situations_v1_situations, 0);

