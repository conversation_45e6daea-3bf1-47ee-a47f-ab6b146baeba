           table_name           |                        structure                        
--------------------------------+---------------------------------------------------------
 assets                         |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - cognito_jwt_sub (text)                              +
                                |  - name (text)                                         +
                                |  - type (integer)                                      +
                                |  - status (integer)                                    +
                                |  - latitude (double precision)                         +
                                |  - longitude (double precision)                        +
                                |  - location_update_time (timestamp with time zone)     +
                                |  - contact_no (text)                                   +
                                |  - contact_email (text)                                +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - status_changed_time (timestamp with time zone)      +
                                |  - resource_type (text)                                +
                                |  - additional_info_json (json)                         +
                                |  - is_internal (boolean)                               +
                                |  - active_since_time (timestamp with time zone)        +
                                |  - last_active_time (timestamp with time zone)
 call_forward_events            |                                                        +
                                |  - id (integer)                                        +
                                |  - call_sid (text)                                     +
                                |  - org_id (integer)                                    +
                                |  - from_number (text)                                  +
                                |  - to_number (text)                                    +
                                |  - status (text)                                       +
                                |  - child_call_sid (text)                               +
                                |  - situation_id (uuid)                                 +
                                |  - initiated_at (timestamp with time zone)             +
                                |  - ringing_at (timestamp with time zone)               +
                                |  - answered_at (timestamp with time zone)              +
                                |  - completed_at (timestamp with time zone)             +
                                |  - ring_to_answer_sec (integer)                        +
                                |  - answer_to_complete_sec (integer)                    +
                                |  - bridged (boolean)                                   +
                                |  - failure_reason (text)                               +
                                |  - sip_response_code (integer)                         +
                                |  - stir_status (text)                                  +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - assigned_asset_id (text)
 call_queue                     |                                                        +
                                |  - call_sid (text)                                     +
                                |  - org_id (integer)                                    +
                                |  - caller (text)                                       +
                                |  - caller_name (text)                                  +
                                |  - asset_id (text)                                     +
                                |  - situation_id (text)                                 +
                                |  - state (text)                                        +
                                |  - direction (text)                                    +
                                |  - attributes (jsonb)                                  +
                                |  - priority (integer)                                  +
                                |  - notes (text)                                        +
                                |  - enqueue_time (timestamp with time zone)             +
                                |  - call_start_time (timestamp with time zone)          +
                                |  - call_end_time (timestamp with time zone)            +
                                |  - last_hold_start (timestamp with time zone)
 camera_events                  |                                                        +
                                |  - id (uuid)                                           +
                                |  - org_id (integer)                                    +
                                |  - stream_name (character varying)                     +
                                |  - asset_id (text)                                     +
                                |  - s3_key (character varying)                          +
                                |  - camera_timestamp (timestamp with time zone)         +
                                |  - created_at (timestamp with time zone)
 case_asset_associations        |                                                        +
                                |  - id (text)                                           +
                                |  - case_id (text)                                      +
                                |  - asset_id (text)                                     +
                                |  - association_type (integer)                          +
                                |  - assigned_at (timestamp with time zone)              +
                                |  - notes (text)                                        +
                                |  - assigner_asset_id (text)                            +
                                |  - org_id (integer)
 case_audit_logs                |                                                        +
                                |  - id (text)                                           +
                                |  - case_id (text)                                      +
                                |  - action (integer)                                    +
                                |  - actor_asset_id (text)                               +
                                |  - timestamp (timestamp with time zone)                +
                                |  - field_path (text)                                   +
                                |  - old_value (text)                                    +
                                |  - new_value (text)                                    +
                                |  - note (text)                                         +
                                |  - org_id (integer)
 case_entities                  |                                                        +
                                |  - case_id (text)                                      +
                                |  - ref_id (text)                                       +
                                |  - ref_type (text)                                     +
                                |  - ref_version (integer)                               +
                                |  - ref_display_name (text)                             +
                                |  - relation_type (text)                                +
                                |  - org_id (integer)
 case_reports                   |                                                        +
                                |  - case_id (text)                                      +
                                |  - report_id (text)                                    +
                                |  - org_id (integer)
 case_situations                |                                                        +
                                |  - case_id (text)                                      +
                                |  - situation_id (text)                                 +
                                |  - org_id (integer)
 case_snapshots                 |                                                        +
                                |  - case_id (text)                                      +
                                |  - version (integer)                                   +
                                |  - snapshot (jsonb)                                    +
                                |  - timestamp (timestamp with time zone)                +
                                |  - org_id (integer)
 case_status_updates            |                                                        +
                                |  - id (integer)                                        +
                                |  - case_id (text)                                      +
                                |  - timestamp (timestamp with time zone)                +
                                |  - new_status (integer)                                +
                                |  - previous_status (integer)                           +
                                |  - note (text)                                         +
                                |  - updater_id (text)                                   +
                                |  - update_source (integer)                             +
                                |  - org_id (integer)
 case_tags                      |                                                        +
                                |  - case_id (text)                                      +
                                |  - tag (text)                                          +
                                |  - org_id (integer)
 case_update_file_attachments   |                                                        +
                                |  - id (text)                                           +
                                |  - case_id (text)                                      +
                                |  - case_update_id (integer)                            +
                                |  - file_id (text)                                      +
                                |  - caption (text)                                      +
                                |  - display_name (text)                                 +
                                |  - display_order (integer)                             +
                                |  - file_category (text)                                +
                                |  - metadata (jsonb)                                    +
                                |  - org_id (integer)                                    +
                                |  - created_at (timestamp with time zone)
 case_updates                   |                                                        +
                                |  - id (integer)                                        +
                                |  - case_id (text)                                      +
                                |  - message (text)                                      +
                                |  - event_time (timestamp with time zone)               +
                                |  - update_source (integer)                             +
                                |  - updater_id (text)                                   +
                                |  - event_type (text)                                   +
                                |  - display_name (text)                                 +
                                |  - data (jsonb)                                        +
                                |  - org_id (integer)
 case_watchers                  |                                                        +
                                |  - case_id (text)                                      +
                                |  - asset_id (text)                                     +
                                |  - org_id (integer)
 cases                          |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - type (integer)                                      +
                                |  - title (text)                                        +
                                |  - description (text)                                  +
                                |  - status (integer)                                    +
                                |  - priority (integer)                                  +
                                |  - release_status (integer)                            +
                                |  - resource_type (text)                                +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - due_date (timestamp with time zone)                 +
                                |  - resolved_time (timestamp with time zone)            +
                                |  - close_time (timestamp with time zone)               +
                                |  - etag (bigint)                                       +
                                |  - version (integer)                                   +
                                |  - additional_info_json (jsonb)                        +
                                |  - created_by_asset_id (text)                          +
                                |  - updated_by_asset_id (text)                          +
                                |  - search_vector (tsvector)
 entities                       |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - schema_id (text)                                    +
                                |  - schema_version (integer)                            +
                                |  - data (jsonb)                                        +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - entity_type (integer)                               +
                                |  - created_by (text)                                   +
                                |  - updated_by (text)                                   +
                                |  - version (integer)                                   +
                                |  - status (integer)                                    +
                                |  - tags (ARRAY)                                        +
                                |  - resource_type (text)                                +
                                |  - data_values (text)
 entity_references              |                                                        +
                                |  - entity_id (text)                                    +
                                |  - ref_id (text)                                       +
                                |  - ref_type (text)                                     +
                                |  - ref_version (integer)                               +
                                |  - display_name (text)                                 +
                                |  - relation_type (text)                                +
                                |  - org_id (integer)
 entity_schema_versions         |                                                        +
                                |  - schema_id (text)                                    +
                                |  - version (integer)                                   +
                                |  - schema_snapshot (jsonb)                             +
                                |  - modified_by (text)                                  +
                                |  - modified_time (timestamp with time zone)            +
                                |  - change_comment (text)                               +
                                |  - org_id (integer)
 entity_schemas                 |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - name (text)                                         +
                                |  - description (text)                                  +
                                |  - schema_definition (jsonb)                           +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - created_by (text)                                   +
                                |  - updated_by (text)                                   +
                                |  - version (integer)                                   +
                                |  - entity_type (integer)                               +
                                |  - status (integer)                                    +
                                |  - tags (ARRAY)                                        +
                                |  - resource_type (text)
 entity_versions                |                                                        +
                                |  - entity_id (text)                                    +
                                |  - version (integer)                                   +
                                |  - entity_snapshot (jsonb)                             +
                                |  - modified_by (text)                                  +
                                |  - modified_time (timestamp with time zone)            +
                                |  - change_comment (text)                               +
                                |  - org_id (integer)
 etl_job_errors                 |                                                        +
                                |  - id (text)                                           +
                                |  - job_id (text)                                       +
                                |  - org_id (integer)                                    +
                                |  - error_code (text)                                   +
                                |  - error_message (text)                                +
                                |  - report_id (text)                                    +
                                |  - field_path (text)                                   +
                                |  - occurred_at (timestamp with time zone)              +
                                |  - is_retryable (boolean)                              +
                                |  - retry_attempt (integer)
 etl_jobs                       |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - agency_id (text)                                    +
                                |  - output_format (integer)                             +
                                |  - status (integer)                                    +
                                |  - report_ids (ARRAY)                                  +
                                |  - date_from (text)                                    +
                                |  - date_to (text)                                      +
                                |  - case_types (ARRAY)                                  +
                                |  - total_reports (integer)                             +
                                |  - reports_processed (integer)                         +
                                |  - reports_failed (integer)                            +
                                |  - reports_skipped (integer)                           +
                                |  - created_at (timestamp with time zone)               +
                                |  - started_at (timestamp with time zone)               +
                                |  - completed_at (timestamp with time zone)             +
                                |  - last_updated_at (timestamp with time zone)          +
                                |  - error_message (text)                                +
                                |  - generated_content (bytea)                           +
                                |  - content_type (text)                                 +
                                |  - content_size_bytes (bigint)                         +
                                |  - agency_submission_id (text)                         +
                                |  - submission_response (text)                          +
                                |  - submitted_at (timestamp with time zone)             +
                                |  - retry_count (integer)                               +
                                |  - max_retries (integer)                               +
                                |  - last_retry_at (timestamp with time zone)            +
                                |  - auto_retry_enabled (boolean)                        +
                                |  - created_by_asset_id (text)                          +
                                |  - preview_only (boolean)                              +
                                |  - job_name (text)
 event_labels                   |                                                        +
                                |  - id (uuid)                                           +
                                |  - org_id (integer)                                    +
                                |  - event_id (uuid)                                     +
                                |  - label (character varying)                           +
                                |  - confidence (numeric)                                +
                                |  - created_at (timestamp with time zone)
 feature_flags                  |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - feature (integer)                                   +
                                |  - asset_id (text)                                     +
                                |  - enabled (boolean)                                   +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 fga_outbox                     |                                                        +
                                |  - id (uuid)                                           +
                                |  - write_payload (jsonb)                               +
                                |  - delete_payload (jsonb)                              +
                                |  - completed (boolean)                                 +
                                |  - error_count (integer)                               +
                                |  - error_message (text)                                +
                                |  - created_at (timestamp with time zone)
 file_access_logs               |                                                        +
                                |  - id (text)                                           +
                                |  - file_id (text)                                      +
                                |  - user_id (text)                                      +
                                |  - action (text)                                       +
                                |  - timestamp (timestamp with time zone)                +
                                |  - ip_address (inet)                                   +
                                |  - user_agent (text)                                   +
                                |  - referrer (text)                                     +
                                |  - file_size (integer)                                 +
                                |  - session_id (text)                                   +
                                |  - metadata (jsonb)                                    +
                                |  - org_id (integer)
 files                          |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - owner_id (text)                                     +
                                |  - file_name (text)                                    +
                                |  - file_type (text)                                    +
                                |  - file_size (integer)                                 +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - last_accessed (timestamp with time zone)            +
                                |  - status (integer)                                    +
                                |  - provider (integer)                                  +
                                |  - storage_key (text)                                  +
                                |  - bucket_name (text)                                  +
                                |  - object_version (text)                               +
                                |  - integrity_hash (text)                               +
                                |  - storage_class (integer)                             +
                                |  - encryption_key_id (text)                            +
                                |  - checksum (text)                                     +
                                |  - storage_tags (jsonb)                                +
                                |  - provider_metadata (jsonb)                           +
                                |  - extra_metadata (jsonb)                              +
                                |  - download_count (integer)                            +
                                |  - is_public (boolean)                                 +
                                |  - thumbnail_url (text)
 goose_db_version               |                                                        +
                                |  - id (integer)                                        +
                                |  - version_id (bigint)                                 +
                                |  - is_applied (boolean)                                +
                                |  - tstamp (timestamp without time zone)
 object_role_assignment_audit   |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - object_type (character varying)                     +
                                |  - object_id (text)                                    +
                                |  - operation (character varying)                       +
                                |  - changes (jsonb)                                     +
                                |  - user_id (text)                                      +
                                |  - affected_role_id (text)                             +
                                |  - created_at (timestamp with time zone)
 object_user_assignment_audit   |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - object_type (character varying)                     +
                                |  - object_id (text)                                    +
                                |  - operation (character varying)                       +
                                |  - changes (jsonb)                                     +
                                |  - user_id (text)                                      +
                                |  - affected_user_id (text)                             +
                                |  - created_at (timestamp with time zone)
 order_allowed_asset_types      |                                                        +
                                |  - order_id (text)                                     +
                                |  - asset_type (integer)                                +
                                |  - org_id (integer)
 order_blacklisted_asset_ids    |                                                        +
                                |  - order_id (text)                                     +
                                |  - asset_id (text)                                     +
                                |  - org_id (integer)
 order_permissions              |                                                        +
                                |  - order_id (text)                                     +
                                |  - scope (USER-DEFINED)                                +
                                |  - asset_type (integer)                                +
                                |  - org_id (integer)
 order_status_updates           |                                                        +
                                |  - id (integer)                                        +
                                |  - org_id (integer)                                    +
                                |  - order_id (text)                                     +
                                |  - entry_timestamp (timestamp with time zone)          +
                                |  - new_status (integer)                                +
                                |  - previous_status (integer)                           +
                                |  - new_type_specific_status (text)                     +
                                |  - previous_type_specific_status (text)                +
                                |  - note (text)                                         +
                                |  - updater_id (text)                                   +
                                |  - update_source (integer)                             +
                                |  - status_update_timestamp (timestamp with time zone)
 order_updates                  |                                                        +
                                |  - id (integer)                                        +
                                |  - order_id (text)                                     +
                                |  - message (text)                                      +
                                |  - timestamp (timestamp with time zone)                +
                                |  - update_source (integer)                             +
                                |  - org_id (integer)
 orders                         |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - situation_id (text)                                 +
                                |  - asset_id (text)                                     +
                                |  - report_id (text)                                    +
                                |  - review_round_id (text)                              +
                                |  - type (integer)                                      +
                                |  - status (integer)                                    +
                                |  - instructions (text)                                 +
                                |  - priority (integer)                                  +
                                |  - additional_info_json (json)                         +
                                |  - type_specific_status (text)                         +
                                |  - notes (text)                                        +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - completion_time (timestamp with time zone)          +
                                |  - assigned_time (timestamp with time zone)            +
                                |  - acknowledged_time (timestamp with time zone)        +
                                |  - estimated_completion_time (timestamp with time zone)+
                                |  - cancellation_or_rejection_reason (text)             +
                                |  - retry_count (integer)                               +
                                |  - created_by (integer)                                +
                                |  - title (text)                                        +
                                |  - snooze_reason (text)                                +
                                |  - snooze_until (timestamp with time zone)             +
                                |  - snooze_count (integer)                              +
                                |  - resource_type (text)                                +
                                |  - case_id (text)
 org_api_users                  |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - hashed_password (text)                              +
                                |  - encrypted_password (text)                           +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 org_call_forwarding_config     |                                                        +
                                |  - org_id (integer)                                    +
                                |  - is_call_forwarding_enabled (boolean)                +
                                |  - forward_to_number (text)                            +
                                |  - assigned_asset_id (text)                            +
                                |  - updated_at (timestamp with time zone)
 org_contacts_book              |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - name (text)                                         +
                                |  - phone (text)                                        +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 orgs                           |                                                        +
                                |  - id (integer)                                        +
                                |  - name (text)                                         +
                                |  - domains (ARRAY)                                     +
                                |  - service_type (integer)                              +
                                |  - twiml_app_sid (text)                                +
                                |  - twilio_number (text)                                +
                                |  - twilio_number_sid (text)                            +
                                |  - twilio_api_user_id (text)                           +
                                |  - template_id (text)                                  +
                                |  - primary_phone_number (text)                         +
                                |  - dispatch_role_id (text)                             +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 permission_sets                |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - name (text)                                         +
                                |  - description (text)                                  +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - deleted_at (timestamp with time zone)
 permission_sets_audit          |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - permission_set_id (text)                            +
                                |  - operation (character varying)                       +
                                |  - changes (jsonb)                                     +
                                |  - user_id (text)                                      +
                                |  - created_at (timestamp with time zone)
 perms_config                   |                                                        +
                                |  - model_id (text)                                     +
                                |  - store_id (text)                                     +
                                |  - name (text)                                         +
                                |  - hash (text)                                         +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 pre_registration_user_mappings |                                                        +
                                |  - id (uuid)                                           +
                                |  - email (character varying)                           +
                                |  - org_id (integer)                                    +
                                |  - asset_type (integer)                                +
                                |  - created_at (timestamp with time zone)               +
                                |  - used_at (timestamp with time zone)                  +
                                |  - created_by (character varying)
 properties                     |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - property_number (text)                              +
                                |  - is_evidence (boolean)                               +
                                |  - retention_period (text)                             +
                                |  - property_status (integer)                           +
                                |  - disposal_type (integer)                             +
                                |  - notes (text)                                        +
                                |  - current_custodian (text)                            +
                                |  - current_location (text)                             +
                                |  - custody_chain (jsonb)                               +
                                |  - property_schema (jsonb)                             +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - created_by (text)                                   +
                                |  - updated_by (text)                                   +
                                |  - version (integer)                                   +
                                |  - status (integer)                                    +
                                |  - resource_type (text)
 property_file_attachments      |                                                        +
                                |  - id (text)                                           +
                                |  - property_id (text)                                  +
                                |  - file_id (text)                                      +
                                |  - caption (text)                                      +
                                |  - display_name (text)                                 +
                                |  - display_order (integer)                             +
                                |  - file_category (text)                                +
                                |  - metadata (jsonb)                                    +
                                |  - org_id (integer)                                    +
                                |  - created_at (timestamp with time zone)
 property_versions              |                                                        +
                                |  - property_id (text)                                  +
                                |  - version (integer)                                   +
                                |  - property_snapshot (jsonb)                           +
                                |  - modified_by (text)                                  +
                                |  - modified_time (timestamp with time zone)            +
                                |  - change_comment (text)                               +
                                |  - org_id (integer)
 related_cases                  |                                                        +
                                |  - case_id (text)                                      +
                                |  - related_case_id (text)                              +
                                |  - org_id (integer)
 report_comments                |                                                        +
                                |  - id (text)                                           +
                                |  - report_id (text)                                    +
                                |  - section_id (text)                                   +
                                |  - reply_to_comment_id (text)                          +
                                |  - author_asset_id (text)                              +
                                |  - text (text)                                         +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - resolved (boolean)                                  +
                                |  - resolved_at (timestamp with time zone)              +
                                |  - resolved_by_asset_id (text)                         +
                                |  - resource_type (text)                                +
                                |  - display_name (text)
 report_relations               |                                                        +
                                |  - id (text)                                           +
                                |  - report_id (text)                                    +
                                |  - object_a_type (text)                                +
                                |  - object_a_report_scoped_id (text)                    +
                                |  - object_a_global_id (text)                           +
                                |  - object_a_external_id (text)                         +
                                |  - object_a_section_id (text)                          +
                                |  - object_a_display_name (text)                        +
                                |  - object_a_metadata (jsonb)                           +
                                |  - object_b_type (text)                                +
                                |  - object_b_report_scoped_id (text)                    +
                                |  - object_b_global_id (text)                           +
                                |  - object_b_external_id (text)                         +
                                |  - object_b_section_id (text)                          +
                                |  - object_b_display_name (text)                        +
                                |  - object_b_metadata (jsonb)                           +
                                |  - relation_type (text)                                +
                                |  - description (text)                                  +
                                |  - metadata (jsonb)                                    +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - created_by_asset_id (text)
 report_review_rounds           |                                                        +
                                |  - id (text)                                           +
                                |  - report_id (text)                                    +
                                |  - reviewer_asset_id (text)                            +
                                |  - level (integer)                                     +
                                |  - status (integer)                                    +
                                |  - sent_to_level (integer)                             +
                                |  - sent_to_asset_id (text)                             +
                                |  - requested_at (timestamp with time zone)             +
                                |  - resolved_at (timestamp with time zone)              +
                                |  - round_note (text)                                   +
                                |  - snapshot_version (integer)                          +
                                |  - due_at (timestamp with time zone)                   +
                                |  - create_by_asset_id (text)                           +
                                |  - note_for_reviewer (text)
 report_sections                |                                                        +
                                |  - id (text)                                           +
                                |  - report_id (text)                                    +
                                |  - type (integer)                                      +
                                |  - content (jsonb)                                     +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 report_snapshots               |                                                        +
                                |  - report_id (text)                                    +
                                |  - version (integer)                                   +
                                |  - report_snapshot (jsonb)                             +
                                |  - created_at (timestamp with time zone)
 reports                        |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - author_asset_id (text)                              +
                                |  - title (text)                                        +
                                |  - status (integer)                                    +
                                |  - assigned_at (timestamp with time zone)              +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - completed_at (timestamp with time zone)             +
                                |  - additional_info_json (jsonb)                        +
                                |  - version (integer)                                   +
                                |  - situation_id (text)                                 +
                                |  - case_id (text)                                      +
                                |  - watcher_asset_ids (ARRAY)                           +
                                |  - resource_type (text)                                +
                                |  - created_by_asset_id (text)                          +
                                |  - report_type (integer)
 role_assignment_audit          |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - role_id (text)                                      +
                                |  - affected_user_id (text)                             +
                                |  - operation (character varying)                       +
                                |  - changes (jsonb)                                     +
                                |  - user_id (text)                                      +
                                |  - created_at (timestamp with time zone)
 roles                          |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - name (character varying)                            +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)               +
                                |  - deleted_at (timestamp with time zone)               +
                                |  - is_internal (boolean)
 roles_audit                    |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - role_id (text)                                      +
                                |  - operation (character varying)                       +
                                |  - changes (jsonb)                                     +
                                |  - user_id (text)                                      +
                                |  - created_at (timestamp with time zone)
 situation_media_attachments    |                                                        +
                                |  - situation_id (text)                                 +
                                |  - org_id (integer)                                    +
                                |  - attachment_id (text)                                +
                                |  - url (text)                                          +
                                |  - content_type (text)
 situation_related              |                                                        +
                                |  - situation_id (text)                                 +
                                |  - org_id (integer)                                    +
                                |  - related_situation_id (text)
 situation_status_updates       |                                                        +
                                |  - id (integer)                                        +
                                |  - org_id (integer)                                    +
                                |  - situation_id (text)                                 +
                                |  - timestamp (timestamp with time zone)                +
                                |  - new_status (integer)                                +
                                |  - previous_status (integer)                           +
                                |  - note (text)                                         +
                                |  - updater_id (text)                                   +
                                |  - update_source (integer)
 situation_tags                 |                                                        +
                                |  - situation_id (text)                                 +
                                |  - tag (text)                                          +
                                |  - org_id (integer)
 situation_updates              |                                                        +
                                |  - id (integer)                                        +
                                |  - org_id (integer)                                    +
                                |  - situation_id (text)                                 +
                                |  - message (text)                                      +
                                |  - timestamp (timestamp with time zone)                +
                                |  - update_source (integer)                             +
                                |  - display_name (text)                                 +
                                |  - event_type (text)                                   +
                                |  - updater_id (text)
 situations                     |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - priority (integer)                                  +
                                |  - title (text)                                        +
                                |  - type (integer)                                      +
                                |  - resource_type (text)                                +
                                |  - description (text)                                  +
                                |  - status (integer)                                    +
                                |  - reporter_id (text)                                  +
                                |  - create_time (timestamp with time zone)              +
                                |  - update_time (timestamp with time zone)              +
                                |  - due_time (timestamp with time zone)                 +
                                |  - resolved_time (timestamp with time zone)            +
                                |  - incident_time (timestamp with time zone)            +
                                |  - trigger_source (integer)                            +
                                |  - reporter_name (text)                                +
                                |  - contact_no (text)                                   +
                                |  - contact_email (text)                                +
                                |  - address (text)                                      +
                                |  - latitude (double precision)                         +
                                |  - longitude (double precision)                        +
                                |  - address_update_time (timestamp with time zone)      +
                                |  - automation_enabled (boolean)                        +
                                |  - additional_info_json (jsonb)
 twilio_queue_configurations    |                                                        +
                                |  - id (integer)                                        +
                                |  - friendly_name (character varying)                   +
                                |  - twilio_queue_sid (character varying)                +
                                |  - org_id (integer)                                    +
                                |  - description (text)                                  +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 zello_channels                 |                                                        +
                                |  - id (text)                                           +
                                |  - org_id (integer)                                    +
                                |  - zello_channel_id (text)                             +
                                |  - display_name (text)                                 +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
 zello_creds                    |                                                        +
                                |  - asset_id (text)                                     +
                                |  - org_id (integer)                                    +
                                |  - username (character varying)                        +
                                |  - encrypted_password (character varying)              +
                                |  - created_at (timestamp with time zone)               +
                                |  - updated_at (timestamp with time zone)
(67 rows)

