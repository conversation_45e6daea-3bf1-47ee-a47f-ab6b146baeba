package client

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"common/herosentry"
)

// TwilioClient provides a consistent interface for Twilio API interactions
type TwilioClient interface {
	ModifyCall(ctx context.Context, callSid string, twiml string) error
	RedirectCall(ctx context.Context, callSid string, url string) error
	RedirectQueueMember(ctx context.Context, queueSid string, callSid string, url string) error
}

type twilioClient struct {
	accountSid string
	authToken  string
	httpClient *http.Client
}

// NewTwilioClient creates a new Twilio API client
func NewTwilioClient(accountSid string, authToken string) TwilioClient {
	return &twilioClient{
		accountSid: accountSid,
		authToken:  authToken,
		httpClient: &http.Client{Timeout: 10 * time.Second},
	}
}

// ModifyCall updates an in-progress call with new TwiML instructions
func (c *twilioClient) ModifyCall(ctx context.Context, callSid string, twiml string) error {
	apiURL := fmt.Sprintf("https://api.twilio.com/2010-04-01/Accounts/%s/Calls/%s.json",
		c.accountSid, callSid)

	data := url.Values{}
	data.Set("Twiml", twiml)

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		err = fmt.Errorf("error creating request: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal, "Failed to create Twilio request")
		return err
	}

	req.SetBasicAuth(c.accountSid, c.authToken)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		err = fmt.Errorf("error sending request: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "Failed to send request to Twilio")
		return err
	}
	defer resp.Body.Close()

	bodyBytes, _ := io.ReadAll(resp.Body)

	if resp.StatusCode >= 300 {
		err := fmt.Errorf("twilio API error (status %d): %s",
			resp.StatusCode, string(bodyBytes))
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "Twilio API returned error for url: "+req.URL.String())
		return err
	}

	log.Printf("Successfully modified call %s with TwiML", callSid)
	return nil
}

// RedirectCall redirects a call to a new URL for TwiML instructions
func (c *twilioClient) RedirectCall(ctx context.Context, callSid string, redirectURL string) error {
	apiURL := fmt.Sprintf("https://api.twilio.com/2010-04-01/Accounts/%s/Calls/%s.json",
		c.accountSid, callSid)

	data := url.Values{}
	data.Set("Url", redirectURL)
	data.Set("Method", "POST")

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		err = fmt.Errorf("error creating request: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal, "Failed to create Twilio request")
		return err
	}

	req.SetBasicAuth(c.accountSid, c.authToken)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		err = fmt.Errorf("error sending request: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "Failed to send request to Twilio")
		return err
	}
	defer resp.Body.Close()

	bodyBytes, _ := io.ReadAll(resp.Body)

	if resp.StatusCode >= 300 {
		err := fmt.Errorf("twilio API error (status %d): %s",
			resp.StatusCode, string(bodyBytes))
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "Twilio API returned error")
		return err
	}

	log.Printf("Successfully redirected call %s to URL: %s", callSid, redirectURL)
	return nil
}

// RedirectQueueMember redirects a specific member (call) in a Twilio queue to a TwiML endpoint
// This implements the Twilio Queue Member API: POST /Accounts/{AccountSid}/Queues/{QueueSid}/Members/{CallSid}.json
func (c *twilioClient) RedirectQueueMember(ctx context.Context, queueSid string, callSid string, redirectURL string) error {
	// Construct the API URL for the Queue Member resource
	apiURL := fmt.Sprintf("https://api.twilio.com/2010-04-01/Accounts/%s/Queues/%s/Members/%s.json",
		c.accountSid, queueSid, callSid)

	// Set the URL parameter to the TwiML endpoint that will handle the call
	data := url.Values{}
	data.Set("Url", redirectURL)
	data.Set("Method", "POST")

	// Create the HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		err = fmt.Errorf("error creating queue member redirect request: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeInternal, "Failed to create queue member redirect request")
		return err
	}

	// Set authentication and headers
	req.SetBasicAuth(c.accountSid, c.authToken)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Send the request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		err = fmt.Errorf("error sending queue member redirect request: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "Failed to send queue member redirect request")
		return err
	}
	defer resp.Body.Close()

	// Read the response body for error reporting
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check for error status codes
	if resp.StatusCode >= 300 {
		err := fmt.Errorf("twilio queue member API error (status %d): %s",
			resp.StatusCode, string(bodyBytes))
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeExternal, "Twilio queue member API error")
		return err
	}

	log.Printf("Successfully redirected queue member (call %s) in queue %s to URL: %s",
		callSid, queueSid, redirectURL)
	return nil
}
