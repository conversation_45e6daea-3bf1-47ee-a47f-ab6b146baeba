package main

import (
	database "common/database"
	"common/herosentry"
	"common/middleware"
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"time"

	"workflow/internal/assets"
	"workflow/internal/cases"
	"workflow/internal/entity"
	"workflow/internal/etl"
	"workflow/internal/orders"
	"workflow/internal/property"
	"workflow/internal/reports"
	"workflow/internal/situations"

	assetRepository "workflow/internal/assets/data"
	caseRepository "workflow/internal/cases/data"
	entityRepository "workflow/internal/entity/data"
	etlRepository "workflow/internal/etl/data"
	orderRepository "workflow/internal/orders/data"
	propertyRepository "workflow/internal/property/data"
	reportRepository "workflow/internal/reports/data"
	situationRepository "workflow/internal/situations/data"
)

// fatalWithFlush ensures Sentry events are sent before exiting
func fatalWithFlush(err error, msg string) {
	herosentry.CaptureException(context.Background(), err, herosentry.ErrorTypeInternal, msg)
	herosentry.Flush()
	log.Fatalf("%s: %v", msg, err)
}

func main() {
	// Initialize herosentry for error tracking and performance monitoring
	// Filter out validation and not-found errors to reduce noise
	falseVal := false
	err := herosentry.Init("workflow-service", herosentry.Config{
		CustomSamplingRules: map[string]float64{
			// 1% sampling for all List operations across all layers
			"*Service.List*": 0.01, // RPC service layer (e.g., OrderService.ListOrders)
		},
		// Filter out expected errors to reduce Sentry noise
		CaptureValidationErrors: &falseVal, // Don't capture validation/bad request errors
		CaptureNotFoundErrors:   &falseVal, // Don't capture 404/not found errors
	})
	if err != nil {
		log.Fatalf("Herosentry initialization failed: %v", err)
	}
	defer herosentry.Flush() //nolint:gocritic // fatalWithFlush handles flush before exit

	baseMux := http.NewServeMux()

	// Initialize all DB
	repositoryType := os.Getenv("REPO_TYPE")

	var postGresDB *sql.DB = nil

	if repositoryType == "postgres" {
		databaseURL, err := database.CreateDBURL()
		if err != nil {
			fatalWithFlush(err, "Failed to create database URL")
		}
		var openError error
		postGresDB, openError = sql.Open("postgres", databaseURL)
		if openError != nil {
			fatalWithFlush(openError, "Failed to open postgres database")
		}

		// Configure connection pool for optimal performance
		postGresDB.SetMaxOpenConns(100) // Primary service gets largest allocation

		// Max idle connections - keep ready for burst traffic
		postGresDB.SetMaxIdleConns(25) // 25% of max for quick response

		// Connection max lifetime - force connection refresh to prevent stale connections
		postGresDB.SetConnMaxLifetime(5 * time.Minute)

		// Connection max idle time - close idle connections to free resources
		postGresDB.SetConnMaxIdleTime(90 * time.Second)
	}

	// Initialize Asset Repository
	assetRepo, assetDB, err := assetRepository.NewAssetRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize asset repository")
	}

	// Initialize Situation Repository
	situationRepo, situationDB, err := situationRepository.NewSituationRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize situation repository")
	}

	// Initialize Order Repository
	orderRepo, orderDB, err := orderRepository.NewOrderRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize order repository")
	}

	// Initialize Entity Repository
	entityRepo, entityDB, err := entityRepository.NewEntityRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize entity repository")
	}

	// Initialize Report Repository
	reportRepo, reportDB, err := reportRepository.NewReportRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize report repository")
	}

	// Initialize Case Repository
	caseRepo, caseDB, err := caseRepository.NewCaseRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize case repository")
	}

	// Initialize ETL Repository
	etlRepo, etlDB, err := etlRepository.NewETLRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize ETL repository")
	}

	// Initialize Property Repository
	propertyRepo, propertyDB, err := propertyRepository.NewPropertyRepository(postGresDB)
	if err != nil {
		fatalWithFlush(err, "Failed to initialize property repository")
	}

	// Register all endpoints on base mux
	assets.RegisterRoutes(baseMux, assetDB, assetRepo, situationRepo, orderRepo)
	situations.RegisterRoutes(baseMux, situationDB, assetRepo, situationRepo, orderRepo)
	orders.RegisterRoutes(baseMux, orderDB, assetRepo, situationRepo, orderRepo, reportRepo, caseRepo)
	entity.RegisterRoutes(baseMux, entityDB, entityRepo)
	reports.RegisterRoutes(baseMux, reportDB, assetRepo, reportRepo, orderRepo)
	cases.RegisterRoutes(baseMux, caseDB, assetRepo, entityRepo, orderRepo, reportRepo, situationRepo, caseRepo)
	etl.RegisterRoutes(baseMux, etlDB, etlRepo, reportRepo, entityRepo, situationRepo, assetRepo, propertyRepo)
	property.RegisterRoutes(baseMux, propertyDB, propertyRepo)

	// Wrap with database pool monitoring middleware if database is available
	var mux http.Handler = baseMux
	if postGresDB != nil {
		mux = herosentry.DBPoolMiddleware(postGresDB)(baseMux)
	}

	// Additional endpoints.
	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.assets.v2.AssetRegistryService",
			"hero.orders.v2.OrderService",
			"hero.situations.v2.SituationService",
			"hero.entity.v1.EntityService",
			"hero.reports.v2.ReportService",
			"hero.cases.v1.CaseService",
			"hero.etl.v1.ETLService",
			"hero.property.v1.PropertyService",
		},
		HealthResponse: "YES HOW CAN I HELP YOU",
	})

	// Create the server with health endpoints
	skipPerms := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"

	srv, err := middleware.NewServerWithHealth(
		mux,
		healthMux,
		!skipPerms,
	)
	if err != nil {
		fatalWithFlush(err, "Failed to create server")
	}

	if err := middleware.StartServer(srv); err != nil {
		fatalWithFlush(err, "Failed to serve")
	}
}
