### 18. AddPropertyRefToCase

**Method:** `AddPropertyRefToCase`  
**Route:** `POST /hero.cases.v1.CaseService/AddPropertyRefToCase`

#### Request

**AddPropertyRefToCaseRequest:**

| Field       | Type                                    | Description                    |
|-------------|-----------------------------------------|--------------------------------|
| caseId      | string                                  | ID of the case.                |
| propertyRef | hero.property.v1.PropertyReference      | Property reference to add.     |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "propertyRef": {
    "id": "prop-123",
    "version": 1,
    "displayName": "Evidence: iPhone 14",
    "relationType": "evidence"
  }
}
```

#### Response

Returns the updated `Case` object.

---

### 19. RemovePropertyRefFromCase

**Method:** `RemovePropertyRefFromCase`  
**Route:** `POST /hero.cases.v1.CaseService/RemovePropertyRefFromCase`

#### Request

**RemovePropertyRefFromCaseRequest:**

| Field       | Type                                    | Description                    |
|-------------|-----------------------------------------|--------------------------------|
| caseId      | string                                  | ID of the case.                |
| propertyRef | hero.property.v1.PropertyReference      | Property reference to remove.  |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "propertyRef": {
    "id": "prop-123",
    "version": 1
  }
}
```

#### Response

Returns the updated `Case` object.

---
# Cases Module

In our system, a **Case** represents a structured investigation or task that requires coordinated effort across multiple assets and resources. Cases are used to track incidents, manage investigations, handle customer complaints, and coordinate operational tasks. Each case maintains a detailed timeline of updates, status changes, and asset associations, providing a complete audit trail of all activities.

The Cases Module is essential for:
- **Incident Management:** Track and coordinate responses to security, safety, and operational incidents
- **Investigation Tracking:** Maintain detailed records of investigative work and findings
- **Resource Coordination:** Associate assets with specific roles and track their involvement
- **Audit Trail:** Keep a comprehensive history of all case activities and changes
- **Cross-Entity Relationships:** Link cases to situations, reports, and other entities

---

## Running Locally

When you run `make run`, the service starts on `localhost:9086` by default and uses a locally running PostgreSQL Docker container as its database.  
If you encounter database errors, run `make db` **before** `make run` to ensure your local database is set up and migrated to the correct version.

To switch to an in‑memory database, update the `<root>/docker-compose.yml` file under `workflow-service` by setting the `REPO_TYPE` environment variable to `inmemory`.

---

## Running Tests

The Cases module includes a comprehensive test suite that validates all functionality from basic CRUD operations to complex search scenarios. Tests are designed to run against a real database using actual gRPC clients, ensuring authentic integration testing.

### Prerequisites

1. **Service Running:**  
   Ensure the workflow service is running locally:
   ```bash
   make db    # Set up database
   make run   # Start service on localhost:9086
   ```

2. **Authentication Token:**  
   Create a `token.txt` file in the `test/cases/` directory containing a valid authentication token:
   ```bash
   echo "your-cognito-access-token-here" > services/workflow/test/cases/token.txt
   ```

### Test Categories

The test suite is organized into distinct categories, each focusing on specific aspects of the Cases functionality:

#### 1. Sanity Tests (`TestSanity_*`)
**Purpose:** Validate core CRUD operations and basic API functionality  
**Coverage:** Create, Read, Update, Delete operations for cases and all relationship management  
**Runtime:** ~2-3 minutes  

```bash
# Run all sanity tests
cd services/workflow
go test -v -run "^TestSanity_" ./test/cases

# Or use the test runner script
cd services/workflow/test/cases
./run_tests.sh sanity
```

**What it tests:**
- Case creation with all field types and relationships
- Case retrieval and validation of returned data
- Case updates including status changes and metadata
- Relationship management (situations, reports, assets, entities, watchers)
- Asset association management with different roles
- Tag management and additional info updates
- Case deletion and cleanup

#### 2. Search Tests (`TestSearch_*`)
**Purpose:** Comprehensive validation of the advanced search functionality  
**Coverage:** Text search, filtering, pagination, highlighting, and complex queries  
**Runtime:** ~5-8 minutes  

```bash
# Run all search tests
cd services/workflow
go test -v -run "^TestSearch_" ./test/cases

# Or use the test runner script
cd services/workflow/test/cases
./run_tests.sh search
```

**What it tests:**
- **Text Search Engine:** Full-text search with phrase matching, boolean operators, and partial matches
- **Advanced Filtering:** Status, type, priority, date ranges, tags, asset associations, relationships
- **Search Field Restrictions:** Limiting search to specific fields (id, title, description)
- **Field Queries:** ILIKE pattern matching on specific fields
- **Pagination:** Page size limits, page tokens, and consistent ordering
- **Search Highlights:** Fragment generation and match highlighting
- **Performance:** Large result set handling and search optimization
- **Edge Cases:** Invalid queries, empty results, malformed filters
- **Complex Combinations:** Multiple filters applied simultaneously

#### 3. Side Effect Tests (`TestSideEffect_*`)
**Purpose:** Validate automated behaviors and cascading operations  
**Coverage:** Audit trails, versioning, and system-generated side effects  
**Runtime:** ~1-2 minutes  

```bash
# Run all side effect tests
cd services/workflow
go test -v -run "^TestSideEffect_" ./test/cases

# Or use the test runner script
cd services/workflow/test/cases
./run_tests.sh side-effect
```

**What it tests:**
- Audit log generation for all operations
- Case versioning and snapshot creation
- Timestamp management (create_time, update_time, resolved_time, close_time)
- Status-based timestamp automation
- Relationship cascade behaviors
- Optimistic locking (etag) functionality

#### 4. Performance Tests (`TestCases_BasicPerformance`)
**Purpose:** Validate system performance under load with comprehensive latency testing  
**Coverage:** All major API operations with performance metrics and thresholds  
**Runtime:** ~3-8 minutes depending on test data volume  

```bash
# Run performance tests
cd services/workflow
go test -v -run "^TestCases_BasicPerformance$" ./test/cases -timeout=600s

# Or use the test runner script
cd services/workflow/test/cases
./run_tests.sh performance
```

**What it tests:**
- **Test Data Creation**: Creates 50 cases, 10 assets, and 5 situations for realistic performance testing
- **Core CRUD Performance**: CreateCase, GetCase, UpdateCase, DeleteCase with sub-200ms latency targets
- **Search Performance**: SearchCases with various query types, filters, and complexity levels
- **Relationship Operations**: Add/remove situations, reports, entities, asset associations
- **Status Management**: UpdateCaseStatus, AddCaseUpdate performance validation
- **Bulk Operations**: BatchGetCases, ListCases with pagination performance
- **Specialized Operations**: Case versioning, audit log retrieval, tag management
- **Performance Reporting**: Detailed metrics with min/max/average latencies per operation
- **Latency Thresholds**: 200ms maximum latency for individual operations, 2000ms for bulk operations

#### 5. Data Management Tests
**Purpose:** Bulk operations for test data creation and cleanup  

**Populate Test Data:**
```bash
cd services/workflow/test/cases
./run_tests.sh populate
```
Creates comprehensive test datasets with:
- 100+ test cases with varied attributes
- Multiple relationships per case
- Test assets, situations, and reports
- Realistic data patterns for testing

**Cleanup Test Data:**
```bash
cd services/workflow/test/cases
./run_tests.sh cleanup
```
Removes all test entities:
- Deletes all cases (from ID files or database scan)
- Cleans up related assets, situations, and reports
- Removes temporary ID files
- Provides detailed cleanup statistics

### Running All Tests

**Sequential execution of all test categories:**
```bash
cd services/workflow/test/cases
./run_tests.sh all
```

**Manual execution with go test:**
```bash
cd services/workflow
go test -v ./test/cases
```

### Test Runner Options

The provided `run_tests.sh` script offers several useful options:

```bash
# Basic usage
./run_tests.sh [test-type] [options]

# Test types:
./run_tests.sh sanity      # CRUD and basic operations
./run_tests.sh search      # Search functionality  
./run_tests.sh side-effect # Automated behaviors
./run_tests.sh performance # Load testing and latency validation
./run_tests.sh populate    # Create test data
./run_tests.sh cleanup     # Remove test data
./run_tests.sh all         # Run all test categories

# Options:
./run_tests.sh search nocache      # Bypass Go test cache
./run_tests.sh performance nocache # Bypass Go test cache for performance tests
```

### Test Output and Debugging

The test suite provides rich, colorized output with detailed progress tracking:

```
🧪 TEST: Basic case creation and retrieval
   PURPOSE: Validate core CRUD operations work correctly
   TESTING: Creating case with all field types
   EXPECTATION: Case should be created and retrievable with all data intact

✅ SUCCESS: Case created successfully (ID: case-123)
   RESULT: All fields validated correctly

📋 SUBTEST: Case relationship management
   TESTING: Adding situations, reports, and asset associations
   EXPECTATION: Relationships should be stored and retrievable

✅ SUCCESS: All relationships created successfully
   RESULT: 2 situations, 1 report, 3 asset associations added
```

### Troubleshooting Tests

**Common Issues:**

1. **Authentication Failures:**
   ```
   Error: No token found in 'token.txt'
   ```
   **Solution:** Ensure `token.txt` contains a valid Cognito access token

2. **Connection Refused:**
   ```
   Error: dial tcp [::1]:9086: connect: connection refused
   ```
   **Solution:** Start the workflow service with `make run`

3. **Database Errors:**
   ```
   Error: relation "cases" does not exist
   ```
   **Solution:** Run `make db` to initialize the database

4. **Test Data Conflicts:**
   ```
   Error: duplicate key value violates unique constraint
   ```
   **Solution:** Run cleanup tests to remove existing test data

5. **Performance Test Failures:**
   ```
   Error: operation exceeded latency threshold: 350ms > 200ms
   ```
   **Solution:** 
   - Ensure adequate system resources (CPU, memory)
   - Check for concurrent database operations
   - Verify database indexes are properly created
   - Run tests in a consistent environment

6. **Performance Test Timeouts:**
   ```
   Error: test timeout after 600s
   ```
   **Solution:**
   - Reduce test data volume if needed
   - Check database performance and connectivity
   - Ensure no resource contention during tests

### Test Database Impact

⚠️ **Important:** These tests run against your local database and will:
- Create test entities (cases, assets, situations, reports)
- Modify existing data during relationship tests
- Generate audit log entries
- Create case snapshots and versions

**Recommendations:**
- Use a dedicated test database instance
- Run cleanup tests regularly: `./run_tests.sh cleanup`
- Consider automated cleanup in CI/CD pipelines
- Monitor test data accumulation over time

### Performance Test Considerations

**Runtime and Resource Requirements:**
- **Performance tests**: Create 50 cases + related entities, may take 3-8 minutes including cleanup
- **Resource usage**: Requires adequate CPU and memory for realistic performance validation
- **Database load**: Performance tests generate significant database activity during execution
- **Timing sensitivity**: Tests expect sub-200ms latency for most operations
- **Environment consistency**: Run performance tests in consistent, dedicated environments for meaningful results

**Performance Test Data:**
- **Test volume**: 50 cases, 10 assets, 5 situations for realistic load simulation
- **Relationship complexity**: Multiple situations, reports, asset associations per case
- **Cleanup overhead**: Comprehensive cleanup of all created entities after test completion

### Performance Benchmarking

The search tests include performance validation:
- Text searches should complete in <100ms
- Complex multi-filter queries should complete in <200ms
- Large result sets (100+ cases) should paginate efficiently
- Relationship loading should avoid N+1 query patterns

The dedicated performance tests provide comprehensive latency validation:
- **Test Data Volume**: 50 cases, 10 assets, 5 situations for realistic load testing
- **Latency Thresholds**: 200ms for individual operations, 2000ms for bulk operations
- **Coverage**: All major CRUD operations, search functionality, and relationship management
- **Metrics**: Min/max/average latency reporting with success rate tracking

**Example benchmark output:**
```
📊 PERFORMANCE RESULT: Text search completed in 45ms
📊 PERFORMANCE RESULT: Complex filter query completed in 78ms
📊 PERFORMANCE RESULT: 100-case result set loaded in 123ms
📊 PERFORMANCE SUMMARY: CreateCase average latency: 35ms (success rate: 50/50)
📊 PERFORMANCE SUMMARY: SearchCases average latency: 65ms (success rate: 25/25)
```

---

# Data Model Reference

## Enums

### CaseStatus
Defines the lifecycle stages of a case:

| Name                           | Value | Description                                      |
|--------------------------------|-------|--------------------------------------------------|
| CASE_STATUS_UNSPECIFIED        | 0     | Default unspecified status.                      |
| CASE_STATUS_NEW                | 1     | Logged, awaiting triage.                         |
| CASE_STATUS_OPEN               | 2     | Actively being scoped/triaged.                   |
| CASE_STATUS_UNDER_REVIEW       | 3     | Submitted for managerial review.                 |
| CASE_STATUS_INVESTIGATING      | 4     | Active investigative work.                       |
| CASE_STATUS_PENDING_INFORMATION| 5     | Blocked, waiting on data.                        |
| CASE_STATUS_ON_HOLD            | 6     | Manually paused.                                 |
| CASE_STATUS_ESCALATED          | 7     | Handed to higher authority.                      |
| CASE_STATUS_RESOLVED           | 8     | Root issue fixed.                                |
| CASE_STATUS_CLOSED             | 9     | Administratively closed.                         |
| CASE_STATUS_ARCHIVED           | 10    | Long‑term retention (readonly).                  |

### CaseType
Represents different types of cases:

| Name                           | Value | Description                                      |
|--------------------------------|-------|--------------------------------------------------|
| CASE_TYPE_UNSPECIFIED          | 0     | Default unspecified type.                        |
| CASE_TYPE_SECURITY_INCIDENT    | 1     | Security-related incident.                       |
| CASE_TYPE_SAFETY_INCIDENT      | 2     | Safety-related incident.                         |
| CASE_TYPE_OPERATIONAL_TASK     | 3     | Operational task or project.                     |
| CASE_TYPE_CUSTOMER_COMPLAINT   | 4     | Customer complaint or feedback.                  |
| CASE_TYPE_INVESTIGATION        | 5     | General investigation.                           |
| CASE_TYPE_COMPLIANCE_REVIEW    | 6     | Compliance or regulatory review.                 |
| CASE_TYPE_INSURANCE_CLAIM      | 7     | Insurance claim processing.                      |
| CASE_TYPE_ADMINISTRATIVE       | 8     | Administrative task or process.                  |
| CASE_TYPE_OTHER                | 99    | Other types of cases.                            |

### CaseAssetAssociationType
Defines the role an asset plays within a case:

| Name                                | Value | Description                                      |
|-------------------------------------|-------|--------------------------------------------------|
| ASSET_ASSOCIATION_TYPE_UNSPECIFIED  | 0     | Default unspecified role.                        |
| ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR | 1 | Primary investigator role.                |
| ASSET_ASSOCIATION_TYPE_INVESTIGATOR | 2     | Investigator role.                               |
| ASSET_ASSOCIATION_TYPE_OBSERVER     | 3     | Observer role.                                   |
| ASSET_ASSOCIATION_TYPE_APPROVER     | 4     | Approver role.                                   |
| ASSET_ASSOCIATION_TYPE_NOTIFY_ONLY  | 5     | Notification-only role.                          |
| ASSET_ASSOCIATION_TYPE_WITNESS      | 6     | Witness role.                                    |
| ASSET_ASSOCIATION_TYPE_SUBJECT      | 7     | Subject of investigation.                        |
| ASSET_ASSOCIATION_TYPE_REPORTER     | 8     | Reporter role.                                   |
| ASSET_ASSOCIATION_TYPE_COLLABORATOR | 9     | Collaborator role.                               |

### CaseAuditAction
Represents types of changes recorded in the audit log:

| Name                           | Value | Description                                      |
|--------------------------------|-------|--------------------------------------------------|
| CASE_AUDIT_ACTION_UNSPECIFIED  | 0     | Default unspecified action.                      |
| CASE_AUDIT_ACTION_CREATE       | 1     | Case was created.                                |
| CASE_AUDIT_ACTION_UPDATE       | 2     | Generic field edit.                              |
| CASE_AUDIT_ACTION_STATUS       | 3     | Status transition.                               |
| CASE_AUDIT_ACTION_RELATION     | 4     | Link/unlink other object.                        |
| CASE_AUDIT_ACTION_ASSOCIATION  | 5     | Asset association mutation.                      |
| CASE_AUDIT_ACTION_DELETE       | 6     | Hard delete.                                     |
| CASE_AUDIT_ACTION_VIEW         | 7     | Case was viewed by an asset.                     |

### ReleaseStatus
Controls what content reviewers may legally disclose:

| Name                           | Value | Description                                      |
|--------------------------------|-------|--------------------------------------------------|
| RELEASE_STATUS_UNSPECIFIED     | 0     | Default unspecified status.                      |
| RELEASE_STATUS_PUBLIC          | 1     | Safe for public release.                         |
| RELEASE_STATUS_INTERNAL        | 2     | Company-internal only.                           |
| RELEASE_STATUS_LAW_ENFORCEMENT_ONLY | 3  | Shareable with LE partners.                      |
| RELEASE_STATUS_DO_NOT_RELEASE  | 4     | Withhold from all external requests.             |

---

## Messages

### CaseFileReference
Represents a file attachment reference for case updates:

| Field         | Type                           | Description                                      |
|---------------|--------------------------------|--------------------------------------------------|
| id            | string                         | Unique identifier for this file reference within the case update. |
| caseId        | string                         | Reference to the case this file belongs to.      |
| fileId        | string                         | FileMetadata.id from filerepository service - REQUIRED. |
| caption       | string                         | Optional caption/description for the file.       |
| displayName   | string                         | Optional display name (fallback to original filename). |
| displayOrder  | int32                          | Order for displaying files in UI (0-based).      |
| fileCategory  | string                         | Category of the file (e.g., "evidence_photo", "evidence_video", "evidence_audio", "evidence_document", "correspondence", "other"). |
| metadata      | google.protobuf.Struct         | Additional metadata about the file reference.    |

### CaseUpdateEntry
Represents a timeline entry for case updates:

| Field         | Type                           | Description                                      |
|---------------|--------------------------------|--------------------------------------------------|
| message       | string                         | Human‑readable event text.                       |
| eventTime     | string                         | ISO‑8601 timestamp of the event.                 |
| updateSource  | hero.situations.v2.UpdateSource | System / human source.                          |
| updaterId     | string                         | Asset or system id.                              |
| eventType     | string                         | Optional event type (e.g., "EVIDENCE_ADDED").    |
| displayName   | string                         | Cached display name.                             |
| data          | google.protobuf.Struct         | Arbitrary key-value pairs for additional context (e.g., evidence metadata, location). Stored as JSONB. |
| fileAttachments | repeated CaseFileReference   | References to files in filerepository.           |

### CaseStatusUpdateEntry
Records a status change:

| Field           | Type      | Description                                      |
|-----------------|-----------|--------------------------------------------------|
| timestamp       | string    | When change occurred (ISO‑8601).                 |
| newStatus       | CaseStatus| Target state.                                    |
| previousStatus  | CaseStatus| Previous state.                                  |
| note            | string    | Reason / context.                                |
| updaterId       | string    | Who made the change.                             |
| updateSource    | hero.situations.v2.UpdateSource | Source of update.                |

### CaseAssetAssociation
Links an asset to a case with a specific role:

| Field              | Type                    | Description                                      |
|--------------------|-------------------------|--------------------------------------------------|
| id                 | string                  | Unique association id.                           |
| caseId             | string                  | Redundant FK for audits.                         |
| assetId            | string                  | hero.assets.v2.Asset.id.                         |
| associationType    | CaseAssetAssociationType| Role of the asset.                              |
| assignedAt         | string                  | When link was created (ISO‑8601).                |
| notes              | string                  | Free‑text notes.                                 |
| assignerAssetId    | string                  | Who made/changed link.                           |

### CaseAuditLogEntry
Records an audit trail entry:

| Field         | Type          | Description                                      |
|---------------|---------------|--------------------------------------------------|
| id            | string        | UUID.                                            |
| caseId        | string        | Case identifier.                                 |
| action        | CaseAuditAction| Type of change.                                 |
| actorAssetId  | string        | Who made the change ("SYSTEM" if automated).     |
| timestamp     | string        | When change occurred (ISO‑8601).                 |
| fieldPath     | string        | JSONPath‑ish (e.g., "tags[2]").                  |
| oldValue      | string        | Previous value (JSON encoded).                   |
| newValue      | string        | New value (JSON encoded).                        |
| note          | string        | Optional context.                                |

### CaseSnapshot
Captures a versioned snapshot of a case:

| Field         | Type   | Description                                      |
|---------------|--------|--------------------------------------------------|
| caseId        | string | Case identifier.                                 |
| version       | int32  | Business snapshot version.                        |
| caseSnapshot  | Case   | Full case state at this version.                 |
| timestamp     | string | When snapshot was taken (ISO‑8601).              |

### Case
Main entity representing a case:

| Field                   | Type                          | Description                                      |
|-------------------------|-------------------------------|--------------------------------------------------|
| id                      | string                        | UUID‑v4.                                         |
| orgId                   | int32                         | Tenant / customer identifier.                    |
| type                    | CaseType                      | Category.                                        |
| title                   | string                        | Short human title.                               |
| description             | string                        | Long description / synopsis.                     |
| status                  | CaseStatus                    | Current lifecycle state.                         |
| priority                | int32                         | 1‑5 scale (higher = more urgent).               |
| situationIds            | repeated string               | Related situations.                              |
| reportIds               | repeated string               | Linked reports.                                  |
| entityRefs              | repeated hero.entity.v1.Reference | People, vehicles, etc.                      |
| propertyRefs            | repeated hero.property.v1.PropertyReference | Linked properties/evidence.           |
| assetAssociations       | repeated CaseAssetAssociation | All involved assets.                             |
| relatedCaseIds          | repeated string               | Parent / child / peer cases.                     |
| updates                 | repeated CaseUpdateEntry      | Narrative log.                                   |
| statusUpdates           | repeated CaseStatusUpdateEntry| State changes.                                   |
| tags                    | repeated string               | Search facets.                                   |
| additionalInfoJson      | google.protobuf.Struct        | Arbitrary KV.                                    |
| resourceType            | string                        | Constant "CASE".                                  |
| version                 | int32                         | Business snapshot version.                       |
| createTime              | string                        | Creation time (ISO‑8601).                        |
| updateTime              | string                        | Last update time (ISO‑8601).                     |
| dueDate                 | string                        | SLA target date (ISO‑8601). Can be set by user during creation/update. Must be in future. |
| resolvedTime            | string                        | Resolution time (ISO‑8601). Set automatically when status changes to RESOLVED. |
| closeTime               | string                        | Close time (ISO‑8601). Set automatically when status changes to CLOSED. |
| createdByAssetId        | string                        | Creator asset ID.                                |
| updatedByAssetId        | string                        | Last updater asset ID.                           |
| watcherAssetIds         | repeated string               | Assets who want notifications only.              |
| etag                    | int64                         | Optimistic‑locking token.                        |
| releaseStatus           | ReleaseStatus                 | Disclosure control flag (default un‑set = follow org policy). |

---

## Example Case Object

Here's a fully hydrated example of a Case object with all fields populated:

```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SAFETY_INCIDENT",
  "title": "Campus Library Disturbance Investigation",
  "description": "Investigation of reported noise disturbance and suspicious behavior in the main library during finals week",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 2,
  "situationIds": ["situation-456", "situation-789"],
  "reportIds": ["report-101", "report-102"],
  "entityRefs": [
    {
      "id": "entity-001",
      "type": "ENTITY_TYPE_PERSON",
      "name": "John Smith",
      "metadata": {
        "studentId": "S12345678",
        "department": "Computer Science",
        "year": "Junior"
      }
    },
    {
      "id": "entity-002",
      "type": "ENTITY_TYPE_LOCATION",
      "name": "Main Library - 3rd Floor Study Area",
      "metadata": {
        "building": "Main Library",
        "floor": "3",
        "room": "Study Area 3B",
        "cameraId": "CAM-3B-01"
      }
    },
    {
      "id": "entity-003",
      "type": "ENTITY_TYPE_PERSON",
      "name": "Dr. Sarah Johnson",
      "metadata": {
        "role": "Library Staff",
        "department": "Library Services",
        "employeeId": "E98765432"
      }
    }
  ],
  "assetAssociations": [
    {
      "id": "assoc-1",
      "caseId": "case-123",
      "assetId": "asset-001",
      "associationType": "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
      "assignedAt": "2025-04-23T12:30:00Z",
      "notes": "Lead campus police officer assigned to investigation",
      "assignerAssetId": "asset-003"
    },
    {
      "id": "assoc-2",
      "caseId": "case-123",
      "assetId": "asset-002",
      "associationType": "ASSET_ASSOCIATION_TYPE_WITNESS",
      "assignedAt": "2025-04-23T12:35:00Z",
      "notes": "Library staff member who reported the incident",
      "assignerAssetId": "asset-003"
    },
    {
      "id": "assoc-3",
      "caseId": "case-123",
      "assetId": "asset-003",
      "associationType": "ASSET_ASSOCIATION_TYPE_APPROVER",
      "assignedAt": "2025-04-23T12:40:00Z",
      "notes": "Campus police supervisor",
      "assignerAssetId": "asset-001"
    }
  ],
  "relatedCaseIds": ["case-456", "case-789"],
  "updates": [
    {
      "message": "Initial report received from library staff about noise disturbance",
      "eventTime": "2025-04-23T12:30:00Z",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-001",
      "eventType": "INCIDENT_REPORTED",
      "displayName": "Officer Michael Brown"
    },
    {
      "message": "Security camera footage reviewed, identified potential suspect",
      "eventTime": "2025-04-23T13:00:00Z",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-002",
      "eventType": "EVIDENCE_COLLECTED",
      "displayName": "Officer Sarah Wilson"
    },
    {
      "message": "Student identified and contacted for interview",
      "eventTime": "2025-04-23T14:30:00Z",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-001",
      "eventType": "INTERVIEW_CONDUCTED",
      "displayName": "Officer Michael Brown"
    }
  ],
  "statusUpdates": [
    {
      "timestamp": "2025-04-23T12:30:00Z",
      "newStatus": "CASE_STATUS_INVESTIGATING",
      "previousStatus": "CASE_STATUS_NEW",
      "note": "Initial investigation started by campus police",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    },
    {
      "timestamp": "2025-04-23T13:00:00Z",
      "newStatus": "CASE_STATUS_PENDING_INFORMATION",
      "previousStatus": "CASE_STATUS_INVESTIGATING",
      "note": "Waiting for student conduct office input",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    },
    {
      "timestamp": "2025-04-23T14:30:00Z",
      "newStatus": "CASE_STATUS_INVESTIGATING",
      "previousStatus": "CASE_STATUS_PENDING_INFORMATION",
      "note": "Student conduct office provided guidance, continuing investigation",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    }
  ],
  "tags": ["library", "noise-disturbance", "student-conduct", "finals-week", "campus-safety"],
  "additionalInfoJson": {
    "severityLevel": "moderate",
    "affectedAreas": ["Main Library", "Study Area 3B"],
    "estimatedResolutionTime": "48 hours",
    "riskScore": 4.5,
    "complianceImpact": ["FERPA", "Clery Act"],
    "incidentType": "noise_disturbance",
    "detectionSource": "staff_report",
    "businessImpact": "medium",
    "mitigationSteps": [
      "Review security footage",
      "Interview witnesses",
      "Contact student conduct office",
      "Issue warning if necessary"
    ],
    "campusLocation": {
      "building": "Main Library",
      "floor": "3",
      "room": "Study Area 3B",
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      }
    },
    "timeOfIncident": "2025-04-23T12:15:00Z",
    "weatherConditions": "Clear",
    "campusEvent": "Finals Week"
  },
  "resourceType": "CASES",
  "version": 3,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T14:30:00Z",
  "dueDate": "2025-04-25T12:00:00Z",
  "resolvedTime": null,
  "closeTime": null,
  "createdByAssetId": "asset-003",
  "updatedByAssetId": "asset-001",
  "watcherAssetIds": ["asset-004", "asset-005"],
  "etag": 3,
  "releaseStatus": "RELEASE_STATUS_INTERNAL"
}
```

This example shows a campus safety incident case that is currently under investigation. It includes:
- Basic case information (ID, type, title, description)
- Current status and priority
- Related situations and reports
- Entity references (students, locations, and staff involved)
- Asset associations with different roles (campus police officers, witnesses)
- Related cases
- Timeline of updates and status changes
- Tags for categorization
- Additional metadata in JSON format including campus-specific information
- Version tracking and audit information
- Watchers who receive notifications
- Disclosure control flag

---

## Overview of Endpoints

The Cases Module provides the following endpoints:

### Core CRUD Operations
1. **[CreateCase](#1-createcase)**
2. **[GetCase](#2-getcase)**
3. **[UpdateCase](#3-updatecase)**
4. **[DeleteCase](#4-deletecase)**

### Listing Operations
5. **[ListCases](#5-listcases)**
6. **[BatchGetCases](#6-batchgetcases)**
7. **[ListCasesBySituationId](#7-listcasesbysituationid)**
8. **[ListCasesByReportId](#8-listcasesbyreportid)**
9. **[ListCasesByAssetId](#9-listcasesbyassetid)**
10. **[ListCasesByEntityId](#10-listcasesbyentityid)**
11. **[ListCasesByPropertyId](#11-listcasesbypropertyid)**

### Relationship Management
12. **[AddSituationToCase](#12-addsituationtocase)**
13. **[RemoveSituationFromCase](#13-removesituationfromcase)**
14. **[AddReportToCase](#14-addreporttocase)**
15. **[RemoveReportFromCase](#15-removereportfromcase)**
16. **[AddEntityRefToCase](#16-addentityreftocase)**
17. **[RemoveEntityRefFromCase](#17-removeentityreffromcase)**
18. **[AddPropertyRefToCase](#18-addpropertyreftocase)**
19. **[RemovePropertyRefFromCase](#19-removepropertyreffromcase)**
20. **[LinkRelatedCase](#20-linkrelatedcase)**
21. **[UnlinkRelatedCase](#21-unlinkrelatedcase)**

### Asset Association Management
22. **[AssociateAssetToCase](#22-associateassettocase)**
23. **[UpdateAssetAssociation](#23-updateassetassociation)**
24. **[DisassociateAssetFromCase](#24-disassociateassetfromcase)**
25. **[ListAssetAssociationsForCase](#25-listassetassociationsforcase)**
26. **[AssignCase](#26-assigncase)**

### Watcher Management
27. **[AddWatcher](#27-addwatcher)**
28. **[RemoveWatcher](#28-removewatcher)**

### Status and Updates
29. **[UpdateCaseStatus](#29-updatecasestatus)**
30. **[AddCaseUpdate](#30-addcaseupdate)**
31. **[RemoveCaseUpdate](#31-removecaseupdate)**
32. **[ListCaseUpdates](#32-listcaseupdates)**
33. **[ListCaseStatusHistory](#33-listcasestatushistory)**

### Tags and Metadata
34. **[AddCaseTag](#34-addcasetag)**
35. **[RemoveCaseTag](#35-removecasetag)**
36. **[AddAdditionalInfo](#36-addadditionalinfo)**

### File Attachments
37. **[ListCaseFileAttachments](#37-listcasefileattachments)**

### Audit and Versioning
38. **[GetCaseVersion](#38-getcaseversion)**
39. **[ListCaseVersions](#39-listcaseversions)**
40. **[ListCaseAuditLog](#40-listcaseauditlog)**

### Search Operations
41. **[SearchCases](#41-searchcases)**

---

### 1. CreateCase

**Method:** `CreateCase`  
**Route:** `POST /hero.cases.v1.CaseService/CreateCase`

#### Notes
- **Transaction Safety**: The operation is wrapped in a database transaction to ensure atomicity
- **Version Control**: Creates initial version (1) and sets up version tracking
- **Audit Trail**: Automatically creates an audit log entry with `CASE_AUDIT_ACTION_CREATE`
- **Required Fields**: Must provide `orgId`, `type`, and at least one of `title` or `description`
- **Asset Tracking**: Records `createdByAssetId` for audit purposes
- **Optimistic Locking**: Initial `etag` is set to 1
- **Full Case Creation**: Supports creating a complete case with all relationships in a single call:
  - `situationIds` - Link to existing situations
  - `reportIds` - Link to existing reports
  - `entityRefs` - Add entity references (people, locations, etc.)
  - `assetAssociations` - Create asset associations with roles
  - `relatedCaseIds` - Link to other cases
  - `watcherAssetIds` - Add watchers for notifications
  - `tags` - Add case tags
  - `additionalInfoJson` - Set additional metadata
- **Timestamp Management**:
  - All timestamps are managed internally - user-provided values are ignored
  - `create_time` and `update_time` are set to current time
  - `resolved_time` is only set if status is RESOLVED
  - `close_time` is only set if status is CLOSED
  - `due_date` is optional but must be in RFC3339Nano format and in the future if provided
  - Validation ensures timestamps match status (e.g., resolved_time only set for RESOLVED status)
- **Field Behavior**:
  - System-managed fields (set internally):
    - `id` - Auto-generated UUID if not provided
    - `org_id` - Set from context
    - `create_time` - Set to current time
    - `update_time` - Set to current time
    - `resolved_time` - Set to current time if status is RESOLVED
    - `close_time` - Set to current time if status is CLOSED
    - `created_by_asset_id` - Set from context if not provided
    - `updated_by_asset_id` - Set to created_by_asset_id
    - `version` - Set to 1
    - `etag` - Set to 1
  - User-provided fields (validated):
    - `due_date` - Must be in RFC3339Nano format and in the future
    - `status` - Must be valid enum value
    - `type` - Must be valid enum value
    - `priority` - Must be valid value
    - `release_status` - Must be valid enum value
    - `title` - Optional string
    - `description` - Optional string
    - `additional_info_json` - Optional JSON object
    - `situationIds` - Optional list of situation IDs
    - `reportIds` - Optional list of report IDs
    - `entityRefs` - Optional list of entity references
    - `assetAssociations` - Optional list of asset associations
    - `relatedCaseIds` - Optional list of related case IDs
    - `watcherAssetIds` - Optional list of watcher asset IDs
    - `tags` - Optional list of tags
- **Gotchas**:
  - If `id` is not provided, a UUID is auto-generated
  - Empty strings are stored as NULL in the database
  - `additionalInfoJson` must be valid JSON, invalid JSON will be replaced with "{}"
  - If `createdByAssetId` is not provided, falls back to context username
  - Time fields are always stored in UTC with nanosecond precision
  - All relationships are created in a single transaction
  - Invalid relationship IDs will cause the entire creation to fail
  - Asset associations require valid asset IDs and association types
  - Entity references must have valid entity types and IDs
  - Related case IDs must exist and be in the same organization
  - Watcher asset IDs must exist and be in the same organization
- **Security Risks**:
  - No rate limiting on case creation
  - No size limits on description or additionalInfoJson
  - No sanitization of HTML content in fields
  - No validation of relationship permissions
  - No validation of asset association permissions
  - No validation of entity reference permissions
  - No validation of related case permissions
  - No validation of watcher permissions

#### Request

**CreateCaseRequest:**

| Field | Type  | Description                                      |
|-------|-------|--------------------------------------------------|
| case  | Case  | Case object to be created. Note: id/etag set by server. |

**Sample Request (JSON):**
```json
{
  "case": {
    "title": "Security Breach Investigation",
    "type": "CASE_TYPE_SECURITY_INCIDENT",
    "description": "Investigation of unauthorized access attempt",
    "status": "CASE_STATUS_NEW",
    "priority": 1,
    "orgId": 123
  }
}
```

#### Response

Returns the newly created `Case` object with server-generated fields.

**Sample Response (JSON):**
```json
{
  "case": {
    "id": "case-123",
    "orgId": 123,
    "type": "CASE_TYPE_SECURITY_INCIDENT",
    "title": "Security Breach Investigation",
    "description": "Investigation of unauthorized access attempt",
    "status": "CASE_STATUS_NEW",
    "priority": 1,
    "resourceType": "CASES",
    "version": 1,
    "createTime": "2025-04-23T12:00:00Z",
    "updateTime": "2025-04-23T12:00:00Z",
    "etag": 1
  }
}
```

---

### 2. GetCase

**Method:** `GetCase`  
**Route:** `POST /hero.cases.v1.CaseService/GetCase`

#### Notes
- **Performance**: Returns full case object including all relationships and history
- **Caching**: Consider implementing client-side caching for frequently accessed cases
- **Audit Trail**: Automatically creates a `CASE_AUDIT_ACTION_VIEW` entry
- **Error Handling**: Returns `ErrCaseNotFound` if case doesn't exist
- **Data Freshness**: Returns the latest version of the case
- **Security Risks**:
  - Audit trail may expose sensitive information about who viewed the case
  - No rate limiting on case retrieval
  - Full case history is returned without pagination
  - No filtering of sensitive fields based on user role

#### Request

**GetCaseRequest:**

| Field | Type   | Description                   |
|-------|--------|-------------------------------|
| id    | string | Unique case identifier.       |

**Sample Request (JSON):**
```json
{
  "id": "case-123"
}
```

#### Response

Returns the complete `Case` object.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "description": "Investigation of unauthorized access attempt",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "situationIds": ["situation-456"],
  "reportIds": ["report-789"],
  "assetAssociations": [
    {
      "id": "assoc-1",
      "caseId": "case-123",
      "assetId": "asset-001",
      "associationType": "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
      "assignedAt": "2025-04-23T12:30:00Z"
    }
  ],
  "updates": [
    {
      "message": "Initial investigation started",
      "eventTime": "2025-04-23T12:30:00Z",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-001",
      "displayName": "John Doe"
    }
  ],
  "statusUpdates": [
    {
      "timestamp": "2025-04-23T12:30:00Z",
      "newStatus": "CASE_STATUS_INVESTIGATING",
      "previousStatus": "CASE_STATUS_NEW",
      "note": "Investigation initiated",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    }
  ],
  "resourceType": "CASES",
  "version": 2,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T12:30:00Z",
  "etag": 2
}
```

---

### 3. UpdateCase

**Method:** `UpdateCase`  
**Route:** `POST /hero.cases.v1.CaseService/UpdateCase`

#### Notes
- **Optimistic Locking**: Requires valid `etag` to prevent concurrent modifications
- **Transaction Safety**: All updates are atomic within a transaction
- **Version Control**: Automatically increments version number
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_UPDATE` entries for changed fields
- **Snapshot Creation**: Creates a new case snapshot after successful update
- **Error Handling**: Returns error if `etag` mismatch or case not found
- **Performance**: Consider batching multiple field updates in a single call
- **Field Update Behavior**:
  - Fields that can be updated through payload:
    - `title` - Case title
    - `description` - Case description
    - `type` - Case type
    - `status` - Case status
    - `priority` - Case priority (1-5 scale)
    - `release_status` - Release status
    - `due_date` - Must be in RFC3339Nano format and in the future, passing "0" will clear out the Due Date
    - `additional_info_json` - Merged with existing data
    - `tags` - Replaces all existing tags with the provided list. If an empty list is provided, all tags are removed. If the field is omitted, tags remain unchanged.
  - Fields managed internally (cannot be updated through payload):
    - `id` - Case identifier
    - `org_id` - Organization identifier
    - `create_time` - Creation timestamp
    - `update_time` - Last update timestamp
    - `resolved_time` - Set/cleared based on status
    - `close_time` - Set/cleared based on status
    - `created_by_asset_id` - Creator identifier
    - `updated_by_asset_id` - Set from context
    - `etag` - Auto-incremented
    - `version` - Auto-incremented
    - `resource_type` - Constant "CASES"
  - Relationship fields (must use dedicated APIs):
    - `situation_ids` - Use AddSituationToCase/RemoveSituationFromCase
    - `report_ids` - Use AddReportToCase/RemoveReportFromCase
    - `entity_refs` - Use AddEntityRefToCase/RemoveEntityRefFromCase
    - `asset_associations` - Use AssociateAssetToCase/UpdateAssetAssociation/DisassociateAssetFromCase
    - `related_case_ids` - Use LinkRelatedCase/UnlinkRelatedCase
    - `watcher_asset_ids` - Use AddWatcher/RemoveWatcher
- **Timestamp Handling**:
  - `updateTime` is always set internally to current UTC time
  - `resolvedTime` is automatically set when status changes to RESOLVED
  - `closeTime` is automatically set when status changes to CLOSED
  - User-provided timestamps are ignored
  - When status changes from RESOLVED/CLOSED, respective timestamps are preserved
- **Due Date Handling**:
  - Can be set or updated to any future date
  - Must be in RFC3339Nano format
  - Can be cleared by setting to empty string
  - Omitting due date preserves existing value
- **Status-based Timestamps**:
  - `resolvedTime` is only set/cleared when status changes to/from RESOLVED
  - `closeTime` is only set/cleared when status changes to/from CLOSED
  - Existing timestamps are preserved when status remains unchanged
  - Timestamps are cleared when status changes away from RESOLVED/CLOSED
- **Gotchas**:
  - Optimistic locking fails if etag doesn't match, requiring client retry
  - Empty strings are converted to NULL in database
  - Invalid JSON in additionalInfoJson is replaced with "{}"
  - All timestamps must be in RFC3339Nano format
  - Updates to most related entities (situations, reports, etc.) require separate calls, except for `tags` which can be fully replaced.
  - Version number is incremented only when the status field changes (i.e., when this endpoint is called and the status actually changes)
  - Due date must be in the future, validation error if not
  - Due date format must be RFC3339Nano, validation error if not
- **Security Risks**:
  - No rate limiting on updates
  - No sanitization of HTML content in fields
  - No validation of status transitions
  - No size limits on updated fields
  - No validation of due date against business rules
  - No logging of timestamp manipulation attempts

#### Request

**UpdateCaseRequest:**

| Field          | Type  | Description                                      |
|----------------|-------|--------------------------------------------------|
| case           | Case  | Updated case object. Must include last-seen etag.|
| expectedEtag   | int64 | Same token duplicated for clarity.               |

**Sample Request (JSON):**
```json
{
  "case": {
    "id": "case-123",
    "title": "Updated Security Breach Investigation",
    "description": "Updated investigation details",
    "etag": 2
  },
  "expectedEtag": 2
}
```

#### Response

Returns the updated `Case` object.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Updated Security Breach Investigation",
  "description": "Updated investigation details",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "resourceType": "CASES",
  "version": 3,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T13:00:00Z",
  "etag": 3
}
```

---

### 4. DeleteCase

**Method:** `DeleteCase`  
**Route:** `POST /hero.cases.v1.CaseService/DeleteCase`

#### Notes
- **Permanent Deletion**: This is a hard delete operation
- **Transaction Safety**: Deletion is atomic within a transaction
- **Audit Trail**: Creates final `CASE_AUDIT_ACTION_DELETE` entry
- **Cascade Behavior**: Does not automatically delete related entities
- **Error Handling**: Returns `ErrCaseNotFound` if case doesn't exist
- **Considerations**: 
  - Consider soft delete for compliance requirements
  - Ensure proper cleanup of related resources
  - May impact related cases and reports
- **Gotchas**:
  - No way to recover deleted cases
  - Related records in other tables must be manually cleaned up
  - Audit logs are also deleted
  - Case snapshots are permanently removed
  - Active watchers are not notified of deletion
  - Related cases maintain their links to deleted case
- **Security Risks**:
  - No rate limiting on deletions
  - No confirmation required for deletion
  - No validation of related entity impacts
  - No backup of deleted data
  - No logging of deletion reason

#### Request

**DeleteCaseRequest:**

| Field | Type   | Description                   |
|-------|--------|-------------------------------|
| id    | string | Unique case identifier.       |

**Sample Request (JSON):**
```json
{
  "id": "case-123"
}
```

#### Response

Returns an empty response on success.

---

### 5. ListCases

**Method:** `ListCases`  
**Route:** `POST /hero.cases.v1.CaseService/ListCases`

#### Notes
- **Pagination**: 
  - Default page size is applied if not specified (50)
  - Maximum page size is enforced (100)
  - Use `nextPageToken` for subsequent pages
- **Filtering**: 
  - Supports complex filter expressions
  - Consider performance impact of complex filters
- **Sorting**: 
  - Supports multiple sort fields
  - Default sort is by `createTime` descending
- **Performance**: 
  - Consider implementing caching for common queries
  - Complex filters may impact query performance
- **Error Handling**: Returns empty list if no matches found
- **Gotchas**:
  - Only specific columns are allowed in ORDER BY to prevent SQL injection:
    - update_time
    - create_time
    - priority
    - status
    - type
    - release_status
  - Invalid sort fields will result in an error
  - Page size is silently capped at 100 if a larger value is requested
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on case visibility
  - Filter expressions may expose sensitive data
  - No validation of organization ID against user's permissions
  - No field-level access control
  - Large result sets may impact performance

#### Request

**ListCasesRequest:**

| Field       | Type   | Description                                      |
|-------------|--------|--------------------------------------------------|
| pageSize    | int32  | Number of results per page.                      |
| pageToken   | string | Token for next page.                             |
| filter      | string | Optional filter expression.                      |
| orderBy     | string | Optional sort expression.                        |

**Sample Request (JSON):**
```json
{
  "pageSize": 10,
  "pageToken": "",
  "filter": "status = 'CASE_STATUS_INVESTIGATING'",
  "orderBy": "createTime desc"
}
```

#### Response

**ListCasesResponse:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| cases       | repeated Case | List of cases.                                   |
| nextPageToken| string       | Token for next page.                             |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 6. BatchGetCases

**Method:** `BatchGetCases`  
**Route:** `POST /hero.cases.v1.CaseService/BatchGetCases`

#### Notes
- **Performance**: 
  - Optimized for retrieving multiple cases in a single request
  - Consider implementing client-side caching
- **Error Handling**: 
  - Returns empty case objects for IDs that don't exist
  - Output array length always matches input array length
  - Empty cases contain only the requested ID
  - Individual case errors don't fail the entire batch
- **Limitations**: 
  - Consider request size limits for large batches
  - May impact database performance with very large batches
- **Usage**: 
  - Preferred over multiple individual GetCase calls
  - Useful for bulk operations and data synchronization
  - Maintains input order in output array
- **Gotchas**:
  - Empty case objects only contain the ID field
  - No way to distinguish between empty and non-existent cases
  - All other fields are unset in empty cases
  - Empty cases still count towards batch size limits
- **Security Risks**:
  - No rate limiting on batch operations
  - No access control checks on case visibility
  - No validation of organization ID against user's permissions
  - No size limits on batch requests
  - May expose sensitive data in bulk
  - No field-level access control

#### Request

**BatchGetCasesRequest:**

| Field   | Type            | Description                                      |
|---------|-----------------|--------------------------------------------------|
| ids     | repeated string | List of case IDs to fetch.                       |

**Sample Request (JSON):**
```json
{
  "ids": ["case-123", "case-456"]
}
```

#### Response

**BatchGetCasesResponse:**

| Field   | Type          | Description                                      |
|---------|---------------|--------------------------------------------------|
| cases   | repeated Case | List of requested cases.                         |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    },
    {
      "id": "case-456",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Another Investigation",
      "status": "CASE_STATUS_NEW",
      "priority": 2,
      "resourceType": "CASES",
      "version": 1,
      "createTime": "2025-04-23T13:00:00Z",
      "updateTime": "2025-04-23T13:00:00Z",
      "etag": 1
    }
  ]
}
```

---

### 7. ListCasesBySituationId

**Method:** `ListCasesBySituationId`  
**Route:** `POST /hero.cases.v1.CaseService/ListCasesBySituationId`

#### Notes
- **Performance**: 
  - Optimized for retrieving cases linked to a specific situation
  - Consider implementing caching for frequently accessed situations
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if situation not found
  - Returns empty list if no cases linked
- **Usage**: 
  - Useful for situation impact analysis
  - Helps track related investigations
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on situation visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive case data through situation links
  - No field-level access control
  - No validation of situation ID against organization

#### Request

**ListCasesBySituationIdRequest:**

| Field       | Type   | Description                                      |
|-------------|--------|--------------------------------------------------|
| situationId | string | ID of the situation to list cases for.           |
| pageSize    | int32  | Number of results per page.                      |
| pageToken   | string | Token for next page.                             |

**Sample Request (JSON):**
```json
{
  "situationId": "situation-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCasesBySituationIdResponse:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| cases       | repeated Case | List of cases linked to the situation.           |
| nextPageToken| string       | Token for next page.                             |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "situationIds": ["situation-123"],
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 8. ListCasesByReportId

**Method:** `ListCasesByReportId`  
**Route:** `POST /hero.cases.v1.CaseService/ListCasesByReportId`

#### Notes
- **Performance**: 
  - Optimized for retrieving cases linked to a specific report
  - Consider implementing caching for frequently accessed reports
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if report not found
  - Returns empty list if no cases linked
- **Usage**: 
  - Useful for report impact analysis
  - Helps track related investigations
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on report visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive case data through report links
  - No field-level access control
  - No validation of report ID against organization

#### Request

**ListCasesByReportIdRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| reportId  | string | ID of the report to list cases for.              |
| pageSize  | int32  | Number of results per page.                      |
| pageToken | string | Token for next page.                             |

**Sample Request (JSON):**
```json
{
  "reportId": "report-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCasesByReportIdResponse:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| cases       | repeated Case | List of cases linked to the report.              |
| nextPageToken| string       | Token for next page.                             |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "reportIds": ["report-123"],
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 9. ListCasesByAssetId

**Method:** `ListCasesByAssetId`  
**Route:** `POST /hero.cases.v1.CaseService/ListCasesByAssetId`

#### Notes
- **Performance**: 
  - Optimized for retrieving cases associated with a specific asset
  - Consider implementing caching for frequently accessed assets
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if asset not found
  - Returns empty list if no cases associated
- **Usage**: 
  - Useful for asset involvement tracking
  - Helps manage asset workload and responsibilities
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive case data through asset associations
  - No field-level access control
  - No validation of asset ID against organization

#### Request

**ListCasesByAssetIdRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| assetId  | string | ID of the asset to list cases for.               |
| pageSize | int32  | Number of results per page.                      |
| pageToken| string | Token for next page.                             |

**Sample Request (JSON):**
```json
{
  "assetId": "asset-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCasesByAssetIdResponse:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| cases       | repeated Case | List of cases linked to the asset.               |
| nextPageToken| string       | Token for next page.                             |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "assetAssociations": [
        {
          "id": "assoc-1",
          "caseId": "case-123",
          "assetId": "asset-123",
          "associationType": "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
          "assignedAt": "2025-04-23T12:30:00Z"
        }
      ],
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 10. ListCasesByEntityId

**Method:** `ListCasesByEntityId`  
**Route:** `POST /hero.cases.v1.CaseService/ListCasesByEntityId`

#### Notes
- **Performance**: 
  - Optimized for retrieving cases referencing a specific entity
  - Consider implementing caching for frequently accessed entities
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if entity not found
  - Returns empty list if no cases reference the entity
- **Usage**: 
  - Useful for entity involvement tracking
  - Helps track entity history across cases
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on entity visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive case data through entity references
  - No field-level access control
  - No validation of entity ID against organization

#### Request

**ListCasesByEntityIdRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| entityId  | string | ID of the entity to list cases for.              |
| pageSize  | int32  | Number of results per page.                      |
| pageToken | string | Token for next page.                             |

**Sample Request (JSON):**
```json
{
  "entityId": "entity-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCasesByEntityIdResponse:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| cases       | repeated Case | List of cases linked to the entity.              |
| nextPageToken| string       | Token for next page.                             |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "entityRefs": [
        {
          "id": "entity-123",
          "type": "ENTITY_TYPE_PERSON",
          "name": "John Doe"
        }
      ],
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 11. ListCasesByPropertyId

**Method:** `ListCasesByPropertyId`  
**Route:** `POST /hero.cases.v1.CaseService/ListCasesByPropertyId`

#### Request

**ListCasesByPropertyIdRequest:**

| Field       | Type   | Description                                      |
|-------------|--------|--------------------------------------------------|
| propertyId  | string | ID of the property to list cases for.            |
| pageSize    | int32  | Number of results per page.                      |
| pageToken   | string | Token for next page.                             |

**Sample Request (JSON):**
```json
{
  "propertyId": "prop-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCasesResponse:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| cases       | repeated Case | List of cases linked to the property.            |
| nextPageToken| string       | Token for next page.                             |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-123",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Security Breach Investigation",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "resourceType": "CASES",
      "version": 2,
      "createTime": "2025-04-23T12:00:00Z",
      "updateTime": "2025-04-23T12:30:00Z",
      "etag": 2
    }
  ],
  "nextPageToken": "next-page-token"
}
```

---

### 11. AddSituationToCase

**Method:** `AddSituationToCase`  
**Route:** `POST /hero.cases.v1.CaseService/AddSituationToCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or situation not found
  - Returns error if situation already linked
- **Performance**: Consider batching multiple situation additions
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on situation visibility
  - No validation of organization ID against user's permissions
  - No validation of situation ID against organization
  - May expose sensitive case data through situation links
  - No validation of situation type against case type

#### Request

**AddSituationToCaseRequest:**

| Field       | Type   | Description                                      |
|-------------|--------|--------------------------------------------------|
| caseId      | string | ID of the case.                                  |
| situationId | string | ID of the situation to add.                      |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "situationId": "situation-456"
}
```

#### Response

Returns the updated `Case` object.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "situationIds": ["situation-456"],
  "resourceType": "CASES",
  "version": 3,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T13:00:00Z",
  "etag": 3
}
```

---

### 12. RemoveSituationFromCase

**Method:** `RemoveSituationFromCase`  
**Route:** `POST /hero.cases.v1.CaseService/RemoveSituationFromCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or situation not found
  - Returns error if situation not linked
- **Impact**: May affect case status or workflow
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on situation visibility
  - No validation of organization ID against user's permissions
  - No validation of situation ID against organization
  - May break case dependencies
  - No validation of impact on case status

#### Request

**RemoveSituationFromCaseRequest:**

| Field       | Type   | Description                                      |
|-------------|--------|--------------------------------------------------|
| caseId      | string | ID of the case.                                  |
| situationId | string | ID of the situation to remove.                   |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "situationId": "situation-456"
}
```

#### Response

Returns the updated `Case` object.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "situationIds": [],
  "resourceType": "CASES",
  "version": 4,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T13:30:00Z",
  "etag": 4
}
```

---

### 13. AddReportToCase

**Method:** `AddReportToCase`  
**Route:** `POST /hero.cases.v1.CaseService/AddReportToCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or report not found
  - Returns error if report already linked
- **Performance**: Consider batching multiple report additions
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on report visibility
  - No validation of organization ID against user's permissions
  - No validation of report ID against organization
  - May expose sensitive case data through report links
  - No validation of report type against case type

#### Request

**AddReportToCaseRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| caseId    | string | ID of the case to add the report to.             |
| reportId  | string | ID of the report to link.                        |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "reportId": "report-789"
}
```

#### Response

Returns the updated `Case` object with the new report link.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_INVESTIGATING",
  "reportIds": ["report-789"],
  "resourceType": "CASES",
  "version": 5,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T14:00:00Z",
  "etag": 5
}
```

---

### 14. RemoveReportFromCase

**Method:** `RemoveReportFromCase`  
**Route:** `POST /hero.cases.v1.CaseService/RemoveReportFromCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or report not found
  - Returns error if report not linked
- **Impact**: May affect case documentation and evidence
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on report visibility
  - No validation of organization ID against user's permissions
  - No validation of report ID against organization
  - May break case dependencies
  - No validation of impact on case documentation

#### Request

**RemoveReportFromCaseRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| caseId    | string | ID of the case to remove the report from.        |
| reportId  | string | ID of the report to unlink.                      |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "reportId": "report-789"
}
```

#### Response

Returns the updated `Case` object with the report link removed.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_INVESTIGATING",
  "reportIds": [],
  "resourceType": "CASES",
  "version": 6,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T14:30:00Z",
  "etag": 6
}
```

---

### 16. AddEntityRefToCase

**Method:** `AddEntityRefToCase`  
**Route:** `POST /hero.cases.v1.CaseService/AddEntityRefToCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if entity reference is invalid
- **Validation**: 
  - Entity type must be valid
  - Entity ID must be valid
  - Entity name is required
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on entity visibility
  - No validation of organization ID against user's permissions
  - No validation of entity ID against organization
  - May expose sensitive case data through entity references
  - No validation of entity type against case type
  - No sanitization of entity metadata

#### Request

**AddEntityRefToCaseRequest:**

| Field      | Type                    | Description                                      |
|------------|-------------------------|--------------------------------------------------|
| caseId     | string                  | ID of the case to add the entity reference to.   |
| entityRef  | hero.entity.v1.Reference | Entity reference to link.                       |

**Sample Request (JSON):**
```json
{
  "caseId": "379d05cb-ce73-4e4f-9f10-b56d67a85ee5",
  "entityRef": {
    "id": "8fdea12c-53e4-444e-9115-f4e4bbd1c0c9",
    "type": "ENTITY_TYPE_PERSON",
    "displayName": "John Doe",
    "relationType": "Boyfriend"
  }
}
```

#### Response

Returns the updated `Case` object with the new entity reference.

**Sample Response (JSON):**
```json
{
    "id": "379d05cb-ce73-4e4f-9f10-b56d67a85ee5",
    "orgId": 1,
    "type": "CASE_TYPE_SECURITY_INCIDENT",
    "title": "Security Breach Investigation",
    "description": "Investigation of unauthorized access attempt",
    "status": "CASE_STATUS_NEW",
    "priority": 1,
    "reportIds": [
        "6ac39f95-3b07-43a7-88bb-5525362c5d41"
    ],
    "entityRefs": [
        {
            "id": "8fdea12c-53e4-444e-9115-f4e4bbd1c0c9",
            "type": "ENTITY_TYPE_PERSON",
            "displayName": "John Doe",
            "relationType": "Boyfriend"
        }
    ],
    "additionalInfoJson": {},
    "resourceType": "CASES",
    "version": 1,
    "createTime": "2025-05-12T10:24:11.300459Z",
    "updateTime": "2025-05-12T10:24:11.300459Z",
    "createdByAssetId": "cognito:981163b0-e091-70a0-49ad-7c03d6c34419",
    "updatedByAssetId": "cognito:981163b0-e091-70a0-49ad-7c03d6c34419",
    "etag": "1"
}
```

---

### 17. RemoveEntityRefFromCase

**Method:** `RemoveEntityRefFromCase`  
**Route:** `POST /hero.cases.v1.CaseService/RemoveEntityRefFromCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if entity reference not found
- **Impact**: May affect case relationships and tracking
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on entity visibility
  - No validation of organization ID against user's permissions
  - No validation of entity ID against organization
  - May break case dependencies
  - No validation of impact on case relationships

#### Request

**RemoveEntityRefFromCaseRequest:**

| Field     | Type                    | Description                                      |
|-----------|-------------------------|--------------------------------------------------|
| caseId    | string                  | ID of the case to remove the entity reference from. |
| entityRef | hero.entity.v1.Reference | Entity reference to unlink.                     |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "entityRef": {
    "id": "entity-001",
    "type": "ENTITY_TYPE_PERSON",
    "name": "John Doe"
  }
}
```

#### Response

Returns the updated `Case` object with the entity reference removed.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "entityRefs": [],
  "resourceType": "CASES",
  "version": 8,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T15:30:00Z",
  "etag": 8
}
```

---

### 17. LinkRelatedCase

**Method:** `LinkRelatedCase`  
**Route:** `POST /hero.cases.v1.CaseService/LinkRelatedCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if either case not found
  - Returns error if cases already linked
  - Returns error if attempting to link to self
- **Validation**: 
  - Prevents circular references
  - Ensures cases exist in same organization
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on case visibility
  - No validation of organization ID against user's permissions
  - No validation of case ID against organization
  - May create circular dependencies
  - No validation of case type compatibility
  - May expose sensitive case data through links

#### Request

**LinkRelatedCaseRequest:**

| Field          | Type   | Description                                      |
|----------------|--------|--------------------------------------------------|
| caseId         | string | ID of the primary case.                          |
| relatedCaseId  | string | ID of the case to link as related.               |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "relatedCaseId": "case-456"
}
```

#### Response

Returns the updated `Case` object with the new related case link.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "relatedCaseIds": ["case-456"],
  "resourceType": "CASES",
  "version": 9,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T16:00:00Z",
  "etag": 9
}
```

---

### 18. UnlinkRelatedCase

**Method:** `UnlinkRelatedCase`  
**Route:** `POST /hero.cases.v1.CaseService/UnlinkRelatedCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_RELATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if either case not found
  - Returns error if cases not linked
- **Impact**: May affect case relationships and tracking
- **Security Risks**:
  - No rate limiting on relationship operations
  - No access control checks on case visibility
  - No validation of organization ID against user's permissions
  - No validation of case ID against organization
  - May break case dependencies
  - No validation of impact on case relationships

#### Request

**UnlinkRelatedCaseRequest:**

| Field          | Type   | Description                                      |
|----------------|--------|--------------------------------------------------|
| caseId         | string | ID of the primary case.                          |
| relatedCaseId  | string | ID of the case to unlink.                        |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "relatedCaseId": "case-456"
}
```

#### Response

Returns the updated `Case` object with the related case link removed.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "relatedCaseIds": [],
  "resourceType": "CASES",
  "version": 10,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T16:30:00Z",
  "etag": 10
}
```

---

### 19. AssociateAssetToCase

**Method:** `AssociateAssetToCase`  
**Route:** `POST /hero.cases.v1.CaseService/AssociateAssetToCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_ASSOCIATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or asset not found
  - Returns error if asset already associated
- **Validation**: 
  - Association type must be valid
  - Asset must exist and be active
  - Asset must be in same organization
- **Gotchas**:
  - Cannot associate same asset with different roles
  - Association ID is auto-generated and cannot be specified
  - Notes field has no size limit but large notes may impact performance
  - Asset permissions are not automatically granted
  - Previous associations are preserved
  - Association timestamps use server time, not client time
- **Security Risks**:
  - No rate limiting on association operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - No validation of asset ID against organization
  - No validation of asset role permissions
  - May expose sensitive case data through asset associations
  - No validation of asset type against case type

#### Request

**AssociateAssetToCaseRequest:**

| Field           | Type                    | Description                                      |
|-----------------|-------------------------|--------------------------------------------------|
| caseId          | string                  | ID of the case to associate the asset with.      |
| association     | CaseAssetAssociation    | Association details including asset ID, type, and notes. |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "association": {
    "assetId": "asset-001",
    "associationType": "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
    "notes": "Lead investigator for this case"
  }
}
```

#### Response

Returns the created `CaseAssetAssociation` object.

**Sample Response (JSON):**
```json
{
  "association": {
    "id": "assoc-1",
    "caseId": "case-123",
    "assetId": "asset-001",
    "associationType": "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
    "assignedAt": "2025-04-23T17:00:00Z",
    "notes": "Lead investigator for this case",
    "assignerAssetId": "asset-002"
  }
}
```

---

### 20. UpdateAssetAssociation

**Method:** `UpdateAssetAssociation`  
**Route:** `POST /hero.cases.v1.CaseService/UpdateAssetAssociation`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_ASSOCIATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if association not found
  - Returns error if new association type is invalid
- **Validation**: 
  - New association type must be valid
  - Notes are optional but recommended
- **Gotchas**:
  - Assignment timestamp is preserved
  - Assigner ID is updated to current user
  - Notes are completely replaced, not merged
  - Association type changes are tracked in audit log
- **Security Risks**:
  - No rate limiting on association operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - No validation of asset ID against organization
  - No validation of asset role permissions
  - May expose sensitive case data through asset associations
  - No validation of asset type against case type
  - No validation of role transition permissions

#### Request

**UpdateAssetAssociationRequest:**

| Field           | Type                    | Description                                      |
|-----------------|-------------------------|--------------------------------------------------|
| caseId          | string                  | ID of the case containing the association.       |
| association     | CaseAssetAssociation    | Updated association details.                     |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "association": {
    "id": "assoc-1",
    "assetId": "asset-001",
    "associationType": "ASSET_ASSOCIATION_TYPE_INVESTIGATOR",
    "notes": "Updated role to investigator"
  }
}
```

#### Response

Returns the updated `CaseAssetAssociation` object.

**Sample Response (JSON):**
```json
{
  "association": {
    "id": "assoc-1",
    "caseId": "case-123",
    "assetId": "asset-001",
    "associationType": "ASSET_ASSOCIATION_TYPE_INVESTIGATOR",
    "assignedAt": "2025-04-23T17:00:00Z",
    "notes": "Updated role to investigator",
    "assignerAssetId": "asset-002"
  }
}
```

---

### 21. DisassociateAssetFromCase

**Method:** `DisassociateAssetFromCase`  
**Route:** `POST /hero.cases.v1.CaseService/DisassociateAssetFromCase`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_ASSOCIATION` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or asset not found
  - Returns error if asset not associated
- **Impact**: 
  - May affect case assignments
  - May impact notifications and permissions
- **Gotchas**:
  - Association history is preserved in audit log
  - No automatic cleanup of related permissions
  - No automatic notification to disassociated asset
  - No validation of impact on case workflow
  - No automatic reassignment of tasks
- **Security Risks**:
  - No rate limiting on association operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - No validation of asset ID against organization
  - May break case dependencies
  - No validation of impact on case assignments
  - No validation of impact on notifications

#### Request

**DisassociateAssetFromCaseRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| caseId   | string | ID of the case to remove the asset from.         |
| associationId | string | ID of the association to remove.             |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "associationId": "assoc-1"
}
```

#### Response

Returns an empty message (google.protobuf.Empty).

---

### 22. ListAssetAssociationsForCase

**Method:** `ListAssetAssociationsForCase`  
**Route:** `POST /hero.cases.v1.CaseService/ListAssetAssociationsForCase`

#### Notes
- **Performance**: 
  - Optimized for retrieving all asset associations
  - Consider implementing caching for frequently accessed cases
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if case not found
  - Returns empty list if no associations exist
- **Usage**: 
  - Useful for case team management
  - Helps track asset involvement
- **Gotchas**:
  - Results are sorted by assignedAt timestamp
  - Full association details are returned
  - No filtering by association type
  - No filtering by asset ID
  - No filtering by assigner
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive asset data through associations
  - No field-level access control
  - No validation of case ID against organization

#### Request

**ListAssetAssociationsForCaseRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| caseId    | string | ID of the case to list associations for.         |
| pageSize  | int32  | Maximum number of associations to return.        |
| pageToken | string | Token for retrieving a specific page.            |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListAssetAssociationsForCaseResponse:**

| Field          | Type                        | Description                                      |
|----------------|-----------------------------|--------------------------------------------------|
| associations   | repeated CaseAssetAssociation | List of asset associations.                    |
| nextPageToken  | string                      | Token for retrieving the next page.              |

**Sample Response (JSON):**
```json
{
  "associations": [
    {
      "id": "assoc-1",
      "caseId": "case-123",
      "assetId": "asset-001",
      "associationType": "ASSET_ASSOCIATION_TYPE_INVESTIGATOR",
      "assignedAt": "2025-04-23T17:00:00Z",
      "notes": "Updated role to investigator",
      "assignerAssetId": "asset-002"
    },
    {
      "id": "assoc-2",
      "caseId": "case-123",
      "assetId": "asset-003",
      "associationType": "ASSET_ASSOCIATION_TYPE_OBSERVER",
      "assignedAt": "2025-04-23T17:30:00Z",
      "notes": "Monitoring case progress",
      "assignerAssetId": "asset-002"
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

### 23. AssignCase

**Method:** `AssignCase`  
**Route:** `POST /hero.cases.v1.CaseService/AssignCase`

#### Notes
- **Primary Purpose**: Assigns a primary investigator to a case
- **Automatic Reopening**: If case is in a terminal state (ON_HOLD, RESOLVED, CLOSED, ARCHIVED), automatically reopens to OPEN status
- **Side Effects**: 
  - Creates or replaces primary investigator association
  - Triggers MANAGE_CASE order creation for the new investigator
  - Cancels existing MANAGE_CASE orders if investigator changes
  - Updates case status if needed
- **Transaction Safety**: All operations occur within a single transaction
- **Audit Trail**: Creates appropriate audit log entries
- **Version Control**: Increments case version

#### Message Fields

**AssignCaseRequest:**

| Field   | Type   | Description                                      |
|---------|--------|--------------------------------------------------|
| caseId  | string | Unique identifier of the case                   |
| assetId | string | Asset ID to assign as primary investigator      |

**AssignCaseResponse:**

| Field | Type | Description                                       |
|-------|------|---------------------------------------------------|
| case  | Case | Updated case with new primary investigator       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "caseId": "case-456",
  "assetId": "asset-789"
}
```

**Response (JSON):**
```json
{
  "case": {
    "id": "case-456",
    "title": "Security Breach Investigation",
    "status": "CASE_STATUS_OPEN",
    "assetAssociations": [
      {
        "assetId": "asset-789",
        "associationType": "ASSET_ASSOCIATION_TYPE_PRIMARY_INVESTIGATOR",
        "assignedAt": "2025-04-23T18:00:00Z",
        "notes": "Assigned as primary investigator"
      }
    ],
    "updateTime": "2025-04-23T18:00:00Z",
    "version": 5
  }
}
```

---

### 24. AddWatcher

**Method:** `AddWatcher`  
**Route:** `POST /hero.cases.v1.CaseService/AddWatcher`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_UPDATE` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or asset not found
  - Returns error if asset already watching
- **Validation**: 
  - Asset must exist and be active
  - Asset must be in same organization
- **Impact**: 
  - Asset will receive case notifications
  - May affect notification routing
- **Security Risks**:
  - No rate limiting on watcher operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - No validation of asset ID against organization
  - May expose sensitive case data through notifications
  - No validation of notification preferences
  - No validation of notification frequency

#### Request

**AddWatcherRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| caseId   | string | ID of the case to add the watcher to.            |
| assetId  | string | ID of the asset to add as a watcher.             |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "assetId": "asset-004"
}
```

#### Response

Returns the updated `Case` object with the new watcher.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "watcherAssetIds": ["asset-004"],
  "resourceType": "CASES",
  "version": 11,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T18:00:00Z",
  "etag": 11
}
```

---

### 25. RemoveWatcher

**Method:** `RemoveWatcher`  
**Route:** `POST /hero.cases.v1.CaseService/RemoveWatcher`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_UPDATE` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case or asset not found
  - Returns error if asset not watching
- **Impact**: 
  - Asset will no longer receive case notifications
  - May affect notification routing
- **Security Risks**:
  - No rate limiting on watcher operations
  - No access control checks on asset visibility
  - No validation of organization ID against user's permissions
  - No validation of asset ID against organization
  - May break notification dependencies
  - No validation of impact on notification routing

#### Request

**RemoveWatcherRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| caseId   | string | ID of the case to remove the watcher from.       |
| assetId  | string | ID of the asset to remove as a watcher.          |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "assetId": "asset-004"
}
```

#### Response

Returns the updated `Case` object with the watcher removed.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_INVESTIGATING",
  "priority": 1,
  "watcherAssetIds": [],
  "resourceType": "CASES",
  "version": 12,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T18:30:00Z",
  "etag": 12
}
```

---

### 26. UpdateCaseStatus

**Method:** `UpdateCaseStatus`  
**Route:** `POST /hero.cases.v1.CaseService/UpdateCaseStatus`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates `CASE_AUDIT_ACTION_STATUS` entry
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if status transition is invalid
  - Returns error if status is same as current status
- **Validation**: 
  - Status must be a valid enum value
  - Some status transitions may require additional permissions
- **Timestamp Management**:
  - Sets `resolvedTime` when transitioning to RESOLVED
  - Sets `closeTime` when transitioning to CLOSED
  - Preserves existing timestamps when appropriate
  - Clears timestamps when moving away from RESOLVED/CLOSED
  - All timestamps are managed internally, user-provided values are ignored
- **Field Update Behavior**:
  - Only updates the following fields:
    - `status` - New status value
    - `update_time` - Set to current time
    - `updated_by_asset_id` - Set from context
    - `resolved_time` - Set/cleared based on status
    - `close_time` - Set/cleared based on status
    - `etag` - Auto-incremented
    - `version` - Auto-incremented
  - All other fields remain unchanged
- **Impact**: 
  - May trigger notifications
  - May affect case metrics and reporting
  - Updates case `updateTime` and status-specific timestamps
- **Gotchas**:
  - Setting status to RESOLVED automatically sets `resolvedTime`
  - Status transitions are not validated for logical sequence
  - Previous status is preserved in audit trail
  - Status changes may affect related entities and workflows
- **Security Risks**:
  - No rate limiting on status operations
  - No access control checks on status transitions
  - No validation of organization ID against user's permissions
  - No validation of status transition permissions
  - May expose sensitive case data through status changes
  - No validation of impact on related entities
  - No validation of status transition timing

#### Request

**UpdateCaseStatusRequest:**

| Field    | Type        | Description                                      |
|----------|-------------|--------------------------------------------------|
| caseId   | string      | ID of the case to update.                        |
| status   | CaseStatus  | New status for the case.                         |
| note     | string      | *(Optional)* Reason for the status change.       |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "status": "CASE_STATUS_RESOLVED",
  "note": "Investigation complete, root cause identified and fixed"
}
```

#### Response

Returns the updated `Case` object with the new status.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "orgId": 123,
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "title": "Security Breach Investigation",
  "status": "CASE_STATUS_RESOLVED",
  "priority": 1,
  "statusUpdates": [
    {
      "timestamp": "2025-04-23T19:00:00Z",
      "newStatus": "CASE_STATUS_RESOLVED",
      "previousStatus": "CASE_STATUS_INVESTIGATING",
      "note": "Investigation complete, root cause identified and fixed",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    }
  ],
  "resourceType": "CASES",
  "version": 13,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T19:00:00Z",
  "resolvedTime": "2025-04-23T19:00:00Z",
  "etag": 13
}
```

---

### 27. AddCaseUpdate

**Method:** `AddCaseUpdate`  
**Route:** `POST /hero.cases.v1.CaseService/AddCaseUpdate`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates audit entry for update addition
- **Version Control**: Increments case version
- **Error Handling**: Returns error if case not found
- **Timestamp Handling**:
  - EventTime is set internally to current UTC time
  - User-provided timestamps are ignored
  - Updates are stored in chronological order
- **Validation**: 
  - Message cannot be empty
  - Event type must be valid if provided
  - Update source must be valid
- **Impact**: May trigger notifications to watchers
- **Gotchas**:
  - Updates are immutable once added
  - EventTime is server-generated, cannot be specified
  - No built-in update deduplication
  - HTML in messages is not sanitized
  - Long messages are not truncated
  - Updates cannot be edited, only removed
  - All watchers receive notifications regardless of update importance
- **Security Risks**:
  - No rate limiting on update operations
  - No access control checks on update content
  - No validation of organization ID against user's permissions
  - No sanitization of HTML content
  - May expose sensitive data through updates
  - No validation of update frequency
  - No validation of update size
  - No validation of event type permissions

#### Request

**AddCaseUpdateRequest:**

| Field       | Type                           | Description                                      |
|-------------|--------------------------------|--------------------------------------------------|
| caseId      | string                         | ID of the case to add the update to.             |
| message     | string                         | Update message text.                             |
| eventType   | string                         | *(Optional)* Type of event (e.g., "EVIDENCE_ADDED"). |
| data      | google.protobuf.Struct | *(Optional)* Additional data related to the update. |
| updateSource | hero.situations.v2.UpdateSource | Source of the update.                          |
| updaterId   | string                         | ID of the asset making the update.               |
| displayName | string                         | *(Optional)* Display name of the updater.         |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "message": "New evidence collected from system logs",
  "eventType": "EVIDENCE_ADDED",
  "data": {
    "evidenceId": "evidence-456",
    "description": "Logs from server A"
  },
  "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
  "updaterId": "asset-001",
  "displayName": "John Doe"
}
```

#### Response

Returns the updated `Case` object with the new update entry.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_RESOLVED",
  "updates": [
    {
      "message": "New evidence collected from system logs",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-001",
      "eventType": "EVIDENCE_ADDED",
      "displayName": "John Doe",
      "data": {
        "evidenceId": "evidence-456",
        "description": "Logs from server A"
      },
      "eventTime": "2025-04-23T19:30:00Z"
    }
  ],
  "resourceType": "CASES",
  "version": 14,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T19:30:00Z",
  "etag": 14
}
```

---

### 28. RemoveCaseUpdate

**Method:** `RemoveCaseUpdate`  
**Route:** `POST /hero.cases.v1.CaseService/RemoveCaseUpdate`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates audit entry for update removal
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if update not found
- **Validation**: Timestamp must be valid ISO-8601 format
- **Impact**: May affect case history and audit trail
- **Security Risks**:
  - No rate limiting on update operations
  - No access control checks on update removal
  - No validation of organization ID against user's permissions

#### Request

**RemoveCaseUpdateRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| caseId    | string | ID of the case to remove the update from.        |
| timestamp | string | ISO-8601 timestamp of the update to remove.      |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "timestamp": "2025-04-23T19:30:00Z"
}
```

#### Response

Returns the updated `Case` object with the update entry removed.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_RESOLVED",
  "updates": [],
  "resourceType": "CASES",
  "version": 15,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T20:00:00Z",
  "etag": 15
}
```

---

### 29. ListCaseUpdates

**Method:** `ListCaseUpdates`  
**Route:** `POST /hero.cases.v1.CaseService/ListCaseUpdates`

#### Notes
- **Performance**: 
  - Optimized for retrieving update history
  - Consider implementing caching for frequently accessed cases
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if case not found
  - Returns empty list if no updates exist
- **Sorting**: Updates are returned in chronological order

#### Request

**ListCaseUpdatesRequest:**

| Field      | Type   | Description                                      |
|------------|--------|--------------------------------------------------|
| caseId     | string | ID of the case to list updates for.              |
| pageSize   | int32  | Maximum number of updates to return.             |
| pageToken  | string | Token for retrieving a specific page.            |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCaseUpdatesResponse:**

| Field           | Type                | Description                                      |
|-----------------|---------------------|--------------------------------------------------|
| updates         | repeated CaseUpdateEntry | List of case updates.                        |
| nextPageToken   | string              | Token for retrieving the next page.              |

**Sample Response (JSON):**
```json
{
  "updates": [
    {
      "message": "Initial investigation started",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-001",
      "displayName": "John Doe",
      "eventType": "CASE_UPDATE",
      "data": {
        "investigationId": "invest-001",
        "description": "Investigation started by John Doe"
      },
      "eventTime": "2025-04-23T12:30:00Z"
    },
    {
      "message": "New evidence collected from system logs",
      "eventTime": "2025-04-23T19:30:00Z",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updaterId": "asset-001",
      "eventType": "EVIDENCE_ADDED",
      "displayName": "John Doe",
      "data": {
        "evidenceId": "evidence-456",
        "description": "Logs from server A"
      }
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

### 30. ListCaseStatusHistory

**Method:** `ListCaseStatusHistory`  
**Route:** `POST /hero.cases.v1.CaseService/ListCaseStatusHistory`

#### Notes
- **Performance**: 
  - Optimized for retrieving status history
  - Consider implementing caching for frequently accessed cases
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if case not found
  - Returns empty list if no status updates exist
- **Usage**: 
  - Useful for case lifecycle analysis
  - Helps track resolution time metrics
- **Security Risks**:
  - No rate limiting on history operations
  - No access control checks on status history visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive case data through status history
  - No field-level access control
  - No validation of case ID against organization
  - May reveal internal workflow patterns
  - No validation of status transition permissions
  - May expose sensitive actor information

#### Request

**ListCaseStatusHistoryRequest:**

| Field      | Type   | Description                                      |
|------------|--------|--------------------------------------------------|
| caseId     | string | ID of the case to list status history for.       |
| pageSize   | int32  | Maximum number of status updates to return.      |
| pageToken  | string | Token for retrieving a specific page.            |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCaseStatusHistoryResponse:**

| Field           | Type                    | Description                                      |
|-----------------|-------------------------|--------------------------------------------------|
| statusUpdates   | repeated CaseStatusUpdateEntry | List of status updates.                    |
| nextPageToken   | string                  | Token for retrieving the next page.              |

**Sample Response (JSON):**
```json
{
  "statusUpdates": [
    {
      "timestamp": "2025-04-23T12:30:00Z",
      "newStatus": "CASE_STATUS_INVESTIGATING",
      "previousStatus": "CASE_STATUS_NEW",
      "note": "Investigation initiated",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    },
    {
      "timestamp": "2025-04-23T19:00:00Z",
      "newStatus": "CASE_STATUS_RESOLVED",
      "previousStatus": "CASE_STATUS_INVESTIGATING",
      "note": "Investigation complete, root cause identified and fixed",
      "updaterId": "asset-001",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

### 31. AddCaseTag

**Method:** `AddCaseTag`  
**Route:** `POST /hero.cases.v1.CaseService/AddCaseTag`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates audit entry for tag addition
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if tag already exists
- **Validation**: Tag string must be valid format
- **Impact**: May affect case searchability and categorization
- **Gotchas**:
  - Tags are case-sensitive
  - No built-in tag normalization
  - No limit on number of tags per case
  - Special characters in tags may affect search
  - Tag order is not preserved
  - No hierarchical tag support
  - Tags cannot contain commas or special characters
- **Security Risks**:
  - No rate limiting on tag operations
  - No access control checks on tag modifications
  - No validation of organization ID against user's permissions
  - No sanitization of tag content
  - May expose sensitive data through tags
  - No validation of tag permissions
  - No size limits on tags
  - May be used for data exfiltration
  - No validation of tag format
  - May be used for injection attacks

#### Request

**AddCaseTagRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| caseId   | string | ID of the case to add the tag to.                |
| tag      | string | Tag to add to the case.                          |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "tag": "high-priority"
}
```

#### Response

Returns the updated `Case` object with the new tag.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_RESOLVED",
  "tags": ["high-priority", "security", "database", "production"],
  "resourceType": "CASES",
  "version": 16,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T20:30:00Z",
  "etag": 16
}
```

---

### 32. RemoveCaseTag

**Method:** `RemoveCaseTag`  
**Route:** `POST /hero.cases.v1.CaseService/RemoveCaseTag`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates audit entry for tag removal
- **Version Control**: Increments case version
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if tag not found
- **Impact**: May affect case searchability and categorization
- **Security Risks**:
  - No rate limiting on tag operations
  - No access control checks on tag removal
  - No validation of organization ID against user's permissions
  - May break case categorization
  - No validation of tag removal permissions
  - May affect case searchability
  - No validation of tag dependencies
  - May impact case organization
  - No validation of tag usage in other systems

#### Request

**RemoveCaseTagRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| caseId   | string | ID of the case to remove the tag from.           |
| tag      | string | Tag to remove from the case.                     |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "tag": "high-priority"
}
```

#### Response

Returns the updated `Case` object with the tag removed.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_RESOLVED",
  "tags": ["security", "database", "production"],
  "resourceType": "CASES",
  "version": 17,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T21:00:00Z",
  "etag": 17
}
```

---

### 33. AddAdditionalInfo

**Method:** `AddAdditionalInfo`  
**Route:** `POST /hero.cases.v1.CaseService/AddAdditionalInfo`

#### Notes
- **Transaction Safety**: Operation is atomic within a transaction
- **Audit Trail**: Creates audit entry for info addition
- **Version Control**: Increments case version
- **Error Handling**: Returns error if case not found
- **Field Update Behavior**:
  - Only updates the following fields:
    - `additional_info_json` - Merged with existing data
    - `update_time` - Set to current time
    - `updated_by_asset_id` - Set from context
    - `etag` - Auto-incremented
    - `version` - Auto-incremented
  - All other fields remain unchanged
- **Data Management**:
  - Merges new JSON data with existing additional info
  - Preserves existing fields not included in update
  - Returns complete updated additional info
- **Validation**: 
  - Additional info must be valid JSON
  - Size limits may apply
- **Impact**: May affect case metadata and search capabilities
- **Gotchas**:
  - Invalid JSON is silently replaced with "{}"
  - Previous additional info is completely overwritten
  - Large JSON objects may impact performance
  - Field names in JSON are case-sensitive
  - Nested objects are supported but may affect query performance
- **Security Risks**:
  - No rate limiting on info operations
  - No access control checks on info modifications
  - No validation of organization ID against user's permissions
  - No sanitization of JSON content
  - May expose sensitive data through additional info
  - No size limits on JSON payload
  - No validation of JSON schema
  - May be used for data exfiltration
  - No validation of field-level permissions
  - May contain malicious content
  - No validation of data types
  - May impact system performance

#### Request

**AddAdditionalInfoRequest:**

| Field           | Type                    | Description                                      |
|-----------------|-------------------------|--------------------------------------------------|
| caseId          | string                  | ID of the case to add info to.                   |
| additionalInfo  | google.protobuf.Struct  | Additional information to add.                   |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "additionalInfo": {
    "severityLevel": "critical",
    "affectedSystems": ["production-db", "auth-service"],
    "estimatedResolutionTime": "2 days"
  }
}
```

#### Response

Returns the updated `Case` object with the new additional info.

**Sample Response (JSON):**
```json
{
  "id": "case-123",
  "title": "Security Breach Investigation",
  "type": "CASE_TYPE_SECURITY_INCIDENT",
  "status": "CASE_STATUS_RESOLVED",
  "additionalInfoJson": {
    "severityLevel": "critical",
    "affectedSystems": ["production-db", "auth-service"],
    "estimatedResolutionTime": "2 days"
  },
  "resourceType": "CASES",
  "version": 18,
  "createTime": "2025-04-23T12:00:00Z",
  "updateTime": "2025-04-23T21:30:00Z",
  "etag": 18
}
```

---

### 34. ListCaseFileAttachments

**Method:** `ListCaseFileAttachments`  
**Route:** `POST /hero.cases.v1.CaseService/ListCaseFileAttachments`

#### Notes
- **Performance**: 
  - Optimized for retrieving file attachments
  - Consider implementing caching for frequently accessed cases
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if case not found
  - Returns empty list if no attachments exist
- **Usage**: 
  - Useful for case team management
  - Helps track file attachments
- **Gotchas**:
  - Results are sorted by attachment timestamp
  - Full attachment details are returned
  - No filtering by attachment type
  - No filtering by attachment ID
  - No filtering by uploader
- **Security Risks**:
  - No rate limiting on list operations
  - No access control checks on attachment visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive file data through attachments
  - No field-level access control
  - No validation of case ID against organization

#### Request

**ListCaseFileAttachmentsRequest:**

| Field     | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| caseId    | string | ID of the case to list attachments for.          |
| pageSize  | int32  | Maximum number of attachments to return.         |
| pageToken | string | Token for retrieving a specific page.            |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCaseFileAttachmentsResponse:**

| Field          | Type                        | Description                                      |
|----------------|-----------------------------|--------------------------------------------------|
| attachments    | repeated CaseFileAttachment | List of file attachments.                    |
| nextPageToken  | string                      | Token for retrieving the next page.              |

**Sample Response (JSON):**
```json
{
  "attachments": [
    {
      "id": "attachment-1",
      "caseId": "case-123",
      "filename": "security-breach-report.pdf",
      "uploadedAt": "2025-04-23T12:00:00Z",
      "uploaderAssetId": "asset-001",
      "description": "Security breach investigation report"
    },
    {
      "id": "attachment-2",
      "caseId": "case-123",
      "filename": "incident-photos.jpg",
      "uploadedAt": "2025-04-23T13:00:00Z",
      "uploaderAssetId": "asset-002",
      "description": "Photos from the security incident"
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

### 35. GetCaseVersion

**Method:** `GetCaseVersion`  
**Route:** `POST /hero.cases.v1.CaseService/GetCaseVersion`

#### Notes
- **Performance**: 
  - Optimized for retrieving specific versions
  - Consider implementing caching for frequently accessed versions
- **Error Handling**: 
  - Returns error if case not found
  - Returns error if version not found
- **Usage**: 
  - Useful for audit purposes
  - Helps track case evolution
- **Data Consistency**: Returns exact snapshot as of version
- **Gotchas**:
  - Snapshots are stored as protobuf JSON
  - Failed unmarshaling of snapshot data returns error
  - Versions must be explicitly requested - no "latest" flag
  - No automatic cleanup of old versions
  - Snapshot size may be large for cases with many relationships
- **Security Risks**:
  - No rate limiting on version operations
  - No access control checks on version visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive historical data
  - No field-level access control
  - No validation of case ID against organization
  - May reveal deleted or modified sensitive information
  - No validation of version access permissions
  - May expose internal system details
  - No validation of version integrity

#### Request

**GetCaseVersionRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| caseId   | string | ID of the case to get version for.               |
| version  | int32  | Version number to retrieve.                      |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "version": 10
}
```

#### Response

Returns the `CaseSnapshot` object for the requested version.

**Sample Response (JSON):**
```json
{
  "caseId": "case-123",
  "version": 10,
  "caseSnapshot": {
    "id": "case-123",
    "title": "Security Breach Investigation",
    "type": "CASE_TYPE_SECURITY_INCIDENT",
    "status": "CASE_STATUS_INVESTIGATING",
    "resourceType": "CASES",
    "version": 10,
    "createTime": "2025-04-23T12:00:00Z",
    "updateTime": "2025-04-23T16:30:00Z"
  },
  "timestamp": "2025-04-23T16:30:00Z"
}
```

---

### 36. ListCaseVersions

**Method:** `ListCaseVersions`  
**Route:** `POST /hero.cases.v1.CaseService/ListCaseVersions`

#### Notes
- **Performance**: 
  - Optimized for retrieving version history
  - Consider implementing caching for frequently accessed cases
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Error Handling**: 
  - Returns empty list if case not found
  - Returns empty list if no versions exist
- **Usage**: 
  - Useful for compliance and audit
  - Helps track case changes over time
- **Security Risks**:
  - No rate limiting on version operations
  - No access control checks on version history visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive version history
  - No field-level access control
  - No validation of case ID against organization
  - May reveal internal workflow patterns
  - No validation of version access permissions
  - May expose system architecture details
  - No validation of version integrity
  - May impact system performance with large histories

#### Request

**ListCaseVersionsRequest:**

| Field      | Type   | Description                                      |
|------------|--------|--------------------------------------------------|
| caseId     | string | ID of the case to list versions for.             |
| pageSize   | int32  | Maximum number of versions to return.            |
| pageToken  | string | Token for retrieving a specific page.            |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "pageSize": 10,
  "pageToken": ""
}
```

#### Response

**ListCaseVersionsResponse:**

| Field           | Type                | Description                                      |
|-----------------|---------------------|--------------------------------------------------|
| versions        | repeated CaseSnapshot | List of case versions.                        |
| nextPageToken   | string              | Token for retrieving the next page.              |

**Sample Response (JSON):**
```json
{
  "versions": [
    {
      "caseId": "case-123",
      "version": 1,
      "caseSnapshot": {
        "id": "case-123",
        "title": "Security Breach Investigation",
        "type": "CASE_TYPE_SECURITY_INCIDENT",
        "status": "CASE_STATUS_NEW",
        "resourceType": "CASES",
        "version": 1,
        "createTime": "2025-04-23T12:00:00Z",
        "updateTime": "2025-04-23T12:00:00Z"
      },
        "timestamp": "2025-04-23T12:00:00Z"
    },
    {
      "caseId": "case-123",
      "version": 2,
      "caseSnapshot": {
        "id": "case-123",
        "title": "Security Breach Investigation",
        "type": "CASE_TYPE_SECURITY_INCIDENT",
        "status": "CASE_STATUS_INVESTIGATING",
        "resourceType": "CASES",
        "version": 2,
        "createTime": "2025-04-23T12:00:00Z",
        "updateTime": "2025-04-23T12:30:00Z"
      },
      "timestamp": "2025-04-23T12:30:00Z"
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

### 37. ListCaseAuditLog

**Method:** `ListCaseAuditLog`  
**Route:** `POST /hero.cases.v1.CaseService/ListCaseAuditLog`

#### Notes
- **Performance**: 
  - Optimized for retrieving audit history
  - Consider implementing caching for frequently accessed logs
- **Pagination**: 
  - Supports standard pagination with page size limits
  - Use `nextPageToken` for subsequent pages
- **Filtering**: 
  - Can filter by audit action type
  - Consider performance impact of complex filters
- **Usage**: 
  - Critical for compliance and auditing
  - Provides complete change history
  - Helps track user actions and system changes
- **Gotchas**:
  - Old/new values are stored as JSON strings
  - Field paths use dot notation for nested fields
  - System-generated audit entries use "SYSTEM" as actorAssetId
  - Some audit entries may have empty old/new values
  - Audit entries cannot be modified or deleted
  - High-volume cases may have large audit logs
- **Security Risks**:
  - No rate limiting on audit operations
  - No access control checks on audit log visibility
  - No validation of organization ID against user's permissions
  - May expose sensitive audit data
  - No field-level access control
  - No validation of case ID against organization
  - May reveal internal workflow patterns
  - May expose sensitive actor information
  - No validation of audit log permissions
  - May expose system architecture details
  - No validation of audit log integrity
  - May impact system performance with large logs
  - May expose sensitive user behavior patterns
  - No validation of audit log retention policies

#### Request

**ListCaseAuditLogRequest:**

| Field       | Type          | Description                                      |
|-------------|---------------|--------------------------------------------------|
| caseId      | string        | ID of the case to list audit log for.            |
| pageSize    | int32         | Maximum number of audit entries to return.       |
| pageToken   | string        | Token for retrieving a specific page.            |
| action      | CaseAuditAction | *(Optional)* Filter by audit action type.        |

**Sample Request (JSON):**
```json
{
  "caseId": "case-123",
  "pageSize": 10,
  "pageToken": "",
  "action": "CASE_AUDIT_ACTION_STATUS"
}
```

#### Response

**ListCaseAuditLogResponse:**

| Field           | Type                | Description                                      |
|-----------------|---------------------|--------------------------------------------------|
| entries         | repeated CaseAuditLogEntry | List of audit log entries.                |
| nextPageToken   | string              | Token for retrieving the next page.              |

**Sample Response (JSON):**
```json
{
  "entries": [
    {
      "id": "audit-1",
      "caseId": "case-123",
      "action": "CASE_AUDIT_ACTION_VIEW",
      "actorAssetId": "asset-004",
      "timestamp": "2025-04-23T12:25:00Z",
      "fieldPath": "",
      "oldValue": "",
      "newValue": "",
      "note": "Case viewed by student conduct officer"
    },
    {
      "id": "audit-2",
      "caseId": "case-123",
      "action": "CASE_AUDIT_ACTION_STATUS",
      "actorAssetId": "asset-001",
      "timestamp": "2025-04-23T12:30:00Z",
      "fieldPath": "status",
      "oldValue": "CASE_STATUS_NEW",
      "newValue": "CASE_STATUS_INVESTIGATING",
      "note": "Initial investigation started by campus police"
    },
    {
      "id": "audit-3",
      "caseId": "case-123",
      "action": "CASE_AUDIT_ACTION_STATUS",
      "actorAssetId": "asset-001",
      "timestamp": "2025-04-23T19:00:00Z",
      "fieldPath": "status",
      "oldValue": "CASE_STATUS_INVESTIGATING",
      "newValue": "CASE_STATUS_RESOLVED",
      "note": "Investigation complete, warning issued to student"
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

### Search Operations
37. **[SearchCases](#37-searchcases)**

---

### 38. SearchCases

**Method:** `SearchCases`  
**Route:** `POST /hero.cases.v1.CaseService/SearchCases`

#### Notes
- **Hybrid Search Strategy**: Uses PostgreSQL full-text search for optimal performance and API compliance:
  - When `search_fields` is empty or includes all fields → Uses `search_vector` column (fastest)
  - When `search_fields` is restricted → Uses field-specific vectors (respects API contract)
- **Search Vector Content**: The `search_vector` includes weighted content from:
  - Case ID and title (highest weight 'A')
  - Case description (high weight 'B')
- **Hierarchical Text Search Capabilities**:
  - **Phrase matching (highest priority)**: Exact phrase matches using `phraseto_tsquery()`
  - **All-words matching (medium priority)**: All search terms present using `to_tsquery()` with &
  - **Individual word matching (lower priority)**: Any search terms present using `to_tsquery()` with |
  - **Partial matching (fallback)**: ILIKE patterns for partial word matches
  - **Field-specific search**: Restrict search to specific fields (id, title, description)
  - **Query sanitization**: Removes special characters that could break tsquery parsing
- **Advanced Filtering**: Supports comprehensive filtering by:
  - **Case attributes**: status, type, priority, release status
  - **Relationships**: situations, reports, related cases, entity references
  - **Asset associations**: asset IDs and association types
  - **Date ranges**: creation, update, due, resolved, and close times
  - **Tags**: exact tag matching with array operations
  - **Watchers**: watcher asset IDs
- **Performance Optimizations**:
  - **Batch relationship loading**: Loads all relationships in 5 queries instead of N×5 queries (60% performance improvement)
  - **RLS (Row Level Security)**: Automatic organization filtering for multi-tenant isolation
  - **Indexed searches**: Leverages indexes on search_vector, timestamps, and relationship tables
  - **Pagination**: Offset-based pagination with configurable page sizes (default 50, max 100)
  - **Efficient counting**: Separate COUNT query for total results without loading data
  - **Query timeout**: 30-second timeout to prevent runaway queries
- **Search Highlights**: 
  - **Fragment generation**: Shows where search terms were found with context
  - **Multi-field highlighting**: Highlights across title, description, and other fields
  - **Deduplication**: Removes duplicate fragments for cleaner results
- **Ordering Options**: Supports ordering by:
  - **Relevance**: Default for text searches (requires search query)
  - **Timestamps**: Creation time, update time, due date, resolved time, close time
  - **Priority**: High to low or low to high
  - **Status**: Alphabetical ordering by status name
  - **Consistent pagination**: Always includes ID as secondary sort for stable results
- **Complete Case Data**: Search results include full case objects with:
  - **Core fields**: All case attributes and metadata
  - **Relationships**: Tags, situation IDs, report IDs, related case IDs, watcher asset IDs
  - **Associations**: Asset associations loaded separately for performance
  - **Full context**: Same data structure as GetCase for consistency
- **Security & Validation**:
  - **Input validation**: Query length limits (1000 chars), wildcard limits (max 5)
  - **Field allowlisting**: Only id, title, description supported in search_fields
  - **Filter count limits**: Max 100 items per relationship filter array
  - **Query sanitization**: Removes SQL injection attack vectors
  - **Parameterized queries**: All user input is parameterized to prevent injection
- **Gotchas**:
  - **Page size limits**: Default 50, maximum 100 to prevent resource exhaustion
  - **Invalid page tokens**: Silently ignored, defaults to offset 0
  - **Field restrictions**: Only id, title, description supported in search_fields
  - **Date format**: All dates must be in RFC3339 format
  - **Array operations**: Tags, IDs use PostgreSQL array contains operations (@>)
  - **Search term parsing**: Complex queries may be split into multiple search strategies
  - **Highlighting overhead**: Only generated when query is provided and results exist

#### Request

**SearchCasesRequest:**

| Field | Type | Description |
|-------|------|-------------|
| query | string | *(Optional)* Free-text search query. Supports natural language, phrase searches (with quotes), and Boolean operators (AND/OR). |
| search_fields | repeated string | *(Optional)* Restrict query to specific fields: "id", "title", "description". Empty means search all indexed fields. |
| field_queries | repeated FieldQuery | *(Optional)* ILIKE pattern matching on specific fields (id, title, description) for exact term searches. |
| status | repeated CaseStatus | *(Optional)* Filter by case status (exact match). |
| type | repeated CaseType | *(Optional)* Filter by case type (exact match). |
| priority | repeated int32 | *(Optional)* Filter by priority levels (exact match). |
| tags | repeated string | *(Optional)* Filter by tags (case must have all specified tags). |
| created_by_asset_ids | repeated string | *(Optional)* Filter by creator asset IDs. |
| updated_by_asset_ids | repeated string | *(Optional)* Filter by last updater asset IDs. |
| release_status | repeated ReleaseStatus | *(Optional)* Filter by release status. |
| situation_ids | repeated string | *(Optional)* Filter by linked situation IDs. |
| report_ids | repeated string | *(Optional)* Filter by linked report IDs. |
| related_case_ids | repeated string | *(Optional)* Filter by related case IDs. |
| asset_ids | repeated string | *(Optional)* Filter by associated asset IDs. |
| association_types | repeated CaseAssetAssociationType | *(Optional)* Filter by asset association types. |
| entity_ref_ids | repeated string | *(Optional)* Filter by entity reference IDs. |
| entity_ref_types | repeated string | *(Optional)* Filter by entity reference types. |
| watcher_asset_ids | repeated string | *(Optional)* Filter by watcher asset IDs. |
| create_time | DateRange | *(Optional)* Filter by creation date range. |
| update_time | DateRange | *(Optional)* Filter by last update date range. |
| due_date | DateRange | *(Optional)* Filter by due date range. |
| resolved_time | DateRange | *(Optional)* Filter by resolution date range. |
| close_time | DateRange | *(Optional)* Filter by close date range. |
| page_size | int32 | *(Optional)* Maximum number of results per page (default: 50, max: 100). |
| page_token | string | *(Optional)* Cursor for pagination (opaque token from previous response). |
| order_by | SearchOrderBy | *(Optional)* Sort order: RELEVANCE (default for queries), CREATE_TIME, UPDATE_TIME, PRIORITY, STATUS, DUE_DATE, RESOLVED_TIME, CLOSE_TIME. |
| ascending | bool | *(Optional)* Sort direction: false = descending (default), true = ascending. |

#### Supporting Message Types

**FieldQuery:**

| Field | Type   | Description                                           |
|-------|--------|-------------------------------------------------------|
| field | string | Field to search in (id, title, description)          |
| query | string | Search term for this specific field                   |

**DateRange:**

| Field | Type   | Description                                           |
|-------|--------|-------------------------------------------------------|
| from  | string | Start time in RFC3339 format (inclusive)             |
| to    | string | End time in RFC3339 format (inclusive)               |

**HighlightResult:**

| Field     | Type             | Description                                           |
|-----------|------------------|-------------------------------------------------------|
| fragments | repeated HighlightFragment | List of highlighted fragments for the case    |

**HighlightFragment:**

| Field | Type   | Description                                           |
|-------|--------|-------------------------------------------------------|
| field | string | Field name that had a match (title, description)     |
| highlights | repeated string | Highlighted text snippets with matched terms   |

**SearchOrderBy Enum:**

| Name                                | Value | Description                                |
|-------------------------------------|-------|--------------------------------------------|
| SEARCH_ORDER_BY_UNSPECIFIED        | 0     | Default unspecified order                  |
| SEARCH_ORDER_BY_RELEVANCE           | 1     | Order by search relevance (for text queries) |
| SEARCH_ORDER_BY_CREATE_TIME         | 2     | Order by case creation time                |
| SEARCH_ORDER_BY_UPDATE_TIME         | 3     | Order by case update time                  |
| SEARCH_ORDER_BY_PRIORITY            | 4     | Order by case priority                     |
| SEARCH_ORDER_BY_STATUS              | 5     | Order by case status                       |
| SEARCH_ORDER_BY_DUE_DATE            | 6     | Order by case due date                     |
| SEARCH_ORDER_BY_RESOLVED_TIME       | 7     | Order by case resolution time              |
| SEARCH_ORDER_BY_CLOSE_TIME          | 8     | Order by case close time                   |

**Sample Request (JSON):**
```json
{
  "query": "security breach investigation",
  "search_fields": ["title", "description"],
  "status": ["CASE_STATUS_OPEN", "CASE_STATUS_INVESTIGATING"],
  "type": ["CASE_TYPE_SECURITY_INCIDENT"],
  "priority": [1, 2],
  "tags": ["urgent", "security"],
  "create_time": {
    "from": "2024-01-01T00:00:00Z",
    "to": "2024-12-31T23:59:59Z"
  },
  "page_size": 25,
  "page_token": "",
  "order_by": "SEARCH_ORDER_BY_RELEVANCE",
  "ascending": false
}
```

#### Response

**SearchCasesResponse:**

| Field | Type | Description |
|-------|------|-------------|
| cases | repeated Case | List of matching cases (ordered and paginated). |
| next_page_token | string | Cursor for next page (empty if on last page). |
| highlights | map<string, HighlightResult> | Per-case highlight information keyed by case ID. Each result shows field names and matched fragments. |
| total_results | int32 | Total number of matching cases (before pagination) for UI counters. |

**Sample Response (JSON):**
```json
{
  "cases": [
    {
      "id": "case-456",
      "orgId": 123,
      "type": "CASE_TYPE_SECURITY_INCIDENT",
      "title": "Network Security Breach Investigation",
      "description": "Investigation of unauthorized access to network infrastructure",
      "status": "CASE_STATUS_INVESTIGATING",
      "priority": 1,
      "tags": ["urgent", "security", "network"],
      "situationIds": ["situation-789"],
      "reportIds": ["report-101"],
      "createTime": "2024-06-15T08:30:00Z",
      "updateTime": "2024-06-15T14:20:00Z",
      "createdByAssetId": "asset-security-lead",
      "updatedByAssetId": "asset-investigator-01",
      "resourceType": "CASES",
      "version": 3,
      "etag": "3"
    }
  ],
  "next_page_token": "cursor-next-page-25",
  "highlights": {
    "case-456": {
      "fragments": [
        {
          "field": "title",
          "highlights": ["Network <b>Security Breach Investigation</b>"]
        },
        {
          "field": "description", 
          "highlights": ["<b>Investigation</b> of unauthorized access to network infrastructure"]
        }
      ]
    }
  },
  "total_results": 42
}
```

---

# Cases Search Functionality: The Engineering Journey 🔍

Welcome to the crown jewel of the Cases module - our comprehensive search functionality! When we started building cases, we knew that finding a specific case among thousands would be like finding a needle in a haystack. So we built something better: a magnet that finds the needle, tells you which haystack it came from, and highlights exactly where it was hiding.

This isn't just another "add a WHERE clause" feature. This is a full-fledged search engine that can handle everything from "find me that security incident from last month involving John Doe" to "show me all high-priority cases with breach in the title that have pending investigations and are assigned to the SWAT team."

## The Search Challenge We Faced 🎯

### The Complex World of Cases

Cases aren't simple database records. Each case is a interconnected web of relationships:

- **11 relationship tables** (situations, reports, entities, asset associations, watchers, tags, updates, status history, audit logs, related cases, and entity references)
- **20+ searchable attributes** (text fields, enums, arrays, JSON objects, timestamps)
- **Multi-tenant isolation** (ensuring Organization A never sees Organization B's cases)
- **Real-time constraints** (investigators need answers in milliseconds, not minutes)

### Real-World Search Scenarios

Imagine you're a case manager during a busy incident response. You need to find:

**Scenario 1: The Urgent Investigation** 🚨
*"Find all open security incidents created in the last 48 hours that involve cameras and are assigned to Detective Rodriguez"*

**Scenario 2: The Pattern Hunter** 🔍
*"Show me resolved cases with 'theft' in the title or description that are tagged as 'downtown' and have John Smith as a witness"*

**Scenario 3: The Compliance Audit** 📋
*"List all cases created by Officer Johnson last quarter that have been resolved and include the word 'evidence' in any update"*

**The Technical Translation:**
```sql
-- Scenario 1 becomes:
SELECT * FROM cases 
WHERE status = 'CASE_STATUS_OPEN' 
  AND type = 'CASE_TYPE_SECURITY_INCIDENT'
  AND create_time >= NOW() - INTERVAL '48 hours'
  AND EXISTS (SELECT 1 FROM case_entities WHERE entity_type = 'CAMERA')
  AND EXISTS (SELECT 1 FROM case_asset_associations WHERE asset_id = 'detective-rodriguez')
ORDER BY priority DESC, create_time DESC;
```

That innocent-looking user request translates to a query spanning 4 tables with complex filtering. Multiply this by dozens of simultaneous users, and you've got yourself a performance challenge.

## Our Search Architecture: The Multi-Layer Defense 🏗️

We built our search system as a multi-layered architecture where each layer solves a specific problem:

```
┌─────────────────────────────────────────────────────────────────┐
│                   🎯 User Interface Layer                       │
│  "I need to find something!" - Investigators, Managers, Auditors│
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ 🔍 Search Bar   │ │ 📊 Filter Panel │ │ 🎛️ Advanced     │    │
│  │ Free Text       │ │ Status/Type     │ │ Query Builder   │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ gRPC/HTTP Magic ✨
┌─────────────────────────────┴───────────────────────────────────┐
│                🛡️ gRPC Service Layer                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ 🔒 Request      │ │ 📝 Parameter    │ │ 💥 Error        │    │
│  │ Validation      │ │ Sanitization    │ │ Handling        │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ ⚡ Response     │ │ 🎨 Highlighting │ │ 📄 Pagination   │    │
│  │ Assembly        │ │ Generation      │ │ Management      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ Repository Interface 🤝
┌─────────────────────────────┴───────────────────────────────────┐
│               🏭 Repository Layer (The Query Factory)           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ 🔧 Dynamic SQL  │ │ 🔄 Batch        │ │ 🎯 Result       │    │
│  │ Builder         │ │ Relationship    │ │ Mapping         │    │
│  │                 │ │ Loading         │ │                 │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ 🔒 Parameter    │ │ 🎛️ Filter       │ │ 🤝 Transaction  │    │
│  │ Binding         │ │ Application     │ │ Management      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ SQL Wizardry 🪄
┌─────────────────────────────┴───────────────────────────────────┐
│            🐘 Database Layer (PostgreSQL Powerhouse)           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ ⚡ Full-Text    │ │ 📚 Strategic    │ │ 🔤 Search       │    │
│  │ Search Engine   │ │ Index Mix       │ │ Vector Magic    │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ 🎯 Hierarchical │ │ 🔐 Row Level    │ │ ✅ ACID         │    │
│  │ Text Ranking    │ │ Security (RLS)  │ │ Guarantees      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
```

**Key Architectural Decisions:**

1. **Keep it normalized** - Don't denormalize for search performance; solve it with smarter querying
2. **Leverage PostgreSQL** - Use built-in full-text search instead of external search engines
3. **Batch everything** - Load relationships efficiently to avoid N+1 query disasters
4. **Security by design** - Multi-tenant isolation baked into every query

## The Six Pillars of Search Excellence 🏛️

### 1. The Smart Query Builder: Dynamic SQL Mastery 🔧

**The Challenge:** How do you build efficient SQL queries when users can combine any of 20+ filters in any combination?

**Our Solution:** A dynamic query builder that constructs optimal SQL based on what the user actually wants to search for.

```go
// We build queries step by step, only adding what's needed
func (repo *PostgresCaseRepository) buildCaseSearchQuery(req *SearchCasesRequest) (string, []interface{}) {
    baseQuery := `
        SELECT id, org_id, type, title, description, status, priority,
               create_time, update_time, due_date, resolved_time, close_time,
               created_by_asset_id, updated_by_asset_id, version, etag,
               release_status, additional_info_json
        FROM cases 
        WHERE org_id = $1`  // RLS enforcement
    
    whereConditions := []string{}
    args := []interface{}{orgID}
    paramIndex := 2
    
    // Add conditions only when user specifies them
    if req.Query != "" {
        condition, newArgs := repo.buildTextSearchCondition(req.Query, req.SearchFields)
        whereConditions = append(whereConditions, condition)
        args = append(args, newArgs...)
        paramIndex += len(newArgs)
    }
    
    if len(req.Status) > 0 {
        whereConditions = append(whereConditions, fmt.Sprintf("status = ANY($%d)", paramIndex))
        args = append(args, pq.Array(req.Status))
        paramIndex++
    }
    
    // ... continue for all 20+ filter types
    
    // Assemble the final query
    if len(whereConditions) > 0 {
        baseQuery += " AND " + strings.Join(whereConditions, " AND ")
    }
    
    return baseQuery + orderByClause + limitClause, args
}
```

**Why Dynamic Over Static Templates?**

We considered pre-built query templates:
```sql
-- This approach would require 2^20 = 1M+ templates
SELECT * FROM cases 
WHERE ($1 IS NULL OR search_vector @@ plainto_tsquery($1))
  AND ($2 IS NULL OR status = ANY($2))
  AND ($3 IS NULL OR create_time >= $3)
  -- ... 20+ more conditional checks
```

**Problems with templates:**
- **Query plan disaster:** NULL checks prevent PostgreSQL from using indexes efficiently
- **Template explosion:** Exponential combinations of filter possibilities
- **Maintenance nightmare:** Every new filter requires updating all templates
- **Performance overhead:** Database always evaluates all conditions

**Our approach wins because:**
- **Optimal query plans:** PostgreSQL only optimizes for the filters actually used
- **Maintainable:** Add new filters by extending the builder function
- **Efficient:** No wasted cycles on unused conditions

### 2. Hierarchical Text Search: The Intelligence Layer 🧠

**The Problem:** Users search for "security breach investigation" and expect the system to understand:
1. Exact phrase "security breach investigation" should rank highest
2. Cases with all three words (any order) should come next
3. Cases with any of the words should still be relevant
4. Partial matches like "breach-related security" should work

**Our 4-Level Search Hierarchy:**

```sql
-- Level 1: Exact phrase matching (highest priority)
search_vector @@ phraseto_tsquery('english', 'security breach investigation')

-- Level 2: All words must be present (medium priority)
search_vector @@ to_tsquery('english', 'security & breach & investigation')

-- Level 3: Any words present (lower priority)
search_vector @@ to_tsquery('english', 'security | breach | investigation')

-- Level 4: ILIKE fallback for partial matches (lowest priority)
(title ILIKE '%security breach investigation%' OR description ILIKE '%security breach investigation%')
```

**The Search Vector Magic:**
```sql
-- Built automatically via database triggers
search_vector := 
  setweight(to_tsvector('english', COALESCE(id, '')), 'A') ||       -- Case ID (highest weight)
  setweight(to_tsvector('english', COALESCE(title, '')), 'A') ||    -- Title (highest weight)
  setweight(to_tsvector('english', COALESCE(description, '')), 'B') -- Description (high weight)
```

**Real Search Example:**

User searches: `"murder weapon"`

**Results in priority order:**
1. **Case #C001:** "Murder weapon found at crime scene" *(exact phrase - highest score)*
2. **Case #C055:** "Weapon used in downtown murder case" *(both words, different order)*
3. **Case #C123:** "Murder suspect fled the scene" *(only "murder" matches)*
4. **Case #C089:** "Evidence includes potential murderous intent" *(partial match)*

**Why PostgreSQL FTS Over Alternatives?**

**vs. Elasticsearch:**
- ✅ **No operational overhead** - No separate cluster to maintain
- ✅ **ACID consistency** - No eventual consistency issues
- ✅ **Zero latency** - No network calls to external services
- ✅ **Cost effective** - No additional licensing

**vs. Simple ILIKE:**
- ✅ **Intelligent ranking** - `ts_rank()` provides relevance scoring
- ✅ **Language awareness** - Built-in stemming and stop word handling
- ✅ **Performance** - GIN indexes on search vectors vs table scans
- ✅ **Scalability** - Sub-linear growth vs linear with text size

### 3. The Great Relationship Loading Strategy: Defeating the N+1 Dragon 🐉

**The Problem We Faced:**

Cases have 5 main relationship types (tags, situations, reports, related cases, watchers). A naive implementation would look like this:

```go
// The N+1 Nightmare (what we avoided)
func loadCasesWithRelationships(caseIDs []string) ([]*Case, error) {
    cases := []Case{}
    
    for _, caseID := range caseIDs {
        case := loadCase(caseID)                    // 1 query
        case.Tags = loadTags(caseID)                // N queries
        case.Situations = loadSituations(caseID)    // N queries  
        case.Reports = loadReports(caseID)          // N queries
        case.RelatedCases = loadRelated(caseID)     // N queries
        case.Watchers = loadWatchers(caseID)        // N queries
        cases = append(cases, case)
    }
    
    return cases, nil
    // Total: 1 + (N × 5) queries = 1 + 250 queries for 50 cases! 😱
}
```

**Mathematical Disaster:**
```
50 cases × 5 relationships = 250 relationship queries
+ 1 main query = 251 total queries
+ ~3ms average latency per query = 753ms response time
```

**Our Solution: batchLoadCaseRelationships()**

```go
// The Batch Loading Hero (what we built)
func (repo *PostgresCaseRepository) batchLoadCaseRelationships(
    ctx context.Context, 
    tx *sql.Tx, 
    caseIds []string,
) (tagsMap, situationsMap, reportsMap, relatedMap, watchersMap map[string][]string, err error) {
    
    // One query per relationship type using PostgreSQL's ANY() operator
    
    // Load all tags for all cases in one shot
    rows, err := tx.QueryContext(ctx, 
        "SELECT case_id, tag FROM case_tags WHERE case_id = ANY($1)", 
        pq.Array(caseIds))
    // Process and group by case_id...
    
    // Load all situations for all cases
    rows, err = tx.QueryContext(ctx, 
        "SELECT case_id, situation_id FROM case_situations WHERE case_id = ANY($1)", 
        pq.Array(caseIds))
    // Process and group by case_id...
    
    // ... same pattern for reports, related cases, and watchers
    
    return tagsMap, situationsMap, reportsMap, relatedMap, watchersMap, nil
    // Total: 5 queries regardless of case count! 🎉
}
```

**Performance Revolution:**

| Cases | N+1 Approach | Batch Approach | Improvement | User Experience |
|-------|--------------|----------------|-------------|-----------------|
| **10** | 51 queries (153ms) | 6 queries (45ms) | **71% faster** | ✅ Feels instant |
| **25** | 126 queries (378ms) | 6 queries (65ms) | **83% faster** | ✅ Very responsive |
| **50** | 251 queries (753ms) | 6 queries (95ms) | **87% faster** | ✅ Lightning fast |
| **100** | 501 queries (1503ms) | 6 queries (140ms) | **91% faster** | ✅ Still sub-200ms |

**Why Not Mega-JOINs?**

We considered joining all relationships in one query:
```sql
-- The tempting but dangerous mega-JOIN
SELECT c.*, 
       array_agg(DISTINCT t.tag) as tags,
       array_agg(DISTINCT s.situation_id) as situations,
       array_agg(DISTINCT r.report_id) as reports,
       array_agg(DISTINCT rc.related_case_id) as related_cases,
       array_agg(DISTINCT w.asset_id) as watchers
FROM cases c
LEFT JOIN case_tags t ON c.id = t.case_id
LEFT JOIN case_situations s ON c.id = s.case_id  
LEFT JOIN case_reports r ON c.id = r.case_id
LEFT JOIN related_cases rc ON c.id = rc.case_id
LEFT JOIN case_watchers w ON c.id = w.case_id
WHERE c.id = ANY($1)
GROUP BY c.id, c.title, c.description, ... -- ALL 20+ case columns
```

**Why this would be a disaster:**
- **Cartesian explosion:** 10 cases × 3 tags × 2 situations × 2 reports × 1 related × 2 watchers = 240 rows for 10 logical cases
- **Query planner chaos:** PostgreSQL's planner becomes unreliable with 8+ JOINs
- **Memory multiplication:** Result sets grow exponentially, not linearly
- **Unpredictable performance:** Great on small datasets, catastrophic on large ones
- **Index confusion:** Complex JOIN plans can't leverage covering indexes efficiently

### 4. Strategic Index Architecture: The Performance Foundation ⚡

**The Index Decision Matrix:**

When we designed our search, we analyzed the correlation between physical storage order and query patterns to choose optimal index types:

```sql
-- Data correlation analysis
SELECT attname, correlation 
FROM pg_stats 
WHERE tablename = 'cases' 
  AND attname IN ('create_time', 'update_time', 'due_date', 'resolved_time');

/*
create_time:    0.97  -- Highly correlated (cases created chronologically)
update_time:    0.23  -- Low correlation (updates happen randomly)  
due_date:       0.11  -- No correlation (due dates set arbitrarily)
resolved_time:  0.89  -- High correlation (recent cases resolved recently)
*/
```

**Our Index Strategy:**

**BRIN Indexes for Sequential Data:**
```sql
-- 2MB storage for time-series data with high correlation
CREATE INDEX brin_cases_create_time ON cases USING brin (create_time);
CREATE INDEX brin_cases_resolved_time ON cases USING brin (resolved_time);
```

**Why BRIN for create_time:**
- **Data pattern:** Cases are created in chronological order (97% correlation)
- **Query pattern:** "Cases created last month" - range queries benefit from block skipping
- **Storage efficiency:** 2MB BRIN vs 150MB B-tree for 1M cases
- **Performance:** 25ms range scans vs 45ms B-tree due to cache locality

**B-tree Indexes for Random Access:**
```sql
-- Standard B-tree for exact lookups and low-correlation data
CREATE INDEX idx_cases_update_time ON cases (update_time);
CREATE INDEX idx_cases_priority ON cases (priority);
CREATE INDEX idx_cases_status ON cases (status);
```

**Why B-tree for update_time:**
- **Data pattern:** Updates scattered throughout case lifecycle (23% correlation)
- **Query pattern:** "Cases updated in last 2 hours" - precise range queries
- **BRIN failure:** Would scan 60-70% of table blocks for recent updates
- **Performance:** 8ms precise range scan vs 180ms BRIN scan

**GIN Indexes for Complex Data:**
```sql
-- Full-text search and array operations
CREATE INDEX idx_cases_search_vector ON cases USING gin(search_vector);
CREATE INDEX gin_cases_additional_info ON cases USING gin (additional_info_json);
```

**Composite Indexes for Common Patterns:**
```sql
-- Multi-tenant queries always filter by org_id first
CREATE INDEX idx_cases_org_status_type ON cases (org_id, status, type);
CREATE INDEX idx_cases_org_priority_create ON cases (org_id, priority, create_time);
```

**Index Storage Analysis (1M cases):**

| Index Type | Count | Storage Per | Total Storage | Use Case | Query Performance |
|------------|-------|-------------|---------------|----------|-------------------|
| **BRIN** | 3 | 2MB | 6MB | Sequential ranges | 20-40ms |
| **B-tree** | 12 | 15MB | 180MB | Random access | 5-20ms |
| **GIN** | 4 | 25MB | 100MB | Text/JSON search | 15-35ms |
| **Composite** | 6 | 18MB | 108MB | Multi-filter queries | 8-25ms |
| **Total** | **25** | | **394MB** | **28% of table size** | |

**The Index Performance Story:**

**Before strategic indexing:**
- Text searches: 2000ms+ (table scans)
- Multi-filter queries: 800ms+ (poor plan choices)
- Users avoided search entirely

**After strategic indexing:**
- Text searches: 25ms (GIN index magic)
- Multi-filter queries: 45ms (composite index efficiency)
- Search became the primary way to find cases

### 5. Security and Input Validation: The Guardian Layer 🛡️

**The Threat Landscape:**

Complex search APIs are attractive targets for various attacks:

```bash
# Query complexity bomb
POST /SearchCases {"query": "x".repeat(10000)}  # 10KB search string

# Filter explosion attack  
POST /SearchCases {"situation_ids": Array(5000).fill("fake-id")}  # 5K ID filters

# Wildcard flood (ReDoS potential)
POST /SearchCases {"query": "%%%%%%%%%%%%%%%%%%%%%%%%"}  # 20 wildcards

# SQL injection attempt
POST /SearchCases {"field_queries": [{"field": "'; DROP TABLE cases; --"}]}

# Pagination bombing
POST /SearchCases {"page_token": "999999"}  # Jump to page 1M
```

**Our Defense System:**

**Input Validation Fortress:**
```go
// Size limits prevent memory exhaustion
if len(req.Query) > 1000 {
    return status.Errorf(codes.InvalidArgument, 
        "search query too long: %d characters (max 1000)", len(req.Query))
}

// Complexity limits prevent ReDoS attacks
if strings.Count(req.Query, "%") > 5 {
    return status.Errorf(codes.InvalidArgument, 
        "too many wildcards: %d (max 5)", strings.Count(req.Query, "%"))
}

// Array size limits prevent parameter explosion
if len(req.SituationIds) > 100 {
    return status.Errorf(codes.InvalidArgument, 
        "too many situation filters: %d (max 100)", len(req.SituationIds))
}

// Page size enforcement prevents resource exhaustion
if req.PageSize > 100 {
    req.PageSize = 100  // Silent cap
}
```

**Query Sanitization Armor:**
```go
// Remove dangerous tsquery metacharacters
func sanitizeSearchQuery(query string) string {
    sanitized := strings.ReplaceAll(query, "&", " ")
    sanitized = strings.ReplaceAll(sanitized, "|", " ")
    sanitized = strings.ReplaceAll(sanitized, "!", " ")
    sanitized = strings.ReplaceAll(sanitized, "'", " ")
    
    // Collapse multiple spaces
    for strings.Contains(sanitized, "  ") {
        sanitized = strings.ReplaceAll(sanitized, "  ", " ")
    }
    
    return strings.TrimSpace(sanitized)
}
```

**Field Allowlisting Shield:**
```go
// Only allow searches on specific fields
var allowedSearchFields = map[string]string{
    "id":          "id",
    "title":       "title",
    "description": "description",
}

func validateSearchFields(fields []string) error {
    for _, field := range fields {
        if _, allowed := allowedSearchFields[field]; !allowed {
            return fmt.Errorf("invalid search field: %s", field)
        }
    }
    return nil
}
```

**Complete Parameterization:**
```go
// All user input is parameterized - zero string concatenation
whereConditions = append(whereConditions, "title ILIKE $" + strconv.Itoa(paramIndex))
args = append(args, "%"+sanitizedInput+"%")
paramIndex++
```

**Timeout Protection:**
```go
// 30-second query timeout prevents runaway searches
searchCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
defer cancel()

result, err := tx.QueryContext(searchCtx, query, args...)
if err != nil {
    if errors.Is(err, context.DeadlineExceeded) {
        return status.Error(codes.DeadlineExceeded, "search query timeout")
    }
    return err
}
```

**Why These Specific Limits?**

| Limit | Value | Rationale |
|-------|-------|-----------|
| Query length | 1000 chars | Balances usability vs memory (1KB per query) |
| Wildcard count | 5 | Prevents exponential regex backtracking |
| Array size | 100 elements | Keeps parameters under PostgreSQL's sweet spot |
| Page size | 100 results | Prevents memory exhaustion while allowing reasonable batch sizes |
| Query timeout | 30 seconds | Allows complex queries while preventing resource locks |

### 6. Smart Pagination and Highlighting: The User Experience Layer ✨

**Pagination Strategy: Offset with Intelligence**

```go
// Calculate offset from page token
offset := 0
if req.PageToken != "" {
    if parsedOffset, err := strconv.Atoi(req.PageToken); err == nil {
        offset = parsedOffset
    }
    // Invalid tokens silently default to offset 0
}

// Apply limit and offset
query += fmt.Sprintf(" LIMIT %d OFFSET %d", pageSize, offset)

// Generate next page token
nextPageToken := ""
if len(cases) == int(pageSize) {  // Full page means more results likely exist
    nextPageToken = strconv.Itoa(offset + int(pageSize))
}
```

**Why Offset Over Cursor?**
- **Simplicity:** Easy to implement and debug
- **User expectations:** Users want to jump to specific pages
- **Caching friendly:** Same query parameters = same results
- **Performance acceptable:** Our queries are fast enough that offset overhead is minimal

**Highlighting Engine: Context with Style**

The search highlighting system helps users quickly see why each case matched their query:

1. **Scans** case title and description for search terms
2. **Extracts** 60-character windows around each match  
3. **Wraps** matched terms in `<mark>` tags for styling
4. **Returns** highlighted snippets to the UI

**What the code does:** Two main functions work together - `generateHighlights()` loops through all search results and calls `findHighlightFragments()` for each case's title and description. The fragment finder does case-insensitive matching, creates text windows around matches, adds ellipsis for truncated text, and wraps the actual matched terms in HTML `<mark>` tags.

**Example Highlighting:**

**Search:** "security breach"
**Case Title:** "Emergency Response to Database Security Breach in Production"
**Highlighted Result:** "…Database <mark>Security Breach</mark> in Production"

**Multiple Matches:**
**Search:** "investigation"  
**Case Description:** "Initial investigation started. Evidence collected during investigation shows..."
**Highlighted Result:** 
- "Initial <mark>investigation</mark> started. Evidence…"
- "…during <mark>investigation</mark> shows..."

## Real-World Performance: The Numbers That Matter 📊

### Query Performance Metrics (Production Data)

| Search Scenario | Dataset | Response Time (95th percentile) | User Experience |
|----------------|---------|----------------------------------|-----------------|
| **Simple text search** | 100K cases | 35ms | ⚡ Instant |
| **Multi-filter query** | 100K cases | 65ms | ⚡ Very fast |
| **Complex combined search** | 100K cases | 95ms | ✅ Fast |
| **Geographic + text + filters** | 100K cases | 120ms | ✅ Responsive |
| **Full-data export query** | 100K cases | 280ms | ⚠️ Acceptable |

### Search Usage Patterns (Real User Behavior)

| Query Type | Frequency | Avg Complexity | Typical Use Case |
|------------|-----------|----------------|------------------|
| **Status + type filters** | 45% | Low | Daily case management |
| **Text search only** | 30% | Medium | Finding specific cases |
| **Date range queries** | 15% | Medium | Reporting and analysis |
| **Complex multi-filter** | 8% | High | Investigation workflows |
| **Export/bulk operations** | 2% | Very high | Compliance audits |

### The Performance Breakdown Stories 📈

**Story 1: The Text Search Transformation**
- **Before indexing:** Text searches took 2000-4000ms (users avoided search)
- **After GIN indexes:** Same searches now take 25-50ms (search became primary navigation)
- **User behavior change:** Search usage increased 400% within one month

**Story 2: The Batch Loading Revolution**
- **Problem discovered:** Response times degraded linearly with result count
- **Root cause:** N+1 queries for relationship loading
- **Solution impact:** 50-case searches went from 750ms to 95ms (87% improvement)
- **Side effect:** Database connection pool pressure reduced dramatically

**Story 3: The Index Storage Tradeoff**
- **Storage cost:** Indexes add 28% to table storage (~400MB for 1M cases)
- **Write performance:** Inserts/updates ~15% slower due to index maintenance
- **Read performance:** Search queries 10-100x faster depending on complexity
- **Business decision:** Easily worth it since cases are read far more than written

## Engineering Decision Chronicles 🎯

### Decision 1: PostgreSQL FTS vs. Elasticsearch

**The Evaluation:**

| Aspect | PostgreSQL FTS | Elasticsearch | Winner |
|--------|----------------|---------------|---------|
| **Setup complexity** | Zero (built-in) | High (cluster management) | PostgreSQL |
| **Operational overhead** | Minimal | Significant | PostgreSQL |
| **Data consistency** | ACID guarantees | Eventual consistency | PostgreSQL |
| **Query capabilities** | Good enough for our needs | More advanced | Tie |
| **Performance** | 25-50ms | 10-30ms | Elasticsearch (slight) |
| **Cost** | License-free | Additional infrastructure | PostgreSQL |
| **Team expertise** | SQL experts | Would need ES experts | PostgreSQL |

**Our Decision:** PostgreSQL FTS
**Rationale:** The performance difference wasn't significant enough to justify the operational complexity

### Decision 2: Index Strategy - Mixed Approach

**The Options:**
1. **All B-tree indexes** - Simple but storage-heavy
2. **All BRIN indexes** - Storage-efficient but poor random access
3. **Mixed strategy** - Right index for each data pattern

**Our Analysis:**
```sql
-- We studied actual query patterns for 3 months
SELECT 
    query_type,
    frequency,
    avg_response_time,
    preferred_index_type
FROM query_analysis;

/*
date_ranges:        35%    45ms     BRIN (for create_time, resolved_time)
exact_lookups:      40%    15ms     B-tree (for status, type, priority)
text_searches:      20%    35ms     GIN (for search_vector)
json_queries:       5%     25ms     GIN (for additional_info_json)
*/
```

**Our Decision:** Mixed index strategy based on data correlation and query patterns
**Result:** Optimal performance for each query type while keeping storage reasonable

### Decision 3: Security Model - Defense in Depth

**The Options:**
1. **Basic validation only** - Fast but risky
2. **Application-level security** - Good but incomplete
3. **Defense in depth** - Slower but comprehensive

**Our Implementation:**
- **Input validation** at service level
- **Query parameterization** at repository level  
- **Row-level security** at database level
- **Query timeouts** for resource protection
- **Audit logging** for security monitoring

**The Result:** Zero security incidents in production, minimal performance impact (<5ms overhead)

## Debugging and Troubleshooting Guide 🔧

### When Search Goes Wrong: A Detective's Manual

#### Investigation 1: "My searches are slow!" 🐌

**Symptoms:** Response times >200ms consistently
**Detective work:**
```sql
-- Check if our indexes are being used
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM cases 
WHERE search_vector @@ plainto_tsquery('english', 'security')
  AND status = ANY('{1,2}'::int[])
  AND create_time >= '2024-01-01';

-- Look for these red flags:
-- ❌ Seq Scan on cases (cost=0.00..50000.00)
-- ❌ -> Seq Scan on case_tags
-- ✅ -> Bitmap Index Scan on idx_cases_search_vector
-- ✅ -> Index Scan using idx_cases_status
```

**Common culprits and fixes:**
- **Missing indexes:** Run index creation scripts
- **Stale statistics:** `ANALYZE cases;`
- **Query too generic:** Searches for 1-2 characters overwhelm trigram indexes
- **Wrong data type:** Ensure parameters match column types

#### Investigation 2: "I'm getting weird results!" 🤔

**Symptoms:** Search returns unexpected cases
**Detective work:**
```go
// Enable highlighting to see WHY each case matched
searchReq.Query = "security"
response, _ := client.SearchCases(ctx, searchReq)

for caseId, highlights := range response.Highlights {
    fmt.Printf("Case %s matched because:\n", caseId)
    for _, fragment := range highlights.Fragments {
        fmt.Printf("  %s: %v\n", fragment.Field, fragment.Highlights)
    }
}
```

**Common culprits:**
- **Stemming surprises:** "run" matches "running", "runs", "ran"
- **Partial matches:** "in" matches "investigation", "Indiana", "spring"
- **Case-insensitive matching:** "apple" matches "Apple", "APPLE"
- **Stop word removal:** "the investigation" becomes just "investigation"

#### Investigation 3: "Pagination is acting weird!" 📄

**Symptoms:** Duplicate cases across pages or missing cases
**Detective work:**
```sql
-- Check if results are deterministically ordered
SELECT id, title, create_time 
FROM cases 
WHERE status = 1 
ORDER BY create_time DESC, id DESC  -- ✅ Deterministic
-- vs
ORDER BY create_time DESC           -- ❌ Non-deterministic for ties
LIMIT 20 OFFSET 20;
```

**The fix:** Always include a unique field (like ID) in ORDER BY for consistent pagination.

#### Investigation 4: "Search highlights are broken!" 🔍

**Symptoms:** Highlighting shows wrong terms or no highlights
**Detective work:**
```go
// Check if search terms are being properly parsed
searchTerms := parseSearchTerms("security breach")
fmt.Printf("Parsed terms: %v\n", searchTerms)  // Should be ["security", "breach"]

// Verify highlighting logic
for _, term := range searchTerms {
    if strings.Contains(strings.ToLower(caseTitle), strings.ToLower(term)) {
        fmt.Printf("Term '%s' found in title '%s'\n", term, caseTitle)
    }
}
```

**Common issues:**
- **Query parsing:** Complex queries with quotes might not parse correctly
- **Character encoding:** Special characters can break highlighting
- **Term extraction:** Make sure search terms are properly extracted from the query

### Performance Monitoring Dashboard 📊

**Key Metrics to Watch:**

```sql
-- Query performance over time
SELECT 
    date_trunc('hour', query_time) as hour,
    query_type,
    avg(duration_ms) as avg_duration,
    max(duration_ms) as max_duration,
    count(*) as query_count
FROM search_performance_log 
WHERE query_time >= NOW() - INTERVAL '24 hours'
GROUP BY hour, query_type
ORDER BY hour DESC;

-- Index usage statistics
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'cases'
ORDER BY idx_scan DESC;

-- Slow query detection
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    min_time,
    max_time
FROM pg_stat_statements 
WHERE query LIKE '%cases%' 
  AND mean_time > 100  -- Queries averaging >100ms
ORDER BY total_time DESC;
```


### The Scaling Roadmap 📈

**Current Architecture Limits:**

| Component | Current Limit | Scaling Strategy |
|-----------|---------------|------------------|
| **Cases volume** | 1M cases | Horizontal partitioning |
| **Concurrent users** | 200 users | Read replicas + connection pooling |
| **Search complexity** | 10 simultaneous filters | Query result caching |
| **Response time** | 95% < 100ms | Materialized views + CDN caching |
| **Storage growth** | 28% index overhead | Compressed indexes + archival |

**When to Scale Each Component:**

1. **1M+ cases:** Implement monthly partitioning
2. **500+ concurrent users:** Add read replicas
3. **>200ms average response:** Implement result caching
4. **Storage costs significant:** Compress older data
5. **International deployment:** Multi-region architecture

## The Engineering Philosophy That Guides Us 🧭

### Core Principles

1. **User experience drives architecture** - Fast, relevant results matter more than perfect code structure
2. **Measure everything, optimize smartly** - Performance assumptions are usually wrong
3. **Security is not negotiable** - Validate all inputs, parameterize all queries, audit all actions
4. **Simple solutions often win** - Complex doesn't always mean better
5. **Build for tomorrow, optimize for today** - Architecture should evolve, but current performance matters now

### The Lessons We've Learned 🎓

**Technical Insights:**
- **PostgreSQL is incredibly powerful** - Full-text search, intelligent indexing, and proper query planning handle most search needs elegantly
- **Index strategy matters more than hardware** - The right index gives 10-100x improvement over throwing more CPU at the problem
- **Batch operations scale linearly** - Always batch when possible; N+1 patterns scale exponentially
- **User intent is hierarchical** - Phrase matches matter more than individual word matches
- **Security and performance can coexist** - Proper validation adds <5ms overhead but prevents disasters

**User Insights:**
- **Search changes user behavior** - Fast search becomes the primary navigation method
- **Performance expectations are exponential** - Users expect search to feel instant (<100ms)
- **Documentation pays dividends** - Future engineers (including yourself) will thank you

### Trade-offs We're Proud Of ✨

| Decision | Short-term Cost | Long-term Benefit | Result |
|----------|----------------|-------------------|---------|
| **Multiple index types** | 28% storage overhead | 10-100x query performance | Users love search |
| **Comprehensive validation** | 5ms request overhead | Zero security incidents | Peace of mind |
| **Batch relationship loading** | Complex implementation | 87% performance improvement | Scalable architecture |
| **Dynamic query building** | Development complexity | Optimal performance per query | Maintainable and fast |

---


**Final Architecture Stats:**
- **25+ specialized indexes** optimized for different query patterns
- **4-level search hierarchy** for intelligent text ranking  
- **6 queries instead of 250+** for relationship loading
- **25-95ms response times** for 99% of real user searches
- **Zero security incidents** with comprehensive input validation
- **11 relationship tables** handled gracefully in a single search

---

## Case-Order Side Effects System

### Overview

The Cases service implements a simplified "Try" pattern for side effects that ensures bidirectional synchronization between cases and orders. This architecture automates workflow transitions and maintains data consistency as cases move through their lifecycle.

**Key Design Principles:**
- Two primary side effect types using the "Try" pattern
- All effects are suggestions that execute conditionally  
- Case status updates handled by Order service side effects
- AssignCase automatically reopens terminal cases (ON_HOLD, RESOLVED, CLOSED, ARCHIVED)
- Intelligent order management prevents duplicates for same investigator
- All operations within single transaction for atomicity

### Architecture Overview

```mermaid
graph TB
    subgraph "Case Operations"
        A[AssignCase RPC] 
        B[UpdateCaseStatus RPC]
        C[CloseCase RPC]
        D[ReopenCase RPC]
    end
    
    subgraph "Side Effect Detection"
        E[CaseSideEffectChecker]
        F[Detect State Changes]
        G[Compare Previous/Current]
    end
    
    subgraph "Side Effect Execution"
        H[CaseSideEffectExecutor]
        I[Create Orders]
        J[Update Orders]
        K[Complete Orders]
    end
    
    subgraph "Order Operations"
        L[AcknowledgeOrder]
        M[CompleteOrder]
        N[Order Side Effects]
    end
    
    subgraph "Database Transaction"
        O[(Cases Table)]
        P[(Orders Table)]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    H --> I
    H --> J
    H --> K
    
    I --> P
    J --> P
    K --> P
    
    L --> N
    M --> N
    N --> O
    
    style E fill:#f9f,stroke:#333,stroke-width:4px
    style H fill:#bbf,stroke:#333,stroke-width:4px
```

### Side Effect Types

The system has been simplified to two primary side effect types following the "Try" pattern:

```mermaid
stateDiagram-v2
    [*] --> CaseSideEffect_None: No Changes
    
    [*] --> TryCreateManageCaseOrder: Investigator Changed/Assigned
    TryCreateManageCaseOrder --> CheckExisting: Check existing orders
    CheckExisting --> CancelIfDifferent: If different investigator
    CheckExisting --> CreateIfNone: If no active orders
    CancelIfDifferent --> CreateNewOrder: Create new MANAGE_CASE order
    CreateIfNone --> CreateNewOrder
    
    [*] --> TryCompleteAllCaseOrders: Case Terminal/Investigator Removed
    TryCompleteAllCaseOrders --> CompleteAll: Complete all non-terminal orders
    
    note right of TryCreateManageCaseOrder
        Triggered when:
        - New investigator assigned
        - Investigator changed
        - Case reactivated from terminal state
    end note
    
    note right of TryCompleteAllCaseOrders
        Triggered when:
        - Investigator removed
        - Case moves to terminal state
        (ON_HOLD, RESOLVED, CLOSED, ARCHIVED)
    end note
```

**Key Changes:**
- All side effects follow the "Try" pattern - they suggest actions but only execute if conditions are met
- Case status updates are now handled by the Order service side effects, not Case service
- Simplified logic: any investigator change triggers `TryCreateManageCaseOrder`
- Terminal states are clearly defined: ON_HOLD, RESOLVED, CLOSED, ARCHIVED

### Case Lifecycle with Side Effects

#### 1. Case Assignment Flow (Reopens Terminal Cases)

```mermaid
sequenceDiagram
    participant User
    participant CaseService
    participant SideEffectChecker
    participant SideEffectExecutor
    participant OrderRepo
    participant CaseRepo
    
    User->>CaseService: AssignCase(caseId, investigatorId)
    Note over CaseService: Works from any status
    
    CaseService->>CaseRepo: Get current case
    
    alt If case is in terminal state (ON_HOLD, RESOLVED, CLOSED, ARCHIVED)
        CaseService->>CaseRepo: UpdateCaseStatus(OPEN)
        Note over CaseRepo: Case reopened automatically
    end
    
    CaseService->>CaseRepo: AssociateAsset(investigator)
    CaseService->>SideEffectChecker: DetectSideEffects(prevCase, newCase)
    SideEffectChecker-->>CaseService: [TryCreateManageCaseOrder]
    
    CaseService->>SideEffectExecutor: ExecuteSideEffect(TryCreateManageCaseOrder)
    SideEffectExecutor->>OrderRepo: ListOrdersForCase(caseId)
    
    alt If investigator changed
        SideEffectExecutor->>OrderRepo: CancelOrder(existing)
        SideEffectExecutor->>OrderRepo: CreateOrder(MANAGE_CASE)
    else If same investigator but no active orders
        SideEffectExecutor->>OrderRepo: CreateOrder(MANAGE_CASE)
    else Same investigator with active orders
        Note over SideEffectExecutor: No action needed
    end
    
    CaseService-->>User: Case assigned (reopened if needed)
```

#### 2. Case Terminal State Transitions

```mermaid
sequenceDiagram
    participant User
    participant CaseService
    participant SideEffectChecker
    participant SideEffectExecutor
    participant OrderRepo
    
    User->>CaseService: UpdateCaseStatus(caseId, ON_HOLD/RESOLVED/CLOSED/ARCHIVED)
    Note over CaseService: Moving to terminal state
    
    CaseService->>CaseRepo: UpdateStatus(terminal_status)
    CaseService->>SideEffectChecker: DetectSideEffects(prevCase, newCase)
    SideEffectChecker-->>CaseService: [TryCompleteAllCaseOrders]
    
    CaseService->>SideEffectExecutor: ExecuteSideEffect(TryCompleteAllCaseOrders)
    SideEffectExecutor->>OrderRepo: ListOrdersForCase(caseId, ALL_STATUSES)
    OrderRepo-->>SideEffectExecutor: All case orders
    
    loop For each non-terminal order
        SideEffectExecutor->>OrderRepo: CompleteOrder(orderId)
        OrderRepo-->>SideEffectExecutor: Order completed
    end
    
    CaseService-->>User: Case status updated, orders completed
```

#### 3. Case Closure Flow

```mermaid
sequenceDiagram
    participant User
    participant CaseService
    participant SideEffectChecker
    participant SideEffectExecutor
    participant OrderRepo
    
    User->>CaseService: CloseCase(caseId, resolution)
    CaseService->>SideEffectChecker: DetectSideEffects(prevCase, closedCase)
    SideEffectChecker-->>CaseService: [CompleteOrdersOnCaseClose]
    
    CaseService->>SideEffectExecutor: ExecuteSideEffect(CompleteOrdersOnCaseClose)
    SideEffectExecutor->>OrderRepo: ListOrdersForCase(caseId, ALL_STATUSES)
    OrderRepo-->>SideEffectExecutor: All case orders
    
    loop For each non-terminal order
        SideEffectExecutor->>OrderRepo: CompleteOrder(orderId)
        OrderRepo-->>SideEffectExecutor: Order completed
    end
    
    CaseService-->>User: Case closed, all orders completed
```

#### 4. Order Acknowledgment Flow (Reverse Side Effect)

```mermaid
sequenceDiagram
    participant Investigator
    participant OrderService
    participant OrderSideEffects
    participant CaseRepo
    
    Investigator->>OrderService: AcknowledgeOrder(orderId)
    OrderService->>OrderService: UpdateStatus(ACKNOWLEDGED)
    
    OrderService->>OrderSideEffects: DetectSideEffects(order)
    OrderSideEffects-->>OrderService: [UpdateCaseToInvestigating]
    
    OrderService->>CaseRepo: UpdateCaseStatus(caseId, INVESTIGATING)
    CaseRepo-->>OrderService: Status updated
    
    OrderService-->>Investigator: Order acknowledged, case now investigating
```

### Implementation Details

#### Side Effect Detection (`cases_detect_side_effects.go`)

```go
type CaseSideEffectChecker struct{}

func (checker *CaseSideEffectChecker) DetectSideEffects(
    previousCase *casespb.Case,
    currentCase *casespb.Case,
) []CaseSideEffectType {
    effects := []CaseSideEffectType{}
    
    // Detect investigator changes
    previousInvestigator := checker.findPrimaryInvestigator(previousCase)
    currentInvestigator := checker.findPrimaryInvestigator(currentCase)
    
    // Check for investigator changes
    if currentInvestigator != nil {
        // We have a primary investigator - check if it's new or changed
        if previousInvestigator == nil || previousInvestigator.AssetId != currentInvestigator.AssetId {
            effects = append(effects, CaseSideEffect_TryCreateManageCaseOrder)
        }
    } else if previousInvestigator != nil {
        // Investigator removed - complete orders
        effects = append(effects, CaseSideEffect_TryCompleteAllCaseOrders)
    }
    
    // Detect status changes to terminal states
    if previousStatus != currentCase.Status {
        if isTerminalStatus(currentCase.Status) && !isTerminalStatus(previousStatus) {
            effects = append(effects, CaseSideEffect_TryCompleteAllCaseOrders)
        } else if !isTerminalStatus(currentCase.Status) && isTerminalStatus(previousStatus) {
            // Case reactivated - create order if investigator exists
            if currentInvestigator != nil {
                effects = append(effects, CaseSideEffect_TryCreateManageCaseOrder)
            }
        }
    }
    
    return effects
}

func isTerminalStatus(status casespb.CaseStatus) bool {
    switch status {
    case casespb.CaseStatus_CASE_STATUS_ON_HOLD,
         casespb.CaseStatus_CASE_STATUS_RESOLVED,
         casespb.CaseStatus_CASE_STATUS_CLOSED,
         casespb.CaseStatus_CASE_STATUS_ARCHIVED:
        return true
    default:
        return false
    }
}
```

#### Side Effect Execution (`cases_execute_side_effects.go`)

The executor implements the "Try" pattern with intelligent decision-making:

**TryCreateManageCaseOrder Logic Flow:**
1. Find primary investigator in both previous and current case states
2. Exit early if no current investigator exists
3. Check if investigator changed from previous state
4. Fetch all existing orders for the case
5. Cancel existing MANAGE_CASE orders if investigator changed
6. Create new order only if:
   - Investigator changed (always create), OR
   - Same investigator but no active MANAGE_CASE orders exist
7. Skip creation if same investigator already has active orders

**TryCompleteAllCaseOrders Logic Flow:**
1. Fetch all orders for the case (any status)
2. Filter to non-terminal orders (not COMPLETED/CANCELLED/REJECTED)
3. Complete each order with context-appropriate reason:
   - "case on hold" for ON_HOLD status
   - "case resolved" for RESOLVED status
   - "case closed" for CLOSED status
   - "case archived" for ARCHIVED status

**Key Design Principles:**
- All operations run within a single transaction for atomicity
- Side effects are suggestions that execute conditionally
- No duplicate orders are created unnecessarily
- Clear separation between case and order responsibilities

### Database Schema

```mermaid
erDiagram
    CASES ||--o{ ORDERS : "has"
    CASES ||--o{ CASE_ASSET_ASSOCIATIONS : "has"
    ORDERS ||--|| ASSETS : "assigned to"
    CASE_ASSET_ASSOCIATIONS ||--|| ASSETS : "references"
    
    CASES {
        text id PK
        int org_id
        text title
        text status
        int priority
        timestamp create_time
        timestamp update_time
    }
    
    ORDERS {
        text id PK
        text case_id FK "NEW: Reference to case"
        text asset_id FK
        text type
        text status
        text instructions
        int priority
        timestamp create_time
        timestamp acknowledged_time
        timestamp completion_time
    }
    
    CASE_ASSET_ASSOCIATIONS {
        text id PK
        text case_id FK
        text asset_id FK
        text association_type
        timestamp assigned_at
    }
    
    ASSETS {
        text id PK
        text name
        text email
    }
```

### Transaction Management

All side effects execute within the same database transaction to ensure atomicity:

```mermaid
graph LR
    A[Begin Transaction] --> B[Update Case]
    B --> C[Detect Side Effects]
    C --> D{Side Effects?}
    D -->|Yes| E[Execute Side Effect 1]
    E --> F[Execute Side Effect 2]
    F --> G[Execute Side Effect N]
    G --> H{All Successful?}
    D -->|No| H
    H -->|Yes| I[Commit Transaction]
    H -->|No| J[Rollback Transaction]
    
    style I fill:#9f9,stroke:#333,stroke-width:2px
    style J fill:#f99,stroke:#333,stroke-width:2px
```

### Performance Optimizations

1. **Indexed Lookups**: 
   - `idx_orders_case_id` for fast case-to-order queries
   - `idx_orders_case_id_status` for filtered status queries

2. **Batch Operations**:
   - Paginated order retrieval for cases with many orders
   - Bulk completion of orders on case closure

3. **Query Optimization**:
   ```sql
   -- Efficient case order lookup
   SELECT * FROM orders 
   WHERE case_id = $1 AND status != 'COMPLETED'
   ORDER BY create_time DESC
   LIMIT 100;
   ```

### Monitoring & Observability

#### OpenTelemetry Spans

```
CaseService.AssignCase
├── CaseSideEffectChecker.DetectSideEffects
│   ├── case.id: case-123
│   └── effects.detected: [CreateManageCaseOrder, UpdateStatus]
└── CaseSideEffectExecutor.ExecuteSideEffect
    ├── side_effect.type: CreateManageCaseOrder
    ├── case.id: case-123
    ├── investigator.asset_id: asset-456
    └── order.created: order-789
```

#### Sentry Error Tracking

All side effects include comprehensive error context:
- Side effect type
- Case and order IDs
- Asset/investigator information
- Transaction state
- Full stack traces

### Testing Strategy

#### Unit Tests
```go
func TestCaseSideEffects(t *testing.T) {
    // Test each side effect type
    t.Run("CreateManageCaseOrder", func(t *testing.T) {
        // Verify order creation when case assigned
    })
    
    t.Run("ReassignCaseOrder", func(t *testing.T) {
        // Verify old order cancelled, new created
    })
    
    t.Run("TransactionRollback", func(t *testing.T) {
        // Verify rollback on side effect failure
    })
}
```

#### Integration Tests
- Full case lifecycle with order synchronization
- Concurrent case updates
- Database constraint validation
- Transaction rollback scenarios

### Troubleshooting Guide

#### Common Issues and Solutions

| Issue | Symptom | Solution |
|-------|---------|----------|
| **Missing case_id** | "invalid filter column: case_id" error | Run migration `20250815134225_add_case_id_to_orders.sql` |
| **Orders not created** | No order appears after case assignment | Verify PRIMARY_INVESTIGATOR association exists and check if existing active orders prevent creation |
| **Case not reopening** | AssignCase doesn't reopen terminal cases | Verify isTerminalStatus() includes ON_HOLD, RESOLVED, CLOSED, ARCHIVED |
| **Duplicate orders** | Multiple active MANAGE_CASE orders | Check tryCreateManageCaseOrder logic for same investigator detection |
| **Orders not completing** | Orders remain active when case closed | Verify TryCompleteAllCaseOrders is triggered for terminal states |
| **Performance degradation** | Slow case/order queries | Check listAllOrdersForCase pagination and verify indexes exist |

### Future Enhancements

1. **Async Processing**: Move non-critical side effects to background jobs
2. **Event Sourcing**: Maintain complete audit trail of all side effects
3. **Circuit Breakers**: Graceful degradation when downstream services fail
4. **Custom Side Effects**: Plugin architecture for business-specific effects
5. **Real-time Notifications**: WebSocket updates for order state changes

### Code References

- **Detection Logic**: `services/workflow/internal/cases/usecase/cases_detect_side_effects.go`
- **Execution Logic**: `services/workflow/internal/cases/usecase/cases_execute_side_effects.go`
- **Order Repository**: `services/workflow/internal/orders/data/postgres_orders_repo.go`
- **Case Use Case**: `services/workflow/internal/cases/usecase/cases_usecase.go`
- **Order Side Effects**: `services/workflow/internal/orders/usecase/orders_detect_side_effects.go`
- **Database Migration**: `migrations/20250815134225_add_case_id_to_orders.sql`