package connect

import (
	"context"
	"database/sql"

	connecthelper "common/connect"
	casesv1 "proto/hero/cases/v1"
	"workflow/internal/cases/usecase"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/emptypb"
)

// CaseServer implements the hero.cases.v1.CaseService interface.
type CaseServer struct {
	useCase *usecase.CaseUseCase
	db      *sql.DB
}

// NewCaseServer creates a new CaseServer instance.
func NewCaseServer(useCase *usecase.CaseUseCase, db *sql.DB) *CaseServer {
	return &CaseServer{
		useCase: useCase,
		db:      db,
	}
}

// CreateCase implements the CreateCase RPC.
func (caseServer *CaseServer) CreateCase(ctx context.Context, req *connect.Request[casesv1.CreateCaseRequest]) (*connect.Response[casesv1.CreateCaseResponse], error) {
	case_, err := caseServer.useCase.CreateCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "CreateCase")
	}
	return connect.NewResponse(&casesv1.CreateCaseResponse{Case_: case_}), nil
}

// GetCase implements the GetCase RPC.
func (caseServer *CaseServer) GetCase(ctx context.Context, req *connect.Request[casesv1.GetCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.GetCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetCase")
	}
	return connect.NewResponse(case_), nil
}

// UpdateCase implements the UpdateCase RPC.
func (caseServer *CaseServer) UpdateCase(ctx context.Context, req *connect.Request[casesv1.UpdateCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.UpdateCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateCase")
	}
	return connect.NewResponse(case_), nil
}

// DeleteCase implements the DeleteCase RPC.
func (caseServer *CaseServer) DeleteCase(ctx context.Context, req *connect.Request[casesv1.DeleteCaseRequest]) (*connect.Response[emptypb.Empty], error) {
	err := caseServer.useCase.DeleteCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DeleteCase")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

// ListCases implements the ListCases RPC.
func (caseServer *CaseServer) ListCases(ctx context.Context, req *connect.Request[casesv1.ListCasesRequest]) (*connect.Response[casesv1.ListCasesResponse], error) {
	response, err := caseServer.useCase.ListCases(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCases")
	}
	return connect.NewResponse(response), nil
}

// BatchGetCases implements the BatchGetCases RPC.
func (caseServer *CaseServer) BatchGetCases(ctx context.Context, req *connect.Request[casesv1.BatchGetCasesRequest]) (*connect.Response[casesv1.BatchGetCasesResponse], error) {
	response, err := caseServer.useCase.BatchGetCases(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "BatchGetCases")
	}
	return connect.NewResponse(response), nil
}

// ListCasesBySituationId implements the ListCasesBySituationId RPC.
func (caseServer *CaseServer) ListCasesBySituationId(ctx context.Context, req *connect.Request[casesv1.ListCasesBySituationIdRequest]) (*connect.Response[casesv1.ListCasesResponse], error) {
	response, err := caseServer.useCase.ListCasesBySituationID(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCasesBySituationId")
	}
	return connect.NewResponse(response), nil
}

// ListCasesByReportId implements the ListCasesByReportId RPC.
func (caseServer *CaseServer) ListCasesByReportId(ctx context.Context, req *connect.Request[casesv1.ListCasesByReportIdRequest]) (*connect.Response[casesv1.ListCasesResponse], error) {
	response, err := caseServer.useCase.ListCasesByReportID(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCasesByReportId")
	}
	return connect.NewResponse(response), nil
}

// ListCasesByAssetId implements the ListCasesByAssetId RPC.
func (caseServer *CaseServer) ListCasesByAssetId(ctx context.Context, req *connect.Request[casesv1.ListCasesByAssetIdRequest]) (*connect.Response[casesv1.ListCasesResponse], error) {
	response, err := caseServer.useCase.ListCasesByAssetID(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCasesByAssetId")
	}
	return connect.NewResponse(response), nil
}

// ListCasesByEntityId implements the ListCasesByEntityId RPC.
func (caseServer *CaseServer) ListCasesByEntityId(ctx context.Context, req *connect.Request[casesv1.ListCasesByEntityIdRequest]) (*connect.Response[casesv1.ListCasesResponse], error) {
	response, err := caseServer.useCase.ListCasesByEntityID(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCasesByEntityId")
	}
	return connect.NewResponse(response), nil
}

// ListCasesByPropertyId implements the ListCasesByPropertyId RPC.
func (caseServer *CaseServer) ListCasesByPropertyId(ctx context.Context, req *connect.Request[casesv1.ListCasesByPropertyIdRequest]) (*connect.Response[casesv1.ListCasesResponse], error) {
	response, err := caseServer.useCase.ListCasesByPropertyID(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCasesByPropertyId")
	}
	return connect.NewResponse(response), nil
}

// AddSituationToCase implements the AddSituationToCase RPC.
func (caseServer *CaseServer) AddSituationToCase(ctx context.Context, req *connect.Request[casesv1.AddSituationToCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddSituation(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddSituationToCase")
	}
	return connect.NewResponse(case_), nil
}

// RemoveSituationFromCase implements the RemoveSituationFromCase RPC.
func (caseServer *CaseServer) RemoveSituationFromCase(ctx context.Context, req *connect.Request[casesv1.RemoveSituationFromCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemoveSituation(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveSituationFromCase")
	}
	return connect.NewResponse(case_), nil
}

// AddReportToCase implements the AddReportToCase RPC.
func (caseServer *CaseServer) AddReportToCase(ctx context.Context, req *connect.Request[casesv1.AddReportToCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddReport(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddReportToCase")
	}
	return connect.NewResponse(case_), nil
}

// RemoveReportFromCase implements the RemoveReportFromCase RPC.
func (caseServer *CaseServer) RemoveReportFromCase(ctx context.Context, req *connect.Request[casesv1.RemoveReportFromCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemoveReport(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveReportFromCase")
	}
	return connect.NewResponse(case_), nil
}

// AddEntityRefToCase implements the AddEntityRefToCase RPC.
func (caseServer *CaseServer) AddEntityRefToCase(ctx context.Context, req *connect.Request[casesv1.AddEntityRefToCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddEntityRef(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddEntityRefToCase")
	}
	return connect.NewResponse(case_), nil
}

// AddPropertyRefToCase implements the AddPropertyRefToCase RPC.
func (caseServer *CaseServer) AddPropertyRefToCase(ctx context.Context, req *connect.Request[casesv1.AddPropertyRefToCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddPropertyRef(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddPropertyRefToCase")
	}
	return connect.NewResponse(case_), nil
}

// RemoveEntityRefFromCase implements the RemoveEntityRefFromCase RPC.
func (caseServer *CaseServer) RemoveEntityRefFromCase(ctx context.Context, req *connect.Request[casesv1.RemoveEntityRefFromCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemoveEntityRef(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveEntityRefFromCase")
	}
	return connect.NewResponse(case_), nil
}

// RemovePropertyRefFromCase implements the RemovePropertyRefFromCase RPC.
func (caseServer *CaseServer) RemovePropertyRefFromCase(ctx context.Context, req *connect.Request[casesv1.RemovePropertyRefFromCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemovePropertyRef(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemovePropertyRefFromCase")
	}
	return connect.NewResponse(case_), nil
}

// LinkRelatedCase implements the LinkRelatedCase RPC.
func (caseServer *CaseServer) LinkRelatedCase(ctx context.Context, req *connect.Request[casesv1.LinkRelatedCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.LinkRelatedCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "LinkRelatedCase")
	}
	return connect.NewResponse(case_), nil
}

// UnlinkRelatedCase implements the UnlinkRelatedCase RPC.
func (caseServer *CaseServer) UnlinkRelatedCase(ctx context.Context, req *connect.Request[casesv1.UnlinkRelatedCaseRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.UnlinkRelatedCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UnlinkRelatedCase")
	}
	return connect.NewResponse(case_), nil
}

// AssociateAssetToCase implements the AssociateAssetToCase RPC.
func (caseServer *CaseServer) AssociateAssetToCase(ctx context.Context, req *connect.Request[casesv1.AssociateAssetToCaseRequest]) (*connect.Response[casesv1.AssociateAssetToCaseResponse], error) {
	association, err := caseServer.useCase.AssociateAsset(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AssociateAssetToCase")
	}
	return connect.NewResponse(&casesv1.AssociateAssetToCaseResponse{Association: association}), nil
}

// UpdateAssetAssociation implements the UpdateAssetAssociation RPC.
func (caseServer *CaseServer) UpdateAssetAssociation(ctx context.Context, req *connect.Request[casesv1.UpdateAssetAssociationRequest]) (*connect.Response[casesv1.UpdateAssetAssociationResponse], error) {
	association, err := caseServer.useCase.UpdateAssetAssociation(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateAssetAssociation")
	}
	return connect.NewResponse(&casesv1.UpdateAssetAssociationResponse{Association: association}), nil
}

// DisassociateAssetFromCase implements the DisassociateAssetFromCase RPC.
func (caseServer *CaseServer) DisassociateAssetFromCase(ctx context.Context, req *connect.Request[casesv1.DisassociateAssetFromCaseRequest]) (*connect.Response[emptypb.Empty], error) {
	err := caseServer.useCase.DisassociateAsset(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "DisassociateAssetFromCase")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

// ListAssetAssociationsForCase implements the ListAssetAssociationsForCase RPC.
func (caseServer *CaseServer) ListAssetAssociationsForCase(ctx context.Context, req *connect.Request[casesv1.ListAssetAssociationsForCaseRequest]) (*connect.Response[casesv1.ListAssetAssociationsForCaseResponse], error) {
	response, err := caseServer.useCase.ListAssetAssociations(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListAssetAssociationsForCase")
	}
	return connect.NewResponse(response), nil
}

// AddWatcher implements the AddWatcher RPC.
func (caseServer *CaseServer) AddWatcher(ctx context.Context, req *connect.Request[casesv1.AddWatcherRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddWatcher(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddWatcher")
	}
	return connect.NewResponse(case_), nil
}

// RemoveWatcher implements the RemoveWatcher RPC.
func (caseServer *CaseServer) RemoveWatcher(ctx context.Context, req *connect.Request[casesv1.RemoveWatcherRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemoveWatcher(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveWatcher")
	}
	return connect.NewResponse(case_), nil
}

// UpdateCaseStatus implements the UpdateCaseStatus RPC.
func (caseServer *CaseServer) UpdateCaseStatus(ctx context.Context, req *connect.Request[casesv1.UpdateCaseStatusRequest]) (*connect.Response[casesv1.UpdateCaseStatusResponse], error) {
	case_, err := caseServer.useCase.UpdateCaseStatus(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "UpdateCaseStatus")
	}
	return connect.NewResponse(&casesv1.UpdateCaseStatusResponse{Case_: case_}), nil
}

// AddCaseUpdate implements the AddCaseUpdate RPC.
func (caseServer *CaseServer) AddCaseUpdate(ctx context.Context, req *connect.Request[casesv1.AddCaseUpdateRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddCaseUpdate(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddCaseUpdate")
	}
	return connect.NewResponse(case_), nil
}

// RemoveCaseUpdate implements the RemoveCaseUpdate RPC.
func (caseServer *CaseServer) RemoveCaseUpdate(ctx context.Context, req *connect.Request[casesv1.RemoveCaseUpdateRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemoveCaseUpdate(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveCaseUpdate")
	}
	return connect.NewResponse(case_), nil
}

// ListCaseUpdates implements the ListCaseUpdates RPC.
func (caseServer *CaseServer) ListCaseUpdates(ctx context.Context, req *connect.Request[casesv1.ListCaseUpdatesRequest]) (*connect.Response[casesv1.ListCaseUpdatesResponse], error) {
	response, err := caseServer.useCase.ListCaseUpdates(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCaseUpdates")
	}
	return connect.NewResponse(response), nil
}

// ListCaseFileAttachments implements the ListCaseFileAttachments RPC.
func (caseServer *CaseServer) ListCaseFileAttachments(ctx context.Context, req *connect.Request[casesv1.ListCaseFileAttachmentsRequest]) (*connect.Response[casesv1.ListCaseFileAttachmentsResponse], error) {
	response, err := caseServer.useCase.ListCaseFileAttachments(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCaseFileAttachments")
	}
	return connect.NewResponse(response), nil
}

// ListCaseStatusHistory implements the ListCaseStatusHistory RPC.
func (caseServer *CaseServer) ListCaseStatusHistory(ctx context.Context, req *connect.Request[casesv1.ListCaseStatusHistoryRequest]) (*connect.Response[casesv1.ListCaseStatusHistoryResponse], error) {
	response, err := caseServer.useCase.ListCaseStatusHistory(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCaseStatusHistory")
	}
	return connect.NewResponse(response), nil
}

// AddCaseTag implements the AddCaseTag RPC.
func (caseServer *CaseServer) AddCaseTag(ctx context.Context, req *connect.Request[casesv1.AddCaseTagRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.AddCaseTag(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddCaseTag")
	}
	return connect.NewResponse(case_), nil
}

// RemoveCaseTag implements the RemoveCaseTag RPC.
func (caseServer *CaseServer) RemoveCaseTag(ctx context.Context, req *connect.Request[casesv1.RemoveCaseTagRequest]) (*connect.Response[casesv1.Case], error) {
	case_, err := caseServer.useCase.RemoveCaseTag(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "RemoveCaseTag")
	}
	return connect.NewResponse(case_), nil
}

// AddAdditionalInfo implements the AddAdditionalInfo RPC.
func (caseServer *CaseServer) AddAdditionalInfo(ctx context.Context, req *connect.Request[casesv1.AddAdditionalInfoRequest]) (*connect.Response[casesv1.AddAdditionalInfoResponse], error) {
	response, err := caseServer.useCase.UpdateAdditionalInfo(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AddAdditionalInfo")
	}
	return connect.NewResponse(response), nil
}

// GetCaseVersion implements the GetCaseVersion RPC.
func (caseServer *CaseServer) GetCaseVersion(ctx context.Context, req *connect.Request[casesv1.GetCaseVersionRequest]) (*connect.Response[casesv1.CaseSnapshot], error) {
	snapshot, err := caseServer.useCase.GetCaseVersion(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "GetCaseVersion")
	}
	return connect.NewResponse(snapshot), nil
}

// ListCaseVersions implements the ListCaseVersions RPC.
func (caseServer *CaseServer) ListCaseVersions(ctx context.Context, req *connect.Request[casesv1.ListCaseVersionsRequest]) (*connect.Response[casesv1.ListCaseVersionsResponse], error) {
	response, err := caseServer.useCase.ListCaseVersions(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCaseVersions")
	}
	return connect.NewResponse(response), nil
}

// ListCaseAuditLog implements the ListCaseAuditLog RPC.
func (caseServer *CaseServer) ListCaseAuditLog(ctx context.Context, req *connect.Request[casesv1.ListCaseAuditLogRequest]) (*connect.Response[casesv1.ListCaseAuditLogResponse], error) {
	response, err := caseServer.useCase.ListCaseAuditLog(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "ListCaseAuditLog")
	}
	return connect.NewResponse(response), nil
}

// SearchCases implements the SearchCases RPC.
func (caseServer *CaseServer) SearchCases(ctx context.Context, req *connect.Request[casesv1.SearchCasesRequest]) (*connect.Response[casesv1.SearchCasesResponse], error) {
	response, err := caseServer.useCase.SearchCases(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "SearchCases")
	}
	return connect.NewResponse(response), nil
}

// AssignCase implements the AssignCase RPC.
func (caseServer *CaseServer) AssignCase(ctx context.Context, req *connect.Request[casesv1.AssignCaseRequest]) (*connect.Response[casesv1.AssignCaseResponse], error) {
	response, err := caseServer.useCase.AssignCase(ctx, req.Msg)
	if err != nil {
		return nil, connecthelper.AsConnectError(ctx, err, "AssignCase")
	}
	return connect.NewResponse(response), nil
}
