package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	casespb "proto/hero/cases/v1"
	entitypb "proto/hero/entity/v1"
	propertypb "proto/hero/property/v1"
)

// ---------------------------------------------------------------------------
// Common errors
// ---------------------------------------------------------------------------

var (
	// ErrCaseNotFound is returned when the target case row does not exist.
	ErrCaseNotFound = fmt.Errorf("case not found")

	// ErrAssociationNotFound is returned when a requested asset–case association
	// cannot be found.
	ErrAssociationNotFound = errors.New("asset association not found")
)

// ---------------------------------------------------------------------------
// Fixed resource‑type constant (generated column in DB, but handy for tests)
// ---------------------------------------------------------------------------

const FixedResourceTypeCase = "CASE"

// ---------------------------------------------------------------------------
// Repository interface
// ---------------------------------------------------------------------------

// CaseRepository abstracts all persistence for hero.cases.v1.
// Every mutating call accepts a *sql.Tx so that callers decide the
// transactional boundaries (e.g., to co‑commit a Case and a Situation).
type CaseRepository interface {
	// ---------- Core CRUD ----------
	CreateCase(ctx context.Context, tx *sql.Tx, c *casespb.Case) (*casespb.Case, error)
	GetCase(ctx context.Context, tx *sql.Tx, id string) (*casespb.Case, error)
	UpdateCase(ctx context.Context, tx *sql.Tx, req *casespb.UpdateCaseRequest) (*casespb.Case, error)
	DeleteCase(ctx context.Context, tx *sql.Tx, id string) error

	// ---------- Listing ----------
	ListCases(ctx context.Context, tx *sql.Tx, req *casespb.ListCasesRequest) (*casespb.ListCasesResponse, error)
	BatchGetCases(ctx context.Context, tx *sql.Tx, ids []string) ([]*casespb.Case, error)

	ListCasesBySituationID(ctx context.Context, tx *sql.Tx, req *casespb.ListCasesBySituationIdRequest) (*casespb.ListCasesResponse, error)
	ListCasesByReportID(ctx context.Context, tx *sql.Tx, req *casespb.ListCasesByReportIdRequest) (*casespb.ListCasesResponse, error)
	ListCasesByAssetID(ctx context.Context, tx *sql.Tx, req *casespb.ListCasesByAssetIdRequest) (*casespb.ListCasesResponse, error)
	ListCasesByEntityID(ctx context.Context, tx *sql.Tx, req *casespb.ListCasesByEntityIdRequest) (*casespb.ListCasesResponse, error)
	ListCasesByPropertyID(ctx context.Context, tx *sql.Tx, req *casespb.ListCasesByPropertyIdRequest) (*casespb.ListCasesResponse, error)

	// ---------- Relationship mutators ----------
	AddSituation(ctx context.Context, tx *sql.Tx, caseID, situationID string) (*casespb.Case, error)
	RemoveSituation(ctx context.Context, tx *sql.Tx, caseID, situationID string) (*casespb.Case, error)

	AddReport(ctx context.Context, tx *sql.Tx, caseID, reportID string) (*casespb.Case, error)
	RemoveReport(ctx context.Context, tx *sql.Tx, caseID, reportID string) (*casespb.Case, error)

	AddEntityRef(ctx context.Context, tx *sql.Tx, caseID string, ref *entitypb.Reference) (*casespb.Case, error)
	RemoveEntityRef(ctx context.Context, tx *sql.Tx, caseID string, ref *entitypb.Reference) (*casespb.Case, error)
	AddPropertyRef(ctx context.Context, tx *sql.Tx, caseID string, ref *propertypb.PropertyReference) (*casespb.Case, error)
	RemovePropertyRef(ctx context.Context, tx *sql.Tx, caseID string, ref *propertypb.PropertyReference) (*casespb.Case, error)

	LinkRelatedCase(ctx context.Context, tx *sql.Tx, caseID, relatedCaseID string) (*casespb.Case, error)
	UnlinkRelatedCase(ctx context.Context, tx *sql.Tx, caseID, relatedCaseID string) (*casespb.Case, error)

	// ---------- Asset associations ----------
	AssociateAsset(ctx context.Context, tx *sql.Tx, caseID string, assoc *casespb.CaseAssetAssociation) (*casespb.CaseAssetAssociation, error)
	UpdateAssetAssociation(ctx context.Context, tx *sql.Tx, caseID string, assoc *casespb.CaseAssetAssociation) (*casespb.CaseAssetAssociation, error)
	DisassociateAsset(ctx context.Context, tx *sql.Tx, caseID, associationID string) error
	ListAssetAssociations(ctx context.Context, tx *sql.Tx, req *casespb.ListAssetAssociationsForCaseRequest) (*casespb.ListAssetAssociationsForCaseResponse, error)

	// ---------- Watchers ----------
	AddWatcher(ctx context.Context, tx *sql.Tx, caseID, assetID string) (*casespb.Case, error)
	RemoveWatcher(ctx context.Context, tx *sql.Tx, caseID, assetID string) (*casespb.Case, error)

	// ---------- Status / updates / tags ----------
	UpdateCaseStatus(ctx context.Context, tx *sql.Tx, req *casespb.UpdateCaseStatusRequest) (*casespb.Case, error)
	AddCaseUpdate(ctx context.Context, tx *sql.Tx, req *casespb.AddCaseUpdateRequest) (*casespb.Case, error)
	RemoveCaseUpdate(ctx context.Context, tx *sql.Tx, req *casespb.RemoveCaseUpdateRequest) (*casespb.Case, error)
	ListCaseUpdates(ctx context.Context, tx *sql.Tx, req *casespb.ListCaseUpdatesRequest) (*casespb.ListCaseUpdatesResponse, error)
	ListCaseFileAttachments(ctx context.Context, tx *sql.Tx, req *casespb.ListCaseFileAttachmentsRequest) (*casespb.ListCaseFileAttachmentsResponse, error)
	ListCaseStatusHistory(ctx context.Context, tx *sql.Tx, req *casespb.ListCaseStatusHistoryRequest) (*casespb.ListCaseStatusHistoryResponse, error)

	AddCaseTag(ctx context.Context, tx *sql.Tx, caseID, tag string) (*casespb.Case, error)
	RemoveCaseTag(ctx context.Context, tx *sql.Tx, caseID, tag string) (*casespb.Case, error)

	UpdateAdditionalInfo(ctx context.Context, tx *sql.Tx, req *casespb.AddAdditionalInfoRequest) (*casespb.AddAdditionalInfoResponse, error)

	// ---------- Versioning / Audit ----------
	GetCaseVersion(ctx context.Context, tx *sql.Tx, caseID string, version int32) (*casespb.CaseSnapshot, error)
	ListCaseVersions(ctx context.Context, tx *sql.Tx, caseID string) ([]int32, error)
	ListCaseAuditLog(ctx context.Context, tx *sql.Tx, req *casespb.ListCaseAuditLogRequest) (*casespb.ListCaseAuditLogResponse, error)

	// ---------- Search ----------
	SearchCases(ctx context.Context, tx *sql.Tx, req *casespb.SearchCasesRequest) (*casespb.SearchCasesResponse, error)
}

// NewCaseRepository wires up the Postgres implementation and returns the
// concrete repo plus the underlying *sql.DB (handy for callers that need raw
// access for other tasks).
func NewCaseRepository(db *sql.DB) (CaseRepository, *sql.DB, error) {
	if db == nil {
		return nil, nil, errors.New("database is nil: cannot initialise CaseRepository")
	}
	return NewPostgresCaseRepository(db), db, nil
}
