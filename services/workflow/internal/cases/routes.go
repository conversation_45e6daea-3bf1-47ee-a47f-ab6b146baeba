package cases

import (
	"database/sql"
	"log"
	"net/http"
	"os"

	clients "common/clients/services"
	"common/herosentry"

	casesconnect "proto/hero/cases/v1/casesconnect"

	connectgo "connectrpc.com/connect"

	assetrepository "workflow/internal/assets/data"
	caseapi "workflow/internal/cases/api/connect"
	caserepository "workflow/internal/cases/data"
	"workflow/internal/cases/usecase"
	"workflow/internal/common/middleware"
	entityrepository "workflow/internal/entity/data"
	orderrepository "workflow/internal/orders/data"
	reportrepository "workflow/internal/reports/data"
	situationrepository "workflow/internal/situations/data"
)

// RegisterRoutes wires all HTTP handlers for the case service onto the given mux.
// It expects a fully‑initialised *sql.DB and repository implementations that
// persist data for the cases domain.
func RegisterRoutes(
	mux *http.ServeMux,
	casesDB *sql.DB,
	assetRepo assetrepository.AssetRepository,
	entityRepo entityrepository.EntityRepository,
	orderRepo orderrepository.OrderRepository,
	reportRepo reportrepository.ReportRepository,
	situationRepo situationrepository.SituationRepository,
	casesRepo caserepository.CaseRepository,
) {

	permsUrl := os.Getenv("PERMS_SERVICE_URL")
	permsClient := clients.NewPermissionClient(permsUrl, herosentry.RPCClientInterceptor())
	// Construct the domain use‑case layer
	caseUseCase, err := usecase.NewCaseUseCase(casesDB, casesRepo, reportRepo, orderRepo, permsClient)
	if err != nil {
		log.Fatalf("Failed to initialise case use case: %v", err)
	}

	// Create the Connect server implementation
	caseServer := caseapi.NewCaseServer(caseUseCase, casesDB)

	// Generate a Connect handler and service path with herosentry interceptor
	servicePath, serviceHandler := casesconnect.NewCaseServiceHandler(
		caseServer,
		connectgo.WithInterceptors(herosentry.RPCServiceInterceptor()),
	)

	// Expose the handler with logging middleware
	mux.Handle(servicePath, middleware.LoggingMiddleware(serviceHandler))
}
