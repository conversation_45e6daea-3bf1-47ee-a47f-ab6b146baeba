# Entity Module

In our system, an Entity represents a dynamic object whose structure is defined by a flexible JSON schema. An entity can represent a person, vehicle, property, or any other type as dictated by its associated schema. The module supports robust auditing, versioning, and fine‑grained access control. It also includes bulk operations, restore/diff endpoints, and **advanced search capabilities** which enable real‑time operational decision making along with detailed historical tracking.

The Entity Module is essential for storing dynamic records, validating data against schemas, maintaining an audit trail with version history, and providing powerful search functionality for operational workflows.

---

## Data Model Reference

### Enums

#### EntityType

| Field                   | Value | Description                                      |
|-------------------------|-------|--------------------------------------------------|
| ENTITY_TYPE_UNSPECIFIED | 0     | Default unspecified entity type.               |
| ENTITY_TYPE_PERSON      | 1     | Represents a person.                             |
| ENTITY_TYPE_VEHICLE     | 2     | Represents a vehicle.                            |
| ENTITY_TYPE_PROPERTY    | 3     | Represents a property.                           |
| ENTITY_TYPE_OTHER       | 4     | Represents any other or undefined type.          |
| ENTITY_TYPE_ORGANIZATION | 5    | Represents an organization.                      |

#### RecordStatus

| Field                     | Value | Description                                         |
|---------------------------|-------|-----------------------------------------------------|
| RECORD_STATUS_UNSPECIFIED | 0     | Default unspecified status.                         |
| RECORD_STATUS_ACTIVE      | 1     | The record is active and in use.                   |
| RECORD_STATUS_ARCHIVED    | 2     | The record is archived for historical purposes.    |
| RECORD_STATUS_DEPRECATED  | 3     | The record is outdated or superseded.              |
| RECORD_STATUS_DELETED     | 4     | The record is marked as deleted (soft deletion).   |
| RECORD_STATUS_DRAFT       | 5     | The record is in a draft stage.                    |

---

### Messages

#### EntitySchema

Defines a JSON schema used to validate dynamic entity data along with audit metadata and versioning details.

| Field                 | Type                     | Description                                                                                         |
|-----------------------|--------------------------|-----------------------------------------------------------------------------------------------------|
| id                    | string                   | Unique identifier for the schema.                                                                   |
| orgId                 | string                   | Organization identifier that owns the schema.                                                       |
| name                  | string                   | Friendly name for the schema.                                                                       |
| description           | string                   | A description of what this schema represents.                                                     |
| schemaDefinition      | google.protobuf.Struct   | Detailed JSON schema for validating entity data.                                                  |
| createTime            | string                   | ISO8601 formatted creation timestamp.                                                             |
| updateTime            | string                   | ISO8601 formatted last update timestamp.                                                          |
| createdBy             | string                   | User ID who created the schema.                                                                     |
| updatedBy             | string                   | User ID who last updated the schema.                                                                |
| version               | int32                   | Version number; incremented upon every change.                                                    |
| entityType            | EntityType               | The type of entity defined by the schema (e.g., PERSON, VEHICLE).                                   |
| status                | RecordStatus             | Current status of the schema.                                                                       |
| tags                  | repeated string          | Optional tags for categorization.                                                                   |
| permissionRulesetId   | string                   | Identifier for the associated permission ruleset.                                                 |

---

#### EntitySchemaVersion

Stores a historical snapshot of an **EntitySchema** for auditing and rollback purposes.

| Field          | Type         | Description                                                        |
|----------------|--------------|--------------------------------------------------------------------|
| schemaId       | string       | Identifier of the corresponding entity schema.                     |
| version        | int32       | Version number for the snapshot.                                   |
| schemaSnapshot | EntitySchema | The state of the schema at this version.                           |
| modifiedBy     | string       | User ID who made the change.                                         |
| modifiedTime   | string       | ISO8601 formatted timestamp of when the change occurred.           |
| changeComment  | string       | Optional comment explaining the change.                            |

---

#### Entity

Represents a dynamic record whose structure is defined by an **EntitySchema**. This message includes metadata for versioning and auditing.

| Field               | Type                     | Description                                                                              |
|---------------------|--------------------------|------------------------------------------------------------------------------------------|
| id                  | string                   | Unique identifier for the entity.                                                        |
| orgId               | string                   | Organization identifier that owns the entity.                                            |
| schemaId            | string                   | Identifier of the schema that defines the entity.                                        |
| schemaVersion       | int32                   | The version of the schema used by the entity.                                            |
| data                | google.protobuf.Struct   | Arbitrary JSON data conforming to the referenced schema.                                 |
| references          | repeated Reference       | Relationships to other entities or external objects.                                     |
| createTime          | string                   | ISO8601 formatted creation timestamp.                                                    |
| updateTime          | string                   | ISO8601 formatted last update timestamp.                                                 |
| entityType          | EntityType               | Classification of the entity (e.g., PERSON, VEHICLE).                                      |
| createdBy           | string                   | User ID who created the entity.                                                          |
| updatedBy           | string                   | User ID who last updated the entity.                                                     |
| version             | int32                   | Current version number of the entity (used for audit/history purposes).                  |
| status              | RecordStatus             | Operational status of the entity.                                                        |
| tags                | repeated string          | Optional tags for further categorization.                                                |
| permissionRulesetId | string                   | Identifier for the associated permission ruleset.                                        |

---

#### EntityVersion

Records a historical snapshot of an **Entity** at a specific version. This facilitates audit trails and potential rollback operations.

| Field          | Type   | Description                                                   |
|----------------|--------|---------------------------------------------------------------|
| entityId       | string | Unique identifier for the entity.                             |
| version        | int32 | Version number of the snapshot.                               |
| entitySnapshot | Entity | The state of the entity at this version.                      |
| modifiedBy     | string | User ID who made the change.                                  |
| modifiedTime   | string | ISO8601 formatted timestamp when the change occurred.         |
| changeComment  | string | Optional comment explaining the change.                       |

---

#### Reference

Captures a relationship between an entity (or schema) and an external object.

| Field | Type   | Description                                       |
|-------|--------|---------------------------------------------------|
| id    | string | Unique identifier for the referenced object.    |
| type  | string | The type of the referenced object (e.g., "entity", "report", etc.). |
| version     | int32 | Version of the referenced object.                         |
| displayName | string | *(Optional)* Display name for the reference.      

--- 

## Example Entity Schema

Below is an example JSON schema for a person entity. This schema defines the structure and validations for person-related data. This is particularly helpful for rendering form in front end, validating data on both front end and backend, and externally control our data object model without changing the underlying implementation again and again. Gives us full flexibility of data we can store, but with robust validation and permission control. We can have different type of data model of similar objects catered for different organizations or municipalities business need. 

```json
{
  "title": "Person",
  "type": "object",
  "properties": {
    "name": {
      "type": "string",
      "description": "Full name of the person"
    },
    "age": {
      "type": "integer",
      "description": "Age of the person",
      "minimum": 0
    },
    "dob": {
      "type": "string",
      "format": "date-time",
      "description": "Date of birth in ISO8601 format"
    },
    "univ_association": {
      "type": "string",
      "description": "University or association details",
      "default": ""
    },
    "address": {
      "type": "object",
      "description": "Nested physical address",
      "properties": {
        "street": { "type": "string", "description": "Street address" },
        "city": { "type": "string", "description": "City" },
        "state": { "type": "string", "description": "State or province", "default": "" },
        "zip": { "type": "string", "description": "Postal code", "default": "" },
        "country": { "type": "string", "description": "Country name", "default": "" }
      },
      "required": ["street", "city"],
      "additionalProperties": false
    },
    "primary_phone": {
      "type": "string",
      "description": "Primary phone number",
      "default": ""
    },
    "secondary_phone": {
      "type": "string",
      "description": "Secondary phone number",
      "default": ""
    },
    "report_id": {
      "type": "array",
      "description": "List of report IDs linked to the person",
      "items": { "type": "string" },
      "default": []
    }
  },
  "required": ["name"],
  "additionalProperties": false
}
```

When creating an entity schema for a person, the above JSON would be stored in the `schemaDefinition` field of the **EntitySchema** message.

---

## Overview of Endpoints

The Entity Module now defines the following operations:

### Entity Endpoints

You can import the postman collection using the file `services/workflow/internal/entity/postman collection/EntityLocalPostman.json`

1. **[CreateEntity](#1-createentity)**
2. **[GetLatestEntity](#2-getlatestentity)**
3. **[GetEntityByVersion](#3-getentitybyversion)**
4. **[GetLatestActiveEntity](#4-getlatestactiveentity)**
5. **[UpdateEntity](#5-updateentity)**
6. **[ListLatestEntities](#6-listlatestentities)**
7. **[DeleteAllVersionsOfEntity](#7-deleteallversionsofentity)**
8. **[DeleteSpecificVersionOfEntity](#8-deletespecificversionofentity)**
9. **[ListAllVersionsOfEntity](#9-listallversionsofentity)**
10. **[BulkCreateEntities](#10-bulkcreateentities)**
11. **[BulkUpdateEntities](#11-bulkupdateentities)**
12. **[BulkDeleteEntities](#12-bulkdeleteentities)**
13. **[RestoreEntityVersion](#13-restoreentityversion)**
14. **[DiffEntityVersions](#14-diffentityversions)**
15. **[CheckEntityPermissions](#15-checkentitypermissions)**
16. **[BatchGetLatestEntities](#16-batchgetlatestentities)**
17. **[SearchEntities](#17-searchentities)**

### EntitySchema Endpoints

1. **[CreateEntitySchema](#1-createentityschema)**
2. **[GetLatestEntitySchema](#2-getlatestentityschema)**
3. **[GetLatestActiveEntitySchema](#3-getlatestactiveentityschema)**
4. **[GetEntitySchemaByVersion](#4-getentityschemabyversion)**
5. **[UpdateEntitySchema](#5-updateentityschema)**
6. **[DeleteAllVersionsOfEntitySchema](#6-deleteallversionsofentityschema)**
7. **[DeleteSpecificVersionOfEntitySchema](#7-deletespecificversionofentityschema)**
8. **[ListLatestEntitySchemas](#8-listlatestentityschemas)**
9. **[ListAllVersionsOfEntitySchema](#9-listallversionsofentityschema)**
10. **[BulkCreateEntitySchemas](#10-bulkcreateentityschemas)**
11. **[BulkUpdateEntitySchemas](#11-bulkupdateentityschemas)**
12. **[BulkDeleteEntitySchemas](#12-bulkdeleteentityschemas)**
13. **[RestoreEntitySchemaVersion](#13-restoreentityschemaversion)**
14. **[DiffEntitySchemaVersions](#14-diffentityschemaversions)**
15. **[CheckEntitySchemaPermissions](#15-checkentityschemapermissions)**

---

### Entity Endpoints

---

#### 1. CreateEntity

**Method:** `CreateEntity`  
**Route:** `POST /hero.entity.v1.EntityService/CreateEntity`

#### Message Fields

**CreateEntityRequest:**

| Field  | Type   | Description                                      |
|--------|--------|--------------------------------------------------|
| entity | Entity | The entity object to be created.               |

**CreateEntityResponse:**

| Field  | Type   | Description                                                                                              |
|--------|--------|----------------------------------------------------------------------------------------------------------|
| entity | Entity | The newly created entity object with a generated `id` (refer to the [Entity](#entity) data model).         |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "entity": {
    "orgId": "org-001",
    "schemaId": "schema-123",
    "schemaVersion": 1,
    "data": { "firstName": "Alice", "age": 25 },
    "createdBy": "user-001",
    "updatedBy": "user-001",
    "entityType": "ENTITY_TYPE_PERSON",
    "tags": ["customer"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

**Response (JSON):**
```json
{
  "entity": {
    "id": "generated-uuid-entity",
    "orgId": "org-001",
    "schemaId": "schema-123",
    "schemaVersion": 1,
    "data": { "firstName": "Alice", "age": 25 },
    "references": [],
    "createTime": "2025-04-09T08:00:00Z",
    "updateTime": "2025-04-09T08:00:00Z",
    "entityType": "ENTITY_TYPE_PERSON",
    "createdBy": "user-001",
    "updatedBy": "user-001",
    "version": 1,
    "status": "RECORD_STATUS_ACTIVE",
    "tags": ["customer"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

---

#### 2. GetLatestEntity

**Method:** `GetLatestEntity`  
**Route:** `POST /hero.entity.v1.EntityService/GetLatestEntity`

#### Message Fields

**GetLatestEntityRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier for the entity. |

**Response:**

| Field  | Type   | Description                                                                             |
|--------|--------|-----------------------------------------------------------------------------------------|
|        | Entity | The entity object with the latest version (refer to the [Entity](#entity) data model).    |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123"
}
```

**Response (JSON):**
```json
{
  "id": "entity-123",
  "orgId": "org-001",
  "schemaId": "schema-123",
  "schemaVersion": 1,
  "data": { "firstName": "Alice", "age": 25 },
  "references": [],
  "createTime": "2025-04-09T08:00:00Z",
  "updateTime": "2025-04-09T08:30:00Z",
  "entityType": "ENTITY_TYPE_PERSON",
  "createdBy": "user-001",
  "updatedBy": "user-001",
  "version": 1,
  "status": "RECORD_STATUS_ACTIVE",
  "tags": ["customer"],
  "permissionRulesetId": "ruleset-001"
}
```

---

#### 3. GetEntityByVersion

**Method:** `GetEntityByVersion`  
**Route:** `POST /hero.entity.v1.EntityService/GetEntityByVersion`

#### Message Fields

**GetEntityByVersionRequest:**

| Field   | Type   | Description                                    |
|---------|--------|------------------------------------------------|
| id      | string | Unique identifier for the entity.              |
| version | int32 | The specific version number of the entity.     |

**Response:**

| Field  | Type   | Description                                                                      |
|--------|--------|----------------------------------------------------------------------------------|
|        | Entity | The entity object corresponding to the specified version.                      |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123",
  "version": 1
}
```

**Response (JSON):**
```json
{
  "id": "entity-123",
  "orgId": "org-001",
  "schemaId": "schema-123",
  "schemaVersion": 1,
  "data": { "firstName": "Alice", "age": 25 },
  "references": [],
  "createTime": "2025-04-09T08:00:00Z",
  "updateTime": "2025-04-09T08:30:00Z",
  "entityType": "ENTITY_TYPE_PERSON",
  "createdBy": "user-001",
  "updatedBy": "user-001",
  "version": 1,
  "status": "RECORD_STATUS_ACTIVE",
  "tags": ["customer"],
  "permissionRulesetId": "ruleset-001"
}
```

---

#### 4. GetLatestActiveEntity

**Method:** `GetLatestActiveEntity`  
**Route:** `POST /hero.entity.v1.EntityService/GetLatestActiveEntity`

#### Message Fields

**GetLatestActiveEntityRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier for the entity. |

**Response:**

| Field  | Type   | Description                                                                             |
|--------|--------|-----------------------------------------------------------------------------------------|
|        | Entity | The latest active entity (refer to the [Entity](#entity) data model).                   |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123"
}
```

**Response (JSON):**
```json
{
  "id": "entity-123",
  "orgId": "org-001",
  "schemaId": "schema-123",
  "schemaVersion": 1,
  "data": { "firstName": "Alice", "age": 25 },
  "references": [],
  "createTime": "2025-04-09T08:00:00Z",
  "updateTime": "2025-04-09T08:45:00Z",
  "entityType": "ENTITY_TYPE_PERSON",
  "createdBy": "user-001",
  "updatedBy": "user-002",
  "version": 2,
  "status": "RECORD_STATUS_ACTIVE",
  "tags": ["customer"],
  "permissionRulesetId": "ruleset-001"
}
```

---

#### 5. UpdateEntity

**Method:** `UpdateEntity`  
**Route:** `POST /hero.entity.v1.EntityService/UpdateEntity`

#### Message Fields

**UpdateEntityRequest:**

| Field  | Type   | Description                                       |
|--------|--------|---------------------------------------------------|
| entity | Entity | The complete entity object to update. **Note:** Must include the `id`. |

**UpdateEntity Response:**

| Field  | Type   | Description                                                                                |
|--------|--------|--------------------------------------------------------------------------------------------|
| entity | Entity | The updated entity object reflecting the new changes (refer to the [Entity](#entity) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "entity": {
    "id": "entity-123",
    "orgId": "org-001",
    "schemaId": "schema-123",
    "schemaVersion": 1,
    "data": { "firstName": "Alice", "age": 26 },
    "updatedBy": "user-002",
    "entityType": "ENTITY_TYPE_PERSON",
    "tags": ["customer", "loyal"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

**Response (JSON):**
```json
{
  "entity": {
    "id": "entity-123",
    "orgId": "org-001",
    "schemaId": "schema-123",
    "schemaVersion": 1,
    "data": { "firstName": "Alice", "age": 26 },
    "references": [],
    "createTime": "2025-04-09T08:00:00Z",
    "updateTime": "2025-04-09T09:00:00Z",
    "entityType": "ENTITY_TYPE_PERSON",
    "createdBy": "user-001",
    "updatedBy": "user-002",
    "version": 2,
    "status": "RECORD_STATUS_ACTIVE",
    "tags": ["customer", "loyal"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

---

#### 6. ListLatestEntities

**Method:** `ListLatestEntities`  
**Route:** `POST /hero.entity.v1.EntityService/ListLatestEntities`

#### Message Fields

**ListLatestEntitiesRequest:**

| Field     | Type   | Description                                                         |
|-----------|--------|---------------------------------------------------------------------|
| pageSize  | int32  | Maximum number of entities to return in the response.               |
| pageToken | string | Token identifying a specific page of results to retrieve.           |
| entityType| EntityType | (Optional) Filter entities by type. If unspecified, returns all entities. |

**ListLatestEntitiesResponse:**

| Field         | Type            | Description                                                    |
|---------------|-----------------|----------------------------------------------------------------|
| entities      | repeated Entity | List of entity objects.                                        |
| nextPageToken | string          | Token to retrieve the next page of results, if any.            |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "pageSize": 10,
  "pageToken": "",
  "entityType": "ENTITY_TYPE_PERSON"
}
```

**Response (JSON):**
```json
{
  "entities": [
    {
      "id": "entity-123",
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 26 },
      "references": [],
      "createTime": "2025-04-09T08:00:00Z",
      "updateTime": "2025-04-09T09:00:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-001",
      "updatedBy": "user-002",
      "version": 2,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["customer", "loyal"],
      "permissionRulesetId": "ruleset-001"
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

#### 7. DeleteAllVersionsOfEntity

**Method:** `DeleteAllVersionsOfEntity`  
**Route:** `POST /hero.entity.v1.EntityService/DeleteAllVersionsOfEntity`

#### Message Fields

**DeleteAllVersionsOfEntityRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier for the entity. |

**DeleteAllVersionsOfEntity Response:**

| Field | Type              | Description                |
|-------|-------------------|----------------------------|
|       | google.protobuf.Empty | (Empty response message.) |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123"
}
```

**Response (JSON):**
```json
{}
```

---

#### 8. DeleteSpecificVersionOfEntity

**Method:** `DeleteSpecificVersionOfEntity`  
**Route:** `POST /hero.entity.v1.EntityService/DeleteSpecificVersionOfEntity`

#### Message Fields

**DeleteSpecificVersionOfEntityRequest:**

| Field   | Type   | Description                         |
|---------|--------|-------------------------------------|
| id      | string | Unique identifier for the entity. |
| version | int32 | The version to delete.              |

**DeleteSpecificVersionOfEntity Response:**

| Field | Type              | Description                |
|-------|-------------------|----------------------------|
|       | google.protobuf.Empty | (Empty response message.) |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123",
  "version": 1
}
```

**Response (JSON):**
```json
{}
```

---

#### 9. ListAllVersionsOfEntity

**Method:** `ListAllVersionsOfEntity`  
**Route:** `POST /hero.entity.v1.EntityService/ListAllVersionsOfEntity`

#### Message Fields

**ListAllVersionsOfEntityRequest:**

| Field    | Type   | Description                        |
|----------|--------|------------------------------------|
| entityId | string | Unique identifier for the entity. |

**ListAllVersionsOfEntityResponse:**

| Field    | Type                   | Description                                                                       |
|----------|------------------------|-----------------------------------------------------------------------------------|
| versions | repeated EntityVersion | Historical snapshots of the entity (refer to the [EntityVersion](#entityversion) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "entityId": "entity-123"
}
```

**Response (JSON):**
```json
{
  "versions": [
    {
      "entityId": "entity-123",
      "version": 1,
      "entitySnapshot": {
        "id": "entity-123",
        "orgId": "org-001",
        "schemaId": "schema-123",
        "schemaVersion": 1,
        "data": { "firstName": "Alice", "age": 25 },
        "references": [],
        "createTime": "2025-04-09T08:00:00Z",
        "updateTime": "2025-04-09T08:00:00Z",
        "entityType": "ENTITY_TYPE_PERSON",
        "createdBy": "user-001",
        "updatedBy": "user-001",
        "version": 1,
        "status": "RECORD_STATUS_ACTIVE",
        "tags": ["customer"],
        "permissionRulesetId": "ruleset-001"
      },
      "modifiedBy": "user-001",
      "modifiedTime": "2025-04-09T08:30:00Z",
      "changeComment": "Initial creation"
    },
    {
      "entityId": "entity-123",
      "version": 2,
      "entitySnapshot": {
        "id": "entity-123",
        "orgId": "org-001",
        "schemaId": "schema-123",
        "schemaVersion": 1,
        "data": { "firstName": "Alice", "age": 26 },
        "references": [],
        "createTime": "2025-04-09T08:00:00Z",
        "updateTime": "2025-04-09T09:00:00Z",
        "entityType": "ENTITY_TYPE_PERSON",
        "createdBy": "user-001",
        "updatedBy": "user-002",
        "version": 2,
        "status": "RECORD_STATUS_ACTIVE",
        "tags": ["customer"],
        "permissionRulesetId": "ruleset-001"
      },
      "modifiedBy": "user-002",
      "modifiedTime": "2025-04-09T09:00:00Z",
      "changeComment": "Updated age from 25 to 26"
    }
  ]
}

```

---

#### 10. BulkCreateEntities

**Note:** Not tested

**Method:** `BulkCreateEntities`  
**Route:** `POST /hero.entity.v1.EntityService/BulkCreateEntities`

#### Message Fields

**BulkCreateEntitiesRequest:**

| Field   | Type             | Description                                |
|---------|------------------|--------------------------------------------|
| entities| repeated Entity  | List of entity objects to be created.      |

**BulkCreateEntitiesResponse:**

| Field   | Type             | Description                                |
|---------|------------------|--------------------------------------------|
| entities| repeated Entity  | List of newly created entities.            |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "entities": [
    {
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 25 },
      "createdBy": "user-001",
      "updatedBy": "user-001",
      "entityType": "ENTITY_TYPE_PERSON",
      "tags": ["customer"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "orgId": "org-002",
      "schemaId": "schema-456",
      "schemaVersion": 1,
      "data": { "firstName": "Bob", "age": 30 },
      "createdBy": "user-002",
      "updatedBy": "user-002",
      "entityType": "ENTITY_TYPE_PERSON",
      "tags": ["client"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

**Response (JSON):**
```json
{
  "entities": [
    {
      "id": "generated-uuid-entity1",
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 25 },
      "references": [],
      "createTime": "2025-04-09T08:00:00Z",
      "updateTime": "2025-04-09T08:00:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-001",
      "updatedBy": "user-001",
      "version": 1,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["customer"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "id": "generated-uuid-entity2",
      "orgId": "org-002",
      "schemaId": "schema-456",
      "schemaVersion": 1,
      "data": { "firstName": "Bob", "age": 30 },
      "references": [],
      "createTime": "2025-04-09T08:05:00Z",
      "updateTime": "2025-04-09T08:05:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-002",
      "updatedBy": "user-002",
      "version": 1,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["client"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

---

#### 11. BulkUpdateEntities

**Note:** Not tested

**Method:** `BulkUpdateEntities`  
**Route:** `POST /hero.entity.v1.EntityService/BulkUpdateEntities`

#### Message Fields

**BulkUpdateEntitiesRequest:**

| Field   | Type             | Description                                 |
|---------|------------------|---------------------------------------------|
| entities| repeated Entity  | List of entity objects with updated values.  |

**BulkUpdateEntitiesResponse:**

| Field   | Type             | Description                                 |
|---------|------------------|---------------------------------------------|
| entities| repeated Entity  | List of updated entities.                    |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "entities": [
    {
      "id": "entity-123",
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 26 },
      "updatedBy": "user-002",
      "entityType": "ENTITY_TYPE_PERSON",
      "tags": ["customer", "vip"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "id": "entity-456",
      "orgId": "org-002",
      "schemaId": "schema-456",
      "schemaVersion": 1,
      "data": { "firstName": "Bob", "age": 31 },
      "updatedBy": "user-003",
      "entityType": "ENTITY_TYPE_PERSON",
      "tags": ["client", "premium"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

**Response (JSON):**
```json
{
  "entities": [
    {
      "id": "entity-123",
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 26 },
      "references": [],
      "createTime": "2025-04-09T08:00:00Z",
      "updateTime": "2025-04-09T09:00:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-001",
      "updatedBy": "user-002",
      "version": 2,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["customer", "vip"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "id": "entity-456",
      "orgId": "org-002",
      "schemaId": "schema-456",
      "schemaVersion": 1,
      "data": { "firstName": "Bob", "age": 31 },
      "references": [],
      "createTime": "2025-04-09T08:05:00Z",
      "updateTime": "2025-04-09T09:10:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-002",
      "updatedBy": "user-003",
      "version": 2,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["client", "premium"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

---

#### 12. BulkDeleteEntities

**Note:** Not tested

**Method:** `BulkDeleteEntities`  
**Route:** `POST /hero.entity.v1.EntityService/BulkDeleteEntities`

#### Message Fields

**BulkDeleteEntitiesRequest:**

| Field | Type              | Description                                    |
|-------|-------------------|------------------------------------------------|
| ids   | repeated string   | List of unique identifiers of the entities to delete. |

**BulkDeleteEntities Response:**

| Field | Type                    | Description                |
|-------|-------------------------|----------------------------|
|       | google.protobuf.Empty   | (Empty response message.)  |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "ids": ["entity-123", "entity-456"]
}
```

**Response (JSON):**
```json
{}
```

---

#### 13. RestoreEntityVersion

**Method:** `RestoreEntityVersion`  
**Route:** `POST /hero.entity.v1.EntityService/RestoreEntityVersion`

#### Message Fields

**RestoreEntityVersionRequest:**

| Field   | Type   | Description                                   |
|---------|--------|-----------------------------------------------|
| id      | string | Unique identifier for the entity.             |
| version | int32 | Version number to which the entity should be restored. |

**RestoreEntityVersionResponse:**

| Field  | Type   | Description                                                                      |
|--------|--------|----------------------------------------------------------------------------------|
| entity | Entity | The entity object restored to the specified version (refer to the [Entity](#entity) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123",
  "version": 1
}
```

**Response (JSON):**
```json
{
  "entity": {
    "id": "entity-123",
    "orgId": "org-001",
    "schemaId": "schema-123",
    "schemaVersion": 1,
    "data": { "firstName": "Alice", "age": 25 },
    "references": [],
    "createTime": "2025-04-09T08:00:00Z",
    "updateTime": "2025-04-09T08:30:00Z",
    "entityType": "ENTITY_TYPE_PERSON",
    "createdBy": "user-001",
    "updatedBy": "user-001",
    "version": 1,
    "status": "RECORD_STATUS_ACTIVE",
    "tags": ["customer"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

---

#### 14. DiffEntityVersions

**Note:** Unstable - need more work

**Method:** `DiffEntityVersions`  
**Route:** `POST /hero.entity.v1.EntityService/DiffEntityVersions`

#### Message Fields

**DiffEntityVersionsRequest:**

| Field    | Type   | Description                                     |
|----------|--------|-------------------------------------------------|
| id       | string | Unique identifier for the entity.               |
| version1 | int32 | The first version number to compare.            |
| version2 | int32 | The second version number to compare.           |

**DiffEntityVersionsResponse:**

| Field      | Type   | Description                                     |
|------------|--------|-------------------------------------------------|
| diffResult | string | Textual or JSON diff result between the two versions. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "entity-123",
  "version1": 1,
  "version2": 2
}
```

**Response (JSON):**
```json
{
  "diffResult": "{ \"age\": { \"from\": 25, \"to\": 26 } }"
}
```

---

#### 15. CheckEntityPermissions

**Note:** Not implemented


**Method:** `CheckEntityPermissions`  
**Route:** `POST /hero.entity.v1.EntityService/CheckEntityPermissions`

#### Message Fields

**CheckEntityPermissionsRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| entityId | string | Unique identifier for the entity.                |
| userId   | string | User ID to check permissions for.                |
| action   | string | The action to check (e.g., "read", "write", "delete"). |

**CheckEntityPermissionsResponse:**

| Field   | Type   | Description                        |
|---------|--------|------------------------------------|
| allowed | bool   | Whether the user is permitted for the action. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "entityId": "entity-123",
  "userId": "user-001",
  "action": "write"
}
```

**Response (JSON):**
```json
{
  "allowed": true
}
```

---

#### 16. BatchGetLatestEntities

**Note:** It will maintain the order of the ids in the response, if the id is not found it will return an empty entity

**Method:** `BatchGetLatestEntities`  
**Route:** `POST /hero.entity.v1.EntityService/BatchGetLatestEntities`

#### Message Fields

**BatchGetLatestEntitiesRequest:**

| Field | Type             | Description                                    |
|-------|------------------|------------------------------------------------|
| ids   | repeated string  | The list of entity IDs to retrieve.           |

**BatchGetLatestEntitiesResponse:**

| Field    | Type             | Description                                    |
|----------|------------------|------------------------------------------------|
| entities | repeated Entity  | The set of entities matching the provided IDs. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "ids": ["entity-123", "entity-456"]
}
```

**Response (JSON):**
```json
{
  "entities": [
    {
      "id": "entity-123",
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 25 },
      "references": [],
      "createTime": "2025-04-09T08:00:00Z",
      "updateTime": "2025-04-09T08:30:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-001",
      "updatedBy": "user-001",
      "version": 1,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["customer"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "id": "entity-456",
      "orgId": "org-002",
      "schemaId": "schema-456",
      "schemaVersion": 1,
      "data": { "firstName": "Bob", "age": 30 },
      "references": [],
      "createTime": "2025-04-09T08:15:00Z",
      "updateTime": "2025-04-09T08:45:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-002",
      "updatedBy": "user-002",
      "version": 1,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["client"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

---

#### 17. SearchEntities

**Method:** `SearchEntities`  
**Route:** `POST /hero.entity.v1.EntityService/SearchEntities`

**Important Note:** SearchEntities no longer returns archived entities by default. To include archived entities in search results, set `"include_archived_entities": true` in the request. Alternatively, you can explicitly specify `"status": ["RECORD_STATUS_ARCHIVED"]` for archived entities only, or `"status": ["RECORD_STATUS_ACTIVE", "RECORD_STATUS_ARCHIVED"]` for both active and archived entities.

#### Message Fields

**SearchEntitiesRequest:**

| Field             | Type                     | Description                                                                                         |
|-------------------|--------------------------|-----------------------------------------------------------------------------------------------------|
| query             | string                   | Full-text search query across all indexed fields (or fields specified in `search_fields`)         |
| search_fields     | repeated string          | Limit `query` to specific fields; empty = search all fields                                        |
| field_queries     | repeated FieldQuery      | Individual field-specific search queries (independent of `query`)                                  |
| status            | repeated RecordStatus    | Filter by entity status (exact match)                                                              |
| created_by        | repeated string          | Filter by creator user IDs (exact match)                                                           |
| updated_by        | repeated string          | Filter by updater user IDs (exact match)                                                           |
| schema_ids        | repeated string          | Filter by schema IDs (exact match)                                                                 |
| entity_types      | repeated EntityType      | Filter by entity types (exact match)                                                               |
| org_ids           | repeated int32           | Filter by organization IDs (exact match)                                                           |
| tags              | repeated string          | Filter by tags (exact match)                                                                       |
| reference_ids     | repeated string          | Filter by reference IDs (exact match)                                                              |
| reference_types   | repeated string          | Filter by reference types (exact match)                                                            |
| create_time       | DateRange                | Filter by creation time range                                                                       |
| update_time       | DateRange                | Filter by update time range                                                                         |
| page_size         | int32                    | Maximum number of results (1-1000, default: 50)                                                    |
| page_token        | string                   | Pagination cursor                                                                                   |
| order_by          | SearchOrderBy            | Sort order (RELEVANCE, CREATED_AT, UPDATED_AT, STATUS)                                             |
| ascending         | bool                     | Sort direction (default: false = DESC)                                                             |
| include_archived_entities | bool              | Include archived entities in search results (default: false)                                        |

**SearchEntitiesResponse:**

| Field           | Type                              | Description                                                                     |
|-----------------|-----------------------------------|---------------------------------------------------------------------------------|
| entities        | repeated Entity                   | Page of matching entities                                                       |
| next_page_token | string                            | Cursor for next page (empty if last page)                                      |
| highlights      | map<string, HighlightResult>      | Search term highlights keyed by entity ID                                      |
| total_results   | int32                             | Total number of matching entities (before pagination)                          |

#### Comprehensive Search Fields Documentation

The search functionality supports two distinct approaches for text-based searching:

1. **Global Search (`query` parameter)**: Search across multiple fields simultaneously
2. **Field-Specific Search (`field_queries` parameter)**: Target specific fields with individual terms

### Search Fields Reference

The following fields are supported for both `search_fields` and `field_queries.field`:

#### `id` - Entity Identifier Search
- **Target**: `entities.id` column
- **Search Type**: Partial/fuzzy matching using trigram indexes
- **Use Cases**: Find entities by partial ID, UUID fragments
- **Performance**: Optimized with GIN trigram index (`idx_entities_id_trgm`)
- **Example**: Search for "abc-123" will match entity IDs like "entity-abc-123-xyz"

#### `data` - JSON Data Content Search  
- **Target**: `entities.data_values` generated column (extracts only JSON values, excludes keys)
- **Search Type**: Pattern matching across JSON values only (no keys/field names)
- **Use Cases**: Search within entity data values without matching field names
- **Performance**: Uses GIN trigram index (`gin_entities_data_values`) and B-tree index (`idx_entities_data_values_text`)
- **Example**: Search for "Alice" will match `{"firstName": "Alice", "lastName": "Smith"}` but searching for "firstName" will NOT match
- **Implementation**: Uses a recursive PL/pgSQL function that:
  - Extracts all values from nested JSON objects and arrays
  - Excludes all keys/field names from the search text
  - Removes duplicates and sorts values alphabetically
- **Note**: This ensures field names are never searchable, only the actual data values

#### `tags` - Entity Tags Search
- **Target**: `entities.tags` text array column
- **Search Type**: Pattern matching across concatenated tag values
- **Use Cases**: Find entities with specific tag content
- **Performance**: Uses GIN index on tag arrays (`gin_entities_tags`)
- **Example**: Search for "customer" will match entities with tags `["vip_customer", "loyal"]`
- **Implementation**: Converts array to space-separated string: `array_to_string(entities.tags, ' ')`

#### `reference_display_name` - Reference Display Names
- **Target**: `entity_references.display_name` column
- **Search Type**: Partial/fuzzy matching using trigram indexes
- **Use Cases**: Find entities by their reference relationships' display names
- **Performance**: Optimized with GIN trigram index (`idx_entity_references_display_name_trgm`)
- **Join Behavior**: Automatically triggers LEFT JOIN with `entity_references` table
- **Example**: Search for "Emergency" will match entities linked to references with display names like "Emergency Response Report"

#### `reference_relation_type` - Reference Relation Types
- **Target**: `entity_references.relation_type` column  
- **Search Type**: Partial/fuzzy matching using trigram indexes
- **Use Cases**: Find entities by relationship type descriptions
- **Performance**: Optimized with GIN trigram index (`idx_entity_references_relation_type_trgm`)
- **Join Behavior**: Automatically triggers LEFT JOIN with `entity_references` table
- **Example**: Search for "primary" will match entities with relation types like "primary_contact" or "primary_witness"

### Search Parameters Behavior

#### `query` Parameter Behavior

**When `search_fields` is empty (default behavior):**
```sql
-- Searches across ALL supported fields
WHERE (
  e.id ILIKE '%search_term%' OR 
  e.data_values ILIKE '%search_term%' OR 
  array_to_string(e.tags, ' ') ILIKE '%search_term%' OR 
  er.display_name ILIKE '%search_term%' OR 
  er.relation_type ILIKE '%search_term%'
)
```

**When `search_fields` is specified:**
```sql
-- Only searches the specified fields
-- Example: search_fields = ["data", "tags"]
WHERE (
  e.data_values ILIKE '%search_term%' OR 
  array_to_string(e.tags, ' ') ILIKE '%search_term%'
)
```

**Important Notes:**
- The `query` parameter uses **OR** logic across selected fields
- Searches are **case-insensitive** (ILIKE operator)
- Uses **substring matching** (wraps terms in `%` wildcards)
- Empty `query` parameter is ignored (no text search performed)

#### `search_fields` Parameter Behavior

**Validation:**
- Must contain only supported field names (see list above)
- Invalid field names will result in an error
- Duplicate field names are automatically deduplicated
- Case-sensitive field names (must match exactly)

**Performance Impact:**
- Restricting fields can improve query performance
- Reference fields (`reference_display_name`, `reference_relation_type`) trigger table JOINs
- Non-reference fields avoid unnecessary JOINs

**Examples:**
```json
// Search all fields (default)
{
  "query": "Alice",
  "search_fields": []
}

// Search only entity data and tags  
{
  "query": "customer", 
  "search_fields": ["data", "tags"]
}

// Search only reference display names
{
  "query": "emergency",
  "search_fields": ["reference_display_name"]
}
```

#### `field_queries` Parameter Behavior

The `field_queries` parameter allows for **field-specific searches** that are **independent** of the global `query` parameter.

**FieldQuery Structure:**
```protobuf
message FieldQuery {
  string field = 1;   // Must be one of the supported field names
  string query = 2;   // Search term for this specific field
}
```

**Behavior:**
- Each `FieldQuery` targets exactly one field
- Multiple `field_queries` are combined with **AND** logic
- `field_queries` are **combined with** (not replaced by) the global `query` using **AND** logic
- Each field query uses the same ILIKE pattern matching as global search
- Empty `query` in a FieldQuery is ignored

**SQL Generation Example:**
```json
// Request with both global query and field queries
{
  "query": "Alice",
  "search_fields": ["data"],
  "field_queries": [
    {"field": "reference_display_name", "query": "emergency"},
    {"field": "tags", "query": "customer"}
  ]
}
```

```sql
-- Generated WHERE clause
WHERE (
  -- Global query (limited to search_fields)
  e.data_values ILIKE '%Alice%'
) AND (
  -- Field-specific queries (AND logic)
  er.display_name ILIKE '%emergency%' AND
  array_to_string(e.tags, ' ') ILIKE '%customer%'
)
```

### Search Combination Examples

#### Example 1: Global Search Only
```json
{
  "query": "Alice"
}
```
**Behavior**: Searches for "Alice" across all supported fields (id, data, tags, reference_display_name, reference_relation_type). **Note:** This will only return active entities by default. To include archived entities, add `"status": ["RECORD_STATUS_ARCHIVED"]` for archived entities only, or `"status": ["RECORD_STATUS_ACTIVE", "RECORD_STATUS_ARCHIVED"]` for both.

#### Example 2: Restricted Global Search  
```json
{
  "query": "Alice",
  "search_fields": ["data", "tags"]
}
```
**Behavior**: Searches for "Alice" only in entity data and tags fields

#### Example 3: Field-Specific Search Only
```json
{
  "field_queries": [
    {"field": "data", "query": "manager"},
    {"field": "reference_display_name", "query": "incident"}
  ]
}
```
**Behavior**: Finds entities where data contains "manager" AND references have display names containing "incident"

#### Example 4: Combined Global + Field-Specific
```json
{
  "query": "Alice",
  "search_fields": ["data"],
  "field_queries": [
    {"field": "reference_relation_type", "query": "primary"}
  ]
}
```
**Behavior**: Finds entities where data contains "Alice" AND reference relation types contain "primary"

#### Example 5: Complex Multi-Field Search
```json
{
  "query": "customer",
  "search_fields": ["data", "tags"],
  "field_queries": [
    {"field": "reference_display_name", "query": "emergency"},
    {"field": "id", "query": "person"}
  ],
  "status": ["RECORD_STATUS_ACTIVE"],
  "entity_types": ["ENTITY_TYPE_PERSON"]
}
```
**Behavior**: 
- Global: data OR tags contains "customer" 
- AND reference display names contain "emergency"
- AND entity ID contains "person"  
- AND status is ACTIVE
- AND entity type is PERSON

#### Example 6: Search Including Archived Entities
```json
{
  "query": "customer",
  "search_fields": ["data", "tags"],
  "status": ["RECORD_STATUS_ACTIVE", "RECORD_STATUS_ARCHIVED"],
  "entity_types": ["ENTITY_TYPE_PERSON"]
}
```
**Behavior**: 
- Searches for "customer" in data and tags fields
- Returns both active and archived entities
- Filters by PERSON entity type

#### Example 7: Search Only Archived Entities
```json
{
  "query": "customer",
  "search_fields": ["data", "tags"],
  "status": ["RECORD_STATUS_ARCHIVED"],
  "entity_types": ["ENTITY_TYPE_PERSON"]
}
```
**Behavior**: 
- Searches for "customer" in data and tags fields
- Returns only archived entities
- Filters by PERSON entity type

### Performance Considerations

#### Index Usage by Field
- **`id`**: Uses trigram GIN index - excellent for partial matches
- **`data`**: Uses trigram GIN index on `data_values` column - excellent for value-only searches
- **`tags`**: Uses array GIN index - excellent for tag content
- **`reference_display_name`**: Uses trigram GIN index - excellent for partial matches
- **`reference_relation_type`**: Uses trigram GIN index - excellent for partial matches

#### JOIN Implications
- **Reference fields trigger JOINs**: Using `reference_display_name` or `reference_relation_type` in either `search_fields` or `field_queries` will cause a LEFT JOIN with `entity_references`
- **DISTINCT required**: JOINs automatically add DISTINCT to prevent duplicate entity results
- **Performance impact**: JOINs may reduce performance for large datasets

#### Query Optimization Tips
1. **Limit search fields**: Use `search_fields` to restrict search scope when possible
2. **Avoid reference fields**: If not needed, avoid reference field searches to prevent JOINs  
3. **Combine filters**: Use exact filters (status, entity_type) to reduce the dataset before text search
4. **Reasonable page sizes**: Keep `page_size` under 100 for optimal performance

### Error Handling

#### Invalid Field Names
```json
{
  "search_fields": ["invalid_field"]
}
```
**Result**: Returns error indicating invalid field name

#### Empty Queries
```json
{
  "query": "",
  "field_queries": [{"field": "data", "query": ""}]
}
```
**Result**: Empty queries are ignored, search proceeds with other criteria

#### Field Query Validation
```json
{
  "field_queries": [{"field": "unknown_field", "query": "test"}]
}
```
**Result**: Returns error for unsupported field in field_queries

### Sample Request and Response

---

### EntitySchema Endpoints

---

#### 1. CreateEntitySchema

**Method:** `CreateEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/CreateEntitySchema`

#### Message Fields

**CreateEntitySchemaRequest:**

| Field  | Type         | Description                                      |
|--------|--------------|--------------------------------------------------|
| schema | EntitySchema | The entity schema object to be created.          |

**CreateEntitySchemaResponse:**

| Field  | Type         | Description                                                                                              |
|--------|--------------|----------------------------------------------------------------------------------------------------------|
| schema | EntitySchema | The newly created entity schema with a generated `id` (refer to the [EntitySchema](#entityschema) data model). |

#### Sample Request and Response

In this example, we create a schema for a person using the example JSON below:

**Request (JSON):**
```json
{
  "schema": {
    "orgId": "org-001",
    "name": "Person Schema",
    "description": "Schema for storing person entities",
    "schemaDefinition": {
      "title": "Person",
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "Full name of the person"
        },
        "age": {
          "type": "integer",
          "description": "Age of the person",
          "minimum": 0
        },
        "dob": {
          "type": "string",
          "format": "date-time",
          "description": "Date of birth in ISO8601 format"
        },
        "univ_association": {
          "type": "string",
          "description": "University or association details",
          "default": ""
        },
        "address": {
          "type": "object",
          "description": "Nested physical address",
          "properties": {
            "street": { "type": "string", "description": "Street address" },
            "city": { "type": "string", "description": "City" },
            "state": { "type": "string", "description": "State or province", "default": "" },
            "zip": { "type": "string", "description": "Postal code", "default": "" },
            "country": { "type": "string", "description": "Country name", "default": "" }
          },
          "required": ["street", "city"],
          "additionalProperties": false
        },
        "primary_phone": {
          "type": "string",
          "description": "Primary phone number",
          "default": ""
        },
        "secondary_phone": {
          "type": "string",
          "description": "Secondary phone number",
          "default": ""
        },
        "report_id": {
          "type": "array",
          "description": "List of report IDs linked to the person",
          "items": { "type": "string" },
          "default": []
        }
      },
      "required": ["name"],
      "additionalProperties": false
    },
    "createdBy": "user-001",
    "updatedBy": "user-001",
    "entityType": "ENTITY_TYPE_PERSON",
    "tags": ["person"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

**Response (JSON):**
```json
{
  "schema": {
    "id": "schema-123",
    "orgId": "org-001",
    "name": "Person Schema",
    "description": "Schema for storing person entities",
    "schemaDefinition": {
      "title": "Person",
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "Full name of the person"
        },
        "age": {
          "type": "integer",
          "description": "Age of the person",
          "minimum": 0
        },
        "dob": {
          "type": "string",
          "format": "date-time",
          "description": "Date of birth in ISO8601 format"
        },
        "univ_association": {
          "type": "string",
          "description": "University or association details",
          "default": ""
        },
        "address": {
          "type": "object",
          "description": "Nested physical address",
          "properties": {
            "street": { "type": "string", "description": "Street address" },
            "city": { "type": "string", "description": "City" },
            "state": { "type": "string", "description": "State or province", "default": "" },
            "zip": { "type": "string", "description": "Postal code", "default": "" },
            "country": { "type": "string", "description": "Country name", "default": "" }
          },
          "required": ["street", "city"],
          "additionalProperties": false
        },
        "primary_phone": {
          "type": "string",
          "description": "Primary phone number",
          "default": ""
        },
        "secondary_phone": {
          "type": "string",
          "description": "Secondary phone number",
          "default": ""
        },
        "report_id": {
          "type": "array",
          "description": "List of report IDs linked to the person",
          "items": { "type": "string" },
          "default": []
        }
      },
      "required": ["name"],
      "additionalProperties": false
    },
    "createTime": "2025-04-09T07:00:00Z",
    "updateTime": "2025-04-09T07:00:00Z",
    "createdBy": "user-001",
    "updatedBy": "user-001",
    "version": 1,
    "entityType": "ENTITY_TYPE_PERSON",
    "status": "RECORD_STATUS_ACTIVE",
    "tags": ["person"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

---

#### 2. GetLatestEntitySchema

**Method:** `GetLatestEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/GetLatestEntitySchema`

#### Message Fields

**GetLatestEntitySchemaRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier for the schema.  |

**Response:**

| Field  | Type         | Description                                                                          |
|--------|--------------|--------------------------------------------------------------------------------------|
|        | EntitySchema | The latest version of the entity schema (refer to the [EntitySchema](#entityschema) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123"
}
```

**Response (JSON):**
```json
{
  "id": "schema-123",
  "orgId": "org-001",
  "name": "Person Schema",
  "description": "Schema for storing person entities",
  "schemaDefinition": {
    "title": "Person",
    "type": "object",
    "properties": {
      "name": { "type": "string", "description": "Full name of the person" },
      "age": { "type": "integer", "description": "Age of the person", "minimum": 0 }
    },
    "required": ["name"],
    "additionalProperties": false
  },
  "createTime": "2025-04-09T07:00:00Z",
  "updateTime": "2025-04-09T07:00:00Z",
  "createdBy": "user-001",
  "updatedBy": "user-001",
  "version": 1,
  "entityType": "ENTITY_TYPE_PERSON",
  "status": "RECORD_STATUS_ACTIVE",
  "tags": ["person"],
  "permissionRulesetId": "ruleset-001"
}
```

---

#### 3. GetLatestActiveEntitySchema

**Method:** `GetLatestActiveEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/GetLatestActiveEntitySchema`

#### Message Fields

**GetLatestActiveEntitySchemaRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier for the schema.  |

**GetLatestActiveEntitySchemaResponse:**

| Field  | Type         | Description                                                                          |
|--------|--------------|--------------------------------------------------------------------------------------|
|        | EntitySchema | The latest active version of the entity schema (refer to the [EntitySchema](#entityschema) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123"
}
```

**Response (JSON):**
```json
{
  "id": "schema-123",
  "orgId": "org-001",
  "name": "Person Schema",
  "description": "Schema for storing person entities",
  "schemaDefinition": {
    "title": "Person",
    "type": "object",
    "properties": {
      "name": { "type": "string", "description": "Full name of the person" },
      "age": { "type": "integer", "description": "Age of the person", "minimum": 0 }
    },
    "required": ["name"],
    "additionalProperties": false
  },
  "createTime": "2025-04-09T07:00:00Z",
  "updateTime": "2025-04-09T07:10:00Z",
  "createdBy": "user-001",
  "updatedBy": "user-001",
  "version": 1,
  "entityType": "ENTITY_TYPE_PERSON",
  "status": "RECORD_STATUS_ACTIVE",
  "tags": ["person"],
  "permissionRulesetId": "ruleset-001"
}
```

---

#### 4. GetEntitySchemaByVersion

**Method:** `GetEntitySchemaByVersion`  
**Route:** `POST /hero.entity.v1.EntityService/GetEntitySchemaByVersion`

#### Message Fields

**GetEntitySchemaByVersionRequest:**

| Field   | Type   | Description                                    |
|---------|--------|------------------------------------------------|
| id      | string | Unique identifier for the schema.              |
| version | int32 | Specific version number of the schema to retrieve. |

**GetEntitySchemaByVersion Response:**

| Field  | Type         | Description                                                                      |
|--------|--------------|----------------------------------------------------------------------------------|
|        | EntitySchema | The entity schema corresponding to the specified version.                      |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123",
  "version": 1
}
```

**Response (JSON):**
```json
{
  "id": "schema-123",
  "orgId": "org-001",
  "name": "Person Schema",
  "description": "Schema for storing person entities",
  "schemaDefinition": {
    "title": "Person",
    "type": "object",
    "properties": {
      "name": { "type": "string", "description": "Full name of the person" },
      "age": { "type": "integer", "description": "Age of the person", "minimum": 0 }
    },
    "required": ["name"],
    "additionalProperties": false
  },
  "createTime": "2025-04-09T07:00:00Z",
  "updateTime": "2025-04-09T07:00:00Z",
  "createdBy": "user-001",
  "updatedBy": "user-001",
  "version": 1,
  "entityType": "ENTITY_TYPE_PERSON",
  "status": "RECORD_STATUS_ACTIVE",
  "tags": ["person"],
  "permissionRulesetId": "ruleset-001"
}
```

---

#### 5. UpdateEntitySchema

**Method:** `UpdateEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/UpdateEntitySchema`

#### Message Fields

**UpdateEntitySchemaRequest:**

| Field  | Type         | Description                                         |
|--------|--------------|-----------------------------------------------------|
| schema | EntitySchema | The complete entity schema to update. **Note:** Must include the `id`. |

**UpdateEntitySchema Response:**

| Field  | Type         | Description                                                                              |
|--------|--------------|------------------------------------------------------------------------------------------|
| schema | EntitySchema | The updated entity schema object (refer to the [EntitySchema](#entityschema) data model).   |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "schema": {
    "id": "schema-123",
    "orgId": "org-001",
    "name": "Person Schema Updated",
    "description": "Updated schema with additional validations",
    "schemaDefinition": {
      "title": "Person",
      "type": "object",
      "properties": {
        "name": { "type": "string", "description": "Full name of the person" },
        "age": { "type": "integer", "description": "Age of the person", "minimum": 0 },
        "email": { "type": "string", "description": "Email address" }
      },
      "required": ["name"],
      "additionalProperties": false
    },
    "updatedBy": "user-002",
    "entityType": "ENTITY_TYPE_PERSON",
    "tags": ["person", "updated"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

**Response (JSON):**
```json
{
  "schema": {
    "id": "schema-123",
    "orgId": "org-001",
    "name": "Person Schema Updated",
    "description": "Updated schema with additional validations",
    "schemaDefinition": {
      "title": "Person",
      "type": "object",
      "properties": {
        "name": { "type": "string", "description": "Full name of the person" },
        "age": { "type": "integer", "description": "Age of the person", "minimum": 0 },
        "email": { "type": "string", "description": "Email address" }
      },
      "required": ["name"],
      "additionalProperties": false
    },
    "createTime": "2025-04-09T07:00:00Z",
    "updateTime": "2025-04-09T08:00:00Z",
    "createdBy": "user-001",
    "updatedBy": "user-002",
    "version": 2,
    "entityType": "ENTITY_TYPE_PERSON",
    "status": "RECORD_STATUS_ACTIVE",
    "tags": ["person", "updated"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

---

#### 6. DeleteAllVersionsOfEntitySchema

**Method:** `DeleteAllVersionsOfEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/DeleteAllVersionsOfEntitySchema`

#### Message Fields

**DeleteAllVersionsOfEntitySchemaRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier for the schema.  |

**DeleteAllVersionsOfEntitySchema Response:**

| Field | Type                   | Description                 |
|-------|------------------------|-----------------------------|
|       | google.protobuf.Empty  | (Empty response message.)   |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123"
}
```

**Response (JSON):**
```json
{}
```

---

#### 7. DeleteSpecificVersionOfEntitySchema

**Method:** `DeleteSpecificVersionOfEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/DeleteSpecificVersionOfEntitySchema`

#### Message Fields

**DeleteSpecificVersionOfEntitySchemaRequest:**

| Field   | Type   | Description                                    |
|---------|--------|------------------------------------------------|
| id      | string | Unique identifier for the schema.               |
| version | int32 | Specific version number to delete.             |

**Response:**

| Field | Type                   | Description                 |
|-------|------------------------|-----------------------------|
|       | google.protobuf.Empty  | (Empty response message.)   |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123",
  "version": 1
}
```

**Response (JSON):**
```json
{}
```

---

#### 8. ListLatestEntitySchemas

**Method:** `ListLatestEntitySchemas`  
**Route:** `POST /hero.entity.v1.EntityService/ListLatestEntitySchemas`

#### Message Fields

**ListLatestEntitySchemasRequest:**

| Field      | Type   | Description                                                         |
|------------|--------|---------------------------------------------------------------------|
| pageSize   | int32  | Maximum number of schemas to return in the response.                |
| pageToken  | string | Token identifying a specific page of results to retrieve.           |
| entityType | EntityType | (Optional) Filter schemas by entity type; if unspecified, returns all. |

**ListLatestEntitySchemasResponse:**

| Field         | Type                  | Description                                                          |
|---------------|-----------------------|----------------------------------------------------------------------|
| schemas       | repeated EntitySchema | List of entity schema objects.                                       |
| nextPageToken | string                | Token to retrieve the next page of results, if any.                  |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "pageSize": 10,
  "pageToken": "",
  "entityType": "ENTITY_TYPE_PERSON"
}
```

**Response (JSON):**
```json
{
  "schemas": [
    {
      "id": "schema-123",
      "orgId": "org-001",
      "name": "Person Schema Updated",
      "description": "Updated schema with additional validations",
      "schemaDefinition": {
        "title": "Person",
        "type": "object",
        "properties": {
          "name": { "type": "string", "description": "Full name of the person" },
          "age": { "type": "integer", "description": "Age of the person", "minimum": 0 },
          "email": { "type": "string", "description": "Email address" }
        },
        "required": ["name"],
        "additionalProperties": false
      },
      "createTime": "2025-04-09T07:00:00Z",
      "updateTime": "2025-04-09T08:00:00Z",
      "createdBy": "user-001",
      "updatedBy": "user-002",
      "version": 2,
      "entityType": "ENTITY_TYPE_PERSON",
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["person", "updated"],
      "permissionRulesetId": "ruleset-001"
    }
  ],
  "nextPageToken": "next-schema-page-token"
}
```

---

#### 9. ListAllVersionsOfEntitySchema

**Method:** `ListAllVersionsOfEntitySchema`  
**Route:** `POST /hero.entity.v1.EntityService/ListAllVersionsOfEntitySchema`

#### Message Fields

**ListAllVersionsOfEntitySchemaRequest:**

| Field    | Type   | Description                                    |
|----------|--------|------------------------------------------------|
| schemaId | string | Unique identifier for the entity schema.       |

**ListAllVersionsOfEntitySchemaResponse:**

| Field    | Type                           | Description                                                        |
|----------|--------------------------------|--------------------------------------------------------------------|
| versions | repeated EntitySchemaVersion   | Historical snapshots of the schema (refer to the [EntitySchemaVersion](#entityschemaversion) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "schemaId": "schema-123"
}
```

**Response (JSON):**
```json
{
  "versions": [
    {
      "schemaId": "schema-123",
      "version": 1,
      "schemaSnapshot": {
        "id": "schema-123",
        "orgId": "org-001",
        "name": "Person Schema",
        "description": "Schema for storing person entities",
        "schemaDefinition": {
          "title": "Person",
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "description": "Full name of the person"
            },
            "age": {
              "type": "integer",
              "description": "Age of the person",
              "minimum": 0
            }
          },
          "required": ["name"],
          "additionalProperties": false
        },
        "createTime": "2025-04-09T07:00:00Z",
        "updateTime": "2025-04-09T07:00:00Z",
        "createdBy": "user-001",
        "updatedBy": "user-001",
        "version": 1,
        "entityType": "ENTITY_TYPE_PERSON",
        "status": "RECORD_STATUS_ACTIVE",
        "tags": ["person"],
        "permissionRulesetId": "ruleset-001"
      },
      "modifiedBy": "user-001",
      "modifiedTime": "2025-04-09T07:00:00Z",
      "changeComment": "Initial schema creation"
    },
    {
      "schemaId": "schema-123",
      "version": 2,
      "schemaSnapshot": {
        "id": "schema-123",
        "orgId": "org-001",
        "name": "Person Schema Updated",
        "description": "Updated schema with additional validations and email field",
        "schemaDefinition": {
          "title": "Person",
          "type": "object",
          "properties": {
            "name": {
              "type": "string",
              "description": "Full name of the person"
            },
            "age": {
              "type": "integer",
              "description": "Age of the person",
              "minimum": 0
            },
            "email": {
              "type": "string",
              "description": "Email address"
            }
          },
          "required": ["name"],
          "additionalProperties": false
        },
        "createTime": "2025-04-09T07:00:00Z",
        "updateTime": "2025-04-09T08:00:00Z",
        "createdBy": "user-001",
        "updatedBy": "user-002",
        "version": 2,
        "entityType": "ENTITY_TYPE_PERSON",
        "status": "RECORD_STATUS_ACTIVE",
        "tags": ["person", "updated"],
        "permissionRulesetId": "ruleset-001"
      },
      "modifiedBy": "user-002",
      "modifiedTime": "2025-04-09T08:00:00Z",
      "changeComment": "Updated validations and added email field"
    }
  ]
}
```

---

#### 10. BulkCreateEntitySchemas

**Note:** Not tested

**Method:** `BulkCreateEntitySchemas`  
**Route:** `POST /hero.entity.v1.EntityService/BulkCreateEntitySchemas`

#### Message Fields

**BulkCreateEntitySchemasRequest:**

| Field   | Type                  | Description                                 |
|---------|-----------------------|---------------------------------------------|
| schemas | repeated EntitySchema | List of entity schema objects to create.    |

**BulkCreateEntitySchemasResponse:**

| Field   | Type                  | Description                                 |
|---------|-----------------------|---------------------------------------------|
| schemas | repeated EntitySchema | List of newly created entity schemas.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "schemas": [
    {
      "orgId": "org-001",
      "name": "Person Schema",
      "description": "Schema for storing person entities",
      "schemaDefinition": { /* JSON Schema Definition */ },
      "createdBy": "user-001",
      "updatedBy": "user-001",
      "entityType": "ENTITY_TYPE_PERSON",
      "tags": ["person"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "orgId": "org-002",
      "name": "Vehicle Schema",
      "description": "Schema for storing vehicle entities",
      "schemaDefinition": { /* JSON Schema Definition */ },
      "createdBy": "user-002",
      "updatedBy": "user-002",
      "entityType": "ENTITY_TYPE_VEHICLE",
      "tags": ["vehicle"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

**Response (JSON):**
```json
{
  "schemas": [
    {
      "id": "schema-123",
      "orgId": "org-001",
      "name": "Person Schema",
      "description": "Schema for storing person entities",
      "schemaDefinition": { /* JSON Schema Definition */ },
      "createTime": "2025-04-09T07:00:00Z",
      "updateTime": "2025-04-09T07:00:00Z",
      "createdBy": "user-001",
      "updatedBy": "user-001",
      "version": 1,
      "entityType": "ENTITY_TYPE_PERSON",
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["person"],
      "permissionRulesetId": "ruleset-001"
    },
    {
      "id": "schema-456",
      "orgId": "org-002",
      "name": "Vehicle Schema",
      "description": "Schema for storing vehicle entities",
      "schemaDefinition": { /* JSON Schema Definition */ },
      "createTime": "2025-04-09T07:05:00Z",
      "updateTime": "2025-04-09T07:05:00Z",
      "createdBy": "user-002",
      "updatedBy": "user-002",
      "version": 1,
      "entityType": "ENTITY_TYPE_VEHICLE",
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["vehicle"],
      "permissionRulesetId": "ruleset-002"
    }
  ]
}
```

---

#### 11. BulkUpdateEntitySchemas

**Note:** Not tested

**Method:** `BulkUpdateEntitySchemas`  
**Route:** `POST /hero.entity.v1.EntityService/BulkUpdateEntitySchemas`

#### Message Fields

**BulkUpdateEntitySchemasRequest:**

| Field   | Type                  | Description                                   |
|---------|-----------------------|-----------------------------------------------|
| schemas | repeated EntitySchema | List of entity schemas to be updated.         |

**BulkUpdateEntitySchemasResponse:**

| Field   | Type                  | Description                                    |
|---------|-----------------------|------------------------------------------------|
| schemas | repeated EntitySchema | List of updated entity schemas.                 |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "schemas": [
    {
      "id": "schema-123",
      "orgId": "org-001",
      "name": "Person Schema Updated",
      "description": "Updated schema with additional validations",
      "schemaDefinition": { /* Updated JSON Schema Definition */ },
      "updatedBy": "user-002",
      "entityType": "ENTITY_TYPE_PERSON",
      "tags": ["person", "updated"],
      "permissionRulesetId": "ruleset-001"
    }
  ]
}
```

**Response (JSON):**
```json
{
  "schemas": [
    {
      "id": "schema-123",
      "orgId": "org-001",
      "name": "Person Schema Updated",
      "description": "Updated schema with additional validations",
      "schemaDefinition": { /* Updated JSON Schema Definition */ },
      "createTime": "2025-04-09T07:00:00Z",
      "updateTime": "2025-04-09T08:00:00Z",
      "createdBy": "user-001",
      "updatedBy": "user-002",
      "version": 2,
      "entityType": "ENTITY_TYPE_PERSON",
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["person", "updated"],
      "permissionRulesetId": "ruleset-001"
    }
  ]
}
```

---

#### 12. BulkDeleteEntitySchemas

**Note:** Not tested

**Method:** `BulkDeleteEntitySchemas`  
**Route:** `POST /hero.entity.v1.EntityService/BulkDeleteEntitySchemas`

#### Message Fields

**BulkDeleteEntitySchemasRequest:**

| Field | Type             | Description                                      |
|-------|------------------|--------------------------------------------------|
| ids   | repeated string  | List of schema IDs to delete.                     |

**BulkDeleteEntitySchemas Response:**

| Field | Type                   | Description                 |
|-------|------------------------|-----------------------------|
|       | google.protobuf.Empty  | (Empty response message.)   |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "ids": ["schema-123", "schema-456"]
}
```

**Response (JSON):**
```json
{}
```

---

#### 13. RestoreEntitySchemaVersion

**Method:** `RestoreEntitySchemaVersion`  
**Route:** `POST /hero.entity.v1.EntityService/RestoreEntitySchemaVersion`

#### Message Fields

**RestoreEntitySchemaVersionRequest:**

| Field   | Type   | Description                                      |
|---------|--------|--------------------------------------------------|
| id      | string | Unique identifier for the schema.                |
| version | int32 | Version number to which the schema should be restored. |

**RestoreEntitySchemaVersionResponse:**

| Field  | Type         | Description                                                                       |
|--------|--------------|-----------------------------------------------------------------------------------|
| schema | EntitySchema | The entity schema restored to the specified version (refer to the [EntitySchema](#entityschema) data model). |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123",
  "version": 1
}
```

**Response (JSON):**
```json
{
  "schema": {
    "id": "schema-123",
    "orgId": "org-001",
    "name": "Person Schema",
    "description": "Schema for storing person entities",
    "schemaDefinition": { /* JSON Schema Definition */ },
    "createTime": "2025-04-09T07:00:00Z",
    "updateTime": "2025-04-09T07:00:00Z",
    "createdBy": "user-001",
    "updatedBy": "user-001",
    "version": 1,
    "entityType": "ENTITY_TYPE_PERSON",
    "status": "RECORD_STATUS_ACTIVE",
    "tags": ["person"],
    "permissionRulesetId": "ruleset-001"
  }
}
```

---

#### 14. DiffEntitySchemaVersions

**Note:** Unstable - need more work


**Method:** `DiffEntitySchemaVersions`  
**Route:** `POST /hero.entity.v1.EntityService/DiffEntitySchemaVersions`

#### Message Fields

**DiffEntitySchemaVersionsRequest:**

| Field    | Type   | Description                                      |
|----------|--------|--------------------------------------------------|
| id       | string | Unique identifier for the schema.                |
| version1 | int32 | The first version number to compare.             |
| version2 | int32 | The second version number to compare.            |

**DiffEntitySchemaVersionsResponse:**

| Field      | Type   | Description                                      |
|------------|--------|--------------------------------------------------|
| diffResult | string | The computed diff output between the two versions. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "schema-123",
  "version1": 1,
  "version2": 2
}
```

**Response (JSON):**
```json
{
  "diffResult": "{ \"email\": { \"added\": \"<EMAIL>\" } }"
}
```

---

#### 15. CheckEntitySchemaPermissions

**Note:** Not implemented


**Method:** `CheckEntitySchemaPermissions`  
**Route:** `POST /hero.entity.v1.EntityService/CheckEntitySchemaPermissions`

#### Message Fields

**CheckEntitySchemaPermissionsRequest:**

| Field    | Type   | Description                                     |
|----------|--------|-------------------------------------------------|
| schemaId | string | Unique identifier for the schema.               |
| userId   | string | User ID to check permissions for.               |
| action   | string | The action to check (e.g., "read", "write", "delete"). |

**CheckEntitySchemaPermissionsResponse:**

| Field   | Type   | Description                              |
|---------|--------|------------------------------------------|
| allowed | bool   | Whether the user is permitted to perform the specified action. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "schemaId": "schema-123",
  "userId": "user-001",
  "action": "write"
}
```

**Response (JSON):**
```json
{
  "allowed": true
}
```

---

## Test Suite

The Entity Module includes a comprehensive test suite located in `services/workflow/test/entity/` that validates all Entity and EntitySchema operations, including extensive search functionality testing.

### Running Tests

#### Using the Test Script (Recommended)

The easiest way to run tests is using the provided test script:

```bash
cd services/workflow/test/entity
./run_tests.sh
```

The test script supports various options:

```bash
# Run all tests (default)
./run_tests.sh
./run_tests.sh all

# Run specific test categories
./run_tests.sh sanity          # Basic CRUD operations
./run_tests.sh side-effect     # Complex state change tests  
./run_tests.sh search          # Search functionality tests
./run_tests.sh performance     # Load testing and latency validation

# Run test data management
./run_tests.sh populate        # Create test entities
./run_tests.sh cleanup         # Delete test entities

# Bypass test cache (force re-run)
./run_tests.sh all nocache
./run_tests.sh search nocache
./run_tests.sh performance nocache
```

#### Manual Execution

For more control, you can run tests manually:

```bash
# Run all entity tests
cd services/workflow
go test -v ./test/entity/

# Run specific test patterns
go test -v ./test/entity/ -run TestSanity_EntityService
go test -v ./test/entity/ -run TestSearch_
go test -v ./test/entity/ -run TestSideEffect_

# Run with coverage
go test -v ./test/entity/ -coverprofile=coverage.out

# Run with race condition detection
go test -v ./test/entity/ -race

# Force cache bypass
go test -v ./test/entity/ -count=1
```

### Test Categories

#### Sanity Tests (`TestSanity_*`)
Basic CRUD operations and core functionality:
- Entity creation, retrieval, update, deletion
- Schema operations
- Version management
- Bulk operations
- Error handling

#### Search Tests (`TestSearch_*`)
Comprehensive search functionality validation:
- **Text Search**: Query and field-specific searches
- **Filter Combinations**: Multiple filter types together
- **Pagination**: Page navigation and token handling
- **Highlighting**: Search result highlighting
- **Performance**: Query execution timing
- **Edge Cases**: Empty results, malformed queries

#### Side-Effect Tests (`TestSideEffect_*`)
Complex scenarios testing state interactions:
- Concurrent entity modifications
- Cross-entity relationships
- Schema evolution impact
- Transaction isolation

#### Performance Tests (`TestEntity_BasicPerformance`)
Comprehensive performance testing and latency validation:
- **Test Data Volume**: Creates 100 entities and 10 schemas for realistic load testing
- **CRUD Operations**: GetLatestEntity, UpdateEntity, ListLatestEntities performance
- **Batch Operations**: BatchGetLatestEntities, BulkCreateEntities performance
- **Search Performance**: Multiple search patterns and filtering scenarios
- **Schema Operations**: GetLatestEntitySchema, ListLatestEntitySchemas performance
- **Version Management**: GetEntityByVersion, ListAllVersionsOfEntity performance
- **Permission Checks**: CheckEntityPermissions latency validation
- **Latency Thresholds**: Sub-200ms target for most operations, 2000ms for bulk operations
- **Performance Metrics**: Detailed timing analysis with min/max/average reporting
- **Runtime**: 3-10 minutes including test data creation and cleanup

### Test Environment Setup

#### Prerequisites

1. **Authentication Token**: Create a `token.txt` file with a valid Cognito access token:
   ```bash
   echo 'your-cognito-token-here' > services/workflow/test/entity/token.txt
   ```

2. **Environment Variables**: Ensure proper database and service configuration:
   ```bash
   export COGNITO_ACCESS_TOKEN="your-token"
   export DATABASE_URL="your-test-database-url"
   export API_ENDPOINT="your-test-api-endpoint"
   ```

3. **Database State**: Tests assume a clean database state or use the `populate`/`cleanup` scripts

#### Test Data Management

The test suite includes utilities for managing test data:

```bash
# Create test entities for search testing
./run_tests.sh populate

# Clean up all test entities
./run_tests.sh cleanup
```

### Adding New Tests

When adding new functionality, extend the test suite following established patterns:

#### 1. Add to Appropriate Test Category

**For basic functionality** (`entity_api_sanity_test.go`):
```go
func TestSanity_NewEntityFeature(t *testing.T) {
    // Setup
    ctx := context.Background()
    client := setupTestClient(t)
    
    // Test execution
    result, err := client.NewFeature(ctx, &NewFeatureRequest{
        // test data
    })
    
    // Assertions
    require.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, expectedValue, result.SomeField)
}
```

**For search functionality** (`entity_search_test.go`):
```go
func TestSearch_NewSearchFeature(t *testing.T) {
    // Setup test entities
    entities := createTestEntities(t, 5)
    defer cleanupTestEntities(t, entities)
    
    // Test search functionality
    response, err := client.SearchEntities(ctx, &entity.SearchEntitiesRequest{
        Query: "test search term",
        // other search parameters
    })
    
    // Verify results
    require.NoError(t, err)
    assert.Len(t, response.Entities, expectedCount)
    assert.Contains(t, response.Highlights, entities[0].Id)
}
```

#### 2. Update Helper Functions (`test_utils.go`)

Add reusable test utilities:

```go
func createTestEntityWithData(t *testing.T, data map[string]interface{}) *entity.Entity {
    // Implementation for creating test entities with specific data
}

func assertEntitySearchResult(t *testing.T, response *entity.SearchEntitiesResponse, expectedCount int) {
    // Common assertions for search results
}
```

#### 3. Follow Testing Best Practices

1. **Isolation**: Each test should be independent and clean up after itself
2. **Descriptive Names**: Use clear, descriptive test function names
3. **Comprehensive Coverage**: Test both success and error cases
4. **Performance Awareness**: Include timing checks for search operations
5. **Documentation**: Comment complex test logic and expected behaviors
6. **Data Cleanup**: Always clean up test data to prevent interference

#### 5. Performance Test Best Practices

1. **Realistic Data Volumes**: Use representative test data sizes (100+ entities for meaningful performance testing)
2. **Latency Thresholds**: Set realistic performance expectations (200ms for individual operations, 2000ms for bulk operations)
3. **Resource Management**: Ensure adequate system resources during performance testing
4. **Metrics Collection**: Track and report comprehensive performance metrics (min/max/average)
5. **Environment Consistency**: Run performance tests in consistent environments with minimal background activity
6. **Timeout Handling**: Use appropriate timeouts (600s) for performance tests that create large datasets
7. **Cleanup Automation**: Implement robust cleanup to prevent test data accumulation

#### 4. Search Test Patterns

When testing search functionality, follow these patterns:

```go
func TestSearch_ComplexQuery(t *testing.T) {
    // 1. Setup: Create entities with known data
    testEntities := []struct {
        id   string
        data map[string]interface{}
        tags []string
    }{
        {"entity-1", map[string]interface{}{"name": "Alice", "age": 25}, []string{"person", "customer"}},
        {"entity-2", map[string]interface{}{"name": "Bob", "age": 30}, []string{"person", "employee"}},
    }
    
    entities := make([]*entity.Entity, len(testEntities))
    for i, te := range testEntities {
        entities[i] = createTestEntityWithData(t, te.data)
        entities[i].Tags = te.tags
    }
    defer cleanupTestEntities(t, entities)
    
    // 2. Execute: Perform search with specific criteria
    response, err := client.SearchEntities(ctx, &entity.SearchEntitiesRequest{
        Query: "Alice",
        SearchFields: []string{"data"},
        Status: []entity.RecordStatus{entity.RecordStatus_RECORD_STATUS_ACTIVE},
        PageSize: 10,
    })
    
    // 3. Verify: Check results, pagination, and highlights
    require.NoError(t, err)
    assert.Len(t, response.Entities, 1)
    assert.Equal(t, "entity-1", response.Entities[0].Id)
    assert.Contains(t, response.Highlights, "entity-1")
    assert.Equal(t, int32(1), response.TotalResults)
}
```

### Test Debugging

For debugging test failures:

```bash
# Run specific test with verbose output
go test -v ./test/entity/ -run TestSearch_SpecificTest

# Run with race detection
go test -v ./test/entity/ -race -run TestSearch_SpecificTest

# Generate test coverage report
go test -v ./test/entity/ -coverprofile=coverage.out
go tool cover -html=coverage.out

# Enable debug logging (if implemented)
ENTITY_DEBUG=true go test -v ./test/entity/ -run TestSearch_SpecificTest

# Run performance tests with extended timeout
go test -v ./test/entity/ -run TestEntity_BasicPerformance -timeout=600s
```

### Performance Test Considerations

**Runtime and Resource Requirements:**
- **Performance tests**: Create 100 entities + 10 schemas, may take 3-10 minutes including cleanup
- **Resource usage**: Requires adequate CPU and memory for realistic performance validation
- **Database load**: Performance tests generate significant database activity during execution
- **Timing sensitivity**: Tests expect sub-200ms latency for most operations, 2000ms for bulk operations
- **Environment consistency**: Performance results depend on system resources and database performance

**Troubleshooting Performance Test Failures:**
- **Latency threshold failures**: Tests expect sub-200ms response times for most operations
- **Database performance**: Ensure proper indexes and no concurrent heavy operations during testing
- **Resource constraints**: Performance tests require adequate system resources (CPU, memory)
- **Timeout errors**: Performance tests use 600-second timeout; adjust if needed for slower systems
- **Test data conflicts**: Performance tests create and cleanup their own data automatically

**Note**: Ensure you have a valid authentication token in `token.txt` and the necessary environment variables set before running tests. The test runner will validate the setup and provide helpful error messages if configuration is missing.

---

# Entity Search Functionality

This section provides a comprehensive technical deep-dive into the SearchEntities functionality, covering its architecture, implementation patterns, performance optimization strategies, and operational considerations. Whether you're debugging search queries, optimizing performance, or extending search capabilities, this guide provides the foundational knowledge you need.

## Architecture Overview

The SearchEntities functionality is built on a sophisticated multi-layered architecture that leverages PostgreSQL's advanced search capabilities, combining them with intelligent application-level query optimization and result enhancement:

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
│  Frontend Apps │ API Clients │ Search UIs │ Analytics Tools     │
└─────────────────────────────┬───────────────────────────────────┘
                              │ gRPC/HTTP Requests
┌─────────────────────────────┴───────────────────────────────────┐
│                      gRPC Service Layer                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Request         │ │ Response        │ │ Error           │    │
│  │ Validation      │ │ Formatting      │ │ Handling        │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Parameter       │ │ Result          │ │ Pagination      │    │
│  │ Normalization   │ │ Highlighting    │ │ Management      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ Repository Interface
┌─────────────────────────────┴───────────────────────────────────┐
│                    Repository Layer                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Query           │ │ Transaction     │ │ Result          │    │
│  │ Construction    │ │ Management      │ │ Mapping         │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Parameter       │ │ Filter          │ │ Session         │    │
│  │ Binding         │ │ Application     │ │ Handling        │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────┬───────────────────────────────────┘
                              │ SQL Queries
┌─────────────────────────────┴───────────────────────────────────┐
│                     Database Layer (PostgreSQL)                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Query Engine    │ │ Index           │ │ JSONB           │    │
│  │ & Planner       │ │ Utilization     │ │ Operations      │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │ Trigram Search  │ │ Transaction     │ │ Constraint      │    │
│  │ & ILIKE         │ │ Isolation       │ │ Enforcement     │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. SearchEntities Service Method
- **Purpose**: Primary entry point for all entity search operations
- **Responsibilities**: Request validation, parameter normalization, response assembly
- **Key Features**: Multi-criteria filtering, intelligent query routing, error boundary

#### 2. Dynamic Query Builder
- **Purpose**: Constructs optimized SQL queries based on search criteria
- **Capabilities**: 
  - Conditional JOIN logic (only joins `entity_references` when needed)
  - Parameterized query construction for SQL injection prevention
  - Filter prioritization for optimal query plans
  - Smart index hint generation

#### 3. Multi-Modal Filter Engine
- **Text Search**: ILIKE pattern matching with trigram indexes
- **Exact Filters**: Status, ID, and enum matching with B-tree indexes
- **Date Range Filters**: Time-series queries optimized with BRIN indexes
- **Reference-Specific Filters**: Reference ID/type matching with conditional JOINs

#### 4. Intelligent Indexing Strategy
- **Primary Indexes (B-tree)**: Fast exact matches and range queries
- **Trigram Indexes (GIN)**: Efficient partial text matching for ILIKE operations
- **JSONB Indexes**: Path-specific and general content indexing
- **Time-Series Indexes (BRIN)**: Minimal overhead for timestamp ranges
- **Reference Indexes**: Optimized access to entity relationships

#### 5. Advanced Highlighting Engine
- **Context Window Generation**: 40-character windows around matches
- **Multi-Field Priority**: Intelligent field selection for highlights
- **Performance Optimization**: Limited fragment generation (max 3 per entity)
- **Format-Aware Processing**: Handles JSON and text content

#### 6. Efficient Pagination System
- **Offset-Based Navigation**: Simple page jumping with LIMIT/OFFSET
- **Total Count Optimization**: Separate optimized COUNT queries
- **Consistent Ordering**: Secondary sort by ID for deterministic results
- **Token-Based State**: Stateless pagination with offset tokens

## Implementation Details

### Query Construction Pipeline

The search implementation follows a systematic 9-step approach to query construction, ensuring optimal performance and maintainable code:

```
1. Request Parameters Intake & Validation
       ↓
2. Parameter Normalization & Defaults
       ↓
3. Organization Context Extraction
       ↓
4. JOIN Strategy Determination
       ↓
5. Filter Construction & Prioritization
       ↓
6. Query Assembly with Pagination
       ↓
7. Main Query Execution
       ↓
8. Count Query Execution
       ↓
9. Result Enhancement & Response Assembly
```

### Filter Types and Implementation Patterns

The search implementation handles four distinct types of filters, each optimized for different data patterns and query performance characteristics:

#### 1. Text Search Filters (ILIKE + GIN Trigram)

**Purpose**: Partial/fuzzy matching across textual fields
**Fields Supported**: 
- `id` (entity identifier)
- `data` (JSON content search)
- `tags` (tag array search)
- `reference_display_name` (reference display names)
- `reference_relation_type` (reference relation types)

**Implementation Pattern:**
```sql
-- Global search across multiple fields
WHERE (
  e.id ILIKE $1 OR 
  e.data::text ILIKE $1 OR 
  array_to_string(e.tags, ' ') ILIKE $1 OR 
  er.display_name ILIKE $1 OR 
  er.relation_type ILIKE $1
)
```

**Performance Optimizations:**
- Uses GIN trigram indexes for fast ILIKE operations
- Conditional JOIN logic to avoid unnecessary table scans when reference fields aren't being searched
- Parameter binding to prevent SQL injection

#### 2. Exact Value Filters (B-tree Indexes)

**Purpose**: Precise matching for enums, IDs, and categorical data
**Fields Supported**: 
- `status` (RecordStatus enum)
- `entity_types` (EntityType enum)
- `created_by`, `updated_by` (user IDs)
- `schema_ids` (schema identifiers)
- `org_ids` (organization IDs)
- `tags` (exact tag matching)

**Implementation Pattern:**
```sql
-- Status filter with multiple values
WHERE e.status = ANY($1)

-- Entity type filter
WHERE e.entity_type = ANY($2)

-- Tag exact matching using array operators
WHERE e.tags && $3
```

**Performance Optimizations:**
- Leverages B-tree indexes for fast IN/ANY operations
- Uses PostgreSQL array operators for efficient tag matching
- Prioritizes highly selective filters first

#### 3. Date Range Filters (BRIN Indexes)

**Purpose**: Time-series filtering for temporal queries
**Fields Supported**: 
- `create_time` (entity creation timestamps)
- `update_time` (entity modification timestamps)

**Implementation Pattern:**
```sql
-- Date range filtering with BETWEEN
WHERE e.create_time BETWEEN $1 AND $2
AND e.update_time BETWEEN $3 AND $4
```

**Performance Optimizations:**
- Uses BRIN indexes for efficient timestamp range queries
- Minimal storage overhead compared to B-tree indexes
- Optimized for time-series workloads

#### 4. Reference Filters (Conditional JOIN)

**Purpose**: Filtering based on entity relationships
**Fields Supported**: 
- `reference_ids` (referenced object IDs)
- `reference_types` (reference type classifications)

**Implementation Pattern:**
```sql
-- Only JOIN when reference filters are present
FROM entities e
LEFT JOIN entity_references er ON e.id = er.entity_id
WHERE er.ref_id = ANY($1)
AND er.ref_type = ANY($2)
```

**Performance Optimizations:**
- Conditional JOIN logic - only joins `entity_references` when needed
- Uses `DISTINCT` to avoid duplicate rows from multiple references
- Leverages B-tree indexes on reference fields

### Database Schema Design for Search

The entity search functionality leverages a carefully designed database schema with specialized indexes:

#### Core Tables

```sql
-- Main entities table
CREATE TABLE entities (
    id TEXT PRIMARY KEY,
    org_id INTEGER NOT NULL,
    schema_id TEXT,
    schema_version INT,
    data JSONB,
    create_time TIMESTAMPTZ,
    update_time TIMESTAMPTZ,
    entity_type INT,
    created_by TEXT,
    updated_by TEXT,
    version INT NOT NULL,
    status INT,
    tags TEXT[]
);

-- Entity relationships table
CREATE TABLE entity_references (
    entity_id TEXT NOT NULL,
    ref_id TEXT NOT NULL,
    ref_type TEXT NOT NULL,
    ref_version INTEGER,
    display_name TEXT,
    relation_type TEXT,
    org_id INTEGER NOT NULL,
    PRIMARY KEY (entity_id, ref_id, ref_type)
);
```

#### Search-Optimized Indexes

```sql
-- Trigram indexes for fuzzy text search
CREATE INDEX idx_entities_id_trgm ON entities USING gin (id gin_trgm_ops);
CREATE INDEX idx_entity_references_display_name_trgm ON entity_references 
    USING gin (lower(display_name) gin_trgm_ops) WHERE display_name IS NOT NULL;
CREATE INDEX idx_entity_references_relation_type_trgm ON entity_references 
    USING gin (lower(relation_type) gin_trgm_ops) WHERE relation_type IS NOT NULL;

-- JSONB indexes for data field search
CREATE INDEX gin_entities_data ON entities USING gin (data);
CREATE INDEX gin_entities_tags ON entities USING gin (tags);

-- B-tree indexes for exact matching
CREATE INDEX idx_entities_status ON entities (status);
CREATE INDEX idx_entities_entity_type ON entities (entity_type);
CREATE INDEX idx_entities_created_by ON entities (created_by) WHERE created_by IS NOT NULL;
CREATE INDEX idx_entities_updated_by ON entities (updated_by) WHERE updated_by IS NOT NULL;

-- BRIN indexes for time-series queries
CREATE INDEX brin_entities_create_time ON entities USING brin (create_time);
CREATE INDEX brin_entities_update_time ON entities USING brin (update_time);

-- Composite indexes for common search patterns
CREATE INDEX idx_entities_org_status ON entities (org_id, status);
CREATE INDEX idx_entities_org_type ON entities (org_id, entity_type);
```

### Search Performance Characteristics

#### Query Performance Metrics

- **Simple text search**: 10-50ms for datasets up to 1M entities
- **Complex multi-filter queries**: 50-200ms with proper indexing
- **Date range queries**: 5-20ms using BRIN indexes
- **Reference searches**: 20-100ms depending on JOIN complexity

#### Pagination Performance

- **Offset-based pagination**: Consistent performance for reasonable offsets (<10K)
- **Count queries**: Optimized with separate COUNT execution
- **Large datasets**: Performance degrades linearly with offset size

#### Memory Usage

- **Query execution**: 1-10MB per concurrent search
- **Highlighting**: Additional 100KB-1MB depending on result size
- **Index memory**: GIN indexes require 20-30% additional storage

## Advanced Search Features

### Highlighting System

The entity search includes a sophisticated highlighting system that generates contextual snippets:

#### Highlighting Algorithm
1. **Term Extraction**: Extract search terms from query and field queries
2. **Content Scanning**: Scan entity fields for term matches (case-insensitive)
3. **Context Generation**: Create 40-character windows around each match
4. **Fragment Limiting**: Cap at 3 fragments per entity for performance
5. **Deduplication**: Remove duplicate fragments to avoid redundancy

#### Implementation Example
```go
func (repository *PostgresEntityRepository) createHighlightFragment(text, term string) string {
    lowerText := strings.ToLower(text)
    lowerTerm := strings.ToLower(term)
    
    index := strings.Index(lowerText, lowerTerm)
    if index == -1 {
        return ""
    }
    
    start := repository.maxInt(0, index-20)
    end := repository.minInt(len(text), index+len(term)+20)
    
    fragment := text[start:end]
    if start > 0 {
        fragment = "…" + fragment
    }
    if end < len(text) {
        fragment = fragment + "…"
    }
    
    return fragment
}
```

### Search Query Optimization

#### Conditional JOIN Logic
The system intelligently determines when to JOIN with the `entity_references` table:

```go
// Only JOIN when reference-related filters or searches are present
joinReferences := len(request.ReferenceIds) > 0 || 
                 len(request.ReferenceTypes) > 0 ||
                 repository.needsReferenceFieldSearch(request)

if joinReferences {
    baseQuery = `
        SELECT DISTINCT e.id, e.org_id, e.schema_id, e.schema_version, e.data, 
               e.create_time, e.update_time, e.entity_type, e.created_by, e.updated_by, 
               e.version, e.status, e.tags, e.resource_type
        FROM entities e
        LEFT JOIN entity_references er ON e.id = er.entity_id`
} else {
    baseQuery = `
        SELECT e.id, e.org_id, e.schema_id, e.schema_version, e.data, 
               e.create_time, e.update_time, e.entity_type, e.created_by, e.updated_by, 
               e.version, e.status, e.tags, e.resource_type
        FROM entities e`
}
```

#### Filter Prioritization
Filters are applied in order of selectivity to optimize query execution:

1. **Organization ID** (most selective - required for access control)
2. **Status filters** (typically high selectivity)
3. **Entity type filters** (moderate selectivity)
4. **Date range filters** (time-based selectivity)
5. **Text search filters** (least selective - applied last)

## Troubleshooting and Debugging

### Common Performance Issues

#### Issue: Slow text search queries
**Symptoms**: ILIKE queries taking >1 second
**Solutions**:
- Verify trigram indexes are being used: `EXPLAIN ANALYZE`
- Consider query specificity - very short terms may be inefficient
- Check if `pg_trgm` extension is installed

#### Issue: High memory usage during search
**Symptoms**: Out of memory errors or high memory consumption
**Solutions**:
- Reduce `page_size` to limit result sets
- Optimize highlight generation by limiting search terms
- Consider pagination for large result sets

#### Issue: Inconsistent pagination results
**Symptoms**: Duplicate or missing entities across pages
**Solutions**:
- Ensure consistent `order_by` with secondary sort by ID
- Verify stable sorting criteria across requests
- Check for concurrent modifications affecting result order

### Query Analysis

Use PostgreSQL's query analysis tools to understand search performance:

```sql
-- Analyze a typical search query
EXPLAIN (ANALYZE, BUFFERS) 
SELECT DISTINCT e.id, e.org_id, e.schema_id, e.schema_version, e.data, 
       e.create_time, e.update_time, e.entity_type, e.created_by, e.updated_by, 
       e.version, e.status, e.tags, e.resource_type
FROM entities e
LEFT JOIN entity_references er ON e.id = er.entity_id
WHERE e.org_id = $1 
  AND e.status = ANY($2)
  AND e.data::text ILIKE $3
ORDER BY e.create_time DESC, e.id
LIMIT 50 OFFSET 0;
```

### Index Monitoring

Monitor index usage and performance:

```sql
-- Check index usage statistics
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('entities', 'entity_references')
ORDER BY idx_scan DESC;

-- Monitor index size and efficiency
SELECT schemaname, tablename, indexname, 
       pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE tablename IN ('entities', 'entity_references');
```

## Future Enhancements

### Planned Improvements

1. **Full-Text Search Integration**: PostgreSQL FTS integration for better text search
2. **Faceted Search**: Count-based filtering and drill-down capabilities  
3. **Geospatial Search**: Location-based entity filtering
4. **Search Analytics**: Query performance monitoring and optimization suggestions
5. **Caching Layer**: Redis-based caching for frequent searches
6. **Real-time Search**: WebSocket-based live search updates

### Performance Scaling

As entity datasets grow, consider these scaling strategies:

1. **Partitioning**: Time-based or organization-based table partitioning
2. **Read Replicas**: Separate read workloads from write operations
3. **Connection Pooling**: Optimize database connection management
4. **Index Optimization**: Regular maintenance and analysis of index performance

### Sample Request and Response

**Request (JSON):**
```json
{
  "query": "Alice",
  "search_fields": ["data", "tags"],
  "field_queries": [
    {
      "field": "reference_display_name",
      "query": "emergency"
    }
  ],
  "status": ["RECORD_STATUS_ACTIVE"],
  "entity_types": ["ENTITY_TYPE_PERSON"],
  "create_time": {
    "from": "2025-01-01T00:00:00Z",
    "to": "2025-12-31T23:59:59Z"
  },
  "page_size": 10,
  "order_by": "SEARCH_ORDER_BY_RELEVANCE",
  "ascending": false
}
```

**Response (JSON):**
```json
{
  "entities": [
    {
      "id": "entity-123",
      "orgId": "org-001",
      "schemaId": "schema-123",
      "schemaVersion": 1,
      "data": { "firstName": "Alice", "age": 25, "role": "emergency_contact" },
      "references": [
        {
          "id": "ref-456",
          "type": "report",
          "version": 1,
          "displayName": "Emergency Response Report"
        }
      ],
      "createTime": "2025-04-09T08:00:00Z",
      "updateTime": "2025-04-09T08:30:00Z",
      "entityType": "ENTITY_TYPE_PERSON",
      "createdBy": "user-001",
      "updatedBy": "user-001",
      "version": 1,
      "status": "RECORD_STATUS_ACTIVE",
      "tags": ["customer", "alice_related"],
      "permissionRulesetId": "ruleset-001"
    }
  ],
  "nextPageToken": "10",
  "highlights": {
    "entity-123": {
      "field": "data",
      "fragments": ["…firstName: Alice…", "…emergency_contact…"]
    }
  },
  "totalResults": 1
}
```
