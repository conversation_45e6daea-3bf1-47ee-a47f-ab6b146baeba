# Order Module

In our system, an **Order** represents a task or instruction issued in response to a situation. Orders coordinate actions such as triaging incidents, assisting members, submitting final reports, and more. The Order Module is responsible for creating, updating, listing, and tracking the lifecycle of these orders, ensuring proper execution and monitoring of resource assignments.

Orders are tightly integrated with the Situations and Assets modules. They help to:
- **Assign Tasks:** Define and delegate tasks to assets based on situation requirements.
- **Monitor Progress:** Track status changes and maintain detailed update entries.
- **Manage Permissions:** Control which asset types can change order status or be assigned to an order.
- **Enforce Business Logic:** Automatically trigger side effects—such as updating a situation's status—based on order updates.

---

## Running Locally

When you run `make run`, the service starts on `localhost:9086` by default and uses a locally running PostgreSQL Docker container as its database.  
If you encounter database errors, run `make db` **before** `make run` to ensure your local database is set up and migrated to the correct version.

To switch to an in‑memory database, update the `<root>/docker-compose.yml` file under `workflow-service` by setting the `REPO_TYPE` environment variable to `inmemory`.

---

# Data Model Reference

## Enums

### OrderStatus
Defines the lifecycle stages of an order:

| Name                      | Value | Description                                      |
|---------------------------|-------|--------------------------------------------------|
| ORDER_STATUS_UNSPECIFIED  | 0     | Default unspecified order status.              |
| ORDER_STATUS_CREATED      | 1     | Order has been created.                          |
| ORDER_STATUS_ACKNOWLEDGED | 2     | Asset has acknowledged the order.                |
| ORDER_STATUS_REJECTED     | 3     | Order was rejected by the asset.                 |
| ORDER_STATUS_SNOOZED      | 4     | Order has been snoozed (delayed).                |
| ORDER_STATUS_IN_PROGRESS  | 5     | Order is actively being worked on.               |
| ORDER_STATUS_COMPLETED    | 6     | Order has been completed.                        |
| ORDER_STATUS_CANCELLED    | 7     | Order has been cancelled.                        |

### OrderType
Represents different types of orders:

| Name                             | Value | Description                                                      |
|----------------------------------|-------|------------------------------------------------------------------|
| ORDER_TYPE_UNSPECIFIED           | 0     | Default unspecified order type.                                  |
| ORDER_TYPE_TRIAGE_MEMBER_REPORT  | 1     | Order for triaging a situation reported by a member.             |
| ORDER_TYPE_ASSIST_MEMBER         | 2     | Order for assisting a member.                                    |
| ORDER_TYPE_SUBMIT_FINAL_REPORT   | 3     | Order to submit a final report.                                  |
| ORDER_TYPE_ASSIGN_AGENT          | 4     | Order to assign an agent to a task.                              |
| ORDER_TYPE_TRIAGE_AGENT_REPORT   | 5     | Order for triaging a situation reported by an agent.             |
| ORDER_TYPE_TRIAGE_CAMERA_INCIDENT| 6     | Order for triaging an incident captured by a camera.             |
| ORDER_TYPE_WRITE_REPORT          | 7     | Order for writing a report.                                      |
| ORDER_TYPE_REVIEW_REPORT         | 8     | Order for reviewing a report.                                    |
| ORDER_TYPE_REVISE_REPORT         | 9     | Order for revising a report.                                     |
| ORDER_TYPE_MANAGE_CASE           | 10    | Order for managing and investigating a case.                     |

---

## Messages

### OrderPermissions
Defines which asset types are allowed to change the status or assign an asset to an order.

| Field           | Type                                      | Description                                      |
|-----------------|-------------------------------------------|--------------------------------------------------|
| canChangeStatus | repeated hero.assets.v2.AssetType         | Asset types allowed to change the order status.  |
| canAssignAsset  | repeated hero.assets.v2.AssetType         | Asset types allowed to assign an asset.          |

### OrderUpdateEntry
Tracks an update entry with a timestamp and source.

| Field        | Type                           | Description                        |
|--------------|--------------------------------|------------------------------------|
| message      | string                         | Description of the update.         |
| timestamp    | string                         | ISO8601 timestamp of the update.   |
| updateSource | hero.situations.v2.UpdateSource | Source of the update.              |

### OrderStatusUpdateEntry
Captures detailed status transition information for orders.

| Field                          | Type                           | Description                                                |
|--------------------------------|--------------------------------|------------------------------------------------------------|
| timestamp                      | string                         | ISO8601 timestamp when the status update occurred.        |
| newStatus                      | OrderStatus                    | The new status of the order.                               |
| previousStatus                 | OrderStatus                    | The previous status before this update.                    |
| newTypeSpecificStatus          | string                         | The new type-specific status of the order.                 |
| previousTypeSpecificStatus     | string                         | The previous type-specific status before this update.      |
| note                           | string                         | *(Optional)* A note explaining the status change.          |
| updaterId                      | string                         | *(Optional)* ID of the updater.                            |
| updateSource                   | hero.situations.v2.UpdateSource | *(Optional)* Source of the update.                         |
| statusUpdateTimestamp          | string                         | *(Optional)* ISO8601 timestamp for when the status update was set. If not provided, defaults to the timestamp value. |

### Order
Represents an order entity.

| Field                          | Type                           | Description                                                                           |
|--------------------------------|--------------------------------|---------------------------------------------------------------------------------------|
| id                             | string                         | Unique identifier for the order.                                                      |
| situationId                    | string                         | Reference to the associated situation.                                                |
| caseId                         | string                         | Reference to the associated case (for case-related orders).                           |
| assetId                        | string                         | Reference to the asset assigned to execute this order.                                |
| type                           | OrderType                      | Specifies the type of order.                                                          |
| status                         | OrderStatus                    | Indicates the current execution status of the order.                                  |
| instructions                   | string                         | Detailed instructions for the assigned asset.                                         |
| priority                       | int32                          | Priority level of the order.                                                          |
| additionalInfoJson             | string                         | JSON string containing order-specific parameters.                                     |
| typeSpecificStatus             | string                         | Additional status information specific to the order type.                             |
| notes                          | string                         | Free-form notes or comments regarding the order.                                      |
| createTime                     | string                         | ISO8601 timestamp when the order was created.                                         |
| updateTime                     | string                         | ISO8601 timestamp when the order was last updated.                                    |
| completionTime                 | string                         | ISO8601 timestamp when the order was completed.                                       |
| updates                        | repeated OrderUpdateEntry      | List of update entries tracking changes.                                              |
| assignedTime                   | string                         | ISO8601 timestamp when the order was assigned to an asset.                            |
| acknowledgedTime               | string                         | ISO8601 timestamp when the asset acknowledged the order.                              |
| estimatedCompletionTime        | string                         | ISO8601 timestamp for order completion.                                               |
| cancellationOrRejectionReason  | string                         | Reason for order cancellation or rejection.                                           |
| retryCount                     | int32                          | Number of times the order has been retried.                                             |
| createdBy                      | hero.situations.v2.UpdateSource | Source of order creation.                                                               |
| title                          | string                         | A short descriptive title for the order.                                              |
| allowedAssetTypes              | repeated hero.assets.v2.AssetType | Asset types that can perform this order.                                             |
| snoozeReason                   | string                         | Reason for snoozing the order.                                                          |
| snoozeUntil                    | string                         | ISO8601 timestamp until which the order is snoozed.                                   |
| snoozeCount                    | int32                          | Number of times the order has been snoozed.                                             |
| blacklistedAssetIds            | repeated string                | List of asset IDs that should NOT be assigned to this order.                            |
| resourceType                   | string                         | Fixed value `"ORDER"`.                                                                  |
| permissions                    | OrderPermissions               | Permissions specifying allowed status changes and asset assignments.                    |
| statusUpdates                  | repeated OrderStatusUpdateEntry| Timeseries status updates for the order.                                              |
| reportId                       | string                         | Reference this order to a report.                                                       |
| reviewRoundId                  | string                         | If this is a review order, point back at the ReviewRound.                               |

> **Note:** The new `statusUpdates` field captures every status update event (including the initial creation update) for an order, providing a complete audit trail.

---

## Overview of Endpoints

The Order Module provides a comprehensive set of endpoints for managing orders. Below is an overview:

1. **[CreateOrder](#1-createorder)**
2. **[GetOrder](#2-getorder)**
3. **[UpdateOrder](#3-updateorder)**
4. **[ListOrders](#4-listorders)**
5. **[DeleteOrder](#5-deleteorder)**
6. **[AddOrderUpdate](#6-addorderupdate)**
7. **[RemoveOrderUpdate](#7-removeorderupdate)**
8. **[AddOrderStatusUpdate](#8-addorderstatusupdate)**
9. **[RemoveOrderStatusUpdate](#9-removeorderstatusupdate)**
10. **[AddAllowedAssetType](#10-addallowedassettype)**
11. **[RemoveAllowedAssetType](#11-removeallowedassettype)**
12. **[AddBlacklistedAssetId](#12-addblacklistedassetid)**
13. **[RemoveBlacklistedAssetId](#13-removeblacklistedassetid)**
14. **[AcknowledgeOrder](#14-acknowledgeorder)**
15. **[RejectOrder](#15-rejectorder)**
16. **[SnoozeOrder](#16-snoozeorder)**
17. **[CancelOrder](#17-cancelorder)**
18. **[CompleteOrder](#18-completeorder)**
19. **[ListActiveAssignedOrdersForAsset](#19-listactiveassignedordersforasset)**
20. **[ListNewOrdersForAsset](#20-listnewordersforasset)**
21. **[UpdateOrderPermissions](#21-updateorderpermissions)**
22. **[ListOrdersForSituation](#22-listordersforsituation)**
23. **[ListOrdersForAsset](#23-listordersforasset)**
24. **[AddAdditionalInfo](#24-addadditionalinfo)**
25. **[ListOrdersForReport](#25-listordersforreport)**
26. **[ListOrdersForReviewRound](#26-listordersforreviewround)**
27. **[ListOrdersForCase](#27-listordersforcase)**

---

### 1. CreateOrder

**Method:** `CreateOrder`  
**Route:** `POST /hero.orders.v2.OrderService/CreateOrder`

#### Message Fields

**CreateOrderRequest:**

| Field | Type  | Description                                                                       |
|-------|-------|-----------------------------------------------------------------------------------|
| order | Order | Order object to be created. **Note:** The `id` should not be provided; it is auto-generated. |

**CreateOrderResponse:**

| Field | Type  | Description                                                       |
|-------|-------|-------------------------------------------------------------------|
| order | Order | The newly created order object with auto-generated fields (e.g., `id`, `createTime`, `updateTime`), default values (e.g., `resourceType` set to `"ORDER"`), and complete status update history in `statusUpdates`. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "order": {
    "situationId": "situation-123",
    "assetId": "asset-456",
    "type": "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
    "status": "ORDER_STATUS_CREATED",
    "instructions": "Initiate triage for the reported situation.",
    "priority": 1,
    "additionalInfoJson": "{\"detail\": \"Check sensor readings\"}",
    "notes": "Urgent order",
    "createdBy": "UPDATE_SOURCE_HUMAN_OPERATOR",
    "title": "Triage Request"
  }
}
```

**Response (JSON):**
```json
{
  "order": {
    "id": "generated-uuid",
    "situationId": "situation-123",
    "assetId": "asset-456",
    "type": "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
    "status": "ORDER_STATUS_CREATED",
    "instructions": "Initiate triage for the reported situation.",
    "priority": 1,
    "additionalInfoJson": "{\"detail\": \"Check sensor readings\"}",
    "notes": "Urgent order",
    "createdBy": "UPDATE_SOURCE_HUMAN_OPERATOR",
    "title": "Triage Request",
    "createTime": "2025-03-06T12:00:00Z",
    "updateTime": "2025-03-06T12:00:00Z",
    "resourceType": "ORDER",
    "reportId": "",
    "reviewRoundId": "",
    "statusUpdates": [ {
      "timestamp": "2025-03-06T12:00:00Z",
      "newStatus": "ORDER_STATUS_CREATED"
    }]
  }
}
```

> **Note:** The complete order record is returned—including the new `statusUpdates` field which will capture all recorded status transitions.

---

### 2. GetOrder

**Method:** `GetOrder`  
**Route:** `POST /hero.orders.v2.OrderService/GetOrder`

#### Message Fields

**GetOrderRequest:**

| Field | Type   | Description                    |
|-------|--------|--------------------------------|
| id    | string | Unique identifier of the order. |

**GetOrder Response:**

*(Returns the complete Order object; refer to the Order data model for complete field details.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "situationId": "situation-123",
  "assetId": "asset-456",
  "type": "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
  "status": "ORDER_STATUS_CREATED",
  "instructions": "Initiate triage for the reported situation.",
  "priority": 1,
  "additionalInfoJson": "{\"detail\": \"Check sensor readings\"}",
  "notes": "Urgent order",
  "createdBy": "UPDATE_SOURCE_HUMAN_OPERATOR",
  "title": "Triage Request",
  "createTime": "2025-03-06T12:00:00Z",
  "updateTime": "2025-03-06T12:05:00Z",
  "resourceType": "ORDER",
  "reportId": "",
  "reviewRoundId": "",
  "statusUpdates": [ {
      "timestamp": "2025-03-06T12:00:00Z",
      "newStatus": "ORDER_STATUS_CREATED"
    }]
}
```

---

### 3. UpdateOrder

**Method:** `UpdateOrder`  
**Route:** `POST /hero.orders.v2.OrderService/UpdateOrder`

#### Message Fields

**UpdateOrderRequest:**

| Field | Type  | Description                                                                          |
|-------|-------|--------------------------------------------------------------------------------------|
| order | Order | Updated order object. **Note:** Must include the `id` of the order to be updated.     |

**UpdateOrder Response:**

*(Returns the complete updated Order object.)*

#### Sample Request and Response

**Sample Request (JSON):**
```json
{
  "order": {
    "id": "c3cfae08-5698-4b25-8cbb-b812901e262b",
    "typeSpecificStatus": "Random"
  }
}
```

**Sample Response (JSON):**
```json
{
  "id": "c3cfae08-5698-4b25-8cbb-b812901e262b",
  "type": "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
  "status": "ORDER_STATUS_COMPLETED",
  "instructions": "Investigate the reported situation and provide an initial assessment.",
  "priority": 1,
  "additionalInfoJson": "{}",
  "typeSpecificStatus": "Random",
  "notes": "High priority case; immediate action required.",
  "createTime": "2025-04-02T08:42:32.588258Z",
  "updateTime": "2025-04-02T08:44:45.974755Z",
  "completionTime": "0001-01-01T00:00:00Z",
  "assignedTime": "0001-01-01T00:00:00Z",
  "acknowledgedTime": "0001-01-01T00:00:00Z",
  "estimatedCompletionTime": "0001-01-01T00:00:00Z",
  "snoozeUntil": "0001-01-01T00:00:00Z",
  "resourceType": "ORDER",
  "permissions": {},
  "statusUpdates": [
    {
      "timestamp": "2025-04-02T08:42:32.589194Z",
      "newStatus": "ORDER_STATUS_COMPLETED",
      "newTypeSpecificStatus": "Awaiting assignment"
    },
    {
      "timestamp": "2025-04-02T08:44:45.975685Z",
      "newStatus": "ORDER_STATUS_COMPLETED",
      "previousStatus": "ORDER_STATUS_COMPLETED",
      "newTypeSpecificStatus": "Random",
      "previousTypeSpecificStatus": "Awaiting assignment"
    }
  ]
}
```

> **Note:** The updated order record includes the complete audit trail in `statusUpdates`, showing all status changes from creation through the latest update.

---

### 4. ListOrders

**Method:** `ListOrders`  
**Route:** `POST /hero.orders.v2.OrderService/ListOrders`

#### Message Fields

**ListOrdersRequest:**

| Field    | Type         | Description                                                              |
|----------|--------------|--------------------------------------------------------------------------|
| pageSize | int32        | Maximum number of orders to return.                                      |
| pageToken| string       | Token identifying a specific page of results.                          |
| status   | OrderStatus  | *(Optional)* Filter by order status.                                     |
| type     | OrderType    | *(Optional)* Filter by order type.                                       |
| orderBy  | string       | *(Optional)* Specifies the ordering (e.g., "create_time desc").            |

**ListOrdersResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of Order objects.                                       |
| nextPageToken | string         | Token for retrieving the next page of results, if any.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "pageSize": 5,
  "pageToken": "",
  "status": "ORDER_STATUS_UNSPECIFIED",
  "orderBy": "create_time desc"
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "situationId": "situation-123",
      "assetId": "asset-456",
      "type": "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
      "status": "ORDER_STATUS_CREATED",
      "instructions": "Initiate triage.",
      "priority": 1,
      "title": "Triage Request",
      "createTime": "2025-03-06T12:00:00Z",
      "resourceType": "ORDER",
      "statusUpdates": []
    },
    {
      "id": "order-002",
      "situationId": "situation-124",
      "assetId": "asset-789",
      "type": "ORDER_TYPE_ASSIST_MEMBER",
      "status": "ORDER_STATUS_ACKNOWLEDGED",
      "instructions": "Assist with incident.",
      "priority": 2,
      "title": "Assistance Request",
      "createTime": "2025-03-06T12:05:00Z",
      "resourceType": "ORDER",
      "statusUpdates": []
    }
  ],
  "nextPageToken": "next-page-token-xyz"
}
```

---

### 5. DeleteOrder

**Method:** `DeleteOrder`  
**Route:** `POST /hero.orders.v2.OrderService/DeleteOrder`

#### Message Fields

**DeleteOrderRequest:**

| Field | Type   | Description                         |
|-------|--------|-------------------------------------|
| id    | string | Unique identifier of the order.    |

**DeleteOrder Response:**

*(Returns an empty message – google.protobuf.Empty)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid"
}
```

**Response (JSON):**
```json
{}
```

---

### 6. AddOrderUpdate

**Method:** `AddOrderUpdate`  
**Route:** `POST /hero.orders.v2.OrderService/AddOrderUpdate`

#### Message Fields

**AddOrderUpdateRequest:**

| Field  | Type             | Description                                                       |
|--------|------------------|-------------------------------------------------------------------|
| id     | string           | Unique identifier of the order to update.                         |
| update | OrderUpdateEntry | Update entry to be added.                                         |

**AddOrderUpdate Response:**

*(Returns the Order object with updated `updates` field.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "update": {
    "message": "Order acknowledged by asset.",
    "timestamp": "2025-03-06T12:06:00Z",
    "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
  }
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "updates": [
    {
      "message": "Order acknowledged by asset.",
      "timestamp": "2025-03-06T12:06:00Z",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    }
  ]
}
```

---

### 7. RemoveOrderUpdate

**Method:** `RemoveOrderUpdate`  
**Route:** `POST /hero.orders.v2.OrderService/RemoveOrderUpdate`

#### Message Fields

**RemoveOrderUpdateRequest:**

| Field  | Type             | Description                                                       |
|--------|------------------|-------------------------------------------------------------------|
| id     | string           | Unique identifier of the order.                                    |
| update | OrderUpdateEntry | The update entry to remove (matched by its fields).                |

**RemoveOrderUpdate Response:**

*(Returns the Order object with the update removed.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "update": {
    "message": "Order acknowledged by asset.",
    "timestamp": "2025-03-06T12:06:00Z",
    "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
  }
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "updates": []
}
```

---

### 8. AddOrderStatusUpdate

**Method:** `AddOrderStatusUpdate`  
**Route:** `POST /hero.orders.v2.OrderService/AddOrderStatusUpdate`

#### Message Fields

**AddOrderStatusUpdateRequest:**

| Field              | Type   | Description                                                        |
|--------------------|--------|--------------------------------------------------------------------|
| id                 | string | Unique identifier of the order to update.                          |
| statusUpdate       | OrderStatusUpdateEntry | Status update to be added.                                       |

**AddOrderStatusUpdate Response:**

| Field              | Type   | Description                                                |
|--------------------|--------|------------------------------------------------------------|
| id                 | string | Identifier of the updated order.                           |
| statusUpdates      | repeated OrderStatusUpdateEntry | Updated statusUpdates after adding the new status update. |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "statusUpdate": {
    "timestamp": "2025-03-06T12:06:00Z",
    "newStatus": "ORDER_STATUS_ACKNOWLEDGED",
    "previousStatus": "ORDER_STATUS_CREATED",
    "newTypeSpecificStatus": "Awaiting assignment",
    "previousTypeSpecificStatus": "Random",
    "note": "Asset acknowledged the order",
    "updaterId": "UPDATE_SOURCE_HUMAN_OPERATOR",
    "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
  }
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "statusUpdates": [
    {
      "timestamp": "2025-03-06T12:06:00Z",
      "newStatus": "ORDER_STATUS_ACKNOWLEDGED",
      "previousStatus": "ORDER_STATUS_CREATED",
      "newTypeSpecificStatus": "Awaiting assignment",
      "previousTypeSpecificStatus": "Random",
      "note": "Asset acknowledged the order",
      "updaterId": "UPDATE_SOURCE_HUMAN_OPERATOR",
      "updateSource": "UPDATE_SOURCE_HUMAN_OPERATOR"
    }
  ]
}
```

---

### 9. RemoveOrderStatusUpdate

**Method:** `RemoveOrderStatusUpdate`  
**Route:** `POST /hero.orders.v2.OrderService/RemoveOrderStatusUpdate`

#### Message Fields

**RemoveOrderStatusUpdateRequest:**

| Field           | Type   | Description                                                        |
|-----------------|--------|--------------------------------------------------------------------|
| id              | string | Unique identifier of the order to update.                          |
| statusUpdateId  | int32  | ID of the status update entry to remove.                           |

**RemoveOrderStatusUpdate Response:**

*(Returns the Order object with the status update removed.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "statusUpdateId": 123
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "statusUpdates": []
}
```

---

### 10. AddAllowedAssetType

**Method:** `AddAllowedAssetType`  
**Route:** `POST /hero.orders.v2.OrderService/AddAllowedAssetType`

#### Message Fields

**AddAllowedAssetTypeRequest:**

| Field            | Type   | Description                                     |
|------------------|--------|-------------------------------------------------|
| id               | string | Unique identifier of the order.               |
| allowedAssetType | string | Asset type to be added (e.g., "ASSET_TYPE_RESPONDER"). |

**AddAllowedAssetType Response:**

*(Returns the Order object with updated allowedAssetTypes list.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "allowedAssetType": "ASSET_TYPE_RESPONDER"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "allowedAssetTypes": ["ASSET_TYPE_RESPONDER"]
}
```

---

### 11. RemoveAllowedAssetType

**Method:** `RemoveAllowedAssetType`  
**Route:** `POST /hero.orders.v2.OrderService/RemoveAllowedAssetType`

#### Message Fields

**RemoveAllowedAssetTypeRequest:**

| Field            | Type   | Description                                     |
|------------------|--------|-------------------------------------------------|
| id               | string | Unique identifier of the order.               |
| allowedAssetType | string | Asset type to be removed.                       |

**RemoveAllowedAssetType Response:**

*(Returns the Order object with updated allowedAssetTypes list.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "allowedAssetType": "ASSET_TYPE_RESPONDER"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "allowedAssetTypes": []
}
```

---

### 12. AddBlacklistedAssetId

**Method:** `AddBlacklistedAssetId`  
**Route:** `POST /hero.orders.v2.OrderService/AddBlacklistedAssetId`

#### Message Fields

**AddBlacklistedAssetIdRequest:**

| Field   | Type   | Description                                |
|---------|--------|--------------------------------------------|
| id      | string | Unique identifier of the order.          |
| assetId | string | Asset ID to add to the blacklist.        |

**AddBlacklistedAssetId Response:**

*(Returns the Order object with updated blacklistedAssetIds.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "assetId": "asset-999"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "blacklistedAssetIds": ["asset-999"]
}
```

---

### 13. RemoveBlacklistedAssetId

**Method:** `RemoveBlacklistedAssetId`  
**Route:** `POST /hero.orders.v2.OrderService/RemoveBlacklistedAssetId`

#### Message Fields

**RemoveBlacklistedAssetIdRequest:**

| Field   | Type   | Description                                |
|---------|--------|--------------------------------------------|
| id      | string | Unique identifier of the order.          |
| assetId | string | Asset ID to remove from the blacklist.   |

**RemoveBlacklistedAssetId Response:**

*(Returns the Order object with updated blacklistedAssetIds.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "assetId": "asset-999"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "blacklistedAssetIds": []
}
```

---

### 14. AcknowledgeOrder

**Method:** `AcknowledgeOrder`  
**Route:** `POST /hero.orders.v2.OrderService/AcknowledgeOrder`

#### Message Fields

**AcknowledgeOrderRequest:**

| Field | Type   | Description                    |
|-------|--------|--------------------------------|
| id    | string | Unique identifier of the order. |

**AcknowledgeOrder Response:**

*(Returns the Order object with updated status and acknowledgedTime.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "status": "ORDER_STATUS_ACKNOWLEDGED",
  "acknowledgedTime": "2025-03-06T12:07:00Z"
}
```

---

### 15. RejectOrder

**Method:** `RejectOrder`  
**Route:** `POST /hero.orders.v2.OrderService/RejectOrder`

#### Message Fields

**RejectOrderRequest:**

| Field | Type   | Description                                   |
|-------|--------|-----------------------------------------------|
| id    | string | Unique identifier of the order.             |
| reason| string | Reason for rejecting the order.             |

**RejectOrder Response:**

*(Returns the Order object with updated status and cancellationOrRejectionReason.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "reason": "Asset unavailable"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "status": "ORDER_STATUS_REJECTED",
  "cancellationOrRejectionReason": "Asset unavailable"
}
```

---

### 16. SnoozeOrder

**Method:** `SnoozeOrder`  
**Route:** `POST /hero.orders.v2.OrderService/SnoozeOrder`

#### Message Fields

**SnoozeOrderRequest:**

| Field       | Type                      | Description                                             |
|-------------|---------------------------|---------------------------------------------------------|
| id          | string                    | Unique identifier of the order.                       |
| snoozeReason| string                    | Reason for snoozing the order.                          |
| snoozeUntil | string                    | ISO8601 timestamp until which the order is snoozed.     |

**SnoozeOrder Response:**

*(Returns the Order object with updated status, snoozeReason, and snoozeUntil.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "snoozeReason": "Waiting for additional info",
  "snoozeUntil": "2025-03-06T13:00:00Z"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "status": "ORDER_STATUS_SNOOZED",
  "snoozeReason": "Waiting for additional info",
  "snoozeUntil": "2025-03-06T13:00:00Z"
}
```

---

### 17. CancelOrder

**Method:** `CancelOrder`  
**Route:** `POST /hero.orders.v2.OrderService/CancelOrder`

#### Message Fields

**CancelOrderRequest:**

| Field | Type   | Description                                   |
|-------|--------|-----------------------------------------------|
| id    | string | Unique identifier of the order.             |
| reason| string | Reason for cancellation.                      |

**CancelOrder Response:**

*(Returns the Order object with updated status and cancellationOrRejectionReason.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "reason": "Order no longer needed"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "status": "ORDER_STATUS_CANCELLED",
  "cancellationOrRejectionReason": "Order no longer needed"
}
```

---

### 18. CompleteOrder

**Method:** `CompleteOrder`  
**Route:** `POST /hero.orders.v2.OrderService/CompleteOrder`

#### Message Fields

**CompleteOrderRequest:**

| Field | Type   | Description                    |
|-------|--------|--------------------------------|
| id    | string | Unique identifier of the order. |

**CompleteOrder Response:**

*(Returns the Order object with updated status and completionTime.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "status": "ORDER_STATUS_COMPLETED",
  "completionTime": "2025-03-06T12:30:00Z"
}
```

---

### 19. ListActiveAssignedOrdersForAsset

**Method:** `ListActiveAssignedOrdersForAsset`  
**Route:** `POST /hero.orders.v2.OrderService/ListActiveAssignedOrdersForAsset`

#### Message Fields

**ListActiveAssignedOrdersForAssetRequest:**

| Field    | Type   | Description                                        |
|----------|--------|----------------------------------------------------|
| assetId  | string | Asset ID for which to retrieve active orders.      |
| pageSize | int32  | Maximum number of orders to return.                |
| pageToken| string | Pagination token for a specific page of results.   |

**ListActiveAssignedOrdersForAssetResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of active Order objects.                                |
| nextPageToken | string         | Token to retrieve the next page of results, if any.          |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "assetId": "asset-456",
  "pageSize": 100,
  "pageToken": ""
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "assetId": "asset-456",
      "status": "ORDER_STATUS_ACKNOWLEDGED",
      "type": "ORDER_TYPE_ASSIST_MEMBER",
      "title": "Assistance Request",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

### 20. ListNewOrdersForAsset

**Method:** `ListNewOrdersForAsset`  
**Route:** `POST /hero.orders.v2.OrderService/ListNewOrdersForAsset`

#### Message Fields

**ListNewOrdersForAssetRequest:**

| Field    | Type   | Description                                        |
|----------|--------|----------------------------------------------------|
| assetId  | string | Asset ID for which to retrieve new orders.         |
| pageSize | int32  | Maximum number of orders to return.                |
| pageToken| string | Pagination token for a specific page of results.   |

**ListNewOrdersForAssetResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of new Order objects.                                   |
| nextPageToken | string         | Token for retrieving the next page of results, if any.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "assetId": "asset-456",
  "pageSize": 100,
  "pageToken": ""
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-002",
      "assetId": "asset-456",
      "status": "ORDER_STATUS_CREATED",
      "type": "ORDER_TYPE_TRIAGE_MEMBER_REPORT",
      "title": "Triage Request",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

### 21. UpdateOrderPermissions

**Method:** `UpdateOrderPermissions`  
**Route:** `POST /hero.orders.v2.OrderService/UpdateOrderPermissions`

#### Message Fields

**UpdateOrderPermissionsRequest:**

| Field       | Type         | Description                                               |
|-------------|--------------|-----------------------------------------------------------|
| id          | string       | Unique identifier of the order.                         |
| permissions | OrderPermissions | New permissions object to replace existing permissions. |

**UpdateOrderPermissions Response:**

*(Returns the Order object with updated permissions.)*

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "permissions": {
    "canChangeStatus": ["ASSET_TYPE_RESPONDER"],
    "canAssignAsset": ["ASSET_TYPE_DISPATCHER"]
  }
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "permissions": {
    "canChangeStatus": ["ASSET_TYPE_RESPONDER"],
    "canAssignAsset": ["ASSET_TYPE_DISPATCHER"]
  },
  "statusUpdates": []
}
```

---

### 22. ListOrdersForSituation

**Method:** `ListOrdersForSituation`  
**Route:** `POST /hero.orders.v2.OrderService/ListOrdersForSituation`

#### Message Fields

**ListOrdersForSituationRequest:**

| Field       | Type   | Description                                        |
|-------------|--------|----------------------------------------------------|
| situationId | string | ID of the situation for which to list orders.       |
| pageSize    | int32  | Maximum number of orders to return.                |
| pageToken   | string | Pagination token for a specific page of results.   |
| status      | OrderStatus | *(Optional)* Filter by order status.           |

**ListOrdersForSituationResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of Order objects associated with the situation.         |
| nextPageToken | string         | Token to retrieve the next page of results, if any.          |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "situationId": "situation-123",
  "pageSize": 5,
  "pageToken": "",
  "status": "ORDER_STATUS_UNSPECIFIED"
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "situationId": "situation-123",
      "status": "ORDER_STATUS_CREATED",
      "title": "Triage Request",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

### 23. ListOrdersForAsset

**Method:** `ListOrdersForAsset`  
**Route:** `POST /hero.orders.v2.OrderService/ListOrdersForAsset`

#### Message Fields

**ListOrdersForAssetRequest:**

| Field    | Type       | Description                                        |
|----------|------------|----------------------------------------------------|
| assetId  | string     | Asset ID for which to list orders.                 |
| pageSize | int32      | Maximum number of orders to return.                |
| pageToken| string     | Pagination token for a specific page of results.   |
| status   | OrderStatus| *(Optional)* Filter by order status.               |

**ListOrdersForAssetResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of Order objects for the asset.                         |
| nextPageToken | string         | Token for retrieving the next page of results, if any.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "assetId": "asset-456",
  "pageSize": 5,
  "pageToken": "",
  "status": "ORDER_STATUS_UNSPECIFIED"
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "assetId": "asset-456",
      "status": "ORDER_STATUS_ACKNOWLEDGED",
      "title": "Assistance Request",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

### 24. AddAdditionalInfo

**Method:** `AddAdditionalInfo`  
**Route:** `POST /hero.orders.v2.OrderService/AddAdditionalInfo`

#### Message Fields

**AddAdditionalInfoRequest:**

| Field              | Type   | Description                                                        |
|--------------------|--------|--------------------------------------------------------------------|
| id                 | string | Unique identifier of the order to update.                          |
| additionalInfoJson | string | JSON string to be merged into the order's existing additionalInfoJson. |

**AddAdditionalInfoResponse:**

| Field              | Type   | Description                                                |
|--------------------|--------|------------------------------------------------------------|
| id                 | string | Identifier of the updated order.                           |
| additionalInfoJson | string | Updated additionalInfoJson after merging.                  |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "id": "generated-uuid",
  "additionalInfoJson": "{\"newField\": \"newValue\"}"
}
```

**Response (JSON):**
```json
{
  "id": "generated-uuid",
  "additionalInfoJson": "{\"detail\": \"Check sensor readings\", \"newField\": \"newValue\"}"
}
```

---

### 25. ListOrdersForReport

**Method:** `ListOrdersForReport`  
**Route:** `POST /hero.orders.v2.OrderService/ListOrdersForReport`

#### Message Fields

**ListOrdersForReportRequest:**

| Field     | Type       | Description                                        |
|-----------|------------|----------------------------------------------------|
| reportId  | string     | ID of the report for which to list orders.         |
| pageSize  | int32      | Maximum number of orders to return.                |
| pageToken | string     | Pagination token for a specific page of results.   |
| status    | OrderStatus| *(Optional)* Filter by order status.               |

**ListOrdersForReportResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of Order objects associated with the report.           |
| nextPageToken | string         | Token for retrieving the next page of results, if any.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "reportId": "report-123",
  "pageSize": 5,
  "pageToken": "",
  "status": "ORDER_STATUS_UNSPECIFIED"
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "reportId": "report-123",
      "status": "ORDER_STATUS_IN_PROGRESS",
      "type": "ORDER_TYPE_WRITE_REPORT",
      "title": "Write Initial Report",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

### 26. ListOrdersForReviewRound

**Method:** `ListOrdersForReviewRound`  
**Route:** `POST /hero.orders.v2.OrderService/ListOrdersForReviewRound`

#### Message Fields

**ListOrdersForReviewRoundRequest:**

| Field          | Type       | Description                                        |
|----------------|------------|----------------------------------------------------|
| reviewRoundId  | string     | ID of the review round for which to list orders.   |
| pageSize       | int32      | Maximum number of orders to return.                |
| pageToken      | string     | Pagination token for a specific page of results.   |
| status         | OrderStatus| *(Optional)* Filter by order status.               |

**ListOrdersForReviewRoundResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of Order objects associated with the review round.     |
| nextPageToken | string         | Token for retrieving the next page of results, if any.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "reviewRoundId": "review-round-123",
  "pageSize": 5,
  "pageToken": "",
  "status": "ORDER_STATUS_UNSPECIFIED"
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "reviewRoundId": "review-round-123",
      "status": "ORDER_STATUS_IN_PROGRESS",
      "type": "ORDER_TYPE_REVIEW_REPORT",
      "title": "Review Report Draft",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

### 27. ListOrdersForCase

**Method:** `ListOrdersForCase`  
**Route:** `POST /hero.orders.v2.OrderService/ListOrdersForCase`

#### Message Fields

**ListOrdersForCaseRequest:**

| Field     | Type       | Description                                        |
|-----------|------------|----------------------------------------------------|
| caseId    | string     | ID of the case for which to list orders.           |
| pageSize  | int32      | Maximum number of orders to return.                |
| pageToken | string     | Pagination token for a specific page of results.   |
| status    | OrderStatus| *(Optional)* Filter by order status.               |

**ListOrdersForCaseResponse:**

| Field         | Type           | Description                                                  |
|---------------|----------------|--------------------------------------------------------------|
| orders        | repeated Order | List of Order objects associated with the case.             |
| nextPageToken | string         | Token for retrieving the next page of results, if any.       |

#### Sample Request and Response

**Request (JSON):**
```json
{
  "caseId": "case-456",
  "pageSize": 5,
  "pageToken": "",
  "status": "ORDER_STATUS_UNSPECIFIED"
}
```

**Response (JSON):**
```json
{
  "orders": [
    {
      "id": "order-001",
      "caseId": "case-456",
      "assetId": "asset-789",
      "status": "ORDER_STATUS_IN_PROGRESS",
      "type": "ORDER_TYPE_MANAGE_CASE",
      "title": "Investigate case: Security Breach",
      "instructions": "Investigate case: Security Breach",
      "statusUpdates": []
    }
  ],
  "nextPageToken": ""
}
```

---

## Running Tests

The Order module includes a comprehensive test suite to verify API functionality and ensure reliability. Tests are organized by type and can be run using the provided `run_tests.sh` script in the `services/workflow/test/orders` directory.

### Test Types
- **Sanity Tests**: Verify basic API functionality and data handling
- **Side Effect Tests**: Validate automatic side effects triggered by order operations
- **Search Tests**: Comprehensive search functionality validation
- **Performance Tests**: Load testing and latency validation with comprehensive metrics
- **Other Tests**: Additional test types may be present (see the test directory)

### Setting Up Authentication
All tests require authentication via a bearer token:
1. Create a `token.txt` file in the `services/workflow/test/orders` directory.
2. Add a valid authentication token to this file (token only, no formatting).
3. The script automatically reads this token and configures the HTTP client.

### Running the Tests
Use the `run_tests.sh` script to run tests conveniently:

```bash
# Navigate to the test directory
cd services/workflow/test/orders

# Make the script executable (if not already)
chmod +x run_tests.sh

# Run all tests (default behavior)
./run_tests.sh
# or explicitly specify all
./run_tests.sh all

# Run only sanity tests
./run_tests.sh sanity

# Run only side effect tests
./run_tests.sh side-effect

# Run only search tests
./run_tests.sh search

# Run only performance tests
./run_tests.sh performance

# Run test data management
./run_tests.sh populate        # Create test orders
./run_tests.sh cleanup         # Delete test orders

# To force tests to run without using Go's cache, add the "nocache" argument
./run_tests.sh all nocache
./run_tests.sh sanity nocache
./run_tests.sh performance nocache
```

### Test Environment Requirements
1. **Authorization**: Tests require a valid authentication token in `token.txt`.
2. **Services**: Ensure the workflow service (localhost:9086) and any dependencies are running.
3. **Database State**: Tests will create and delete data in your development database. Each test includes cleanup logic to remove test data after completion.
4. **Performance Tests**: Require adequate system resources and use 600-second timeout for comprehensive testing.

### Performance Tests (`TestOrders_BasicPerformance`)

The Orders module includes comprehensive performance testing to validate system performance under load and ensure latency requirements are met.

#### Test Data Volume
- **100 Orders**: Creates realistic order workload for performance testing
- **10 Assets**: Supporting assets for order assignments
- **10 Situations**: Supporting situations for order context
- **Multiple Order Types**: Tests across all major order types (TRIAGE, ASSIST, SUBMIT_FINAL_REPORT, etc.)
- **Various Statuses**: Tests orders in different lifecycle states

#### Performance Test Categories

**Core CRUD Operations:**
- GetOrder: Individual order retrieval performance
- UpdateOrder: Order modification latency
- ListOrders: Bulk order listing performance

**Status Transition Operations:**
- AcknowledgeOrder: Order acknowledgment performance
- SnoozeOrder: Order snoozing with reason and timing
- CompleteOrder: Order completion performance

**Specialized Listing Operations:**
- ListActiveAssignedOrdersForAsset: Asset-specific active order queries
- ListNewOrdersForAsset: New order notifications for assets
- ListOrdersForAsset: Complete order history for assets
- ListOrdersForSituation: Situation-specific order queries

**Order Management Operations:**
- AddOrderUpdate: Update entry addition performance
- AddAllowedAssetType: Asset type permission updates
- UpdateOrderPermissions: Permission management performance
- AddAdditionalInfo: JSON metadata updates

**Filtered Listing Performance:**
- Status-based filtering (ORDER_STATUS_CREATED, etc.)
- Type-based filtering (ORDER_TYPE_TRIAGE_MEMBER_REPORT, etc.)
- Combined filter performance testing

#### Performance Thresholds
- **Individual Operations**: Sub-200ms latency target
- **Bulk Operations**: Sub-2000ms latency target
- **Success Rate**: 100% success rate expected
- **Comprehensive Metrics**: Min/Max/Average timing analysis

#### Runtime Characteristics
- **Total Duration**: 3-10 minutes including test data creation and cleanup
- **Resource Requirements**: Moderate CPU and memory usage
- **Database Impact**: Significant database activity during execution
- **Automatic Cleanup**: Complete cleanup of all test data

#### Running Performance Tests

```bash
# Run performance tests specifically
./run_tests.sh performance

# Run with cache bypass
./run_tests.sh performance nocache

# Manual execution with timeout
go test -v -run "^TestOrders_BasicPerformance$" ./test/orders -timeout=600s
```

### Handling Test Failures
If tests fail, check for:
- Service availability
- Authentication issues (token expired/invalid)
- Database state inconsistencies
- Code changes affecting test expectations
- **Performance Issues**: System resources, database performance, concurrent operations affecting latency

### Performance Test Troubleshooting
- **Latency Threshold Failures**: Ensure adequate system resources and no concurrent heavy operations
- **Database Performance**: Verify proper indexes and database optimization
- **Resource Constraints**: Performance tests require sufficient CPU and memory
- **Timeout Errors**: Tests use 600-second timeout; adjust for slower systems if needed
- **Test Data Conflicts**: Performance tests manage their own data lifecycle automatically

> **Note:** Tests call actual endpoints and modify your local database. While they include cleanup logic, unexpected failures might leave test data. Periodically clean your development database if you run tests frequently.

---

## Database Schema Overview

The Order Module leverages a PostgreSQL database to store and manage order data. Although the database uses snake_case naming, the service exposes all fields using camelCase.

**Key Tables:**

- **orders:**  
  Stores primary order details, including:
  - `id`: Unique identifier.
  - `situationId`: Foreign key linking to the situations table.
  - `caseId`: Foreign key linking to the cases table (for case-related orders).
  - `assetId`: Foreign key linking to the assets table.
  - `type`: Order type (maps to the OrderType enum).
  - `status`: Current order status (maps to the OrderStatus enum).
  - Fields like `instructions`, `priority`, `additionalInfoJson`, `typeSpecificStatus`, and `notes`.
  - Timestamps: `createTime`, `updateTime`, `completionTime`, `assignedTime`, `acknowledgedTime`, and `estimatedCompletionTime`.
  - `cancellationOrRejectionReason`, `retryCount`, `createdBy`, `title`.
  - `snoozeReason`, `snoozeUntil`, `snoozeCount`.
  - `resourceType`: Fixed value `"ORDER"`.

- **order_updates:**  
  Captures update entries for orders. Each record includes:
  - `orderId` (foreign key to orders)
  - `message`, `timestamp`, and `updateSource`.

- **order_allowed_asset_types:**  
  Defines which asset types are allowed to perform the order.

- **order_blacklisted_asset_ids:**  
  Lists asset IDs that are explicitly excluded from being assigned the order.

- **order_permissions:**  
  Stores permissions specifying which asset types can change order status or assign assets.

---

## Side Effects and Execution

The system automatically detects and executes side effects based on order status changes. These side effects ensure that related situations, cases, and assets are updated appropriately.

### Detection

The **orderSideEffectChecker** inspects an updated order and returns one or more side effects based on its type and status. For example:
- Changing a situation's status when a triage order is acknowledged.
- Updating a case's status when a MANAGE_CASE order is acknowledged.
- Triggering report orders when an assist order is completed.
- Marking assets as busy or available depending on active orders.
- Cloning orders for blacklisted assets when an order is rejected.

### Execution

The **orderSideEffectExecutor** runs the detected side effects within the same transaction as the order update. This includes:
- Updating the associated situation's status.
- Updating the associated case's status (for MANAGE_CASE orders).
- Creating report orders if required.
- Adjusting asset statuses based on order activity.
- Cloning orders for blacklisted assets when necessary.

### Case-Related Side Effects

#### TryMarkCaseInvestigating
When a MANAGE_CASE order is acknowledged, this side effect:
- Checks if the associated case is in an initial state (UNSPECIFIED, NEW, or OPEN)
- Updates the case status to INVESTIGATING if appropriate
- Skips the update if the case is already in INVESTIGATING or a terminal state (ON_HOLD, RESOLVED, CLOSED, ARCHIVED)
- Maintains logical case workflow progression

### Bidirectional Integration with Cases
The Order and Case services work together through coordinated side effects:
- Case service creates MANAGE_CASE orders when investigators are assigned to cases
- Order service updates case status when investigators acknowledge their MANAGE_CASE orders
- Both services use database transactions to ensure consistency
- Changes are atomic - all related updates succeed or fail together

---

## Running Sanity Tests

1. Start all services locally using `make run`.
2. Navigate to the `hero-core/services/workflow` directory.
3. Execute the integration tests with:
   ```bash
   go test -tags=sanity -v ./...
   ```
> **Note:** These tests call actual endpoints and may modify your local database. Ensure that any test data is cleaned up appropriately afterward.

---

## Running Side Effects Tests

1. Start all services locally using `make run`.
2. Navigate to the `hero-core/services/workflow` directory.
3. Execute the side effects tests with:
   ```bash
   go test -tags=sideeffect -v ./...
   ```
> **Note:** These tests may modify your local database. Ensure that test data is cleaned up appropriately afterward.

---