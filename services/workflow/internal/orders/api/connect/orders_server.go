package connect

import (
	"context"

	connecthelper "common/connect"
	orders "proto/hero/orders/v2"
	"workflow/internal/orders/usecase"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/emptypb"
)

// OrderServer implements the OrderService defined in the proto.
type OrderServer struct {
	orderUseCase *usecase.OrderUseCase
}

// NewOrderServer creates a new OrderServer.
func NewOrderServer(orderUseCase *usecase.OrderUseCase) *OrderServer {
	return &OrderServer{
		orderUseCase: orderUseCase,
	}
}

// CreateOrder creates a new order.
func (server *OrderServer) CreateOrder(
	requestContext context.Context,
	req *connect.Request[orders.CreateOrderRequest],
) (*connect.Response[orders.CreateOrderResponse], error) {
	createdOrder, err := server.orderUseCase.CreateOrder(requestContext, req.Msg.Order)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "CreateOrder")
	}
	response := &orders.CreateOrderResponse{
		Order: createdOrder,
	}
	return connect.NewResponse(response), nil
}

// GetOrder retrieves an order by its ID.
func (server *OrderServer) GetOrder(
	requestContext context.Context,
	req *connect.Request[orders.GetOrderRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.GetOrder(requestContext, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "GetOrder")
	}
	return connect.NewResponse(order), nil
}

// UpdateOrder updates an existing order.
func (server *OrderServer) UpdateOrder(
	requestContext context.Context,
	req *connect.Request[orders.UpdateOrderRequest],
) (*connect.Response[orders.Order], error) {
	updatedOrder, err := server.orderUseCase.UpdateOrder(requestContext, req.Msg.Order)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "UpdateOrder")
	}
	return connect.NewResponse(updatedOrder), nil
}

// UpdateOrderPermissions updates the permissions for an order.
func (server *OrderServer) UpdateOrderPermissions(
	requestContext context.Context,
	req *connect.Request[orders.UpdateOrderPermissionsRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.UpdateOrderPermissions(requestContext, req.Msg.Id, req.Msg.Permissions)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "UpdateOrderPermissions")
	}
	return connect.NewResponse(order), nil
}

// ListOrders returns a paginated list of orders, including optional filtering and ordering.
func (server *OrderServer) ListOrders(
	requestContext context.Context,
	req *connect.Request[orders.ListOrdersRequest],
) (*connect.Response[orders.ListOrdersResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListOrders(
		requestContext,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
		req.Msg.Status,
		req.Msg.Type,
		req.Msg.OrderBy,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListOrders")
	}
	response := &orders.ListOrdersResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// DeleteOrder deletes an order.
func (server *OrderServer) DeleteOrder(
	requestContext context.Context,
	req *connect.Request[orders.DeleteOrderRequest],
) (*connect.Response[emptypb.Empty], error) {
	if err := server.orderUseCase.DeleteOrder(requestContext, req.Msg.Id); err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "DeleteOrder")
	}
	return connect.NewResponse(&emptypb.Empty{}), nil
}

// AddOrderUpdate adds an update entry to an order.
func (server *OrderServer) AddOrderUpdate(
	requestContext context.Context,
	req *connect.Request[orders.AddOrderUpdateRequest],
) (*connect.Response[orders.Order], error) {
	if err := server.orderUseCase.AddOrderUpdate(requestContext, req.Msg.Id, req.Msg.Update); err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddOrderUpdate")
	}
	order, err := server.orderUseCase.GetOrder(requestContext, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddOrderUpdate")
	}
	return connect.NewResponse(order), nil
}

// RemoveOrderUpdate removes an update entry from an order.
func (server *OrderServer) RemoveOrderUpdate(
	requestContext context.Context,
	req *connect.Request[orders.RemoveOrderUpdateRequest],
) (*connect.Response[orders.Order], error) {
	if err := server.orderUseCase.RemoveOrderUpdate(requestContext, req.Msg.Id, req.Msg.Update); err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveOrderUpdate")
	}
	order, err := server.orderUseCase.GetOrder(requestContext, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveOrderUpdate")
	}
	return connect.NewResponse(order), nil
}

// AddOrderStatusUpdate adds a status update entry to an order.
func (server *OrderServer) AddOrderStatusUpdate(
	requestContext context.Context,
	req *connect.Request[orders.AddOrderStatusUpdateRequest],
) (*connect.Response[orders.Order], error) {
	if err := server.orderUseCase.AddOrderStatusUpdate(requestContext, req.Msg.Id, req.Msg.StatusUpdate); err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddOrderStatusUpdate")
	}
	order, err := server.orderUseCase.GetOrder(requestContext, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddOrderStatusUpdate")
	}
	return connect.NewResponse(order), nil
}

// AddAllowedAssetType adds an allowed asset type to an order.
func (server *OrderServer) AddAllowedAssetType(
	requestContext context.Context,
	req *connect.Request[orders.AddAllowedAssetTypeRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.AddAllowedAssetType(requestContext, req.Msg.Id, req.Msg.AllowedAssetType)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddAllowedAssetType")
	}
	return connect.NewResponse(order), nil
}

// RemoveAllowedAssetType removes an allowed asset type from an order.
func (server *OrderServer) RemoveAllowedAssetType(
	requestContext context.Context,
	req *connect.Request[orders.RemoveAllowedAssetTypeRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.RemoveAllowedAssetType(requestContext, req.Msg.Id, req.Msg.AllowedAssetType)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveAllowedAssetType")
	}
	return connect.NewResponse(order), nil
}

// AddBlacklistedAssetId adds an asset ID to the blacklist for an order.
func (server *OrderServer) AddBlacklistedAssetId(
	requestContext context.Context,
	req *connect.Request[orders.AddBlacklistedAssetIdRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.AddBlacklistedAssetId(requestContext, req.Msg.Id, req.Msg.AssetId)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddBlacklistedAssetId")
	}
	return connect.NewResponse(order), nil
}

// RemoveBlacklistedAssetId removes an asset ID from the blacklist for an order.
func (server *OrderServer) RemoveBlacklistedAssetId(
	requestContext context.Context,
	req *connect.Request[orders.RemoveBlacklistedAssetIdRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.RemoveBlacklistedAssetId(requestContext, req.Msg.Id, req.Msg.AssetId)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RemoveBlacklistedAssetId")
	}
	return connect.NewResponse(order), nil
}

// AcknowledgeOrder sets the order status to ACKNOWLEDGED.
func (server *OrderServer) AcknowledgeOrder(
	requestContext context.Context,
	req *connect.Request[orders.AcknowledgeOrderRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.AcknowledgeOrder(requestContext, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AcknowledgeOrder")
	}
	return connect.NewResponse(order), nil
}

// RejectOrder sets the order status to REJECTED.
func (server *OrderServer) RejectOrder(
	requestContext context.Context,
	req *connect.Request[orders.RejectOrderRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.RejectOrder(requestContext, req.Msg.Id, req.Msg.Reason)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "RejectOrder")
	}
	return connect.NewResponse(order), nil
}

// SnoozeOrder sets the order status to SNOOZED.
func (server *OrderServer) SnoozeOrder(
	requestContext context.Context,
	req *connect.Request[orders.SnoozeOrderRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.SnoozeOrder(requestContext, req.Msg.Id, req.Msg.SnoozeReason, req.Msg.SnoozeUntil)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "SnoozeOrder")
	}
	return connect.NewResponse(order), nil
}

// CancelOrder sets the order status to CANCELLED.
func (server *OrderServer) CancelOrder(
	requestContext context.Context,
	req *connect.Request[orders.CancelOrderRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.CancelOrder(requestContext, req.Msg.Id, req.Msg.Reason)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "CancelOrder")
	}
	return connect.NewResponse(order), nil
}

// CompleteOrder sets the order status to COMPLETED.
func (server *OrderServer) CompleteOrder(
	requestContext context.Context,
	req *connect.Request[orders.CompleteOrderRequest],
) (*connect.Response[orders.Order], error) {
	order, err := server.orderUseCase.CompleteOrder(requestContext, req.Msg.Id)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "CompleteOrder")
	}
	return connect.NewResponse(order), nil
}

// ListActiveAssignedOrdersForAsset returns active assigned orders for a given asset.
func (server *OrderServer) ListActiveAssignedOrdersForAsset(
	requestContext context.Context,
	req *connect.Request[orders.ListActiveAssignedOrdersForAssetRequest],
) (*connect.Response[orders.ListActiveAssignedOrdersForAssetResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListActiveAssignedOrdersForAsset(
		requestContext,
		req.Msg.AssetId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListActiveAssignedOrdersForAsset")
	}
	response := &orders.ListActiveAssignedOrdersForAssetResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// ListNewOrdersForAsset returns orders in CREATED status for a given asset.
func (server *OrderServer) ListNewOrdersForAsset(
	requestContext context.Context,
	req *connect.Request[orders.ListNewOrdersForAssetRequest],
) (*connect.Response[orders.ListNewOrdersForAssetResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListNewOrdersForAsset(
		requestContext,
		req.Msg.AssetId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListNewOrdersForAsset")
	}
	response := &orders.ListNewOrdersForAssetResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// ListOrdersForSituation returns a paginated list of orders for a given situation.
func (server *OrderServer) ListOrdersForSituation(
	requestContext context.Context,
	req *connect.Request[orders.ListOrdersForSituationRequest],
) (*connect.Response[orders.ListOrdersForSituationResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListOrdersForSituation(
		requestContext,
		req.Msg.SituationId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
		req.Msg.Status,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListOrdersForSituation")
	}
	response := &orders.ListOrdersForSituationResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// ListOrdersForAsset lists orders for an asset with an optional filter by order status.
func (server *OrderServer) ListOrdersForAsset(
	requestContext context.Context,
	req *connect.Request[orders.ListOrdersForAssetRequest],
) (*connect.Response[orders.ListOrdersForAssetResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListOrdersForAsset(
		requestContext,
		req.Msg.AssetId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
		req.Msg.Status,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListOrdersForAsset")
	}
	response := &orders.ListOrdersForAssetResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// AddAdditionalInfo adds additional info to an order by merging provided JSON into the existing additional_info_json.
func (server *OrderServer) AddAdditionalInfo(
	requestContext context.Context,
	req *connect.Request[orders.AddAdditionalInfoRequest],
) (*connect.Response[orders.AddAdditionalInfoResponse], error) {
	id, updatedJSON, err := server.orderUseCase.AddAdditionalInfo(requestContext, req.Msg.Id, req.Msg.AdditionalInfoJson)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "AddAdditionalInfo")
	}
	response := &orders.AddAdditionalInfoResponse{
		Id:                 id,
		AdditionalInfoJson: updatedJSON,
	}
	return connect.NewResponse(response), nil
}

// ListOrdersForReport returns a paginated list of orders for a given report with optional status filter.
func (server *OrderServer) ListOrdersForReport(
	requestContext context.Context,
	req *connect.Request[orders.ListOrdersForReportRequest],
) (*connect.Response[orders.ListOrdersForReportResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListOrdersForReport(
		requestContext,
		req.Msg.ReportId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
		req.Msg.Status,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListOrdersForReport")
	}
	response := &orders.ListOrdersForReportResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// ListOrdersForReviewRound returns a paginated list of orders for a given review round with optional status filter.
func (server *OrderServer) ListOrdersForReviewRound(
	requestContext context.Context,
	req *connect.Request[orders.ListOrdersForReviewRoundRequest],
) (*connect.Response[orders.ListOrdersForReviewRoundResponse], error) {
	ordersList, nextPageToken, err := server.orderUseCase.ListOrdersForReviewRound(
		requestContext,
		req.Msg.ReviewRoundId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
		req.Msg.Status,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListOrdersForReviewRound")
	}
	response := &orders.ListOrdersForReviewRoundResponse{
		Orders:        ordersList,
		NextPageToken: nextPageToken,
	}
	return connect.NewResponse(response), nil
}

// ListOrdersForCase returns a paginated list of orders for a given case with optional status filter.
func (server *OrderServer) ListOrdersForCase(
	requestContext context.Context,
	req *connect.Request[orders.ListOrdersForCaseRequest],
) (*connect.Response[orders.ListOrdersForCaseResponse], error) {
	response, err := server.orderUseCase.ListOrdersForCase(
		requestContext,
		nil, // transaction
		req.Msg.CaseId,
		int(req.Msg.PageSize),
		req.Msg.PageToken,
		req.Msg.Status,
	)
	if err != nil {
		return nil, connecthelper.AsConnectError(requestContext, err, "ListOrdersForCase")
	}
	return connect.NewResponse(response), nil
}
