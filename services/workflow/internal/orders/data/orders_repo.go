package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	assets "proto/hero/assets/v2"
	orders "proto/hero/orders/v2"

	_ "github.com/lib/pq"
)

// ErrOrderNotFound is returned when an order cannot be found.
var ErrOrderNotFound = fmt.Errorf("order not found")

const FixedResourceTypeOrder = "ORDER"

// ListOrdersResult holds the result of listing orders
type ListOrdersResult struct {
	Orders    []*orders.Order
	PageToken string
}

// OrderRepository defines operations for managing orders.
type OrderRepository interface {
	// Basic CRUD operations.
	CreateOrder(ctx context.Context, transaction *sql.Tx, orderRecord *orders.Order) (*orders.Order, error)
	GetOrder(ctx context.Context, transaction *sql.Tx, orderID string) (*orders.Order, error)
	UpdateOrder(ctx context.Context, transaction *sql.Tx, orderRecord *orders.Order) (*orders.Order, error)
	ListOrders(ctx context.Context, transaction *sql.Tx, pageSize int, pageToken string, status orders.OrderStatus, orderType orders.OrderType, orderBy string) (*ListOrdersResult, error)
	DeleteOrder(ctx context.Context, transaction *sql.Tx, orderID string) error

	// Order update entry operations.
	AddOrderUpdate(ctx context.Context, transaction *sql.Tx, orderID string, updateEntry *orders.OrderUpdateEntry) error
	RemoveOrderUpdate(ctx context.Context, transaction *sql.Tx, orderID string, updateEntry *orders.OrderUpdateEntry) error

	// Order status update entry operations.
	AddOrderStatusUpdate(ctx context.Context, transaction *sql.Tx, orderID string, statusUpdate *orders.OrderStatusUpdateEntry) error

	// Status transition operations.
	AcknowledgeOrder(ctx context.Context, transaction *sql.Tx, orderID string) (*orders.Order, error)
	RejectOrder(ctx context.Context, transaction *sql.Tx, orderID, rejectionReason string) (*orders.Order, error)
	SnoozeOrder(ctx context.Context, transaction *sql.Tx, orderID, snoozeReason string, snoozeUntil time.Time) (*orders.Order, error)
	CancelOrder(ctx context.Context, transaction *sql.Tx, orderID, cancellationReason string) (*orders.Order, error)
	CompleteOrder(ctx context.Context, transaction *sql.Tx, orderID string) (*orders.Order, error)

	// Repeated field operations.
	AddAllowedAssetType(ctx context.Context, transaction *sql.Tx, orderID string, allowedAssetType assets.AssetType) (*orders.Order, error)
	RemoveAllowedAssetType(ctx context.Context, transaction *sql.Tx, orderID string, allowedAssetType assets.AssetType) (*orders.Order, error)
	AddBlacklistedAssetId(ctx context.Context, transaction *sql.Tx, orderID, assetID string) (*orders.Order, error)
	RemoveBlacklistedAssetId(ctx context.Context, transaction *sql.Tx, orderID, assetID string) (*orders.Order, error)

	// Specialized listing operations.
	ListActiveAssignedOrdersForAsset(ctx context.Context, transaction *sql.Tx, assetID string, pageSize int, pageToken string) (*ListOrdersResult, error)
	ListNewOrdersForAsset(ctx context.Context, transaction *sql.Tx, assetID string, pageSize int, pageToken string) (*ListOrdersResult, error)
	ListOrdersForSituation(ctx context.Context, transaction *sql.Tx, situationID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error)
	ListOrdersForAsset(ctx context.Context, transaction *sql.Tx, assetID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error)
	ListOrdersForReport(ctx context.Context, transaction *sql.Tx, reportID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error)
	ListOrdersForReviewRound(ctx context.Context, transaction *sql.Tx, reviewRoundID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error)
	ListOrdersForCase(ctx context.Context, transaction *sql.Tx, caseID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error)

	// Permission update operation.
	UpdateOrderPermissions(ctx context.Context, transaction *sql.Tx, orderID string, permissions *orders.OrderPermissions) (*orders.Order, error)
	UpdateAdditionalInfoJSON(ctx context.Context, transaction *sql.Tx, orderID string, additionalInfoJSON string) (string, error)
}

// NewOrderRepository returns an OrderRepository based on the provided configuration.
// It returns the repository implementation, a *sql.DB (if applicable), and an error.
func NewOrderRepository(postgresDB *sql.DB) (OrderRepository, *sql.DB, error) {
	if postgresDB == nil {
		return nil, nil, errors.New("database is nil: cannot initialize OrderRepository")
	}
	return NewPostgresOrderRepository(postgresDB), postgresDB, nil
}
