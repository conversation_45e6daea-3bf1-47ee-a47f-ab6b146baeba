package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	cmncontext "common/context"
	database "common/database"
	"common/herosentry"
	commonUtils "common/utils"

	assets "proto/hero/assets/v2"
	orders "proto/hero/orders/v2"
	situations "proto/hero/situations/v2"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// nullIfEmpty returns nil if the given string is empty; otherwise, it returns the string.
func nullIfEmpty(s string) interface{} {
	if s == "" {
		return nil
	}
	return s
}

// PostgresOrderRepository implements OrderRepository using PostgreSQL.
type PostgresOrderRepository struct {
	database *sql.DB
}

// NewPostgresOrderRepository creates a new repository backed by PostgreSQL.
func NewPostgresOrderRepository(database *sql.DB) *PostgresOrderRepository {
	return &PostgresOrderRepository{database: database}
}

// deferConstraints sets all deferrable constraints to be checked at transaction commit.
func deferConstraints(ctx context.Context, tx *sql.Tx) error {
	if tx != nil {
		_, err := tx.ExecContext(ctx, "SET CONSTRAINTS ALL DEFERRED")
		return err
	}
	return nil
}

// insertOrderStatusUpdate automatically inserts a status update entry.
func (repository *PostgresOrderRepository) insertOrderStatusUpdate(ctx context.Context, tx *sql.Tx, orderID string,
	newStatus orders.OrderStatus, previousStatus orders.OrderStatus, newTypeSpecificStatus string, previousTypeSpecificStatus string,
	// These fields are not implemented yet
	//nolint:unparam
	note string, updaterID string, updateSource situations.UpdateSource) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.insertOrderStatusUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("order.new_status", newStatus.String())
	span.SetTag("order.previous_status", previousStatus.String())
	span.SetTag("order.new_type_specific_status", newTypeSpecificStatus)
	span.SetTag("order.previous_type_specific_status", previousTypeSpecificStatus)

	orgID := cmncontext.GetOrgId(spanContext)
	span.SetTag("org.id", fmt.Sprintf("%d", orgID))

	currentTime := time.Now()

	// These fields are not implemented yet
	// TODO: Implement these fields
	note = ""
	updaterID = ""
	updateSource = situations.UpdateSource_UPDATE_SOURCE_UNKNOWN

	// Use sql.NullInt32 for the status fields.
	var newStatusParam sql.NullInt32
	var previousStatusParam sql.NullInt32
	if newStatus == previousStatus {
		newStatusParam = sql.NullInt32{Valid: false}
		previousStatusParam = sql.NullInt32{Valid: false}
	} else {
		newStatusParam = sql.NullInt32{Int32: int32(newStatus), Valid: true}
		previousStatusParam = sql.NullInt32{Int32: int32(previousStatus), Valid: true}
	}

	// Use sql.NullString for type specific status.
	var newTypeSpecificStatusParam sql.NullString
	var previousTypeSpecificStatusParam sql.NullString
	if newTypeSpecificStatus == previousTypeSpecificStatus {
		newTypeSpecificStatusParam = sql.NullString{Valid: false}
		previousTypeSpecificStatusParam = sql.NullString{Valid: false}
	} else {
		newTypeSpecificStatusParam = sql.NullString{String: newTypeSpecificStatus, Valid: true}
		previousTypeSpecificStatusParam = sql.NullString{String: previousTypeSpecificStatus, Valid: true}
	}

	query := `
		INSERT INTO order_status_updates
			(order_id, org_id, entry_timestamp, new_status, previous_status, new_type_specific_status, previous_type_specific_status, note, updater_id, update_source)
		VALUES
			($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	preparedStatement, err := tx.PrepareContext(spanContext, query)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to prepare order status update statement")
		return err
	}
	defer preparedStatement.Close()

	_, err = preparedStatement.ExecContext(spanContext,
		orderID,
		orgID,
		currentTime,
		newStatusParam,
		previousStatusParam,
		newTypeSpecificStatusParam,
		previousTypeSpecificStatusParam,
		note,
		updaterID,
		int32(updateSource),
	)
	if err != nil {
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to insert order status update")
	}
	return err
}

// CreateOrder inserts a new order record along with its repeated fields.
func (repository *PostgresOrderRepository) CreateOrder(ctx context.Context, transaction *sql.Tx, orderRecord *orders.Order) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.CreateOrder")
	defer finishSpan()

	span.SetTag("order.type", orderRecord.Type.String())
	span.SetTag("order.priority", fmt.Sprintf("%d", orderRecord.Priority))
	span.SetTag("order.status", orderRecord.Status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		// If we're using a transaction, defer foreign key constraints until commit.
		if err := deferConstraints(spanContext, sessionTx); err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to defer constraints")
			return nil, err
		}

		if orderRecord.Id == "" {
			orderRecord.Id = uuid.New().String()
		}

		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		if orderRecord.AdditionalInfoJson == "" {
			orderRecord.AdditionalInfoJson = "{}"
		}

		currentTime := time.Now()
		orderRecord.CreateTime = commonUtils.TimeToISO8601String(currentTime)
		orderRecord.UpdateTime = commonUtils.TimeToISO8601String(currentTime)

		// Set resource_type to fixed value "ORDER" per proto contract.
		orderRecord.ResourceType = FixedResourceTypeOrder

		query := `
			INSERT INTO orders (
				id, org_id, situation_id, asset_id, type, status, instructions, priority,
				additional_info_json, type_specific_status, notes, create_time, update_time,
				completion_time, assigned_time, acknowledged_time, estimated_completion_time,
				cancellation_or_rejection_reason, retry_count, created_by, title,
				snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			)
				VALUES (
					$1, $2, $3, $4, $5, $6, $7,
					$8, $9, $10, $11, $12,
					$13, $14, $15, $16,
					$17, $18, $19, $20,
					$21, $22, $23, $24,
					$25, $26, $27, $28
			)
		`
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			return nil, preparationError
		}
		defer preparedStatement.Close()

		// Convert ISO8601 string timestamps to time.Time for database storage
		var createTime, updateTime time.Time
		var completionTime, assignedTime, acknowledgedTime, estimatedCompletionTime, snoozeUntil sql.NullTime
		var err error

		// Required timestamps (always have values)
		createTime, err = commonUtils.ISO8601StringToTime(orderRecord.CreateTime)
		if err != nil {
			err := fmt.Errorf("invalid create_time format: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, err
		}

		updateTime, err = commonUtils.ISO8601StringToTime(orderRecord.UpdateTime)
		if err != nil {
			err := fmt.Errorf("invalid update_time format: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, err
		}

		// Optional timestamps (may be empty)
		if orderRecord.CompletionTime != "" {
			t, err := commonUtils.ISO8601StringToTime(orderRecord.CompletionTime)
			if err != nil {
				err := fmt.Errorf("invalid completion_time format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			completionTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.AssignedTime != "" {
			t, err := commonUtils.ISO8601StringToTime(orderRecord.AssignedTime)
			if err != nil {
				err := fmt.Errorf("invalid assigned_time format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			assignedTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.AcknowledgedTime != "" {
			t, err := commonUtils.ISO8601StringToTime(orderRecord.AcknowledgedTime)
			if err != nil {
				err := fmt.Errorf("invalid acknowledged_time format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			acknowledgedTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.EstimatedCompletionTime != "" {
			t, err := commonUtils.ISO8601StringToTime(orderRecord.EstimatedCompletionTime)
			if err != nil {
				err := fmt.Errorf("invalid estimated_completion_time format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			estimatedCompletionTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.SnoozeUntil != "" {
			t, err := commonUtils.ISO8601StringToTime(orderRecord.SnoozeUntil)
			if err != nil {
				err := fmt.Errorf("invalid snooze_until format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			snoozeUntil = sql.NullTime{Time: t, Valid: true}
		}

		_, executionError := preparedStatement.ExecContext(spanContext,
			orderRecord.Id,
			orgID,
			nullIfEmpty(orderRecord.SituationId),
			nullIfEmpty(orderRecord.AssetId),
			int32(orderRecord.Type),
			int32(orderRecord.Status),
			orderRecord.Instructions,
			orderRecord.Priority,
			orderRecord.AdditionalInfoJson,
			orderRecord.TypeSpecificStatus,
			orderRecord.Notes,
			createTime,
			updateTime,
			completionTime,
			assignedTime,
			acknowledgedTime,
			estimatedCompletionTime,
			orderRecord.CancellationOrRejectionReason,
			orderRecord.RetryCount,
			int32(orderRecord.CreatedBy),
			orderRecord.Title,
			orderRecord.SnoozeReason,
			snoozeUntil,
			orderRecord.SnoozeCount,
			orderRecord.ResourceType,
			nullIfEmpty(orderRecord.ReportId),
			nullIfEmpty(orderRecord.ReviewRoundId),
			nullIfEmpty(orderRecord.CaseId),
		)
		if executionError != nil {
			return nil, executionError
		}
		preparedStatement.Close()

		// Currently this updaet is not controlled externally
		// It is done internally by the system
		if err := repository.insertOrderStatusUpdate(ctx, sessionTx, orderRecord.Id,
			orderRecord.Status, orders.OrderStatus_ORDER_STATUS_UNSPECIFIED, // new vs. default previous
			orderRecord.TypeSpecificStatus, "", // new vs. empty previous type-specific status
			"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
		); err != nil {
			return nil, err
		}

		// Insert allowed asset types.
		for _, assetType := range orderRecord.AllowedAssetTypes {
			insertAllowedAssetTypeStatement, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_allowed_asset_types (order_id, asset_type, org_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING")
			if err != nil {
				return nil, err
			}
			_, err = insertAllowedAssetTypeStatement.ExecContext(spanContext, orderRecord.Id, int32(assetType), orgID)
			insertAllowedAssetTypeStatement.Close()
			if err != nil {
				return nil, err
			}
		}

		// Insert blacklisted asset IDs.
		for _, assetID := range orderRecord.BlacklistedAssetIds {
			insertBlacklistedAssetStatement, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_blacklisted_asset_ids (order_id, asset_id, org_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING")
			if err != nil {
				return nil, err
			}
			_, err = insertBlacklistedAssetStatement.ExecContext(spanContext, orderRecord.Id, assetID, orgID)
			insertBlacklistedAssetStatement.Close()
			if err != nil {
				return nil, err
			}
		}

		// Insert initial order updates, if any.
		for _, updateEntry := range orderRecord.Updates {
			insertOrderUpdatesStatement, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_updates (order_id, message, timestamp, update_source, org_id) VALUES ($1, $2, $3, $4, $5)")
			if err != nil {
				return nil, err
			}
			updateTime, _ := commonUtils.ISO8601StringToTime(updateEntry.Timestamp)
			_, err = insertOrderUpdatesStatement.ExecContext(spanContext, orderRecord.Id, updateEntry.Message, updateTime, int32(updateEntry.UpdateSource), orgID)
			insertOrderUpdatesStatement.Close()
			if err != nil {
				return nil, err
			}
		}

		// Insert order permissions if provided.
		if orderRecord.Permissions != nil {
			// Insert CHANGE_STATUS permissions.
			for _, assetType := range orderRecord.Permissions.CanChangeStatus {
				stmt, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_permissions (order_id, scope, asset_type, org_id) VALUES ($1, 'CHANGE_STATUS', $2, $3) ON CONFLICT DO NOTHING")
				if err != nil {
					return nil, err
				}
				_, err = stmt.ExecContext(spanContext, orderRecord.Id, int32(assetType), orgID)
				stmt.Close()
				if err != nil {
					return nil, err
				}
			}
			// Insert ASSIGN_ASSET permissions.
			for _, assetType := range orderRecord.Permissions.CanAssignAsset {
				stmt, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_permissions (order_id, scope, asset_type, org_id) VALUES ($1, 'ASSIGN_ASSET', $2, $3) ON CONFLICT DO NOTHING")
				if err != nil {
					return nil, err
				}
				_, err = stmt.ExecContext(spanContext, orderRecord.Id, int32(assetType), orgID)
				stmt.Close()
				if err != nil {
					return nil, err
				}
			}
		}

		return repository.GetOrder(spanContext, sessionTx, orderRecord.Id)
	})
}

// GetOrder retrieves an order by its ID and loads repeated fields.
func (repository *PostgresOrderRepository) GetOrder(ctx context.Context, transaction *sql.Tx, orderID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.GetOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		query := `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
			       additional_info_json, type_specific_status, notes, create_time, update_time,
			       completion_time, assigned_time, acknowledged_time, estimated_completion_time,
			       cancellation_or_rejection_reason, retry_count, created_by, title,
			       snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
			WHERE id = $1
		`
		queryRow := sessionTx.QueryRowContext(spanContext, query, orderID)

		orderRecord := &orders.Order{}
		var orderTypeValue, orderStatusValue, createdByValue int32
		var createTimestamp, updateTimestamp time.Time
		var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
		var resourceType string
		// Use sql.NullString for nullable fields.
		var situationID sql.NullString
		var assetID sql.NullString
		var reportID sql.NullString
		var reviewRoundID sql.NullString
		var caseID sql.NullString
		scanError := queryRow.Scan(
			&orderRecord.Id,
			&situationID,
			&assetID,
			&orderTypeValue,
			&orderStatusValue,
			&orderRecord.Instructions,
			&orderRecord.Priority,
			&orderRecord.AdditionalInfoJson,
			&orderRecord.TypeSpecificStatus,
			&orderRecord.Notes,
			&createTimestamp,
			&updateTimestamp,
			&completionTimestamp,
			&assignedTimestamp,
			&acknowledgedTimestamp,
			&estimatedCompletionTimestamp,
			&orderRecord.CancellationOrRejectionReason,
			&orderRecord.RetryCount,
			&createdByValue,
			&orderRecord.Title,
			&orderRecord.SnoozeReason,
			&snoozeTimestamp,
			&orderRecord.SnoozeCount,
			&resourceType,
			&reportID,
			&reviewRoundID,
			&caseID,
		)
		if scanError != nil {
			if errors.Is(scanError, sql.ErrNoRows) {
				return nil, ErrOrderNotFound
			}
			herosentry.CaptureException(spanContext, scanError, herosentry.ErrorTypeDatabase, "Failed to scan order row")
			return nil, scanError
		}
		// Convert sql.NullString to empty string if NULL.
		if situationID.Valid {
			orderRecord.SituationId = situationID.String
		} else {
			orderRecord.SituationId = ""
		}
		if assetID.Valid {
			orderRecord.AssetId = assetID.String
		} else {
			orderRecord.AssetId = ""
		}
		if reportID.Valid {
			orderRecord.ReportId = reportID.String
		} else {
			orderRecord.ReportId = ""
		}
		if reviewRoundID.Valid {
			orderRecord.ReviewRoundId = reviewRoundID.String
		} else {
			orderRecord.ReviewRoundId = ""
		}
		if caseID.Valid {
			orderRecord.CaseId = caseID.String
		} else {
			orderRecord.CaseId = ""
		}

		orderRecord.Type = orders.OrderType(orderTypeValue)
		orderRecord.Status = orders.OrderStatus(orderStatusValue)

		orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
		orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
		orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

		// Handle nullable timestamps
		orderRecord.CompletionTime = ""
		if completionTimestamp.Valid {
			orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
		}
		orderRecord.AssignedTime = ""
		if assignedTimestamp.Valid {
			orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
		}
		orderRecord.AcknowledgedTime = ""
		if acknowledgedTimestamp.Valid {
			orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
		}
		orderRecord.EstimatedCompletionTime = ""
		if estimatedCompletionTimestamp.Valid {
			orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
		}
		orderRecord.SnoozeUntil = ""
		if snoozeTimestamp.Valid {
			orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
		}
		orderRecord.ResourceType = resourceType

		// Load repeated fields using batch loading for single order
		allowedAssetTypesMap, blacklistedAssetIDsMap, orderUpdatesMap, statusUpdatesMap, permissionsMap, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, []string{orderRecord.Id})
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to load order relationships")
			return nil, err
		}

		orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
		orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
		orderRecord.Updates = orderUpdatesMap[orderRecord.Id]
		orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
		orderRecord.Permissions = permissionsMap[orderRecord.Id]

		return orderRecord, nil
	})
}

// UpdateOrder updates an existing order record and its repeated fields.
func (repository *PostgresOrderRepository) UpdateOrder(ctx context.Context, transaction *sql.Tx, orderRecord *orders.Order) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.UpdateOrder")
	defer finishSpan()

	span.SetTag("order.id", orderRecord.Id)
	span.SetTag("order.type", orderRecord.Type.String())
	span.SetTag("order.status", orderRecord.Status.String())
	span.SetTag("order.priority", fmt.Sprintf("%d", orderRecord.Priority))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		previousOrder, err := repository.GetOrder(spanContext, sessionTx, orderRecord.Id)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to get previous order")
			return nil, err
		}

		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		orderRecord.UpdateTime = commonUtils.TimeToISO8601String(time.Now())
		// Ensure resource_type remains set (typically "ORDER").
		orderRecord.ResourceType = FixedResourceTypeOrder
		if orderRecord.AdditionalInfoJson == "" {
			orderRecord.AdditionalInfoJson = "{}"
		}
		query := `
			UPDATE orders
			SET situation_id = $1,
				asset_id = $2,
				type = $3,
				status = $4,
				instructions = $5,
				priority = $6,
				additional_info_json = $7,
				type_specific_status = $8,
				notes = $9,
				update_time = $10,
				completion_time = $11,
				assigned_time = $12,
				acknowledged_time = $13,
				estimated_completion_time = $14,
				cancellation_or_rejection_reason = $15,
				retry_count = $16,
				created_by = $17,
				title = $18,
				snooze_reason = $19,
				snooze_until = $20,
				snooze_count = $21,
				resource_type = $22,
				report_id = $23,
				review_round_id = $24,
				case_id = $25
			WHERE id = $26
		`
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare update order statement")
			return nil, preparationError
		}
		defer preparedStatement.Close()

		// Convert ISO8601 string timestamps to time.Time for database storage
		var updateTime time.Time
		var completionTime, assignedTime, acknowledgedTime, estimatedCompletionTime, snoozeUntil sql.NullTime
		var timeErr error

		// Required timestamps
		updateTime, timeErr = commonUtils.ISO8601StringToTime(orderRecord.UpdateTime)
		if timeErr != nil {
			err := fmt.Errorf("invalid update_time format: %w", timeErr)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
			return nil, err
		}

		// Optional timestamps
		if orderRecord.CompletionTime != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(orderRecord.CompletionTime)
			if timeErr != nil {
				err := fmt.Errorf("invalid completion_time format: %w", timeErr)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			completionTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.AssignedTime != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(orderRecord.AssignedTime)
			if timeErr != nil {
				err := fmt.Errorf("invalid assigned_time format: %w", timeErr)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			assignedTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.AcknowledgedTime != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(orderRecord.AcknowledgedTime)
			if timeErr != nil {
				err := fmt.Errorf("invalid acknowledged_time format: %w", timeErr)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			acknowledgedTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.EstimatedCompletionTime != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(orderRecord.EstimatedCompletionTime)
			if timeErr != nil {
				err := fmt.Errorf("invalid estimated_completion_time format: %w", timeErr)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			estimatedCompletionTime = sql.NullTime{Time: t, Valid: true}
		}

		if orderRecord.SnoozeUntil != "" {
			t, timeErr := commonUtils.ISO8601StringToTime(orderRecord.SnoozeUntil)
			if timeErr != nil {
				err := fmt.Errorf("invalid snooze_until format: %w", timeErr)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return nil, err
			}
			snoozeUntil = sql.NullTime{Time: t, Valid: true}
		}

		_, executionError := preparedStatement.ExecContext(spanContext,
			nullIfEmpty(orderRecord.SituationId),
			nullIfEmpty(orderRecord.AssetId),
			int32(orderRecord.Type),
			int32(orderRecord.Status),
			orderRecord.Instructions,
			orderRecord.Priority,
			orderRecord.AdditionalInfoJson,
			orderRecord.TypeSpecificStatus,
			orderRecord.Notes,
			updateTime,
			completionTime,
			assignedTime,
			acknowledgedTime,
			estimatedCompletionTime,
			orderRecord.CancellationOrRejectionReason,
			orderRecord.RetryCount,
			int32(orderRecord.CreatedBy),
			orderRecord.Title,
			orderRecord.SnoozeReason,
			snoozeUntil,
			orderRecord.SnoozeCount,
			orderRecord.ResourceType,
			nullIfEmpty(orderRecord.ReportId),
			nullIfEmpty(orderRecord.ReviewRoundId),
			nullIfEmpty(orderRecord.CaseId),
			orderRecord.Id,
		)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to execute update order statement")
			return nil, executionError
		}

		preparedStatement.Close()

		// Update allowed asset types: remove old rows then insert new ones.
		statement, err := sessionTx.PrepareContext(spanContext, "DELETE FROM order_allowed_asset_types WHERE order_id = $1")
		if err != nil {
			return nil, err
		}
		_, err = statement.ExecContext(spanContext, orderRecord.Id)
		statement.Close()
		if err != nil {
			return nil, err
		}
		for _, assetType := range orderRecord.AllowedAssetTypes {
			statement, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_allowed_asset_types (order_id, asset_type, org_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING")
			if err != nil {
				return nil, err
			}
			_, err = statement.ExecContext(spanContext, orderRecord.Id, int32(assetType), orgID)
			statement.Close()
			if err != nil {
				return nil, err
			}
		}

		// Update blacklisted asset IDs: remove old then insert new ones.
		statement, err = sessionTx.PrepareContext(spanContext, "DELETE FROM order_blacklisted_asset_ids WHERE order_id = $1")
		if err != nil {
			return nil, err
		}
		_, err = statement.ExecContext(spanContext, orderRecord.Id)
		statement.Close()
		if err != nil {
			return nil, err
		}
		for _, assetID := range orderRecord.BlacklistedAssetIds {
			statement, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_blacklisted_asset_ids (order_id, asset_id, org_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING")
			if err != nil {
				return nil, err
			}
			_, err = statement.ExecContext(spanContext, orderRecord.Id, assetID, orgID)
			statement.Close()
			if err != nil {
				return nil, err
			}
		}

		if previousOrder.Status != orderRecord.Status || previousOrder.TypeSpecificStatus != orderRecord.TypeSpecificStatus {
			if err := repository.insertOrderStatusUpdate(spanContext, sessionTx, orderRecord.Id,
				orderRecord.Status, previousOrder.Status,
				orderRecord.TypeSpecificStatus, previousOrder.TypeSpecificStatus,
				"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
			); err != nil {
				return nil, err
			}
		}

		return repository.GetOrder(spanContext, sessionTx, orderRecord.Id)
	})
}

// ListOrders returns a paginated list of orders with optional filtering by status and type, and ordering.
func (repository *PostgresOrderRepository) ListOrders(
	ctx context.Context,
	transaction *sql.Tx,
	pageSize int,
	pageToken string,
	status orders.OrderStatus,
	orderType orders.OrderType,
	orderBy string,
) (*ListOrdersResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.ListOrders")
	defer finishSpan()

	span.SetTag("order.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("order.filter.status", status.String())
	span.SetTag("order.filter.type", orderType.String())
	span.SetTag("order.filter.order_by", orderBy)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*ListOrdersResult, error) {
		// Calculate offset from pageToken.
		offset := 0
		if pageToken != "" {
			if parsedOffset, err := strconv.Atoi(pageToken); err == nil {
				offset = parsedOffset
			}
		}

		// Start with the base query.
		query := `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
			       additional_info_json, type_specific_status, notes, create_time, update_time,
			       completion_time, assigned_time, acknowledged_time, estimated_completion_time,
			       cancellation_or_rejection_reason, retry_count, created_by, title,
			       snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
		`
		var args []interface{}
		argCounter := 1

		// Build filtering conditions.
		// Filter by order status if provided.
		if status != orders.OrderStatus_ORDER_STATUS_UNSPECIFIED {
			query += fmt.Sprintf(" WHERE status = $%d", argCounter)
			args = append(args, int32(status))
			argCounter++
		}

		// Filter by order type if provided.
		if orderType != orders.OrderType_ORDER_TYPE_UNSPECIFIED {
			if len(args) == 0 {
				query += fmt.Sprintf(" WHERE type = $%d", argCounter)
			} else {
				query += fmt.Sprintf(" AND type = $%d", argCounter)
			}
			args = append(args, int32(orderType))
			argCounter++
		}

		// Append ordering.
		if orderBy != "" {
			query += " ORDER BY " + orderBy
		} else {
			query += " ORDER BY create_time DESC, id DESC"
		}

		// Append LIMIT and OFFSET.
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argCounter, argCounter+1)
		args = append(args, pageSize, offset)

		rows, queryError := sessionTx.QueryContext(spanContext, query, args...)
		if queryError != nil {
			herosentry.CaptureException(spanContext, queryError, herosentry.ErrorTypeDatabase, "Failed to query orders")
			return &ListOrdersResult{}, queryError
		}
		// Ensure rows are closed no matter what.
		defer rows.Close()

		var orderRecords []*orders.Order
		for rows.Next() {
			orderRecord := &orders.Order{}
			var orderTypeValue, orderStatusValue, createdByValue int32
			var createTimestamp, updateTimestamp time.Time
			var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
			var resourceType string
			// Use sql.NullString for nullable fields.
			var situationID sql.NullString
			var assetID sql.NullString
			var reportID sql.NullString
			var reviewRoundID sql.NullString
			var caseID sql.NullString

			if err := rows.Scan(
				&orderRecord.Id,
				&situationID,
				&assetID,
				&orderTypeValue,
				&orderStatusValue,
				&orderRecord.Instructions,
				&orderRecord.Priority,
				&orderRecord.AdditionalInfoJson,
				&orderRecord.TypeSpecificStatus,
				&orderRecord.Notes,
				&createTimestamp,
				&updateTimestamp,
				&completionTimestamp,
				&assignedTimestamp,
				&acknowledgedTimestamp,
				&estimatedCompletionTimestamp,
				&orderRecord.CancellationOrRejectionReason,
				&orderRecord.RetryCount,
				&createdByValue,
				&orderRecord.Title,
				&orderRecord.SnoozeReason,
				&snoozeTimestamp,
				&orderRecord.SnoozeCount,
				&resourceType,
				&reportID,
				&reviewRoundID,
				&caseID,
			); err != nil {
				return &ListOrdersResult{}, err
			}

			if situationID.Valid {
				orderRecord.SituationId = situationID.String
			} else {
				orderRecord.SituationId = ""
			}
			if assetID.Valid {
				orderRecord.AssetId = assetID.String
			} else {
				orderRecord.AssetId = ""
			}
			if reportID.Valid {
				orderRecord.ReportId = reportID.String
			} else {
				orderRecord.ReportId = ""
			}
			if reviewRoundID.Valid {
				orderRecord.ReviewRoundId = reviewRoundID.String
			} else {
				orderRecord.ReviewRoundId = ""
			}
			if caseID.Valid {
				orderRecord.CaseId = caseID.String
			} else {
				orderRecord.CaseId = ""
			}

			orderRecord.Type = orders.OrderType(orderTypeValue)
			orderRecord.Status = orders.OrderStatus(orderStatusValue)

			orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
			orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
			orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

			// Handle nullable timestamps
			orderRecord.CompletionTime = ""
			if completionTimestamp.Valid {
				orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
			}
			orderRecord.AssignedTime = ""
			if assignedTimestamp.Valid {
				orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
			}
			orderRecord.AcknowledgedTime = ""
			if acknowledgedTimestamp.Valid {
				orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
			}
			orderRecord.EstimatedCompletionTime = ""
			if estimatedCompletionTimestamp.Valid {
				orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
			}
			orderRecord.SnoozeUntil = ""
			if snoozeTimestamp.Valid {
				orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
			}
			orderRecord.ResourceType = resourceType

			orderRecords = append(orderRecords, orderRecord)
		}
		// Closing it manually so that the subsequent call doesn't get blocked for some rea.
		rows.Close()

		if rowsErr := rows.Err(); rowsErr != nil {
			herosentry.CaptureException(spanContext, rowsErr, herosentry.ErrorTypeDatabase, "Failed to iterate order rows")
			return &ListOrdersResult{}, rowsErr
		}

		// Extract order IDs for batch loading
		orderIDs := make([]string, len(orderRecords))
		for i, orderRecord := range orderRecords {
			orderIDs[i] = orderRecord.Id
		}

		// Batch load all relationships for all orders
		allowedAssetTypesMap, blacklistedAssetIDsMap, orderUpdatesMap, statusUpdatesMap, permissionsMap, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, orderIDs)
		if err != nil {
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase, "Failed to load order relationships")
			return &ListOrdersResult{}, err
		}

		// Assign the loaded relationships to each order
		for _, orderRecord := range orderRecords {
			orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
			orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
			orderRecord.Updates = orderUpdatesMap[orderRecord.Id]
			orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
			orderRecord.Permissions = permissionsMap[orderRecord.Id]
		}
		var nextPageToken string
		if len(orderRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &ListOrdersResult{
			Orders:    orderRecords,
			PageToken: nextPageToken,
		}, nil
	})
}

// DeleteOrder deletes an order by its ID.
func (repository *PostgresOrderRepository) DeleteOrder(ctx context.Context, transaction *sql.Tx, orderID string) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.DeleteOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		query := `DELETE FROM orders WHERE id = $1`
		result, executionError := sessionTx.ExecContext(spanContext, query, orderID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to delete order")
			return executionError
		}
		rowsAffected, rowsAffectedError := result.RowsAffected()
		if rowsAffectedError != nil {
			herosentry.CaptureException(spanContext, rowsAffectedError, herosentry.ErrorTypeDatabase, "Failed to get rows affected")
			return rowsAffectedError
		}
		if rowsAffected == 0 {
			return ErrOrderNotFound
		}
		return nil
	})
}

// AddOrderUpdate adds a new update entry for an order.
func (repository *PostgresOrderRepository) AddOrderUpdate(ctx context.Context, transaction *sql.Tx, orderID string, updateEntry *orders.OrderUpdateEntry) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.AddOrderUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("order.update_source", updateEntry.UpdateSource.String())

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		query := `
			INSERT INTO order_updates (order_id, message, timestamp, update_source, org_id)
			VALUES ($1, $2, $3, $4, $5)
		`
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare add order update statement")
			return preparationError
		}
		defer preparedStatement.Close()

		// Convert ISO8601 timestamp string to time.Time
		updateTime, _ := commonUtils.ISO8601StringToTime(updateEntry.Timestamp)

		_, executionError := preparedStatement.ExecContext(spanContext,
			orderID,
			updateEntry.Message,
			updateTime,
			int32(updateEntry.UpdateSource),
			orgID,
		)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to add order update")
		}
		return executionError
	})
}

// RemoveOrderUpdate removes a matching update entry from an order.
func (repository *PostgresOrderRepository) RemoveOrderUpdate(ctx context.Context, transaction *sql.Tx, orderID string, updateEntry *orders.OrderUpdateEntry) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.RemoveOrderUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		query := `
			DELETE FROM order_updates
			WHERE order_id = $1 AND message = $2 AND timestamp = $3 AND update_source = $4
		`
		// Convert ISO8601 timestamp string to time.Time
		updateTime, _ := commonUtils.ISO8601StringToTime(updateEntry.Timestamp)

		result, executionError := sessionTx.ExecContext(spanContext, query, orderID, updateEntry.Message, updateTime, int32(updateEntry.UpdateSource))
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to remove order update")
			return executionError
		}
		rowsAffected, rowsAffectedError := result.RowsAffected()
		if rowsAffectedError != nil {
			return rowsAffectedError
		}
		if rowsAffected == 0 {
			return errors.New("order update not found")
		}
		return nil
	})
}

// AcknowledgeOrder sets the order status to ACKNOWLEDGED.
func (repository *PostgresOrderRepository) AcknowledgeOrder(ctx context.Context, transaction *sql.Tx, orderID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.AcknowledgeOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		previousOrder, err := repository.GetOrder(spanContext, sessionTx, orderID)
		if err != nil {
			return nil, err
		}
		currentTime := time.Now()
		query := `
			UPDATE orders
			SET status = $1, acknowledged_time = $2, update_time = $2
			WHERE id = $3
		`
		newStatus := orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for order acknowledgement")
			return nil, preparationError
		}
		defer preparedStatement.Close()
		_, executionError := preparedStatement.ExecContext(spanContext, newStatus, currentTime, orderID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to acknowledge order")
			return nil, executionError
		}
		if err := repository.insertOrderStatusUpdate(spanContext, sessionTx, orderID,
			newStatus, previousOrder.Status,
			previousOrder.TypeSpecificStatus, previousOrder.TypeSpecificStatus, // type-specific remains unchanged
			"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
		); err != nil {
			return nil, err
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// RejectOrder sets the order status to REJECTED.
func (repository *PostgresOrderRepository) RejectOrder(ctx context.Context, transaction *sql.Tx, orderID, rejectionReason string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.RejectOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("order.rejection_reason", rejectionReason)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		previousOrder, err := repository.GetOrder(spanContext, sessionTx, orderID)
		if err != nil {
			return nil, err
		}
		currentTime := time.Now()
		query := `
			UPDATE orders
			SET status = $1, cancellation_or_rejection_reason = $2, update_time = $3
			WHERE id = $4
		`
		newStatus := orders.OrderStatus_ORDER_STATUS_REJECTED
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for order rejection")
			return nil, preparationError
		}
		defer preparedStatement.Close()
		_, executionError := preparedStatement.ExecContext(spanContext, newStatus, rejectionReason, currentTime, orderID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to reject order")
			return nil, executionError
		}
		if err := repository.insertOrderStatusUpdate(spanContext, sessionTx, orderID,
			newStatus, previousOrder.Status,
			previousOrder.TypeSpecificStatus, previousOrder.TypeSpecificStatus, // type-specific remains unchanged
			"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
		); err != nil {
			return nil, err
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// SnoozeOrder sets the order status to SNOOZED.
func (repository *PostgresOrderRepository) SnoozeOrder(ctx context.Context, transaction *sql.Tx, orderID, snoozeReason string, snoozeUntil time.Time) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.SnoozeOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("order.snooze_reason", snoozeReason)
	span.SetTag("order.snooze_until", snoozeUntil.Format(time.RFC3339))

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		previousOrder, err := repository.GetOrder(spanContext, sessionTx, orderID)
		if err != nil {
			return nil, err
		}
		currentTime := time.Now()
		query := `
			UPDATE orders
			SET status = $1, snooze_reason = $2, snooze_until = $3, update_time = $4
			WHERE id = $5
		`
		newStatus := orders.OrderStatus_ORDER_STATUS_SNOOZED
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for order snooze")
			return nil, preparationError
		}
		defer preparedStatement.Close()
		_, executionError := preparedStatement.ExecContext(spanContext, newStatus, snoozeReason, snoozeUntil, currentTime, orderID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to snooze order")
			return nil, executionError
		}
		if err := repository.insertOrderStatusUpdate(spanContext, sessionTx, orderID,
			newStatus, previousOrder.Status,
			previousOrder.TypeSpecificStatus, previousOrder.TypeSpecificStatus, // type-specific remains unchanged
			"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
		); err != nil {
			return nil, err
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// CancelOrder sets the order status to CANCELLED.
func (repository *PostgresOrderRepository) CancelOrder(ctx context.Context, transaction *sql.Tx, orderID, cancellationReason string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.CancelOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("order.cancellation_reason", cancellationReason)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		previousOrder, err := repository.GetOrder(spanContext, sessionTx, orderID)
		if err != nil {
			return nil, err
		}
		currentTime := time.Now()
		query := `
			UPDATE orders
			SET status = $1, cancellation_or_rejection_reason = $2, update_time = $3
			WHERE id = $4
		`
		newStatus := orders.OrderStatus_ORDER_STATUS_CANCELLED
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for order cancellation")
			return nil, preparationError
		}
		defer preparedStatement.Close()
		_, executionError := preparedStatement.ExecContext(spanContext, newStatus, cancellationReason, currentTime, orderID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to cancel order")
			return nil, executionError
		}
		if err := repository.insertOrderStatusUpdate(spanContext, sessionTx, orderID,
			newStatus, previousOrder.Status,
			previousOrder.TypeSpecificStatus, previousOrder.TypeSpecificStatus, // type-specific remains unchanged
			"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
		); err != nil {
			return nil, err
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// CompleteOrder sets the order status to COMPLETED.
func (repository *PostgresOrderRepository) CompleteOrder(ctx context.Context, transaction *sql.Tx, orderID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.CompleteOrder")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		previousOrder, err := repository.GetOrder(spanContext, sessionTx, orderID)
		if err != nil {
			return nil, err
		}
		currentTime := time.Now()
		query := `
			UPDATE orders
			SET status = $1, completion_time = $2, update_time = $2
			WHERE id = $3
		`
		newStatus := orders.OrderStatus_ORDER_STATUS_COMPLETED
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for order completion")
			return nil, preparationError
		}
		defer preparedStatement.Close()
		_, executionError := preparedStatement.ExecContext(spanContext, newStatus, currentTime, orderID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to complete order")
			return nil, executionError
		}
		if err := repository.insertOrderStatusUpdate(spanContext, sessionTx, orderID,
			newStatus, previousOrder.Status,
			previousOrder.TypeSpecificStatus, previousOrder.TypeSpecificStatus, // type-specific remains unchanged
			"", "", situations.UpdateSource_UPDATE_SOURCE_UNKNOWN,
		); err != nil {
			return nil, err
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// AddAllowedAssetType adds an allowed asset type to an order.
func (repository *PostgresOrderRepository) AddAllowedAssetType(ctx context.Context, transaction *sql.Tx, orderID string, allowedAssetType assets.AssetType) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.AddAllowedAssetType")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset.type", allowedAssetType.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		query := `INSERT INTO order_allowed_asset_types (order_id, asset_type, org_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING`
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for adding allowed asset type")
			return nil, preparationError
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(spanContext, orderID, int32(allowedAssetType), orgID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to add allowed asset type")
			return nil, executionError
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// RemoveAllowedAssetType removes an allowed asset type from an order.
func (repository *PostgresOrderRepository) RemoveAllowedAssetType(ctx context.Context, transaction *sql.Tx, orderID string, allowedAssetType assets.AssetType) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.RemoveAllowedAssetType")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset.type", allowedAssetType.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		query := `DELETE FROM order_allowed_asset_types WHERE order_id = $1 AND asset_type = $2`
		result, executionError := sessionTx.ExecContext(spanContext, query, orderID, int32(allowedAssetType))
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to remove allowed asset type")
			return nil, executionError
		}
		_ = result
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// AddBlacklistedAssetId adds an asset ID to the blacklisted list for an order.
func (repository *PostgresOrderRepository) AddBlacklistedAssetId(ctx context.Context, transaction *sql.Tx, orderID, assetID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.AddBlacklistedAssetId")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset.id", assetID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		query := `INSERT INTO order_blacklisted_asset_ids (order_id, asset_id, org_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING`
		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			herosentry.CaptureException(spanContext, preparationError, herosentry.ErrorTypeDatabase, "Failed to prepare statement for adding blacklisted asset")
			return nil, preparationError
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(spanContext, orderID, assetID, orgID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to add blacklisted asset")
			return nil, executionError
		}
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// RemoveBlacklistedAssetId removes an asset ID from the blacklisted list for an order.
func (repository *PostgresOrderRepository) RemoveBlacklistedAssetId(ctx context.Context, transaction *sql.Tx, orderID, assetID string) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.RemoveBlacklistedAssetId")
	defer finishSpan()

	span.SetTag("order.id", orderID)
	span.SetTag("asset.id", assetID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		query := `DELETE FROM order_blacklisted_asset_ids WHERE order_id = $1 AND asset_id = $2`
		result, executionError := sessionTx.ExecContext(spanContext, query, orderID, assetID)
		if executionError != nil {
			herosentry.CaptureException(spanContext, executionError, herosentry.ErrorTypeDatabase, "Failed to remove blacklisted asset")
			return nil, executionError
		}
		_ = result
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// ListActiveAssignedOrdersForAsset returns orders for the given asset that are in statuses CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS.
func (repository *PostgresOrderRepository) ListActiveAssignedOrdersForAsset(
	ctx context.Context,
	transaction *sql.Tx,
	assetID string,
	pageSize int,
	pageToken string,
) (*ListOrdersResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.ListActiveAssignedOrdersForAsset")
	defer finishSpan()

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("pagination.page_token", pageToken)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*ListOrdersResult, error) {
		// Active statuses: CREATED, ACKNOWLEDGED, SNOOZED, IN_PROGRESS.
		activeStatuses := []int32{
			int32(orders.OrderStatus_ORDER_STATUS_CREATED),
			int32(orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED),
			int32(orders.OrderStatus_ORDER_STATUS_SNOOZED),
			int32(orders.OrderStatus_ORDER_STATUS_IN_PROGRESS),
		}

		offset := 0
		if pageToken != "" {
			var conversionError error
			offset, conversionError = strconv.Atoi(pageToken)
			if conversionError != nil {
				offset = 0
			}
		}

		query := `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
			       additional_info_json, type_specific_status, notes, create_time, update_time,
			       completion_time, assigned_time, acknowledged_time, estimated_completion_time,
			       cancellation_or_rejection_reason, retry_count, created_by, title,
			       snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
			WHERE asset_id = $1 AND status = ANY($2)
			ORDER BY create_time DESC, id DESC
			LIMIT $3 OFFSET $4
		`
		rows, queryError := sessionTx.QueryContext(spanContext, query, assetID, pq.Array(activeStatuses), pageSize, offset)
		if queryError != nil {
			return &ListOrdersResult{}, queryError
		}
		defer rows.Close()

		// Buffer all order records from the result set.
		var orderRecords []*orders.Order
		for rows.Next() {
			orderRecord := &orders.Order{}
			var orderTypeValue, orderStatusValue, createdByValue int32
			var createTimestamp, updateTimestamp time.Time
			var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
			var resourceType string
			var situationID sql.NullString
			var assetID sql.NullString
			var reportID sql.NullString
			var reviewRoundID sql.NullString
			var caseID sql.NullString

			scanError := rows.Scan(
				&orderRecord.Id,
				&situationID,
				&assetID,
				&orderTypeValue,
				&orderStatusValue,
				&orderRecord.Instructions,
				&orderRecord.Priority,
				&orderRecord.AdditionalInfoJson,
				&orderRecord.TypeSpecificStatus,
				&orderRecord.Notes,
				&createTimestamp,
				&updateTimestamp,
				&completionTimestamp,
				&assignedTimestamp,
				&acknowledgedTimestamp,
				&estimatedCompletionTimestamp,
				&orderRecord.CancellationOrRejectionReason,
				&orderRecord.RetryCount,
				&createdByValue,
				&orderRecord.Title,
				&orderRecord.SnoozeReason,
				&snoozeTimestamp,
				&orderRecord.SnoozeCount,
				&resourceType,
				&reportID,
				&reviewRoundID,
				&caseID,
			)
			if scanError != nil {
				return &ListOrdersResult{}, scanError
			}

			if situationID.Valid {
				orderRecord.SituationId = situationID.String
			} else {
				orderRecord.SituationId = ""
			}
			if assetID.Valid {
				orderRecord.AssetId = assetID.String
			} else {
				orderRecord.AssetId = ""
			}
			if reportID.Valid {
				orderRecord.ReportId = reportID.String
			} else {
				orderRecord.ReportId = ""
			}
			if reviewRoundID.Valid {
				orderRecord.ReviewRoundId = reviewRoundID.String
			} else {
				orderRecord.ReviewRoundId = ""
			}
			if caseID.Valid {
				orderRecord.CaseId = caseID.String
			} else {
				orderRecord.CaseId = ""
			}

			orderRecord.Type = orders.OrderType(orderTypeValue)
			orderRecord.Status = orders.OrderStatus(orderStatusValue)
			orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
			orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
			orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

			// Handle nullable timestamps
			orderRecord.CompletionTime = ""
			if completionTimestamp.Valid {
				orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
			}
			orderRecord.AssignedTime = ""
			if assignedTimestamp.Valid {
				orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
			}
			orderRecord.AcknowledgedTime = ""
			if acknowledgedTimestamp.Valid {
				orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
			}
			orderRecord.EstimatedCompletionTime = ""
			if estimatedCompletionTimestamp.Valid {
				orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
			}
			orderRecord.SnoozeUntil = ""
			if snoozeTimestamp.Valid {
				orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
			}
			orderRecord.ResourceType = resourceType

			orderRecords = append(orderRecords, orderRecord)
		}
		if rowsError := rows.Err(); rowsError != nil {
			return &ListOrdersResult{}, rowsError
		}
		// Explicitly close the rows now (although defer will handle it) before making further queries.
		rows.Close()

		// Extract order IDs for batch loading
		orderIDs := make([]string, len(orderRecords))
		for i, orderRecord := range orderRecords {
			orderIDs[i] = orderRecord.Id
		}

		// Batch load relationships for all orders (excluding updates and permissions for this specific function)
		allowedAssetTypesMap, blacklistedAssetIDsMap, _, statusUpdatesMap, _, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, orderIDs)
		if err != nil {
			return &ListOrdersResult{}, err
		}

		// Assign the loaded relationships to each order
		for _, orderRecord := range orderRecords {
			orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
			orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
			orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
		}

		var nextPageToken string
		if len(orderRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &ListOrdersResult{
			Orders:    orderRecords,
			PageToken: nextPageToken,
		}, nil
	})
}

// ListNewOrdersForAsset returns orders for the given asset that are in the CREATED status.
func (repository *PostgresOrderRepository) ListNewOrdersForAsset(ctx context.Context, transaction *sql.Tx, assetID string, pageSize int, pageToken string) (*ListOrdersResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.ListNewOrdersForAsset")
	defer finishSpan()

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("pagination.page_token", pageToken)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*ListOrdersResult, error) {
		offset := 0
		if pageToken != "" {
			var conversionError error
			offset, conversionError = strconv.Atoi(pageToken)
			if conversionError != nil {
				offset = 0
			}
		}

		query := `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
			       additional_info_json, type_specific_status, notes, create_time, update_time,
			       completion_time, assigned_time, acknowledged_time, estimated_completion_time,
			       cancellation_or_rejection_reason, retry_count, created_by, title,
			       snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
			WHERE asset_id = $1 AND status = $2
			ORDER BY create_time DESC, id DESC
			LIMIT $3 OFFSET $4
		`
		createdStatus := int32(orders.OrderStatus_ORDER_STATUS_CREATED)
		rows, queryError := sessionTx.QueryContext(spanContext, query, assetID, createdStatus, pageSize, offset)
		if queryError != nil {
			return &ListOrdersResult{}, queryError
		}
		defer rows.Close()

		// Buffer all order records from the result set.
		var orderRecords []*orders.Order
		for rows.Next() {
			orderRecord := &orders.Order{}
			var orderTypeValue, orderStatusValue, createdByValue int32
			var createTimestamp, updateTimestamp time.Time
			var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
			var resourceType string
			// Use sql.NullString for situation_id and asset_id.
			var situationID sql.NullString
			var assetID sql.NullString
			var reportID sql.NullString
			var reviewRoundID sql.NullString
			var caseID sql.NullString
			scanError := rows.Scan(
				&orderRecord.Id,
				&situationID,
				&assetID,
				&orderTypeValue,
				&orderStatusValue,
				&orderRecord.Instructions,
				&orderRecord.Priority,
				&orderRecord.AdditionalInfoJson,
				&orderRecord.TypeSpecificStatus,
				&orderRecord.Notes,
				&createTimestamp,
				&updateTimestamp,
				&completionTimestamp,
				&assignedTimestamp,
				&acknowledgedTimestamp,
				&estimatedCompletionTimestamp,
				&orderRecord.CancellationOrRejectionReason,
				&orderRecord.RetryCount,
				&createdByValue,
				&orderRecord.Title,
				&orderRecord.SnoozeReason,
				&snoozeTimestamp,
				&orderRecord.SnoozeCount,
				&resourceType,
				&reportID,
				&reviewRoundID,
				&caseID,
			)
			if scanError != nil {
				return &ListOrdersResult{}, scanError
			}
			if situationID.Valid {
				orderRecord.SituationId = situationID.String
			} else {
				orderRecord.SituationId = ""
			}
			if assetID.Valid {
				orderRecord.AssetId = assetID.String
			} else {
				orderRecord.AssetId = ""
			}
			if reportID.Valid {
				orderRecord.ReportId = reportID.String
			} else {
				orderRecord.ReportId = ""
			}
			if reviewRoundID.Valid {
				orderRecord.ReviewRoundId = reviewRoundID.String
			} else {
				orderRecord.ReviewRoundId = ""
			}
			orderRecord.Type = orders.OrderType(orderTypeValue)
			orderRecord.Status = orders.OrderStatus(orderStatusValue)
			orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
			orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
			orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

			// Handle nullable timestamps
			orderRecord.CompletionTime = ""
			if completionTimestamp.Valid {
				orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
			}
			orderRecord.AssignedTime = ""
			if assignedTimestamp.Valid {
				orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
			}
			orderRecord.AcknowledgedTime = ""
			if acknowledgedTimestamp.Valid {
				orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
			}
			orderRecord.EstimatedCompletionTime = ""
			if estimatedCompletionTimestamp.Valid {
				orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
			}
			orderRecord.SnoozeUntil = ""
			if snoozeTimestamp.Valid {
				orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
			}
			orderRecord.ResourceType = resourceType
			if caseID.Valid {
				orderRecord.CaseId = caseID.String
			} else {
				orderRecord.CaseId = ""
			}

			orderRecords = append(orderRecords, orderRecord)
		}
		if err := rows.Err(); err != nil {
			return &ListOrdersResult{}, err
		}
		// Explicitly close the rows before making further queries.
		rows.Close()

		// Extract order IDs for batch loading
		orderIDs := make([]string, len(orderRecords))
		for i, orderRecord := range orderRecords {
			orderIDs[i] = orderRecord.Id
		}

		// Batch load relationships for all orders (excluding updates and permissions for this specific function)
		allowedAssetTypesMap, blacklistedAssetIDsMap, _, statusUpdatesMap, _, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, orderIDs)
		if err != nil {
			return &ListOrdersResult{}, err
		}

		// Assign the loaded relationships to each order
		for _, orderRecord := range orderRecords {
			orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
			orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
			orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
		}

		var nextPageToken string
		if len(orderRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &ListOrdersResult{
			Orders:    orderRecords,
			PageToken: nextPageToken,
		}, nil
	})
}

// ListOrdersForSituation returns a paginated list of orders for a given situation, filtering by status.
// If the status parameter is less or equal to 0, no status filtering is applied.
func (repository *PostgresOrderRepository) ListOrdersForSituation(ctx context.Context, transaction *sql.Tx, situationID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.ListOrdersForSituation")
	defer finishSpan()

	span.SetTag("situation.id", situationID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("pagination.page_token", pageToken)
	span.SetTag("order.filter.status", status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*ListOrdersResult, error) {
		offset := 0
		if pageToken != "" {
			var err error
			offset, err = strconv.Atoi(pageToken)
			if err != nil {
				offset = 0
			}
		}

		query := `
		SELECT id, situation_id, asset_id, type, status, instructions, priority,
		       additional_info_json, type_specific_status, notes, create_time, update_time,
		       completion_time, assigned_time, acknowledged_time, estimated_completion_time,
		       cancellation_or_rejection_reason, retry_count, created_by, title,
		       snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
		FROM orders
		WHERE situation_id = $1`
		params := []interface{}{situationID}
		placeholderIndex := 2
		if int32(status) > 0 {
			query += " AND status = $" + strconv.Itoa(placeholderIndex)
			params = append(params, int32(status))
			placeholderIndex++
		}
		query += " ORDER BY create_time DESC, id DESC LIMIT $" + strconv.Itoa(placeholderIndex) + " OFFSET $" + strconv.Itoa(placeholderIndex+1)
		params = append(params, pageSize, offset)

		rowsRes, queryError := sessionTx.QueryContext(spanContext, query, params...)
		if queryError != nil {
			return nil, queryError
		}
		// Defer the close so cursor are closed no matter what.
		defer rowsRes.Close()

		var orderRecords []*orders.Order
		for rowsRes.Next() {
			orderRecord := &orders.Order{}
			var orderTypeValue, orderStatusValue, createdByValue int32
			var createTimestamp, updateTimestamp time.Time
			var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
			var resourceType string
			var sqlSituationID sql.NullString
			var assetID sql.NullString
			var reportID sql.NullString
			var reviewRoundID sql.NullString
			var caseID sql.NullString
			if scanError := rowsRes.Scan(
				&orderRecord.Id,
				&sqlSituationID,
				&assetID,
				&orderTypeValue,
				&orderStatusValue,
				&orderRecord.Instructions,
				&orderRecord.Priority,
				&orderRecord.AdditionalInfoJson,
				&orderRecord.TypeSpecificStatus,
				&orderRecord.Notes,
				&createTimestamp,
				&updateTimestamp,
				&completionTimestamp,
				&assignedTimestamp,
				&acknowledgedTimestamp,
				&estimatedCompletionTimestamp,
				&orderRecord.CancellationOrRejectionReason,
				&orderRecord.RetryCount,
				&createdByValue,
				&orderRecord.Title,
				&orderRecord.SnoozeReason,
				&snoozeTimestamp,
				&orderRecord.SnoozeCount,
				&resourceType,
				&reportID,
				&reviewRoundID,
				&caseID,
			); scanError != nil {
				return nil, scanError
			}
			if sqlSituationID.Valid {
				orderRecord.SituationId = sqlSituationID.String
			} else {
				orderRecord.SituationId = ""
			}
			if assetID.Valid {
				orderRecord.AssetId = assetID.String
			} else {
				orderRecord.AssetId = ""
			}
			if reportID.Valid {
				orderRecord.ReportId = reportID.String
			} else {
				orderRecord.ReportId = ""
			}
			if reviewRoundID.Valid {
				orderRecord.ReviewRoundId = reviewRoundID.String
			} else {
				orderRecord.ReviewRoundId = ""
			}
			orderRecord.Type = orders.OrderType(orderTypeValue)
			orderRecord.Status = orders.OrderStatus(orderStatusValue)
			orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
			orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
			orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

			// Handle nullable timestamps
			orderRecord.CompletionTime = ""
			if completionTimestamp.Valid {
				orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
			}
			orderRecord.AssignedTime = ""
			if assignedTimestamp.Valid {
				orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
			}
			orderRecord.AcknowledgedTime = ""
			if acknowledgedTimestamp.Valid {
				orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
			}
			orderRecord.EstimatedCompletionTime = ""
			if estimatedCompletionTimestamp.Valid {
				orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
			}
			orderRecord.SnoozeUntil = ""
			if snoozeTimestamp.Valid {
				orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
			}
			orderRecord.ResourceType = resourceType
			if caseID.Valid {
				orderRecord.CaseId = caseID.String
			} else {
				orderRecord.CaseId = ""
			}
			orderRecords = append(orderRecords, orderRecord)
		}
		// Closing it manually here otherwise the following DB calls can get blocked.
		rowsRes.Close()

		// Extract order IDs for batch loading
		orderIDs := make([]string, len(orderRecords))
		for i, orderRecord := range orderRecords {
			orderIDs[i] = orderRecord.Id
		}

		// Batch load all relationships for all orders
		allowedAssetTypesMap, blacklistedAssetIDsMap, orderUpdatesMap, statusUpdatesMap, _, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, orderIDs)
		if err != nil {
			return nil, err
		}

		// Assign the loaded relationships to each order
		for _, orderRecord := range orderRecords {
			orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
			orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
			orderRecord.Updates = orderUpdatesMap[orderRecord.Id]
			orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
		}
		if rowsErr := rowsRes.Err(); rowsErr != nil {
			return nil, rowsErr
		}
		var nextPageToken string
		if len(orderRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &ListOrdersResult{
			Orders:    orderRecords,
			PageToken: nextPageToken,
		}, nil
	})
}

// ListOrdersForAsset lists orders for an asset with an optional filter by order status.
// If the status parameter is less or equal to 0, no status filtering is applied.
func (repository *PostgresOrderRepository) ListOrdersForAsset(ctx context.Context, transaction *sql.Tx, assetID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.ListOrdersForAsset")
	defer finishSpan()

	span.SetTag("asset.id", assetID)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("pagination.page_token", pageToken)
	span.SetTag("order.filter.status", status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*ListOrdersResult, error) {
		offset := 0
		if pageToken != "" {
			if parsedOffset, err := strconv.Atoi(pageToken); err == nil {
				offset = parsedOffset
			}
		}

		query := `
		SELECT id, situation_id, asset_id, type, status, instructions, priority,
		       additional_info_json, type_specific_status, notes, create_time, update_time,
		       completion_time, assigned_time, acknowledged_time, estimated_completion_time,
		       cancellation_or_rejection_reason, retry_count, created_by, title,
		       snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
		FROM orders
		WHERE asset_id = $1`
		params := []interface{}{assetID}
		placeholderIndex := 2
		if int32(status) > 0 {
			query += " AND status = $" + strconv.Itoa(placeholderIndex)
			params = append(params, int32(status))
			placeholderIndex++
		}
		query += " ORDER BY create_time DESC, id DESC LIMIT $" + strconv.Itoa(placeholderIndex) + " OFFSET $" + strconv.Itoa(placeholderIndex+1)
		params = append(params, pageSize, offset)

		rowsRes, err := sessionTx.QueryContext(spanContext, query, params...)
		if err != nil {
			return nil, err
		}
		defer rowsRes.Close()

		// Buffer all order records from the result set without executing extra queries in the loop.
		var orderRecords []*orders.Order
		for rowsRes.Next() {
			orderRecord := &orders.Order{}
			var orderTypeValue, orderStatusValue, createdByValue int32
			var createTimestamp, updateTimestamp time.Time
			var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
			var resourceType string
			var situationID sql.NullString
			var assetID sql.NullString
			var reportID sql.NullString
			var reviewRoundID sql.NullString
			var caseID sql.NullString

			if err := rowsRes.Scan(
				&orderRecord.Id,
				&situationID,
				&assetID,
				&orderTypeValue,
				&orderStatusValue,
				&orderRecord.Instructions,
				&orderRecord.Priority,
				&orderRecord.AdditionalInfoJson,
				&orderRecord.TypeSpecificStatus,
				&orderRecord.Notes,
				&createTimestamp,
				&updateTimestamp,
				&completionTimestamp,
				&assignedTimestamp,
				&acknowledgedTimestamp,
				&estimatedCompletionTimestamp,
				&orderRecord.CancellationOrRejectionReason,
				&orderRecord.RetryCount,
				&createdByValue,
				&orderRecord.Title,
				&orderRecord.SnoozeReason,
				&snoozeTimestamp,
				&orderRecord.SnoozeCount,
				&resourceType,
				&reportID,
				&reviewRoundID,
				&caseID,
			); err != nil {
				return nil, err
			}

			if situationID.Valid {
				orderRecord.SituationId = situationID.String
			} else {
				orderRecord.SituationId = ""
			}
			if assetID.Valid {
				orderRecord.AssetId = assetID.String
			} else {
				orderRecord.AssetId = ""
			}
			if reportID.Valid {
				orderRecord.ReportId = reportID.String
			} else {
				orderRecord.ReportId = ""
			}
			if reviewRoundID.Valid {
				orderRecord.ReviewRoundId = reviewRoundID.String
			} else {
				orderRecord.ReviewRoundId = ""
			}
			if caseID.Valid {
				orderRecord.CaseId = caseID.String
			} else {
				orderRecord.CaseId = ""
			}

			orderRecord.Type = orders.OrderType(orderTypeValue)
			orderRecord.Status = orders.OrderStatus(orderStatusValue)
			orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
			orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
			orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

			// Handle nullable timestamps
			orderRecord.CompletionTime = ""
			if completionTimestamp.Valid {
				orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
			}
			orderRecord.AssignedTime = ""
			if assignedTimestamp.Valid {
				orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
			}
			orderRecord.AcknowledgedTime = ""
			if acknowledgedTimestamp.Valid {
				orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
			}
			orderRecord.EstimatedCompletionTime = ""
			if estimatedCompletionTimestamp.Valid {
				orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
			}
			orderRecord.SnoozeUntil = ""
			if snoozeTimestamp.Valid {
				orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
			}
			orderRecord.ResourceType = resourceType

			orderRecords = append(orderRecords, orderRecord)
		}
		if err := rowsRes.Err(); err != nil {
			return nil, err
		}
		// Explicitly close the rows before loading repeated fields.
		rowsRes.Close()

		// Extract order IDs for batch loading
		orderIDs := make([]string, len(orderRecords))
		for i, orderRecord := range orderRecords {
			orderIDs[i] = orderRecord.Id
		}

		// Batch load relationships for all orders (excluding updates and permissions for this specific function)
		allowedAssetTypesMap, blacklistedAssetIDsMap, _, statusUpdatesMap, _, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, orderIDs)
		if err != nil {
			return nil, err
		}

		// Assign the loaded relationships to each order
		for _, orderRecord := range orderRecords {
			orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
			orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
			orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
		}

		var nextPageToken string
		if len(orderRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &ListOrdersResult{
			Orders:    orderRecords,
			PageToken: nextPageToken,
		}, nil
	})
}

// listOrdersByFilter is a helper function that lists orders based on a filter condition
func (repository *PostgresOrderRepository) listOrdersByFilter(
	ctx context.Context,
	transaction *sql.Tx,
	filterColumn string,
	filterValue string,
	pageSize int,
	pageToken string,
	status orders.OrderStatus,
) (*ListOrdersResult, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.listOrdersByFilter")
	defer finishSpan()

	span.SetTag("filter.column", filterColumn)
	span.SetTag("filter.value", filterValue)
	span.SetTag("pagination.page_size", fmt.Sprintf("%d", pageSize))
	span.SetTag("pagination.page_token", pageToken)
	span.SetTag("order.filter.status", status.String())

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*ListOrdersResult, error) {
		offset := 0
		if pageToken != "" {
			if parsedOffset, err := strconv.Atoi(pageToken); err == nil {
				offset = parsedOffset
			}
		}

		// Build the base query with the appropriate WHERE clause
		var query string
		switch filterColumn {
		case "report_id":
			query = `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
				   additional_info_json, type_specific_status, notes, create_time, update_time,
				   completion_time, assigned_time, acknowledged_time, estimated_completion_time,
				   cancellation_or_rejection_reason, retry_count, created_by, title,
				   snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
			WHERE report_id = $1
			ORDER BY create_time DESC, id DESC`
		case "review_round_id":
			query = `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
				   additional_info_json, type_specific_status, notes, create_time, update_time,
				   completion_time, assigned_time, acknowledged_time, estimated_completion_time,
				   cancellation_or_rejection_reason, retry_count, created_by, title,
				   snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
			WHERE review_round_id = $1
			ORDER BY create_time DESC, id DESC`
		case "case_id":
			query = `
			SELECT id, situation_id, asset_id, type, status, instructions, priority,
				   additional_info_json, type_specific_status, notes, create_time, update_time,
				   completion_time, assigned_time, acknowledged_time, estimated_completion_time,
				   cancellation_or_rejection_reason, retry_count, created_by, title,
				   snooze_reason, snooze_until, snooze_count, resource_type, report_id, review_round_id, case_id
			FROM orders
			WHERE case_id = $1
			ORDER BY create_time DESC, id DESC`
		default:
			err := fmt.Errorf("invalid filter column: %s", filterColumn)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeValidation)
			return nil, err
		}

		params := []interface{}{filterValue}
		placeholderIndex := 2
		if int32(status) > 0 {
			query += " AND status = $" + strconv.Itoa(placeholderIndex)
			params = append(params, int32(status))
			placeholderIndex++
		}
		query += " LIMIT $" + strconv.Itoa(placeholderIndex) + " OFFSET $" + strconv.Itoa(placeholderIndex+1)
		params = append(params, pageSize, offset)

		rowsRes, queryError := sessionTx.QueryContext(spanContext, query, params...)
		if queryError != nil {
			return nil, queryError
		}
		defer rowsRes.Close()

		var orderRecords []*orders.Order
		for rowsRes.Next() {
			orderRecord := &orders.Order{}
			var orderTypeValue, orderStatusValue, createdByValue int32
			var createTimestamp, updateTimestamp time.Time
			var completionTimestamp, assignedTimestamp, acknowledgedTimestamp, estimatedCompletionTimestamp, snoozeTimestamp sql.NullTime
			var resourceType string
			var situationID sql.NullString
			var assetID sql.NullString
			var reportID sql.NullString
			var reviewRoundID sql.NullString
			var caseID sql.NullString

			if err := rowsRes.Scan(
				&orderRecord.Id,
				&situationID,
				&assetID,
				&orderTypeValue,
				&orderStatusValue,
				&orderRecord.Instructions,
				&orderRecord.Priority,
				&orderRecord.AdditionalInfoJson,
				&orderRecord.TypeSpecificStatus,
				&orderRecord.Notes,
				&createTimestamp,
				&updateTimestamp,
				&completionTimestamp,
				&assignedTimestamp,
				&acknowledgedTimestamp,
				&estimatedCompletionTimestamp,
				&orderRecord.CancellationOrRejectionReason,
				&orderRecord.RetryCount,
				&createdByValue,
				&orderRecord.Title,
				&orderRecord.SnoozeReason,
				&snoozeTimestamp,
				&orderRecord.SnoozeCount,
				&resourceType,
				&reportID,
				&reviewRoundID,
				&caseID,
			); err != nil {
				return nil, err
			}

			if situationID.Valid {
				orderRecord.SituationId = situationID.String
			} else {
				orderRecord.SituationId = ""
			}
			if assetID.Valid {
				orderRecord.AssetId = assetID.String
			} else {
				orderRecord.AssetId = ""
			}
			if reportID.Valid {
				orderRecord.ReportId = reportID.String
			} else {
				orderRecord.ReportId = ""
			}
			if reviewRoundID.Valid {
				orderRecord.ReviewRoundId = reviewRoundID.String
			} else {
				orderRecord.ReviewRoundId = ""
			}
			if caseID.Valid {
				orderRecord.CaseId = caseID.String
			} else {
				orderRecord.CaseId = ""
			}

			orderRecord.Type = orders.OrderType(orderTypeValue)
			orderRecord.Status = orders.OrderStatus(orderStatusValue)
			orderRecord.CreatedBy = situations.UpdateSource(createdByValue)
			orderRecord.CreateTime = commonUtils.TimeToISO8601String(createTimestamp)
			orderRecord.UpdateTime = commonUtils.TimeToISO8601String(updateTimestamp)

			// Handle nullable timestamps
			orderRecord.CompletionTime = ""
			if completionTimestamp.Valid {
				orderRecord.CompletionTime = commonUtils.TimeToISO8601String(completionTimestamp.Time)
			}
			orderRecord.AssignedTime = ""
			if assignedTimestamp.Valid {
				orderRecord.AssignedTime = commonUtils.TimeToISO8601String(assignedTimestamp.Time)
			}
			orderRecord.AcknowledgedTime = ""
			if acknowledgedTimestamp.Valid {
				orderRecord.AcknowledgedTime = commonUtils.TimeToISO8601String(acknowledgedTimestamp.Time)
			}
			orderRecord.EstimatedCompletionTime = ""
			if estimatedCompletionTimestamp.Valid {
				orderRecord.EstimatedCompletionTime = commonUtils.TimeToISO8601String(estimatedCompletionTimestamp.Time)
			}
			orderRecord.SnoozeUntil = ""
			if snoozeTimestamp.Valid {
				orderRecord.SnoozeUntil = commonUtils.TimeToISO8601String(snoozeTimestamp.Time)
			}
			orderRecord.ResourceType = resourceType

			orderRecords = append(orderRecords, orderRecord)
		}
		if err := rowsRes.Err(); err != nil {
			return nil, err
		}

		// Explicitly close the rows before loading repeated fields.
		rowsRes.Close()

		// Extract order IDs for batch loading
		orderIDs := make([]string, len(orderRecords))
		for i, orderRecord := range orderRecords {
			orderIDs[i] = orderRecord.Id
		}

		// Batch load relationships for all orders (excluding updates and permissions for this specific function)
		allowedAssetTypesMap, blacklistedAssetIDsMap, _, statusUpdatesMap, _, err := repository.batchLoadOrderRelationships(spanContext, sessionTx, orderIDs)
		if err != nil {
			return nil, err
		}

		// Assign the loaded relationships to each order
		for _, orderRecord := range orderRecords {
			orderRecord.AllowedAssetTypes = allowedAssetTypesMap[orderRecord.Id]
			orderRecord.BlacklistedAssetIds = blacklistedAssetIDsMap[orderRecord.Id]
			orderRecord.StatusUpdates = statusUpdatesMap[orderRecord.Id]
		}

		var nextPageToken string
		if len(orderRecords) == pageSize {
			nextPageToken = strconv.Itoa(offset + pageSize)
		}
		return &ListOrdersResult{
			Orders:    orderRecords,
			PageToken: nextPageToken,
		}, nil
	})
}

// ListOrdersForReport lists orders associated with a report.
func (repository *PostgresOrderRepository) ListOrdersForReport(ctx context.Context, transaction *sql.Tx, reportID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error) {
	return repository.listOrdersByFilter(ctx, transaction, "report_id", reportID, pageSize, pageToken, status)
}

// ListOrdersForReviewRound lists orders associated with a review round.
func (repository *PostgresOrderRepository) ListOrdersForReviewRound(ctx context.Context, transaction *sql.Tx, reviewRoundID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error) {
	return repository.listOrdersByFilter(ctx, transaction, "review_round_id", reviewRoundID, pageSize, pageToken, status)
}

// ListOrdersForCase lists orders associated with a case.
func (repository *PostgresOrderRepository) ListOrdersForCase(ctx context.Context, transaction *sql.Tx, caseID string, pageSize int, pageToken string, status orders.OrderStatus) (*ListOrdersResult, error) {
	return repository.listOrdersByFilter(ctx, transaction, "case_id", caseID, pageSize, pageToken, status)
}

// UpdateOrderPermissions updates the permissions for an existing order.
func (repository *PostgresOrderRepository) UpdateOrderPermissions(ctx context.Context, transaction *sql.Tx, orderID string, permissions *orders.OrderPermissions) (*orders.Order, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.UpdateOrderPermissions")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (*orders.Order, error) {
		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		// Delete any existing permissions for this order.
		deleteQuery := `DELETE FROM order_permissions WHERE order_id = $1`
		result, err := sessionTx.ExecContext(spanContext, deleteQuery, orderID)
		if err != nil {
			return nil, err
		}
		_ = result

		// Insert new CHANGE_STATUS permissions.
		for _, assetType := range permissions.CanChangeStatus {
			stmt, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_permissions (order_id, scope, asset_type, org_id) VALUES ($1, 'CHANGE_STATUS', $2, $3)")
			if err != nil {
				return nil, err
			}
			_, err = stmt.ExecContext(spanContext, orderID, int32(assetType), orgID)
			stmt.Close()
			if err != nil {
				return nil, err
			}
		}

		// Insert new ASSIGN_ASSET permissions.
		for _, assetType := range permissions.CanAssignAsset {
			stmt, err := sessionTx.PrepareContext(spanContext, "INSERT INTO order_permissions (order_id, scope, asset_type, org_id) VALUES ($1, 'ASSIGN_ASSET', $2, $3)")
			if err != nil {
				return nil, err
			}
			_, err = stmt.ExecContext(spanContext, orderID, int32(assetType), orgID)
			stmt.Close()
			if err != nil {
				return nil, err
			}
		}

		// Return the updated order.
		return repository.GetOrder(spanContext, sessionTx, orderID)
	})
}

// UpdateAdditionalInfoJSON updates the AdditionalInfoJson field of the order in PostgreSQL.
func (repository *PostgresOrderRepository) UpdateAdditionalInfoJSON(ctx context.Context, transaction *sql.Tx, orderID string, additionalInfoJSON string) (string, error) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.UpdateAdditionalInfoJSON")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSession(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) (string, error) {
		currentTime := time.Now()

		updateQuery := `
		UPDATE orders
		SET additional_info_json = $1, update_time = $2
		WHERE id = $3
	`
		result, err := sessionTx.ExecContext(spanContext, updateQuery, additionalInfoJSON, currentTime, orderID)
		if err != nil {
			return "", err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return "", err
		}
		if rowsAffected == 0 {
			return "", ErrOrderNotFound
		}
		return orderID, nil
	})
}

// batchLoadOrderRelationships loads all relationships for multiple orders in batches to avoid N+1 queries
// This replaces individual relationship loading calls for better performance.
// Instead of N*5 queries (N orders * 5 relationships each), this executes only 5 total queries.
func (repository *PostgresOrderRepository) batchLoadOrderRelationships(ctx context.Context, tx *sql.Tx, orderIDs []string) (
	map[string][]assets.AssetType,
	map[string][]string,
	map[string][]*orders.OrderUpdateEntry,
	map[string][]*orders.OrderStatusUpdateEntry,
	map[string]*orders.OrderPermissions,
	error,
) {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.batchLoadOrderRelationships")
	defer finishSpan()

	span.SetTag("order.count", fmt.Sprintf("%d", len(orderIDs)))

	if len(orderIDs) == 0 {
		return make(map[string][]assets.AssetType), make(map[string][]string), make(map[string][]*orders.OrderUpdateEntry), make(map[string][]*orders.OrderStatusUpdateEntry), make(map[string]*orders.OrderPermissions), nil
	}

	// Initialize result maps
	allowedAssetTypesMap := make(map[string][]assets.AssetType)
	blacklistedAssetIDsMap := make(map[string][]string)
	orderUpdatesMap := make(map[string][]*orders.OrderUpdateEntry)
	statusUpdatesMap := make(map[string][]*orders.OrderStatusUpdateEntry)
	permissionsMap := make(map[string]*orders.OrderPermissions)

	// Initialize empty slices for all order IDs to ensure consistent results
	for _, orderID := range orderIDs {
		allowedAssetTypesMap[orderID] = []assets.AssetType{}
		blacklistedAssetIDsMap[orderID] = []string{}
		orderUpdatesMap[orderID] = []*orders.OrderUpdateEntry{}
		statusUpdatesMap[orderID] = []*orders.OrderStatusUpdateEntry{}
		permissionsMap[orderID] = &orders.OrderPermissions{
			CanChangeStatus: []assets.AssetType{},
			CanAssignAsset:  []assets.AssetType{},
		}
	}

	// Batch load allowed asset types - 1 query instead of N queries
	allowedAssetRows, err := tx.QueryContext(spanContext, `SELECT order_id, asset_type FROM order_allowed_asset_types WHERE order_id = ANY($1)`, pq.Array(orderIDs))
	if err != nil {
		err := fmt.Errorf("failed to batch load allowed asset types: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, nil, err
	}
	defer allowedAssetRows.Close()

	for allowedAssetRows.Next() {
		var orderID string
		var assetTypeValue int32
		if err := allowedAssetRows.Scan(&orderID, &assetTypeValue); err != nil {
			err := fmt.Errorf("failed to scan allowed asset type row: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, nil, err
		}
		allowedAssetTypesMap[orderID] = append(allowedAssetTypesMap[orderID], assets.AssetType(assetTypeValue))
	}

	// Batch load blacklisted asset IDs - 1 query instead of N queries
	blacklistedRows, err := tx.QueryContext(spanContext, `SELECT order_id, asset_id FROM order_blacklisted_asset_ids WHERE order_id = ANY($1)`, pq.Array(orderIDs))
	if err != nil {
		err := fmt.Errorf("failed to batch load blacklisted asset IDs: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, nil, err
	}
	defer blacklistedRows.Close()

	for blacklistedRows.Next() {
		var orderID, assetID string
		if err := blacklistedRows.Scan(&orderID, &assetID); err != nil {
			err := fmt.Errorf("failed to scan blacklisted asset ID row: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, nil, err
		}
		blacklistedAssetIDsMap[orderID] = append(blacklistedAssetIDsMap[orderID], assetID)
	}

	// Batch load order updates - 1 query instead of N queries
	updateRows, err := tx.QueryContext(spanContext, `
		SELECT order_id, message, timestamp, update_source
		FROM order_updates
		WHERE order_id = ANY($1)
		ORDER BY order_id, timestamp ASC
	`, pq.Array(orderIDs))
	if err != nil {
		err := fmt.Errorf("failed to batch load order updates: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, nil, err
	}
	defer updateRows.Close()

	for updateRows.Next() {
		var orderID, message string
		var timestamp time.Time
		var updateSource int32
		if err := updateRows.Scan(&orderID, &message, &timestamp, &updateSource); err != nil {
			err := fmt.Errorf("failed to scan order update row: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, nil, err
		}
		updateEntry := &orders.OrderUpdateEntry{
			Message:      message,
			Timestamp:    commonUtils.TimeToISO8601String(timestamp),
			UpdateSource: situations.UpdateSource(updateSource),
		}
		orderUpdatesMap[orderID] = append(orderUpdatesMap[orderID], updateEntry)
	}

	// Batch load order status updates - 1 query instead of N queries
	statusUpdateRows, err := tx.QueryContext(spanContext, `
		SELECT order_id, entry_timestamp, new_status, previous_status, new_type_specific_status, previous_type_specific_status, note, updater_id, update_source, status_update_timestamp
		FROM order_status_updates
			WHERE order_id = ANY($1)
		ORDER BY order_id, entry_timestamp ASC
	`, pq.Array(orderIDs))
	if err != nil {
		err := fmt.Errorf("failed to batch load order status updates: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, nil, err
	}
	defer statusUpdateRows.Close()

	for statusUpdateRows.Next() {
		var orderID, note, updaterID string
		var ts time.Time
		var statusUpdateTs sql.NullTime
		var newStatus, previousStatus sql.NullInt32
		var newTypeSpecific, previousTypeSpecific sql.NullString
		var updateSource int32

		if err := statusUpdateRows.Scan(&orderID, &ts, &newStatus, &previousStatus, &newTypeSpecific, &previousTypeSpecific, &note, &updaterID, &updateSource, &statusUpdateTs); err != nil {
			err := fmt.Errorf("failed to scan order status update row: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, nil, err
		}

		// Convert sql.NullInt32 to orders.OrderStatus
		var convertedNewStatus, convertedPreviousStatus orders.OrderStatus
		if newStatus.Valid {
			convertedNewStatus = orders.OrderStatus(newStatus.Int32)
		} else {
			convertedNewStatus = orders.OrderStatus(0)
		}
		if previousStatus.Valid {
			convertedPreviousStatus = orders.OrderStatus(previousStatus.Int32)
		} else {
			convertedPreviousStatus = orders.OrderStatus(0)
		}

		// Convert sql.NullString to string
		newTypeSpecificStr := ""
		if newTypeSpecific.Valid {
			newTypeSpecificStr = newTypeSpecific.String
		}
		previousTypeSpecificStr := ""
		if previousTypeSpecific.Valid {
			previousTypeSpecificStr = previousTypeSpecific.String
		}

		// Handle status_update_timestamp
		statusUpdateTimestampStr := ""
		if statusUpdateTs.Valid {
			statusUpdateTimestampStr = commonUtils.TimeToISO8601String(statusUpdateTs.Time)
		} else {
			// If status_update_timestamp is NULL, use the timestamp value
			statusUpdateTimestampStr = commonUtils.TimeToISO8601String(ts)
		}

		updateEntry := &orders.OrderStatusUpdateEntry{
			EntryTimestamp:             commonUtils.TimeToISO8601String(ts),
			NewStatus:                  convertedNewStatus,
			PreviousStatus:             convertedPreviousStatus,
			NewTypeSpecificStatus:      newTypeSpecificStr,
			PreviousTypeSpecificStatus: previousTypeSpecificStr,
			Note:                       note,
			UpdaterId:                  updaterID,
			UpdateSource:               situations.UpdateSource(updateSource),
			StatusUpdateTimestamp:      statusUpdateTimestampStr,
		}
		statusUpdatesMap[orderID] = append(statusUpdatesMap[orderID], updateEntry)
	}

	// Batch load order permissions - 1 query instead of N queries
	permissionRows, err := tx.QueryContext(spanContext, `SELECT order_id, scope, asset_type FROM order_permissions WHERE order_id = ANY($1)`, pq.Array(orderIDs))
	if err != nil {
		err := fmt.Errorf("failed to batch load order permissions: %w", err)
		herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
		return nil, nil, nil, nil, nil, err
	}
	defer permissionRows.Close()

	for permissionRows.Next() {
		var orderID, scope string
		var assetTypeValue int32
		if err := permissionRows.Scan(&orderID, &scope, &assetTypeValue); err != nil {
			err := fmt.Errorf("failed to scan order permission row: %w", err)
			herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeDatabase)
			return nil, nil, nil, nil, nil, err
		}
		switch scope {
		case "CHANGE_STATUS":
			permissionsMap[orderID].CanChangeStatus = append(permissionsMap[orderID].CanChangeStatus, assets.AssetType(assetTypeValue))
		case "ASSIGN_ASSET":
			permissionsMap[orderID].CanAssignAsset = append(permissionsMap[orderID].CanAssignAsset, assets.AssetType(assetTypeValue))
		}
	}

	return allowedAssetTypesMap, blacklistedAssetIDsMap, orderUpdatesMap, statusUpdatesMap, permissionsMap, nil
}

// AddOrderStatusUpdate manually adds a status update entry to an order.
func (repository *PostgresOrderRepository) AddOrderStatusUpdate(ctx context.Context, transaction *sql.Tx, orderID string, statusUpdate *orders.OrderStatusUpdateEntry) error {
	spanContext, span, finishSpan := herosentry.StartSpan(ctx, "OrderRepository.AddOrderStatusUpdate")
	defer finishSpan()

	span.SetTag("order.id", orderID)

	return database.WithSessionErr(repository.database, spanContext, transaction, func(sessionTx *sql.Tx) error {
		// Validate that statusUpdate is not nil
		if statusUpdate == nil {
			return errors.New("status update cannot be nil")
		}

		// Retrieve the org_id from spanContext
		orgID := cmncontext.GetOrgId(spanContext)
		span.SetTag("org.id", fmt.Sprintf("%d", orgID))

		// Handle timestamp logic
		var timestamp time.Time
		if statusUpdate.EntryTimestamp != "" {
			// If entry_timestamp is provided in the request, use that value
			var err error
			timestamp, err = commonUtils.ISO8601StringToTime(statusUpdate.EntryTimestamp)
			if err != nil {
				err := fmt.Errorf("invalid entry_timestamp format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}
		} else {
			// Otherwise, use current time
			timestamp = time.Now()
		}

		// Handle status_update_timestamp logic
		var statusUpdateTimestamp time.Time
		if statusUpdate.StatusUpdateTimestamp != "" {
			// If status_update_timestamp is provided in the request, use that value
			var err error
			statusUpdateTimestamp, err = commonUtils.ISO8601StringToTime(statusUpdate.StatusUpdateTimestamp)
			if err != nil {
				err := fmt.Errorf("invalid status_update_timestamp format: %w", err)
				herosentry.CaptureException(spanContext, err, herosentry.ErrorTypeInternal)
				return err
			}
		} else {
			// Otherwise, set status_update_timestamp to the timestamp value
			statusUpdateTimestamp = timestamp
		}

		query := `
			INSERT INTO order_status_updates
				(order_id, org_id, entry_timestamp, new_status, previous_status, new_type_specific_status, previous_type_specific_status, note, updater_id, update_source, status_update_timestamp)
			VALUES
				($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		`

		preparedStatement, preparationError := sessionTx.PrepareContext(spanContext, query)
		if preparationError != nil {
			return preparationError
		}
		defer preparedStatement.Close()

		_, executionError := preparedStatement.ExecContext(spanContext,
			orderID,
			orgID,
			timestamp,
			int32(statusUpdate.NewStatus),
			int32(statusUpdate.PreviousStatus),
			statusUpdate.NewTypeSpecificStatus,
			statusUpdate.PreviousTypeSpecificStatus,
			statusUpdate.Note,
			statusUpdate.UpdaterId,
			int32(statusUpdate.UpdateSource),
			statusUpdateTimestamp,
		)
		return executionError
	})
}
