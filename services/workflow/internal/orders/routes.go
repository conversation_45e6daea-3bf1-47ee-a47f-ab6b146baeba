package orders

import (
	"database/sql"
	"log"
	"net/http"

	"common/herosentry"
	"proto/hero/orders/v2/ordersconnect"
	assetRepository "workflow/internal/assets/data"
	caseRepository "workflow/internal/cases/data"
	"workflow/internal/common/middleware"
	"workflow/internal/orders/api/connect"
	orderRepository "workflow/internal/orders/data"
	"workflow/internal/orders/usecase"
	situationRepository "workflow/internal/situations/data"

	reportRepository "workflow/internal/reports/data"

	connectgo "connectrpc.com/connect"
)

func RegisterRoutes(mux *http.ServeMux,
	orderDB *sql.DB,
	assetRepo assetRepository.AssetRepository,
	situationRepo situationRepository.SituationRepository,
	orderRepo orderRepository.OrderRepository,
	reportRepo reportRepository.ReportRepository,
	caseRepo caseRepository.CaseRepository) {

	// Initialize Order Use Case
	orderUseCase, err := usecase.NewOrderUseCase(orderDB, assetRepo, situationRepo, orderRepo, reportRepo, caseRepo)
	if err != nil {
		log.Fatalf("Failed to initialize Order Use Case: %v", err)
	}

	// Create our Connect-based Order Server
	orderServer := connect.NewOrderServer(orderUseCase)

	// Generate HTTP handler from Connect with herosentry interceptor
	servicePath, serviceHandler := ordersconnect.NewOrderServiceHandler(
		orderServer,
		connectgo.WithInterceptors(herosentry.RPCServiceInterceptor()),
	)

	// Add the handler to the mux
	mux.Handle(servicePath, middleware.LoggingMiddleware(serviceHandler))
}
