// side_effects.go
package usecase

import (
	"proto/hero/orders/v2"
)

// OrderSideEffectType indicates the kind of side effect to process.
type OrderSideEffectType int

const (
	// OrderSideEffect_None represents no side effect; no action is taken.
	OrderSideEffect_None OrderSideEffectType = iota

	// OrderSideEffect_TryChangingSituationToTriaging indicates an attempt to change the situation status to 'triaging',
	// but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryChangingSituationToTriaging

	// OrderSideEffect_TryChangingSituationToDispatching indicates an attempt to change the situation status to 'dispatching',
	// but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryChangingSituationToDispatching

	// OrderSideEffect_TryChangingSituationToAddressing indicates an attempt to change the situation status to 'addressing',
	// typically used when the issue is actively being attended to, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryChangingSituationToAddressing

	// OrderSideEffect_TryChangingSituationToResolved attempts to change the current situation to 'resolved',
	// ensuring all necessary steps have been completed, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryChangingSituationToResolved

	// OrderSideEffect_TryCreatingReportOrder attempts to trigger the creation of a report order,
	// which is used to document the incident or escalate the issue, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryCreatingReportOrder

	// OrderSideEffect_TryChangingSituationToCompleted attempts to change the current situation to 'completed',
	// ensuring that all necessary steps have been completed, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryChangingSituationToCompleted

	// OrderSideEffect_TryCreatingNewOrderWithBlacklistedAsset attempts to create a new order involving an asset that is blacklisted,
	// potentially for further investigation or special handling, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryCreatingNewOrderWithBlacklistedAsset

	// OrderSideEffect_TryMarkingAssetBusy attempts to mark the asset as busy,
	// indicating that it is currently in use and should not be assigned to new tasks, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryMarkingAssetBusy

	// OrderSideEffect_TryMarkingAssetAvailable attempts to mark the asset as available,
	// signifying that it is free to be assigned to tasks again, but the action is not guaranteed - as other conditions need to meet.
	OrderSideEffect_TryMarkingAssetAvailable

	// OrderSideEffect_MarkReportInProgress indicates that a report related to the order is in progress.
	OrderSideEffect_MarkReportInProgress

	// OrderSideEffect_MarkReportUnderReview indicates that a report related to the order is under review.
	OrderSideEffect_MarkReportUnderReview

	// OrderSideEffect_MarkReportInRework indicates that a report related to the order is in rework.
	OrderSideEffect_MarkReportInRework

	// OrderSideEffect_MarkCaseInvestigating indicates that a case should be moved to investigating status.
	OrderSideEffect_MarkCaseInvestigating
)

// OrderSideEffectChecker defines methods to check for side effects based on an entity update.
type OrderSideEffectChecker struct{}

// CheckOrderStatusChangeOrderSideEffect evaluates the order update and returns a slice of side effects
// along with the updated order. Multiple side effects can be returned if multiple conditions apply.
func (d *OrderSideEffectChecker) CheckOrderStatusChangeSideEffect(updatedOrder *orders.Order) ([]OrderSideEffectType, *orders.Order) {
	var effects []OrderSideEffectType

	// Check for an immediate side effect for rejected orders.
	if updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_REJECTED {
		return []OrderSideEffectType{OrderSideEffect_TryCreatingNewOrderWithBlacklistedAsset, OrderSideEffect_TryMarkingAssetAvailable}, updatedOrder
	}

	// Process based on order type.
	switch updatedOrder.Type {
	case orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT, orders.OrderType_ORDER_TYPE_TRIAGE_AGENT_REPORT, orders.OrderType_ORDER_TYPE_TRIAGE_CAMERA_INCIDENT:
		switch updatedOrder.Status {
		case orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED:
			effects = append(effects, OrderSideEffect_TryChangingSituationToTriaging)
		case orders.OrderStatus_ORDER_STATUS_IN_PROGRESS:
			effects = append(effects, OrderSideEffect_TryChangingSituationToTriaging)
		case orders.OrderStatus_ORDER_STATUS_COMPLETED:
			effects = append(effects, OrderSideEffect_TryChangingSituationToResolved, OrderSideEffect_TryChangingSituationToCompleted)
		}

	case orders.OrderType_ORDER_TYPE_ASSIST_MEMBER:
		switch updatedOrder.Status {
		case orders.OrderStatus_ORDER_STATUS_CREATED:
			effects = append(effects, OrderSideEffect_TryChangingSituationToDispatching, OrderSideEffect_TryMarkingAssetBusy)
		case orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED:
			effects = append(effects, OrderSideEffect_TryChangingSituationToAddressing, OrderSideEffect_TryMarkingAssetBusy)
		case orders.OrderStatus_ORDER_STATUS_IN_PROGRESS:
			effects = append(effects, OrderSideEffect_TryChangingSituationToAddressing, OrderSideEffect_TryMarkingAssetBusy)
		case orders.OrderStatus_ORDER_STATUS_COMPLETED:
			effects = append(effects, OrderSideEffect_TryChangingSituationToResolved, OrderSideEffect_TryCreatingReportOrder, OrderSideEffect_TryMarkingAssetAvailable)
		case orders.OrderStatus_ORDER_STATUS_CANCELLED:
			effects = append(effects, OrderSideEffect_TryMarkingAssetAvailable)
		}

	case orders.OrderType_ORDER_TYPE_SUBMIT_FINAL_REPORT:
		if updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_COMPLETED {
			effects = append(effects, OrderSideEffect_TryChangingSituationToCompleted)
		}
	case orders.OrderType_ORDER_TYPE_WRITE_REPORT:
		if updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED ||
			updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_IN_PROGRESS {
			effects = append(effects, OrderSideEffect_MarkReportInProgress)
		}
	case orders.OrderType_ORDER_TYPE_REVIEW_REPORT:
		if updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED ||
			updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_IN_PROGRESS {
			effects = append(effects, OrderSideEffect_MarkReportUnderReview)
		}
	case orders.OrderType_ORDER_TYPE_REVISE_REPORT:
		if updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED ||
			updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_IN_PROGRESS {
			effects = append(effects, OrderSideEffect_MarkReportInRework)
		}
	case orders.OrderType_ORDER_TYPE_MANAGE_CASE:
		if updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED ||
			updatedOrder.Status == orders.OrderStatus_ORDER_STATUS_IN_PROGRESS {
			effects = append(effects, OrderSideEffect_MarkCaseInvestigating)
		}
	}

	// Default side effect if none matched.
	if len(effects) == 0 {
		effects = append(effects, OrderSideEffect_None)
	}

	return effects, updatedOrder
}
