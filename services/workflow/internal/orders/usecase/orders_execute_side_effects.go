// orders_execute_side_effects.go
package usecase

import (
	"context"
	"database/sql"
	"fmt"
	"log"

	"common/herosentry"
	"proto/hero/assets/v2"
	casespb "proto/hero/cases/v1"
	"proto/hero/orders/v2"
	"proto/hero/reports/v2"
	"proto/hero/situations/v2"

	"google.golang.org/protobuf/proto"
)

// OrderSideEffectExecutor handles execution of side effects for orders.
type OrderSideEffectExecutor struct {
}

// NewOrderSideEffectExecutor creates a new OrderSideEffectExecutor.
func NewOrderSideEffectExecutor() *OrderSideEffectExecutor {
	return &OrderSideEffectExecutor{}
}

// terminalReport returns true if the report status is final.
func terminalReport(status reports.ReportStatus) bool {
	return status == reports.ReportStatus_REPORT_STATUS_APPROVED ||
		status == reports.ReportStatus_REPORT_STATUS_REJECTED ||
		status == reports.ReportStatus_REPORT_STATUS_CANCELLED
}

// ExecuteSideEffect executes the given side effect for the provided order in the same transaction.
// The transaction parameter represents the current transaction.
func (exec *OrderSideEffectExecutor) ExecuteSideEffect(ctx context.Context, tx *sql.Tx, effect OrderSideEffectType, updatedOrder *orders.Order, orderUseCase *OrderUseCase) error {
	// Add side effect details to current span
	if span := herosentry.CurrentSpan(ctx); span != nil {
		span.SetTag("side_effect.type", fmt.Sprintf("%v", effect))
		span.SetTag("side_effect.order_id", updatedOrder.Id)
		span.SetData("side_effect.order_status", updatedOrder.Status.String())
		if updatedOrder.SituationId != "" {
			span.SetTag("side_effect.situation_id", updatedOrder.SituationId)
		}
	}

	switch effect {
	case OrderSideEffect_None:
		return nil
	case OrderSideEffect_TryChangingSituationToTriaging:
		return exec.tryChangingSituationToTriaging(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryChangingSituationToDispatching:
		return exec.tryChangingSituationToDispatching(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryChangingSituationToAddressing:
		return exec.tryChangingSituationToAddressing(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryChangingSituationToResolved:
		return exec.tryChangingSituationToResolved(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryCreatingReportOrder:
		return exec.tryCreatingReportOrder(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryChangingSituationToCompleted:
		return exec.tryChangingSituationToCompleted(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryCreatingNewOrderWithBlacklistedAsset:
		return exec.tryCreatingNewOrderWithBlacklistedAsset(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryMarkingAssetBusy:
		return exec.tryMarkingAssetBusy(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_TryMarkingAssetAvailable:
		return exec.tryMarkingAssetAvailable(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_MarkReportInProgress:
		return exec.tryMarkingReportInProgress(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_MarkReportUnderReview:
		return exec.tryMarkingReportUnderReview(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_MarkReportInRework:
		return exec.tryMarkingReportInRework(ctx, tx, updatedOrder, orderUseCase)
	case OrderSideEffect_MarkCaseInvestigating:
		return exec.tryMarkingCaseInvestigating(ctx, tx, updatedOrder, orderUseCase)
	default:
		// No side effect to execute.
		return nil
	}
}

// tryChangingSituationToTriaging contains the logic for changing the situation to triaging.
func (exec *OrderSideEffectExecutor) tryChangingSituationToTriaging(ctx context.Context, tx *sql.Tx, updatedOrder *orders.Order, orderUseCase *OrderUseCase) error {
	log.Printf("Executing side effect within transaction: Changing situation to triaging for order ID %v\n", updatedOrder.Id)
	if updatedOrder.SituationId == "" {
		return nil
	}
	// Transaction is nil for GetSituation because this is a non blocking call.
	currentSituation, err := orderUseCase.situationRepo.GetSituation(ctx, nil, updatedOrder.SituationId)

	if err != nil {
		err := fmt.Errorf("tryChangingSituationToTriaging: failed to get situation: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if currentSituation == nil {
		err := fmt.Errorf("tryChangingSituationToTriaging: situation %s not found", updatedOrder.SituationId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}
	if currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED ||
		currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_CREATED {
		// This makes sure all the other fields are intact and only the status is updated.
		currentSituation.Status = situations.SituationStatus_SITUATION_STATUS_TRIAGING
		if _, err := orderUseCase.situationRepo.UpdateSituation(ctx, tx, currentSituation); err != nil {
			err := fmt.Errorf("tryChangingSituationToTriaging: failed to update situation: %w", err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}
	}
	return nil
}

// tryChangingSituationToDispatching contains the logic for changing the situation to dispatching.
func (exec *OrderSideEffectExecutor) tryChangingSituationToDispatching(ctx context.Context, tx *sql.Tx, updatedOrder *orders.Order, orderUseCase *OrderUseCase) error {
	log.Printf("Executing side effect within transaction: Changing situation to dispatching for order ID %v\n", updatedOrder.Id)
	if updatedOrder.SituationId == "" {
		return nil
	}
	// Transaction is nil for GetSituation because this is a non blocking call.
	currentSituation, err := orderUseCase.situationRepo.GetSituation(ctx, nil, updatedOrder.SituationId)
	if err != nil {
		err := fmt.Errorf("tryChangingSituationToDispatching: failed to get situation: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if currentSituation == nil {
		err := fmt.Errorf("tryChangingSituationToDispatching: situation %s not found", updatedOrder.SituationId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}
	if currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED ||
		currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_CREATED ||
		currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_TRIAGING {
		// This makes sure all the other fields are intact and only the status is updated.
		currentSituation.Status = situations.SituationStatus_SITUATION_STATUS_DISPATCHING
		if _, err := orderUseCase.situationRepo.UpdateSituation(ctx, tx, currentSituation); err != nil {
			err := fmt.Errorf("tryChangingSituationToDispatching: failed to update situation: %w", err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}
	}
	return nil
}

// tryChangingSituationToAddressing contains the logic for changing the situation to addressing.
func (exec *OrderSideEffectExecutor) tryChangingSituationToAddressing(ctx context.Context, tx *sql.Tx, updatedOrder *orders.Order, orderUseCase *OrderUseCase) error {
	log.Printf("Executing side effect within transaction: Changing situation to addressing for order ID %v\n", updatedOrder.Id)
	if updatedOrder.SituationId == "" {
		return nil
	}
	// Transaction is nil for GetSituation because this is a non blocking call.
	currentSituation, err := orderUseCase.situationRepo.GetSituation(ctx, nil, updatedOrder.SituationId)
	if err != nil {
		err := fmt.Errorf("failed to get situation: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if currentSituation == nil {
		err := fmt.Errorf("tryChangingSituationToAddressing: situation %s not found", updatedOrder.SituationId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}
	if currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED ||
		currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_CREATED ||
		currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_TRIAGING ||
		currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_DISPATCHING {
		// This makes sure all the other fields are intact and only the status is updated.
		currentSituation.Status = situations.SituationStatus_SITUATION_STATUS_ADDRESSING
		if _, err := orderUseCase.situationRepo.UpdateSituation(ctx, tx, currentSituation); err != nil {
			err := fmt.Errorf("tryChangingSituationToAddressing: failed to update situation: %w", err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}
	}
	return nil
}

// tryChangingSituationToResolved contains the logic for changing the situation to resolved.
func (exec *OrderSideEffectExecutor) tryChangingSituationToResolved(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Changing situation to resolved for order ID %v\n", updatedOrder.Id)

	// Ensure the order is tied to a situation.
	if updatedOrder.SituationId == "" {
		log.Printf("Order %v has no associated situation; nothing to do.\n", updatedOrder.Id)
		return nil
	}

	// Only proceed if the updated order is finalized.
	if updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED {
		log.Printf("Order %v is not in COMPLETED state (current state: %v); skipping situation update.\n", updatedOrder.Id, updatedOrder.Status)
		return nil
	}

	// List all orders related to the same situation with proper pagination.
	allOrdersForSituation := []*orders.Order{}
	pageToken := ""
	const pageSize = 50

	for {
		ordersList, err := orderUseCase.orderRepo.ListOrdersForSituation(ctx, nil, updatedOrder.SituationId, pageSize, pageToken, orders.OrderStatus_ORDER_STATUS_UNSPECIFIED)
		if err != nil {
			err := fmt.Errorf("error listing orders for situation %s: %w", updatedOrder.SituationId, err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}

		allOrdersForSituation = append(allOrdersForSituation, ordersList.Orders...)

		// Check if there are more pages
		if ordersList.PageToken == "" {
			break
		}
		pageToken = ordersList.PageToken
	}

	allFinalized := true
	// Iterate through all orders in the situation.
	for _, order := range allOrdersForSituation {
		// Skip the updated order if not needed for verification.
		if order.Id == updatedOrder.Id {
			continue
		}
		// Only consider orders of types that are relevant for finalization.
		if order.Type != orders.OrderType_ORDER_TYPE_ASSIST_MEMBER &&
			order.Type != orders.OrderType_ORDER_TYPE_TRIAGE_MEMBER_REPORT &&
			order.Type != orders.OrderType_ORDER_TYPE_TRIAGE_AGENT_REPORT &&
			order.Type != orders.OrderType_ORDER_TYPE_TRIAGE_CAMERA_INCIDENT {
			continue
		}
		// If any relevant order is not in a terminal state, mark as not finalized.
		if order.Status != orders.OrderStatus_ORDER_STATUS_REJECTED &&
			order.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED &&
			order.Status != orders.OrderStatus_ORDER_STATUS_CANCELLED {
			log.Printf("Order %v is not finalized; situation state remains unchanged.\n", order.Id)
			allFinalized = false
			break
		}
	}

	// If all relevant orders are finalized, update the situation to RESOLVED.
	if allFinalized {
		// Retrieve the situation; note the call is non-transactional.
		currentSituation, err := orderUseCase.situationRepo.GetSituation(ctx, nil, updatedOrder.SituationId)
		if err != nil {
			err := fmt.Errorf("failed to get situation: %w", err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}
		if currentSituation == nil {
			err := fmt.Errorf("tryChangingSituationToResolved: situation %s not found", updatedOrder.SituationId)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
			return err
		}
		if currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_CREATED ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_TRIAGING ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_DISPATCHING ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_ADDRESSING {
			// This makes sure all the other fields are intact and only the status is updated.
			currentSituation.Status = situations.SituationStatus_SITUATION_STATUS_RESOLVED
			if _, err := orderUseCase.situationRepo.UpdateSituation(ctx, tx, currentSituation); err != nil {
				err := fmt.Errorf("tryChangingSituationToResolved: failed to update situation: %w", err)
				herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
				return err
			}
		}

		log.Printf("Situation %v has been changed to RESOLVED.\n", currentSituation.Id)
	} else {
		log.Printf("Not all relevant orders in situation %v are finalized; situation state remains unchanged.\n", updatedOrder.SituationId)
	}

	return nil
}

// tryCreatingReportOrder contains the logic for creating a report order.
func (exec *OrderSideEffectExecutor) tryCreatingReportOrder(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Creating report order for order ID %v\n", updatedOrder.Id)

	// Check that the order is of type ASSIST_MEMBER.
	if updatedOrder.Type != orders.OrderType_ORDER_TYPE_ASSIST_MEMBER {
		log.Printf("Order %v is not of type ASSIST_MEMBER; skipping report order creation.\n", updatedOrder.Id)
		return nil
	}

	// Check that the order is going to be completed.
	if updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED {
		log.Printf("Order %v is not in COMPLETED state (current state: %v); skipping report order creation.\n", updatedOrder.Id, updatedOrder.Status)
		return nil
	}

	// Check that the order has an asset associated with it.
	if updatedOrder.AssetId == "" {
		log.Printf("Order %v has no associated asset; skipping report order creation.\n", updatedOrder.Id)
		return nil
	}

	// Check if a report order already exists for the same asset and situation with proper pagination.
	allExistingOrders := []*orders.Order{}
	pageToken := ""
	const pageSize = 50

	for {
		existingOrders, err := orderUseCase.orderRepo.ListOrdersForSituation(ctx, nil, updatedOrder.SituationId, pageSize, pageToken, orders.OrderStatus_ORDER_STATUS_UNSPECIFIED)
		if err != nil {
			err := fmt.Errorf("error listing orders for situation %s: %w", updatedOrder.SituationId, err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}

		allExistingOrders = append(allExistingOrders, existingOrders.Orders...)

		// Check if there are more pages
		if existingOrders.PageToken == "" {
			break
		}
		pageToken = existingOrders.PageToken
	}

	for _, order := range allExistingOrders {
		if order.AssetId == updatedOrder.AssetId && order.Type == orders.OrderType_ORDER_TYPE_SUBMIT_FINAL_REPORT {
			log.Printf("Report order already exists for asset %v and situation %v; skipping report order creation.\n", updatedOrder.AssetId, updatedOrder.SituationId)
			return nil
		}
	}

	// Create a new order with:
	// - Status set to IN_PROGRESS.
	// - Type set to SUBMIT_FINAL_REPORT.
	// - The same asset ID as the updated order.
	// - The same situation ID as the updated order.
	newOrder := &orders.Order{
		AssetId:     updatedOrder.AssetId,
		SituationId: updatedOrder.SituationId,
		Status:      orders.OrderStatus_ORDER_STATUS_IN_PROGRESS,
		Type:        orders.OrderType_ORDER_TYPE_SUBMIT_FINAL_REPORT,
	}

	// Create the new order in the repository using the same transaction.
	if _, err := orderUseCase.orderRepo.CreateOrder(ctx, tx, newOrder); err != nil {
		err := fmt.Errorf("failed to create report order: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Report order created successfully for asset %v.\n", updatedOrder.AssetId)
	return nil
}

// tryChangingSituationToCompleted contains the logic for changing the situation to completed.
func (exec *OrderSideEffectExecutor) tryChangingSituationToCompleted(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Changing situation to completed for order ID %v\n", updatedOrder.Id)

	// Ensure the updated order is tied to a situation.
	if updatedOrder.SituationId == "" {
		log.Printf("Order %v has no associated situation; nothing to do.\n", updatedOrder.Id)
		return nil
	}

	// Only proceed if the updated order is finalized.
	if updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED {
		log.Printf("Order %v is not in COMPLETED state (current state: %v); skipping situation update.\n", updatedOrder.Id, updatedOrder.Status)
		return nil
	}

	// List all orders related to the same situation with proper pagination.
	allOrdersForSituation := []*orders.Order{}
	pageToken := ""
	const pageSize = 50

	for {
		ordersList, err := orderUseCase.orderRepo.ListOrdersForSituation(ctx, nil, updatedOrder.SituationId, pageSize, pageToken, orders.OrderStatus_ORDER_STATUS_UNSPECIFIED)
		if err != nil {
			err := fmt.Errorf("error listing orders for situation %s: %w", updatedOrder.SituationId, err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}

		allOrdersForSituation = append(allOrdersForSituation, ordersList.Orders...)

		// Check if there are more pages
		if ordersList.PageToken == "" {
			break
		}
		pageToken = ordersList.PageToken
	}

	allFinalized := true
	// Iterate through all orders in the situation.
	for _, order := range allOrdersForSituation {
		// Skip the updated order.
		if order.Id == updatedOrder.Id {
			continue
		}
		// If any relevant order is not in a terminal state, mark as not finalized.
		if order.Status != orders.OrderStatus_ORDER_STATUS_REJECTED &&
			order.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED &&
			order.Status != orders.OrderStatus_ORDER_STATUS_CANCELLED {
			log.Printf("Order %v is not finalized; situation state remains unchanged.\n", order.Id)
			allFinalized = false
			break
		}
	}

	// If all relevant orders are finalized, update the situation to COMPLETED.
	if allFinalized {
		// Retrieve the situation using a non-transactional call.
		currentSituation, err := orderUseCase.situationRepo.GetSituation(ctx, nil, updatedOrder.SituationId)
		if err != nil {
			err := fmt.Errorf("failed to get situation: %w", err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}
		if currentSituation == nil {
			err := fmt.Errorf("tryChangingSituationToCompleted: situation %s not found", updatedOrder.SituationId)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
			return err
		}
		// Only update if the situation is in one of the allowed states.
		if currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_UNSPECIFIED ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_CREATED ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_TRIAGING ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_DISPATCHING ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_ADDRESSING ||
			currentSituation.Status == situations.SituationStatus_SITUATION_STATUS_RESOLVED {
			// Change the situation's status to COMPLETED while leaving other fields intact.
			currentSituation.Status = situations.SituationStatus_SITUATION_STATUS_COMPLETED
			if _, err := orderUseCase.situationRepo.UpdateSituation(ctx, tx, currentSituation); err != nil {
				err := fmt.Errorf("tryChangingSituationToCompleted: failed to update situation: %w", err)
				herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
				return err
			}
			log.Printf("Situation %v has been changed to COMPLETED.\n", currentSituation.Id)
		}
	} else {
		log.Printf("Not all orders in situation %v are finalized; situation state remains unchanged.\n", updatedOrder.SituationId)
	}

	return nil
}

// tryCreatingNewOrderWithBlacklistedAsset contains the logic for creating a new order with a blacklisted asset.
func (exec *OrderSideEffectExecutor) tryCreatingNewOrderWithBlacklistedAsset(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Creating new order with blacklisted asset for order ID %v\n", updatedOrder.Id)

	// Step 1: Check that the updated order has been rejected.
	if updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_REJECTED {
		log.Printf("Order %v is not in REJECTED status; skipping creation of new order.\n", updatedOrder.Id)
		return nil
	}

	// Step 2: Create a new order that is a clone of updatedOrder.
	// Create a deep copy of the updatedOrder.
	newOrder := proto.Clone(updatedOrder).(*orders.Order)

	// Mark it a new creted order.
	newOrder.Status = orders.OrderStatus_ORDER_STATUS_CREATED

	// Since this is a new order, clear the ID (assuming the repository will generate a new one).
	newOrder.Id = ""

	// Capture the original asset ID before removing it.
	originalAssetID := updatedOrder.AssetId

	// Remove the asset from the new order.
	newOrder.AssetId = ""

	// Append the original asset ID to the blacklisted asset IDs.
	newOrder.BlacklistedAssetIds = append(newOrder.BlacklistedAssetIds, originalAssetID)

	// Optionally, you may adjust other fields such as timestamps or status if needed.

	// Create the new order using the repository. The new order is created within the same transaction.
	if _, err := orderUseCase.orderRepo.CreateOrder(ctx, tx, newOrder); err != nil {
		err := fmt.Errorf("failed to create new order with blacklisted asset: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("New order created with asset %v blacklisted successfully.\n", originalAssetID)
	return nil
}

// tryMarkingAssetBusy contains the logic for marking the asset as busy.
func (exec *OrderSideEffectExecutor) tryMarkingAssetBusy(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Marking asset as busy for order ID %v\n", updatedOrder.Id)

	// Check if the order has an asset associated with it.
	if updatedOrder.AssetId == "" {
		log.Printf("Order %v has no associated asset; skipping asset busy marking.\n", updatedOrder.Id)
		return nil
	}

	// Check if the order is an ASSIST_MEMBER task.
	if updatedOrder.Type != orders.OrderType_ORDER_TYPE_ASSIST_MEMBER {
		log.Printf("Order %v is not an ASSIST_MEMBER task; skipping asset busy marking.\n", updatedOrder.Id)
		return nil
	}

	// Check if the order's status is either IN_PROGRESS or ACKNOWLEDGED.
	if updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_IN_PROGRESS &&
		updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED &&
		updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_CREATED {
		log.Printf("Order %v is not in CREATED/IN_PROGRESS/ACKNOWLEDGED state (current state: %v); skipping asset busy marking.\n", updatedOrder.Id, updatedOrder.Status)
		return nil
	}

	// Fetch the asset associated with the order.
	// Transaction is nil for GetAsset because this is a non blocking call.
	asset, err := orderUseCase.assetRepo.GetAsset(ctx, nil, updatedOrder.AssetId)
	if err != nil {
		err := fmt.Errorf("failed to retrieve asset with id %v: %w", updatedOrder.AssetId, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if asset == nil {
		err := fmt.Errorf("tryMarkingAssetBusy: asset %s not found", updatedOrder.AssetId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}

	// Mark the asset's status as BUSY.
	// This makes sure all the other fields are intact and only the status is updated.
	asset.Status = assets.AssetStatus_ASSET_STATUS_BUSY

	// Update the asset in the repository.
	if _, err := orderUseCase.assetRepo.UpdateAsset(ctx, tx, asset); err != nil {
		err := fmt.Errorf("failed to update asset %v: %w", asset.Id, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Asset %v marked as busy successfully.\n", asset.Id)
	return nil
}

// tryMarkingAssetAvailable contains the logic for marking the asset as available.
func (exec *OrderSideEffectExecutor) tryMarkingAssetAvailable(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Marking asset as available for order ID %v\n", updatedOrder.Id)

	// Check if the order has an asset associated with it.
	if updatedOrder.AssetId == "" {
		log.Printf("Order %v has no associated asset; skipping asset available marking.\n", updatedOrder.Id)
		return nil
	}

	if updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_COMPLETED &&
		updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_CANCELLED &&
		updatedOrder.Status != orders.OrderStatus_ORDER_STATUS_REJECTED {
		log.Printf("Order %v is not in COMPLETED/CANCELLED/REJECTED/CREATED state (current state: %v); skipping asset available marking.\n", updatedOrder.Id, updatedOrder.Status)
		return nil
	}

	// List all active orders for the asset with proper pagination.
	// Active orders are those in statuses like CREATED, ACKNOWLEDGED, SNOOZED, or IN_PROGRESS.
	allActiveOrders := []*orders.Order{}
	pageToken := ""
	const pageSize = 50

	for {
		activeOrders, nextPageToken, err := orderUseCase.ListActiveAssignedOrdersForAsset(ctx, updatedOrder.AssetId, pageSize, pageToken)
		if err != nil {
			err := fmt.Errorf("error listing active orders for asset %v: %w", updatedOrder.AssetId, err)
			herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
			return err
		}

		allActiveOrders = append(allActiveOrders, activeOrders...)

		// Check if there are more pages
		if nextPageToken == "" {
			break
		}
		pageToken = nextPageToken
	}

	// Check if there is any other ASSIST_MEMBER order for the same asset.
	otherActiveAssistOrderExists := false
	for _, order := range allActiveOrders {
		// Optionally skip the updated order itself.
		if order.Id == updatedOrder.Id {
			continue
		}
		if order.Type == orders.OrderType_ORDER_TYPE_ASSIST_MEMBER &&
			(order.Status == orders.OrderStatus_ORDER_STATUS_ACKNOWLEDGED ||
				order.Status == orders.OrderStatus_ORDER_STATUS_IN_PROGRESS ||
				order.Status == orders.OrderStatus_ORDER_STATUS_CREATED) {
			otherActiveAssistOrderExists = true
			break
		}
	}

	// If there is another active ASSIST_MEMBER order, we keep the asset busy.
	if otherActiveAssistOrderExists {
		log.Printf("Other active ASSIST_MEMBER orders found for asset %v; asset remains busy.\n", updatedOrder.AssetId)
		return nil
	}

	// No other active ASSIST_MEMBER order exists; mark the asset as available.
	// Transaction is nil for GetAsset because this is a non blocking call.
	asset, err := orderUseCase.assetRepo.GetAsset(ctx, nil, updatedOrder.AssetId)
	if err != nil {
		err := fmt.Errorf("failed to retrieve asset %v: %w", updatedOrder.AssetId, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if asset == nil {
		err := fmt.Errorf("tryMarkingAssetAvailable: asset %s not found", updatedOrder.AssetId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}

	// This makes sure all the other fields are intact and only the status is updated.
	asset.Status = assets.AssetStatus_ASSET_STATUS_AVAILABLE

	if _, err := orderUseCase.assetRepo.UpdateAsset(ctx, tx, asset); err != nil {
		err := fmt.Errorf("failed to update asset %v: %w", asset.Id, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Asset %v marked as available successfully.\n", asset.Id)
	return nil
}

// tryMarkingReportInProgress contains the logic for marking the report as in progress.
func (exec *OrderSideEffectExecutor) tryMarkingReportInProgress(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Marking report as in progress for order ID %v\n", updatedOrder.Id)

	// Check if the order has a report ID
	if updatedOrder.ReportId == "" {
		log.Printf("Order %v has no associated report; skipping marking as in progress.\n", updatedOrder.Id)
		return nil
	}

	// Get the report from the repository
	report, err := orderUseCase.reportRepo.GetReport(ctx, nil, updatedOrder.ReportId)
	if err != nil {
		err := fmt.Errorf("failed to get report %s: %w", updatedOrder.ReportId, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if report == nil {
		err := fmt.Errorf("tryMarkingReportInProgress: report %s not found", updatedOrder.ReportId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}

	// Check if the report is already in a terminal state
	if terminalReport(report.Status) {
		log.Printf("Report %v is already in a terminal state; skipping marking as in progress.\n", report.Id)
		return nil
	}

	// Update the report status to IN_PROGRESS
	report.Status = reports.ReportStatus_REPORT_STATUS_IN_PROGRESS

	// Update the report in the repository using the transaction
	_, err = orderUseCase.reportRepo.UpdateReport(ctx, tx, report)
	if err != nil {
		err := fmt.Errorf("failed to update report %s status to IN_PROGRESS: %w", report.Id, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Report %v marked as in progress successfully.\n", report.Id)
	return nil
}

// tryMarkingReportUnderReview contains the logic for marking the report as under review.
func (exec *OrderSideEffectExecutor) tryMarkingReportUnderReview(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Marking report as under review for order ID %v\n", updatedOrder.Id)

	// Check if the order has a report ID
	if updatedOrder.ReportId == "" {
		log.Printf("Order %v has no associated report; skipping marking as under review.\n", updatedOrder.Id)
		return nil
	}

	// Get the report from the repository
	report, err := orderUseCase.reportRepo.GetReport(ctx, nil, updatedOrder.ReportId)
	if err != nil {
		err := fmt.Errorf("failed to get report %s: %w", updatedOrder.ReportId, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if report == nil {
		err := fmt.Errorf("tryMarkingReportUnderReview: report %s not found", updatedOrder.ReportId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}

	// Check if the report is already in a terminal state
	if terminalReport(report.Status) {
		log.Printf("Report %v is already in a terminal state; skipping marking as under review.\n", report.Id)
		return nil
	}

	// Update the report status to UNDER_REVIEW
	report.Status = reports.ReportStatus_REPORT_STATUS_UNDER_REVIEW

	// Update the report in the repository using the transaction
	_, err = orderUseCase.reportRepo.UpdateReport(ctx, tx, report)
	if err != nil {
		err := fmt.Errorf("failed to update report %s status to UNDER_REVIEW: %w", report.Id, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Report %v marked as under review successfully.\n", report.Id)
	return nil
}

// tryMarkingCaseInvestigating updates a case status to investigating when the order is acknowledged/in progress.
func (exec *OrderSideEffectExecutor) tryMarkingCaseInvestigating(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Marking case as investigating for order ID %v\n", updatedOrder.Id)

	// Check if the order has a case ID
	if updatedOrder.CaseId == "" {
		log.Printf("Order %v has no associated case; skipping marking as investigating.\n", updatedOrder.Id)
		return nil
	}

	// Get the case from the repository
	caseData, err := orderUseCase.caseRepo.GetCase(ctx, nil, updatedOrder.CaseId)
	if err != nil {
		err := fmt.Errorf("failed to get case %s: %w", updatedOrder.CaseId, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if caseData == nil {
		err := fmt.Errorf("tryMarkingCaseInvestigating: case %s not found", updatedOrder.CaseId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound, true)
		return err
	}

	// Only update status if case is in initial states (UNSPECIFIED, NEW, or OPEN)
	// Skip update for all other statuses as they indicate case is already being worked on
	if caseData.Status != casespb.CaseStatus_CASE_STATUS_UNSPECIFIED &&
		caseData.Status != casespb.CaseStatus_CASE_STATUS_NEW &&
		caseData.Status != casespb.CaseStatus_CASE_STATUS_OPEN {
		log.Printf("Case %v is already in status %v; skipping status update to INVESTIGATING.\n", caseData.Id, caseData.Status)
		return nil
	}

	// Update case status to investigating
	updateRequest := &casespb.UpdateCaseStatusRequest{
		CaseId: updatedOrder.CaseId,
		Status: casespb.CaseStatus_CASE_STATUS_INVESTIGATING,
		Note:   "Case investigation started - order acknowledged",
	}

	_, err = orderUseCase.caseRepo.UpdateCaseStatus(ctx, tx, updateRequest)
	if err != nil {
		err := fmt.Errorf("failed to update case status to investigating: %w", err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Case %v status updated to investigating successfully.\n", updatedOrder.CaseId)
	return nil
}

// tryMarkingReportInRework contains the logic for marking the report as in rework.
func (exec *OrderSideEffectExecutor) tryMarkingReportInRework(
	ctx context.Context,
	tx *sql.Tx,
	updatedOrder *orders.Order,
	orderUseCase *OrderUseCase,
) error {
	log.Printf("Executing side effect within transaction: Marking report as in rework for order ID %v\n", updatedOrder.Id)

	// Check if the order has a report ID
	if updatedOrder.ReportId == "" {
		log.Printf("Order %v has no associated report; skipping marking as in rework.\n", updatedOrder.Id)
		return nil
	}

	// Get the report from the repository
	report, err := orderUseCase.reportRepo.GetReport(ctx, nil, updatedOrder.ReportId)
	if err != nil {
		err := fmt.Errorf("failed to get report %s: %w", updatedOrder.ReportId, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}
	if report == nil {
		err := fmt.Errorf("tryMarkingReportInRework: report %s not found", updatedOrder.ReportId)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeNotFound)
		return err
	}

	// Check if the report is already in a terminal state
	if terminalReport(report.Status) {
		log.Printf("Report %v is already in a terminal state; skipping marking as in rework.\n", report.Id)
		return nil
	}

	// Update the report status to IN_REWORK
	report.Status = reports.ReportStatus_REPORT_STATUS_IN_REWORK

	// Update the report in the repository using the transaction
	_, err = orderUseCase.reportRepo.UpdateReport(ctx, tx, report)
	if err != nil {
		err := fmt.Errorf("failed to update report %s status to IN_REWORK: %w", report.Id, err)
		herosentry.CaptureException(ctx, err, herosentry.ErrorTypeDatabase)
		return err
	}

	log.Printf("Report %v marked as in rework successfully.\n", report.Id)
	return nil
}
